# 报表注册管理系统详细概要设计

## 1. 系统概述

### 1.1 系统简介
报表注册管理系统是数据运营平台的核心模块，主要负责报表的注册、管理、权限控制及角色关联。系统支持报表的全生命周期管理，包括注册、审批、启用、停用等状态流转，并提供监控告警和任务待办功能。

### 1.2 技术架构
- **框架**: Spring Boot 2.x + Spring Cloud
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **缓存**: Redis
- **服务发现**: Nacos
- **API文档**: Swagger/Knife4j
- **前端**: Vue.js + Element UI

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js前端应用]
    end
    
    subgraph "网关层"
        B[API Gateway]
    end
    
    subgraph "业务服务层"
        C[报表注册管理服务]
        D[用户权限服务]
        E[主数据服务]
        F[业权系统]
    end
    
    subgraph "数据层"
        G[MySQL数据库]
        H[Redis缓存]
    end
    
    subgraph "外部系统"
        I[观远BI]
        J[QuickBI]
        K[BO系统]
        L[FANREPORT]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    C --> F
    C --> G
    C --> H
    C --> I
    C --> J
    C --> K
    C --> L
```

### 2.2 服务架构图

```mermaid
graph LR
    subgraph "报表注册管理服务"
        A1[Controller层]
        A2[Service层]
        A3[Repository层]
        A4[Domain层]
    end
    
    subgraph "外部服务"
        B1[主数据服务]
        B2[业权系统]
        B3[报表系统]
    end
    
    A1 --> A2
    A2 --> A3
    A2 --> A4
    A2 --> B1
    A2 --> B2
    A2 --> B3
```

## 3. 业务流程设计

### 3.1 报表注册流程图

```mermaid
sequenceDiagram
    participant U as 需求分析师/数据产品经理
    participant S as 报表注册系统
    participant M as 主数据系统
    participant A as 业权系统
    participant R as 报表广场
    
    U->>S: 点击新增报表
    S->>U: 显示报表注册表单
    U->>S: 填写报表信息
    S->>M: 获取用户信息
    M->>S: 返回用户信息
    U->>S: 提交报表注册
    S->>S: 表单校验
    S->>A: 提交审批申请
    A->>S: 返回审批结果
    alt 审批通过
        S->>R: 添加报表到报表广场
        S->>U: 报表注册成功
    else 审批失败
        S->>U: 报表注册失败
    end
```

### 3.2 报表查询流程图

```mermaid
flowchart TD
    A[开始] --> B{是否查询报表标题?}
    B -->|是| C[输入报表标题]
    B -->|否| D{是否查询业务主题域?}
    C --> E[执行查询]
    D -->|是| F[选择业务主题域]
    D -->|否| G{是否查询报表编号?}
    F --> E
    G -->|是| H[输入报表编号]
    G -->|否| I{是否需要其他查询条件?}
    H --> E
    I -->|是| J[输入其他条件]
    I -->|否| K{是否选择展示类型?}
    J --> E
    K -->|是| L[选择展示类型]
    K -->|否| M{是否筛选报表状态?}
    L --> E
    M -->|是| N[选择报表状态]
    M -->|否| E
    N --> E
    E --> O[显示查询结果]
    O --> P[结束]
```

## 4. 数据库设计

### 4.1 报表注册主表 (report_registration)

```sql
CREATE TABLE `report_registration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_code` varchar(50) NOT NULL COMMENT '报表编码(系统自动生成)',
  `report_title` varchar(200) NOT NULL COMMENT '报表标题',
  `report_category_id` bigint(20) NOT NULL COMMENT '报表类别ID',
  `business_domain_id` bigint(20) NOT NULL COMMENT '业务主要域ID',
  `report_url` varchar(500) NOT NULL COMMENT '报表链接',
  `report_source` varchar(50) NOT NULL COMMENT '报表来源(观远BI/QuickBI/BO/FANREPORT)',
  `display_type` varchar(20) NOT NULL COMMENT '展示类型(PC端/移动端)',
  `report_status` varchar(20) NOT NULL COMMENT '报表状态(启用/停用/待审批/审批失败)',
  `requirement_owner_id` bigint(20) NOT NULL COMMENT '需求负责人ID',
  `requirement_owner_name` varchar(100) NOT NULL COMMENT '需求负责人姓名',
  `tech_owner_id` bigint(20) NOT NULL COMMENT '技术负责人ID',
  `tech_owner_name` varchar(100) NOT NULL COMMENT '技术负责人姓名',
  `business_owner_id` bigint(20) NOT NULL COMMENT '业务负责人ID',
  `business_owner_name` varchar(100) NOT NULL COMMENT '业务负责人姓名',
  `dataset_refresh_url` varchar(500) COMMENT '数据集刷新链接',
  `report_icon` varchar(200) COMMENT '报表图标路径',
  `description` text COMMENT '描述说明',
  `online_time` datetime COMMENT '上线时间',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) NOT NULL COMMENT '更新人ID',
  `update_name` varchar(100) NOT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0:未删除,1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_report_code` (`report_code`),
  UNIQUE KEY `uk_report_title` (`report_title`),
  KEY `idx_report_category` (`report_category_id`),
  KEY `idx_business_domain` (`business_domain_id`),
  KEY `idx_report_status` (`report_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表注册主表';
```

### 4.2 报表类别表 (report_category)

```sql
CREATE TABLE `report_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_code` varchar(50) NOT NULL COMMENT '类别编码',
  `category_name` varchar(100) NOT NULL COMMENT '类别名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父级ID',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) NOT NULL COMMENT '更新人ID',
  `update_name` varchar(100) NOT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0:未删除,1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表类别表';
```

### 4.3 业务主要域表 (business_domain)

```sql
CREATE TABLE `business_domain` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `domain_code` varchar(50) NOT NULL COMMENT '域编码',
  `domain_name` varchar(100) NOT NULL COMMENT '域名称',
  `description` varchar(500) COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) NOT NULL COMMENT '更新人ID',
  `update_name` varchar(100) NOT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0:未删除,1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain_code` (`domain_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务主要域表';
```

### 4.4 报表权限表 (report_permission)

```sql
CREATE TABLE `report_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` bigint(20) NOT NULL COMMENT '报表ID',
  `permission_type` varchar(20) NOT NULL COMMENT '权限类型(按部门/按地区/按角色)',
  `permission_value` varchar(100) NOT NULL COMMENT '权限值',
  `permission_name` varchar(200) NOT NULL COMMENT '权限名称',
  `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) NOT NULL COMMENT '更新人ID',
  `update_name` varchar(100) NOT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0:未删除,1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_permission_type` (`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表权限表';
```

## 5. 接口设计

### 5.1 报表注册管理接口

#### 5.1.1 查询报表列表

**接口路径**: `GET /api/report/list`

**请求参数**:
```json
{
  "reportTitle": "string",      // 报表标题(可选)
  "reportCategory": "string",   // 报表类别(可选)
  "reportCode": "string",       // 报表编号(可选)
  "reportSource": "string",     // 报表来源(可选)
  "displayType": "string",      // 展示类型(可选)
  "reportStatus": "string",     // 报表状态(可选)
  "pageNum": 1,                 // 页码
  "pageSize": 10                // 每页大小
}
```

**响应结果**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "reportCode": "RPT001",
        "reportTitle": "管理驾驶舱",
        "reportCategory": "管理驾驶舱",
        "reportSource": "观远BI",
        "displayType": "PC端",
        "reportStatus": "启用",
        "createName": "admin",
        "onlineTime": "2024-01-16 10:32:45"
      }
    ]
  }
}
```

#### 5.1.2 新增报表注册

**接口路径**: `POST /api/report/register`

**请求参数**:
```json
{
  "reportTitle": "string",           // 报表标题(必填)
  "reportCategoryId": 1,             // 报表类别ID(必填)
  "businessDomainId": 1,             // 业务主要域ID(必填)
  "reportUrl": "string",             // 报表链接(必填)
  "reportSource": "观远BI",          // 报表来源(必填)
  "displayType": "PC端",             // 展示类型(必填)
  "reportStatus": "启用",            // 报表状态(必填)
  "requirementOwnerId": 1,           // 需求负责人ID(必填)
  "techOwnerId": 1,                  // 技术负责人ID(必填)
  "businessOwnerId": 1,              // 业务负责人ID(必填)
  "datasetRefreshUrl": "string",     // 数据集刷新链接(可选)
  "reportIcon": "string",            // 报表图标(必填)
  "description": "string",           // 描述说明(可选)
  "permissions": [                   // 报表权限(可选)
    {
      "permissionType": "按部门",
      "permissionValue": "DEPT001",
      "permissionName": "技术部"
    }
  ]
}
```

**响应结果**:
```json
{
  "code": 200,
  "message": "报表注册成功",
  "data": {
    "id": 1,
    "reportCode": "RPT001"
  }
}
```

#### 5.1.3 查看报表详情

**接口路径**: `GET /api/report/{id}`

**路径参数**:
- `id`: 报表ID

**响应结果**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "reportCode": "RPT001",
    "reportTitle": "管理驾驶舱",
    "reportCategoryId": 1,
    "reportCategoryName": "管理驾驶舱",
    "businessDomainId": 1,
    "businessDomainName": "订单交付管理",
    "reportUrl": "http://example.com/report",
    "reportSource": "观远BI",
    "displayType": "PC端",
    "reportStatus": "启用",
    "requirementOwnerId": 1,
    "requirementOwnerName": "张三",
    "techOwnerId": 2,
    "techOwnerName": "李四",
    "businessOwnerId": 3,
    "businessOwnerName": "王五",
    "datasetRefreshUrl": "http://example.com/refresh",
    "reportIcon": "/icons/report.png",
    "description": "管理驾驶舱报表",
    "onlineTime": "2024-01-16 10:32:45",
    "permissions": [
      {
        "permissionType": "按部门",
        "permissionValue": "DEPT001",
        "permissionName": "技术部"
      }
    ]
  }
}
```

#### 5.1.4 编辑报表信息

**接口路径**: `PUT /api/report/{id}`

**路径参数**:
- `id`: 报表ID

**请求参数**: (同新增报表注册接口)

**响应结果**:
```json
{
  "code": 200,
  "message": "报表信息更新成功",
  "data": null
}
```

#### 5.1.5 删除报表

**接口路径**: `DELETE /api/report/{id}`

**路径参数**:
- `id`: 报表ID

**响应结果**:
```json
{
  "code": 200,
  "message": "报表删除成功",
  "data": null
}
```

### 5.2 报表类别管理接口

#### 5.2.1 查询报表类别列表

**接口路径**: `GET /api/report/category/list`

**响应结果**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "categoryCode": "CAT001",
      "categoryName": "管理驾驶舱",
      "parentId": 0,
      "sortOrder": 1,
      "status": 1
    }
  ]
}
```

### 5.3 业务主要域管理接口

#### 5.3.1 查询业务主要域列表

**接口路径**: `GET /api/report/domain/list`

**响应结果**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "domainCode": "DOM001",
      "domainName": "订单交付管理",
      "description": "订单交付相关业务域",
      "status": 1
    }
  ]
}
```
