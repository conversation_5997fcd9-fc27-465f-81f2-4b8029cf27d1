<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityAuditTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskPO">
        <id column="id" property="id"/>
        <result column="audit_plan_no" property="auditPlanNo"/>
        <result column="audit_task_no" property="auditTaskNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="audit_product_series" property="auditProductSeries"/>
        <result column="audit_product_range" property="auditProductRange"/>
        <result column="three_category_code" property="threeCategoryCode"/>
        <result column="three_category_name" property="threeCategoryName"/>
        <result column="sqe_id" property="sqeId"/>
        <result column="sqe_name" property="sqeName"/>
        <result column="division_code" property="divisionCode"/>
        <result column="division_name" property="divisionName"/>
        <result column="predict_complete_date" property="predictCompleteDate"/>
        <result column="practical_complete_date" property="practicalCompleteDate"/>
        <result column="audit_conclusion" property="auditConclusion"/>
        <result column="audit_score" property="auditScore"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="audit_result" property="auditResult"/>
        <result column="create_task" property="createTask"/>
        <result column="status" property="status"/>
        <result column="audit_opinion" property="auditOpinion"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="columnAll">
        id, audit_result, audit_score, audit_plan_no, audit_task_no, supplier_code, supplier_name, supplier_short_name,
        audit_product_series, audit_product_range, three_category_code, three_category_name, sqe_id, sqe_name,
        division_code, division_name, predict_complete_date, practical_complete_date, audit_conclusion, audit_remark,
        create_task, status, audit_opinion, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>
    <sql id="selectColumn">
        t1.id,
        t1.audit_result,
        t1.audit_score,
        t1.audit_plan_no,
        t1.audit_task_no,
        t1.supplier_code,
        t1.supplier_name,
        t1.supplier_short_name,
        t1.audit_product_series,
        t1.audit_product_range,
        t1.three_category_code,
        t1.three_category_name,
        t1.sqe_id,
        t1.sqe_name,
        t1.division_code,
        t1.division_name,
        t1.predict_complete_date,
        t1.practical_complete_date,
        t1.audit_conclusion,
        t1.audit_remark,
        t1.create_task,
        t1.`status`,
        t1.audit_opinion,
        t1.create_by,
        t1.create_time,
        t1.create_name,
        t1.update_by,
        t1.update_time,
        t1.update_name,
        t1.is_delete,
        CASE
        WHEN t1.`status` != 'ENDED' THEN
        '进行中'
        WHEN t1.practical_complete_date &lt; t1.predict_complete_date THEN
        '逾期提交' ELSE '按时提交'
        END AS is_overdue,
        t2.sqe_master_id,
        t2.audit_manager_id,
        t2.audit_manager_name,
        t2.sqe_master_name,
        t2.audit_category_type,
        t2.audit_subclass_other,
        t2.audit_subclass_type
    </sql>
    <sql id="selectTable">
        FROM
        supplier_quality_audit_task t1
        LEFT JOIN supplier_quality_audit_plan t2 ON t1.audit_plan_no = t2.audit_plan_no
    </sql>
    <sql id="whereSql">
        <where>
            AND t1.`is_delete` = 0
            AND t2.`is_delete` = 0
            AND t1.status != 'NOT_STARTED'
            <if test="dto.auditTaskNo != null and dto.auditTaskNo != ''">
                AND t1.audit_task_no like concat("%",#{dto.auditTaskNo},"%")
            </if>
            <if test="dto.supplierCode != null and dto.supplierCode != ''">
                AND t1.supplier_code like concat("%",#{dto.supplierCode},"%")
            </if>
            <if test="dto.supplierShortName != null and dto.supplierShortName != ''">
                AND t1.supplier_short_name like concat("%",#{dto.supplierShortName},"%")
            </if>
            <if test="dto.sqeName != null and dto.sqeName != ''">
                AND t1.sqe_name like concat("%",#{dto.sqeName},"%")
            </if>
            <if test="dto.auditProductSeries != null and dto.auditProductSeries != ''">
                AND t1.audit_product_series LIKE concat("%",#{dto.auditProductSeries},"%")
            </if>
            <if test="dto.divisionCode != null and dto.divisionCode != ''">
                AND t1.division_code LIKE concat("%",#{dto.divisionCode},"%")
            </if>
            <if test="dto.status != null and dto.status != ''">
                AND t1.status = #{dto.status}
            </if>
            <if test="dto.sqeId != null">
                AND t1.sqe_id = #{dto.sqeId}
            </if>
            <if test="dto.sqeName != null and dto.sqeName != ''">
                AND t1.sqe_name like concat("%",#{dto.sqeName},"%")
            </if>
            <if test="dto.auditConclusion != null and dto.auditConclusion != ''">
                AND t1.audit_conclusion = #{dto.auditConclusion}
            </if>
            <if test="dto.threeCategoryCodes != null and dto.threeCategoryCodes != ''">
                AND t1.three_category_code in
                <foreach collection="dto.threeCategoryCodes" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.predictCompleteDateStart != null">
                AND t1.predict_complete_date &gt;= #{dto.predictCompleteDateStart}
            </if>
            <if test="dto.predictCompleteDateEnd != null">
                AND t1.predict_complete_date &lt;= #{dto.predictCompleteDateEnd}
            </if>
            <if test="dto.practicalCompleteDateStart != null">
                AND t1.practical_complete_date &gt;= #{dto.practicalCompleteDateStart}
            </if>
            <if test="dto.practicalCompleteDateEnd != null">
                AND t1.practical_complete_date &lt;= #{dto.practicalCompleteDateEnd}
            </if>
            <if test="dto.isOverdue != null and dto.isOverdue">
                AND t1.practical_complete_date &gt; t1.predict_complete_date
                AND t1.status = 'ENDED'
            </if>
            <if test="dto.isOverdue != null and !dto.isOverdue">
                AND (t1.status != 'ENDED' or ( t1.practical_complete_date &lt; t1.predict_complete_date and t1.status =
                'ENDED') )
            </if>
            <if test="dto.auditCategoryType != null and dto.auditCategoryType != ''">
                AND t2.audit_category_type = #{dto.auditCategoryType}
            </if>
            <if test="dto.auditSubclassType != null and dto.auditSubclassType != ''">
                AND t2.audit_subclass_type = #{dto.auditSubclassType}
            </if>
            <if test="dto.sqeMasterId != null">
                AND t2.sqe_master_id = #{dto.sqeMasterId}
            </if>
            <if test="dto.sqeMasterName != null and dto.sqeMasterName != ''">
                AND t2.sqe_master_name like concat("%",#{dto.sqeMasterName},"%")
            </if>

        </where>
    </sql>
    <sql id="orderBy">
        ORDER BY t1.`id` DESC
    </sql>
    <select id="listPageByQuery"
            resultType="com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO">
        SELECT
        <include refid="selectColumn"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        <include refid="orderBy"/>
    </select>

    <select id="listByQuery"
            resultType="com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO">
        SELECT
        <include refid="selectColumn"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        <include refid="orderBy"/>
    </select>

    <sql id="permissionSql">
        t1.sqe_id = #{dto.operationBy}
        OR t2.sqe_master_id = #{dto.operationBy}
        <if test="dto.categoryCodePermissionList != null and dto.categoryCodePermissionList.size > 0">
            OR t1.three_category_code IN
            <foreach collection="dto.categoryCodePermissionList" item="item2" open="(" separator="," close=")">
                #{item2}
            </foreach>
        </if>
    </sql>
    <sql id="pageQuerySql">
        SELECT
        t1.id,
        t1.audit_result,
        t1.audit_score,
        t1.audit_plan_no,
        t1.audit_task_no,
        t1.supplier_code,
        t1.supplier_name,
        t1.supplier_short_name,
        t1.audit_product_series,
        t1.audit_product_range,
        t1.three_category_code,
        t1.three_category_name,
        t1.sqe_id,
        t1.sqe_name,
        t1.division_code,
        t1.division_name,
        t1.predict_complete_date,
        t1.practical_complete_date,
        t1.audit_conclusion,
        t1.audit_remark,
        t1.create_task,
        t1.status,
        t1.create_by,
        t1.audit_opinion,
        t1.create_time,
        t1.create_name,
        t1.update_by,
        t1.update_time,
        t1.update_name,
        t1.is_delete,
        t2.sqe_master_id,
        t2.sqe_master_name,
        t2.audit_category_type,
        t2.audit_subclass_other,
        t2.audit_subclass_type
        FROM
        supplier_quality_audit_task t1
        LEFT JOIN supplier_quality_audit_plan t2 ON t1.audit_plan_no = t2.audit_plan_no
        <where>
            AND t1.`is_delete` = 0
            AND t2.`is_delete` = 0
            and t1.status != 'NOT_STARTED'
            <if test="dto.supplierCodeAndShortName != null and dto.supplierCodeAndShortName != ''">
                AND (
                t1.supplier_code LIKE concat("%",#{dto.supplierCodeAndShortName},"%")
                or t1.supplier_short_name LIKE concat("%",#{dto.supplierCodeAndShortName},"%")
                )
            </if>
            <if test="dto.divisionCode != null and dto.divisionCode != ''">
                AND t1.division_code LIKE concat("%",#{dto.divisionCode},"%")
            </if>
            <if test="dto.sqeId != null">
                AND t1.sqe_id = #{dto.sqeId}
            </if>
            <if test="dto.sqeName != null and dto.sqeName != ''">
                AND t1.sqe_name LIKE concat("%",#{dto.sqeName},"%")
            </if>
            <if test="dto.auditProductSeries != null and dto.auditProductSeries != ''">
                AND t1.audit_product_series LIKE concat("%",#{dto.auditProductSeries},"%")
            </if>
            <if test="dto.auditPlanNo != null and dto.auditPlanNo != ''">
                AND t1.audit_plan_no LIKE concat("%",#{dto.auditPlanNo},"%")
            </if>
            <if test="dto.auditTaskNo != null and dto.auditTaskNo != ''">
                AND t1.audit_task_no LIKE concat("%",#{dto.auditTaskNo},"%")
            </if>
            <if test="dto.status != null and dto.status != ''">
                AND t1.status = #{dto.status}
            </if>
            <if test="dto.threeCategoryCodes != null and dto.threeCategoryCodes.size > 0">
                AND t1.three_category_code IN
                <foreach collection="dto.threeCategoryCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.predictCompleteDateStart != null">
                AND t1.predict_complete_date &gt;= #{dto.predictCompleteDateStart}
            </if>
            <if test="dto.predictCompleteDateEnd != null">
                AND t1.predict_complete_date &lt;= #{dto.predictCompleteDateEnd}
            </if>
            <if test="dto.practicalCompleteDateStart != null">
                AND t1.practical_complete_date &gt;= #{dto.practicalCompleteDateStart}
            </if>
            <if test="dto.practicalCompleteDateEnd != null">
                AND t1.practical_complete_date &lt;= #{dto.practicalCompleteDateEnd}
            </if>
            <if test="dto.permissionType != 'ALL'">
                AND ( t1.create_by = #{dto.operationBy} OR
                <if test="dto.divisionIdPermissionList != null and dto.divisionIdPermissionList.size > 0">
                    <foreach collection="dto.divisionIdPermissionList" item="item" open="(" separator=" or " close=")">
                        t1.division_code LIKE concat("%",#{item},"%")
                    </foreach>
                    OR
                </if>
                <include refid="permissionSql"/>
                )
            </if>
        </where>
        ORDER BY t1.ID DESC
    </sql>


    <select id="pageByQuery"
            resultType="com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO">
        <include refid="pageQuerySql"/>
    </select>

    <select id="noPageByQuery"
            resultType="com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO">
        <include refid="pageQuerySql"/>
    </select>


</mapper>
