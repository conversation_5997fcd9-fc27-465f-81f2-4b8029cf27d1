<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityTimerReceiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityTimerReceivePO">
        <id column="id" property="id" />
        <result column="timer_no" property="timerNo" />
        <result column="receive_no" property="receiveNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_short_name" property="supplierShortName" />
        <result column="supplier_status" property="supplierStatus" />
        <result column="task_name" property="taskName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, timer_no, receive_no,supplier_status, supplier_code, supplier_name, supplier_short_name, task_name, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
