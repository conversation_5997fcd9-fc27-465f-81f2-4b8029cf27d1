<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityAuditPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditPlanPO">
        <id column="id" property="id" />
        <result column="audit_plan_no" property="auditPlanNo" />
        <result column="audit_category_type" property="auditCategoryType" />
        <result column="audit_subclass_type" property="auditSubclassType" />
        <result column="audit_subclass_other" property="auditSubclassOther" />
        <result column="first_category_code" property="firstCategoryCode" />
        <result column="first_category_name" property="firstCategoryName" />
        <result column="sqe_master_id" property="sqeMasterId" />
        <result column="sqe_master_name" property="sqeMasterName" />
        <result column="audit_manager_id" property="auditManagerId" />
        <result column="audit_manager_name" property="auditManagerName" />
        <result column="audit_plan_remark" property="auditPlanRemark" />
        <result column="status" property="status" />
        <result column="audit_result" property="auditResult" />
        <result column="audit_opinion" property="auditOpinion" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_progress" property="auditProgress" />
        <result column="division_code" property="divisionCode"/>
        <result column="division_name" property="divisionName"/>
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, audit_plan_no, audit_category_type, audit_subclass_type, audit_subclass_other, first_category_code, first_category_name, sqe_master_id, sqe_master_name, audit_manager_id, audit_manager_name, audit_plan_remark, status, audit_result, audit_opinion, audit_time, audit_progress,
        division_code, division_name, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
