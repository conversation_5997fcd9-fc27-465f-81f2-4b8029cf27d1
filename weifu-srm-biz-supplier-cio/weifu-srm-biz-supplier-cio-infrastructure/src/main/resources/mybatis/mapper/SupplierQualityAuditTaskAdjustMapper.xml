<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityAuditTaskAdjustMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskAdjustPO">
        <id column="id" property="id" />
        <result column="adjust_no" property="adjustNo" />
        <result column="audit_plan_no" property="auditPlanNo" />
        <result column="audit_task_no" property="auditTaskNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_short_name" property="supplierShortName" />
        <result column="audit_product_series" property="auditProductSeries" />
        <result column="audit_product_range" property="auditProductRange" />
        <result column="three_category_code" property="threeCategoryCode" />
        <result column="three_category_name" property="threeCategoryName" />
        <result column="sqe_id" property="sqeId" />
        <result column="sqe_name" property="sqeName" />
        <result column="division_code" property="divisionCode" />
        <result column="division_name" property="divisionName" />
        <result column="predict_complete_date" property="predictCompleteDate" />
        <result column="status" property="status" />
        <result column="adjust_type" property="adjustType" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, adjust_no, audit_plan_no,adjust_type, audit_task_no, supplier_code, supplier_name, supplier_short_name, audit_product_series, audit_product_range, three_category_code, three_category_name, sqe_id, sqe_name, division_code, division_name, predict_complete_date, status, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
