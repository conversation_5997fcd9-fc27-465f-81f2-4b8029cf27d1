<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualitySupplierTaskSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualitySupplierTaskSubPO">
        <id column="id" property="id"/>
        <result column="task_no" property="taskNo"/>
        <result column="task_type" property="taskType"/>
        <result column="sub_task_no" property="subTaskNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="predict_complete_date" property="predictCompleteDate"/>
        <result column="task_name" property="taskName"/>
        <result column="status" property="status"/>
        <result column="feedback_time" property="feedbackTime"/>
        <result column="feedback_explanation" property="feedbackExplanation"/>
        <result column="end_time" property="endTime"/>
        <result column="audit_result" property="auditResult"/>
        <result column="last_audit_explanation" property="lastAuditExplanation"/>
        <result column="audit_explanation" property="auditExplanation"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="columnAll">
        id, task_no, task_type, sub_task_no, supplier_code, supplier_name, supplier_short_name, predict_complete_date,
        task_name, status, feedback_time, feedback_explanation, end_time, audit_result, last_audit_explanation,
        audit_explanation, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>
    <sql id="tableName">`supplier_quality_supplier_task_sub`</sql>
    <sql id="selectTable">FROM
        <include refid="tableName"/>
    </sql>
    <sql id="selectColumn">

    </sql>
    <sql id="whereSql">
        <where>
            AND t1.`is_delete` = 0
            AND t2.`is_delete` = 0
        </where>
    </sql>
    <sql id="orderBy">
        ORDER BY t1.`id` DESC
    </sql>
    <select id="statisticsCloseStatus"
            resultType="com.weifu.srm.supplier.cio.repository.bo.SupplierQualitySupplierTaskStatusCountBO">
        SELECT
        task_no as taskNo,
        COUNT(IF(`status`="CLOSED" or `status`="WITHDRAWN" ,1, null)) as closedCount,
        COUNT(id)  as totalCount
        from supplier_quality_supplier_task_sub
        where task_no in
        <foreach collection="taskNoList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        GROUP BY task_no
    </select>


    <select id="listPageByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="selectColumn"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        <include refid="orderBy"/>
    </select>

    <select id="listByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="selectColumn"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        <include refid="orderBy"/>
    </select>

    <select id="countBySupplier" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        supplier_quality_supplier_task_sub
        WHERE
        is_delete = 0
        AND supplier_code = #{supplierCode}
        AND `status` IN (
        "SUBMITTED",
        "APPROVE_FAIL")

    </select>

</mapper>
