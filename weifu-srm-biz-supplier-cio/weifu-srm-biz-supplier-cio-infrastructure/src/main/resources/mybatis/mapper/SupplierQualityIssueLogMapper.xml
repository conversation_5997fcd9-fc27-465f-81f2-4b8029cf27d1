<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityIssueLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueLogPO">
        <id column="id" property="id" />
        <result column="issue_no" property="issueNo" />
        <result column="issue_status" property="issueStatus" />
        <result column="audit_result" property="auditResult" />
        <result column="audit_opinion" property="auditOpinion" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, issue_no, issue_status, audit_result, audit_opinion, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>
    <sql id="tableName">`supplier_quality_issue_log`</sql>
</mapper>
