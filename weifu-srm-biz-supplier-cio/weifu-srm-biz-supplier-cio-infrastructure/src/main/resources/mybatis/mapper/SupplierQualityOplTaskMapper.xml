<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityOplTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityOplTaskPO">
        <id column="id" property="id" />
        <result column="opl_no" property="oplNo" />
        <result column="opl_name" property="oplName" />
        <result column="principal_id" property="principalId" />
        <result column="principal_name" property="principalName" />
        <result column="predict_complete_date" property="predictCompleteDate" />
        <result column="practical_complete_date" property="practicalCompleteDate" />
        <result column="opl_type" property="oplType" />
        <result column="opl_type_other" property="oplTypeOther" />
        <result column="relation_type" property="relationType" />
        <result column="relation_no" property="relationNo" />
        <result column="opl_description" property="oplDescription" />
        <result column="status" property="status" />
        <result column="feedback_user_id" property="feedbackUserId" />
        <result column="feedback_user_name" property="feedbackUserName" />
        <result column="feedback_explanation" property="feedbackExplanation" />
        <result column="feedback_time" property="feedbackTime" />
        <result column="end_user_id" property="endUserId" />
        <result column="end_user_name" property="endUserName" />
        <result column="end_time" property="endTime" />
        <result column="audit_result" property="auditResult" />
        <result column="last_audit_explanation" property="lastAuditExplanation" />
        <result column="audit_explanation" property="auditExplanation" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, opl_no, opl_name, principal_id, principal_name, predict_complete_date, practical_complete_date, opl_type, opl_type_other, relation_type, relation_no, opl_description, status, feedback_user_id, feedback_user_name, feedback_explanation, feedback_time, end_user_id, end_user_name, end_time, audit_result, last_audit_explanation, audit_explanation, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
