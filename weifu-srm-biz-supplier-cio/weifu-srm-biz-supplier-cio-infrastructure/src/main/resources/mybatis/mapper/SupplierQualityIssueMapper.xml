<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityIssueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssuePO">
        <id column="id" property="id"/>
        <result column="issue_no" property="issueNo"/>
        <result column="division_code" property="divisionCode"/>
        <result column="division_name" property="divisionName"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="supplier_contacts_id" property="supplierContactsId"/>
        <result column="supplier_contacts_type" property="supplierContactsType"/>
        <result column="supplier_contacts_name" property="supplierContactsName"/>
        <result column="supplier_contacts_email" property="supplierContactsEmail"/>
        <result column="part_sap_no" property="partSapNo"/>
        <result column="complaint_phase" property="complaintPhase"/>
        <result column="analysis_approach" property="analysisApproach"/>
        <result column="analysis_approach_other" property="analysisApproachOther"/>
        <result column="sqe_id" property="sqeId"/>
        <result column="sqe_name" property="sqeName"/>
        <result column="issue_status" property="issueStatus"/>
        <result column="part_product_series_name" property="partProductSeriesName"/>
        <result column="part_assembly_no" property="partAssemblyNo"/>
        <result column="part_name" property="partName"/>
        <result column="part_model" property="partModel"/>
        <result column="part_category_name" property="partCategoryName"/>
        <result column="part_category_code" property="partCategoryCode"/>
        <result column="part_complaint_qty" property="partComplaintQty"/>
        <result column="part_production_date" property="partProductionDate"/>
        <result column="part_production_batch" property="partProductionBatch"/>
        <result column="complaint_title" property="complaintTitle"/>
        <result column="complaint_date" property="complaintDate"/>
        <result column="complaint_custom_code" property="complaintCustomCode"/>
        <result column="complaint_custom_name" property="complaintCustomName"/>
        <result column="complaint_type" property="complaintType"/>
        <result column="complaint_failure_category" property="complaintFailureCategory"/>
        <result column="complaint_failure_category_other" property="complaintFailureCategoryOther"/>
        <result column="complaint_discovery_site" property="complaintDiscoverySite"/>
        <result column="complaint_failure_situation_resume" property="complaintFailureSituationResume"/>
        <result column="complaint_problem_description" property="complaintProblemDescription"/>
        <result column="reason_analysis_feedback_term" property="reasonAnalysisFeedbackTerm"/>
        <result column="prevent_recur_feedback_term" property="preventRecurFeedbackTerm"/>
        <result column="reason_analysis_feedback_date" property="reasonAnalysisFeedbackDate"/>
        <result column="prevent_recur_feedback_date" property="preventRecurFeedbackDate"/>
        <result column="reason_real_feedback_date" property="reasonRealFeedbackDate"/>
        <result column="prevent_real_feedback_date" property="preventRealFeedbackDate"/>
        <result column="submit_time" property="submitTime"/>
        <result column="receive_result" property="receiveResult"/>
        <result column="receive_opinion" property="receiveOpinion"/>
        <result column="temporary_measures" property="temporaryMeasures"/>
        <result column="temporary_is_upgrade" property="temporaryIsUpgrade"/>
        <result column="root_cause_is_recurring" property="rootCauseIsRecurring"/>
        <result column="root_cause_audit_result" property="rootCauseAuditResult"/>
        <result column="root_cause_audit_opinion" property="rootCauseAuditOpinion"/>
        <result column="measure_audit_result" property="measureAuditResult"/>
        <result column="measure_audit_opinion" property="measureAuditOpinion"/>
        <result column="measure_q11_benchmarking" property="measureQ11Benchmarking"/>
        <result column="measure_q11_benchmarking_other" property="measureQ11BenchmarkingOther"/>
        <result column="close_issue_department" property="closeIssueDepartment"/>
        <result column="close_issue_opinion" property="closeIssueOpinion"/>
        <result column="recall_issue_reason_description" property="recallIssueReasonDescription"/>
        <result column="supplier_analysis_description" property="supplierAnalysisDescription"/>
        <result column="supplier_analysis_action_plan" property="supplierAnalysisActionPlan"/>
        <result column="supplier_measure_actual" property="supplierMeasureActual"/>
        <result column="supplier_measure_prevention" property="supplierMeasurePrevention"/>
        <result column="supplier_measure_audit_opinion" property="supplierMeasureAuditOpinion"/>
        <result column="handle_id" property="handleId"/>
        <result column="other_description" property="otherDescription"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`supplier_quality_issue`</sql>
    <sql id="selectTable">FROM
        <include refid="tableName"/>
    </sql>
    <sql id="columnNoKey">
        issue_no, division_code, division_name, supplier_code, supplier_name,
        supplier_contacts_id,supplier_name_en,supplier_short_name,
        supplier_contacts_name, part_sap_no, complaint_phase, analysis_approach_other, analysis_approach, sqe_id,
        sqe_name, issue_status,
        part_product_series_name, part_assembly_no, part_name, part_model, part_category_name,part_category_code,
        part_complaint_qty,
        part_production_date, part_production_batch, complaint_title, complaint_date, complaint_custom_code,
        complaint_custom_name, complaint_type, complaint_failure_category, complaint_failure_category_other,
        complaint_discovery_site,
        complaint_failure_situation_resume, complaint_problem_description, reason_analysis_feedback_term,
        prevent_recur_feedback_term, reason_analysis_feedback_date, prevent_recur_feedback_date, submit_time,
        receive_result, receive_opinion,supplier_contacts_type,supplier_contacts_email,
        reason_real_feedback_date,prevent_real_feedback_date,
        temporary_measures, temporary_is_upgrade, root_cause_is_recurring, root_cause_audit_result,
        root_cause_audit_opinion, measure_audit_result, measure_audit_opinion,
        measure_q11_benchmarking,measure_q11_benchmarking_other,other_description,
        close_issue_department, close_issue_opinion, recall_issue_reason_description, supplier_analysis_description,
        supplier_analysis_action_plan, supplier_measure_actual, supplier_measure_prevention,handle_id,
        supplier_measure_audit_opinion, create_by, create_time, create_name, update_by, update_time, update_name,
        is_delete
    </sql>
    <sql id="columnAll">
        `id`,
        <include refid="columnNoKey"/>
    </sql>

    <sql id="whereSql">
        <where>
            AND `is_delete` = 0
            <if test="dto.issueNoLike != null and dto.issueNoLike != ''">
                AND issue_no like concat("%",#{dto.issueNoLike},"%")
            </if>
            <if test="dto.sqeName != null and dto.sqeName != ''">
                AND sqe_name like concat("%",#{dto.sqeName},"%")
            </if>
            <if test="dto.createName != null and dto.createName != ''">
                AND create_name like concat("%",#{dto.createName},"%")
            </if>
            <if test="dto.partSapNoLike != null and dto.partSapNoLike != ''">
                AND part_sap_no like concat("%",#{dto.partSapNoLike},"%")
            </if>
            <if test="dto.partNameLike != null and dto.partNameLike != ''">
                AND part_name like concat("%",#{dto.partNameLike},"%")
            </if>
            <if test="dto.partProductSeriesName != null and dto.partProductSeriesName != ''">
                AND part_product_series_name like concat("%",#{dto.partProductSeriesName},"%")
            </if>
            <if test="dto.divisionCodeList != null and dto.divisionCodeList.size != 0">
                AND division_code in
                <foreach collection="dto.divisionCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierCodeList != null and dto.supplierCodeList.size != 0">
                AND supplier_code in
                <foreach collection="dto.supplierCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.issueStatusList != null and dto.issueStatusList.size != 0">
                AND issue_status in
                <foreach collection="dto.issueStatusList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.createByList != null and dto.createByList.size != 0">
                AND create_by in
                <foreach collection="dto.createByList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.sqeIdList != null and dto.sqeIdList.size != 0">
                AND sqe_id in
                <foreach collection="dto.sqeIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.analysisApproachList != null  and dto.analysisApproachList.size != 0">
                AND analysis_approach in
                <foreach collection="dto.analysisApproachList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.partCategoryCodeList != null  and dto.partCategoryCodeList.size != 0">
                AND part_category_code in
                <foreach collection="dto.partCategoryCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.complaintCustomCodeList != null  and dto.complaintCustomCodeList.size != 0">
                AND complaint_custom_code in
                <foreach collection="dto.complaintCustomCodeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.complaintTypeList != null  and dto.complaintTypeList.size != 0">
                AND complaint_type in
                <foreach collection="dto.complaintTypeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.complaintFailureCategoryList != null  and dto.complaintFailureCategoryList.size != 0">
                AND complaint_failure_category in
                <foreach collection="dto.complaintFailureCategoryList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.supplierContactsId != null and dto.supplierContactsId != ''">
                AND supplier_contacts_id = #{dto.supplierContactsId}
            </if>
            <if test="dto.complaintPhase != null and dto.complaintPhase != ''">
                AND complaint_phase = #{dto.complaintPhase}
            </if>
            <if test="dto.complaintDateStart != null">
                AND complaint_date &gt;= #{dto.complaintDateStart}
            </if>
            <if test="dto.complaintDateEnd != null">
                AND complaint_date &lt;= #{dto.complaintDateEnd}
            </if>
            <if test="dto.submitTimeStart != null">
                AND submit_time &gt;= #{dto.submitTimeStart}
            </if>
            <if test="dto.submitTimeEnd != null">
                AND submit_time &lt;= #{dto.submitTimeEnd}
            </if>
            <if test="dto.rootCauseIsRecurring != null">
                AND root_cause_is_recurring = #{dto.rootCauseIsRecurring}
            </if>
            <if test="dto.measureQ11Benchmarking != null">
                AND measure_q11_benchmarking like concat("%",#{dto.measureQ11Benchmarking},"%")
            </if>
            <if test="dto.supplierMark != null and dto.supplierMark != ''">
                AND issue_status != 'DRAFT'
            </if>
            <if test="dto.issueNoList != null and dto.issueNoList.size != 0 ">
                AND issue_no in
                <foreach collection="dto.issueNoList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.categoryCodePermissionList != null or dto.divisionCodePermissionList != null ">
                AND (
                 part_category_code in
                    <foreach collection="dto.categoryCodePermissionList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                OR division_code in
                    <foreach collection="dto.divisionCodePermissionList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                )
            </if>

        </where>
    </sql>

    <select id="listPageByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="columnAll"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        ORDER BY `id` DESC
    </select>

    <select id="selectByStatusWithdrawnAndDraft" resultType="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueNumPo">
        SELECT
            sc.supplier_code as supplierCode,
            COUNT(DISTINCT sc.issue_no) as num
        FROM
            supplier_quality_issue sc
        WHERE
            sc.complaint_date BETWEEN #{startTime} and #{endTime}
            and sc.issue_status not in ('WITHDRAWN','DRAFT')
        GROUP BY supplierCode;
    </select>

    <select id="selectStatusHasFeedbackDate" resultType="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueNumPo">
        SELECT
            sc.supplier_code as supplierCode,
            COUNT(DISTINCT sc.issue_no ) as num
        FROM
            supplier_quality_issue sc,
            supplier_quality_issue_log sqil
        WHERE
            sc.issue_no = sqil.issue_no
            and sc.complaint_date BETWEEN #{startTime} and #{endTime}
            and sc.issue_status in ('CAUSE_ANALYSIS_VERIFIED','SUPPLIER_SUBMITTED_MEASURES',
                'MEASURES_VERIFICATION_FAILED','MEASURES_VERIFIED','ISSUE_CLOSED')
            and sqil.issue_status = 'CAUSE_ANALYSIS_VERIFIED'
            and NOT EXISTS (SELECT 1
                    FROM supplier_quality_issue_substitute sqis
                    WHERE sc.issue_no = sqis.issue_no
                    and sqis.substitute_input_type = 'ROOT_CASE_ANALYSIS' )
            and sc.reason_analysis_feedback_date &lt; DATE(sqil.create_time)
       GROUP BY supplierCode;
    </select>

    <select id="selectStatusNotHasFeedbackDate" resultType="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueNumPo">
        SELECT
            sc.supplier_code as supplierCode,
            COUNT(DISTINCT sc.issue_no ) as num
        FROM
            supplier_quality_issue sc
        WHERE
            sc.complaint_date BETWEEN #{startTime} and #{endTime}
            and sc.issue_status in ('PROPOSE_ISSUE','SUBMITTED_TEMPORARY_MEASURES','CONFIRMED_BY_SQE',
                    'SQE_RETURN','SUPPLIER_PROPOSED_CAUSE_ANALYSIS','CAUSE_ANALYSIS_VERIFICATION_FAILED')
            AND NOT EXISTS (SELECT 1
                FROM supplier_quality_issue_substitute sqis
                WHERE sc.issue_no = sqis.issue_no
                and sqis.substitute_input_type = 'ROOT_CASE_ANALYSIS' )
            and sc.reason_analysis_feedback_date &lt; CURDATE()
        GROUP BY supplierCode;
    </select>

    <select id="selectSubstitute" resultType="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueNumPo">
        SELECT
            sc.supplier_code as supplierCode,
            COUNT(DISTINCT sc.issue_no ) as num
        FROM
            supplier_quality_issue sc,
            supplier_quality_issue_substitute sqis
        WHERE
            sc.complaint_date BETWEEN #{startTime} and #{endTime}
            AND sc.issue_no = sqis.issue_no
            AND sc.issue_status not in ('WITHDRAWN','DRAFT')
            AND sqis.substitute_input_type = 'ROOT_CASE_ANALYSIS'
        GROUP BY supplierCode;
    </select>
    <select id="listByPendingProcessing" resultMap="BaseResultMap">
        SELECT
        <include refid="columnAll"/>
        <include refid="selectTable"/>
        where is_delete = 0
        and (
        (
        create_by = #{operationBy}
        and `issue_status` in
        ("DRAFT","PROPOSE_ISSUE","SQE_RETURN")
        )
        or
        (
        sqe_id = #{operationBy}
        and `issue_status` in
        ("CONFIRMED_BY_SQE")
        )
        or
        (
        handle_id = #{operationBy}
        and `issue_status` in
        ("SUPPLIER_PROPOSED_CAUSE_ANALYSIS","SUPPLIER_SUBMITTED_MEASURES")
        )
        )
    </select>
    <select id="countByInternal" resultType="java.lang.Integer">
        SELECT COUNT(*) from supplier_quality_issue
        where is_delete = 0
        and (
        (
         create_by = #{operationBy}
        and `issue_status` in
        ("DRAFT","PROPOSE_ISSUE","SQE_RETURN")
        )
        or
        (
        sqe_id = #{operationBy}
        and `issue_status` in
        ("CONFIRMED_BY_SQE")
        )
        or
        (
         handle_id = #{operationBy}
        and `issue_status` in
        ("SUPPLIER_PROPOSED_CAUSE_ANALYSIS","SUPPLIER_SUBMITTED_MEASURES")
        )
        )
    </select>

    <select id="countBySupplier" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        supplier_quality_issue
        WHERE
        is_delete = 0
        AND supplier_code = #{supplierCode}
        AND `issue_status` IN (
        "SUBMITTED_TEMPORARY_MEASURES",
        "CAUSE_ANALYSIS_VERIFICATION_FAILED",
        "CAUSE_ANALYSIS_VERIFIED",
        "MEASURES_VERIFICATION_FAILED")
    </select>
</mapper>
