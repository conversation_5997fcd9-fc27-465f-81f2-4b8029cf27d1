<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualitySupplierTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualitySupplierTaskPO">
        <id column="id" property="id" />
        <result column="task_no" property="taskNo" />
        <result column="relation_type" property="relationType" />
        <result column="relation_no" property="relationNo" />
        <result column="task_name" property="taskName" />
        <result column="task_type" property="taskType" />
        <result column="task_type_other" property="taskTypeOther" />
        <result column="predict_complete_date" property="predictCompleteDate" />
        <result column="practical_complete_date" property="practicalCompleteDate" />
        <result column="principal_id" property="principalId" />
        <result column="principal_name" property="principalName" />
        <result column="supplier_contact_type" property="supplierContactType" />
        <result column="additional_notifier" property="additionalNotifier" />
        <result column="task_description" property="taskDescription" />
        <result column="create_type" property="createType" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_no, relation_type, relation_no, task_name, task_type, task_type_other, predict_complete_date, practical_complete_date, principal_id, principal_name, supplier_contact_type, additional_notifier, create_type, task_description, status, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
