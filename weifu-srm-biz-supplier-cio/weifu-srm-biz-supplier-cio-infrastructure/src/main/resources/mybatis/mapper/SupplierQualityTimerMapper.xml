<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityTimerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityTimerPO">
        <id column="id" property="id" />
        <result column="timer_no" property="timerNo" />
        <result column="task_name" property="taskName" />
        <result column="task_type" property="taskType" />
        <result column="task_type_other" property="taskTypeOther" />
        <result column="relation_type" property="relationType" />
        <result column="relation_no" property="relationNo" />
        <result column="principal_id" property="principalId" />
        <result column="principal_name" property="principalName" />
        <result column="supplier_contact_type" property="supplierContactType" />
        <result column="additional_notifier" property="additionalNotifier" />
        <result column="task_description" property="taskDescription" />
        <result column="timer_type" property="timerType" />
        <result column="timer_day" property="timerDay" />
        <result column="feedback_day" property="feedbackDay" />
        <result column="timer_end_date" property="timerEndDate" />
        <result column="status" property="status" />
        <result column="task_no" property="taskNo" />
        <result column="result_message" property="resultMessage" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, timer_no, task_name, task_type, task_type_other, relation_type, relation_no, principal_id, principal_name, supplier_contact_type, additional_notifier, task_description, timer_type, timer_day, feedback_day, timer_end_date, status, task_no, result_message, create_by, create_time, create_name, update_by, update_time, update_name, is_delete
    </sql>

</mapper>
