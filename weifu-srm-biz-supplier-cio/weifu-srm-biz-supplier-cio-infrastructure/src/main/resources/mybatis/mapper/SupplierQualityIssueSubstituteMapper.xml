<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityIssueSubstituteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueSubstitutePO">
        <id column="id" property="id"/>
        <result column="issue_no" property="issueNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="substitute_input_type" property="substituteInputType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`supplier_quality_issue_substitute`</sql>
    <sql id="selectTable">FROM
        <include refid="tableName"/>
    </sql>
    <sql id="columnNoKey">
        issue_no, supplier_code, supplier_name, substitute_input_type, create_by, create_time, create_name, update_by,
        update_time, update_name, is_delete
    </sql>
    <sql id="columnAll">
        `id`,
        <include refid="columnNoKey"/>
    </sql>

    <sql id="whereSql">
        <where>
            AND `is_delete` = 0
            <if test="dto.supplierCodeLike != null and dto.supplierCodeLike != ''">
                AND supplier_code like concat("%",#{dto.supplierCodeLike},"%")
            </if>
            <if test="dto.supplierNameLike != null and dto.supplierNameLike != ''">
                AND supplier_name like concat("%",#{dto.supplierNameLike},"%")
            </if>
            <if test="dto.issueNoLike != null and dto.issueNoLike != ''">
                AND issue_no like concat("%",#{dto.issueNoLike},"%")
            </if>
            <if test="dto.substituteInputType != null and dto.substituteInputType != ''">
                AND substitute_input_type = #{dto.substituteInputType}
            </if>
            <if test="dto.createBy != null">
                AND create_by = #{dto.createBy}
            </if>
            <if test="dto.createName != null and dto.createName != ''">
                AND create_name like concat("%",#{dto.createName},"%")
            </if>
            <if test="dto.createTimeStart != null">
                AND create_time &gt;= #{dto.createTimeStart}
            </if>
            <if test="dto.createTimeEnd != null">
                AND create_time &lt;= #{dto.createTimeEnd}
            </if>
        </where>
    </sql>

    <select id="listPageByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="columnAll"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        ORDER BY `id` DESC
    </select>


</mapper>
