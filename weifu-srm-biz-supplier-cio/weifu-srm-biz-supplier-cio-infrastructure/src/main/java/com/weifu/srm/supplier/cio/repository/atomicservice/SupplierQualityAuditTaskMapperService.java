package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskPO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskPageReqDTO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditTaskReportPageReqDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskReportRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 质量审核任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityAuditTaskMapperService extends IService<SupplierQualityAuditTaskPO> {
    IPage<SupplierQualityAuditTaskRespDTO> listPageByQuery(Page<SupplierQualityAuditTaskPO> page, SupplierQualityAuditTaskPageReqDTO reqDTO);
    IPage<SupplierQualityAuditTaskReportRespDTO> listReportPageByQuery(Page<SupplierQualityAuditTaskReportRespDTO> page, SupplierQualityAuditTaskReportPageReqDTO reqDTO);

    List<SupplierQualityAuditTaskRespDTO> listByQuery(SupplierQualityAuditTaskPageReqDTO reqDTO);

    List<SupplierQualityAuditTaskPO> listByAuditPlanNo(String auditPlanNo);
    List<SupplierQualityAuditTaskPO> listByAuditTaskNo(List<String> auditTaskNo);
    List<SupplierQualityAuditTaskPO> listByAuditPlanNoAndStatus(String auditPlanNo,String status);

    List<SupplierQualityAuditTaskPO> listByStatus(List<String> statusList);
    List<SupplierQualityAuditTaskPO> listByStatusAndPredictCompleteDate(List<String> statusList, Date predictCompleteDate);

    boolean isCompleted(String auditPlanNo);

    SupplierQualityAuditTaskPO getByAuditTaskNo(String auditTaskNo);


}
