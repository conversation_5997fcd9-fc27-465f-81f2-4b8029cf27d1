package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditPlanPO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityAuditPlanPageReqDTO;

import java.util.List;

/**
 * <p>
 * 供应商质量审核计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityAuditPlanMapperService extends IService<SupplierQualityAuditPlanPO> {

    IPage<SupplierQualityAuditPlanPO> listPageByQuery(Page<SupplierQualityAuditPlanPO> page, SupplierQualityAuditPlanPageReqDTO reqDTO);

    SupplierQualityAuditPlanPO getByAuditPlanNo(String auditPlanNo);
    List<SupplierQualityAuditPlanPO> listByAuditPlanNos(List<String> auditPlanNos);

    void complete(String auditPlanNo, Long operatorBy, String operatorName);


}
