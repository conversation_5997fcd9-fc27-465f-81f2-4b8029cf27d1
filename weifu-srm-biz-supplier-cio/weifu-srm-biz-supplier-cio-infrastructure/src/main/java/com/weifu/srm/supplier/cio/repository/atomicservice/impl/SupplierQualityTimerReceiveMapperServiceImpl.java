package com.weifu.srm.supplier.cio.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualityTimerReceiveMapperService;
import com.weifu.srm.supplier.cio.repository.mapper.SupplierQualityTimerReceiveMapper;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityTimerReceivePO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 供应商定时任务详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Service
public class SupplierQualityTimerReceiveMapperServiceImpl extends ServiceImpl<SupplierQualityTimerReceiveMapper, SupplierQualityTimerReceivePO> implements SupplierQualityTimerReceiveMapperService {

    @Override
    public List<SupplierQualityTimerReceivePO> listByTimerNo(String timerNo) {
        return this.list(Wrappers.<SupplierQualityTimerReceivePO>lambdaQuery().eq(SupplierQualityTimerReceivePO::getTimerNo, timerNo));
    }
}
