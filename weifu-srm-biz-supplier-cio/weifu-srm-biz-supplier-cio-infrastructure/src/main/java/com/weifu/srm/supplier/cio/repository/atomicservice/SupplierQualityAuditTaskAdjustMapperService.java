package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityAuditTaskAdjustPO;

import java.util.List;

/**
 * <p>
 * 质量审核任务-调整记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface SupplierQualityAuditTaskAdjustMapperService extends IService<SupplierQualityAuditTaskAdjustPO> {

    List<SupplierQualityAuditTaskAdjustPO> listByAdjustNo(String adjustNo);

    void updateAdjustStatus(List<Long> idList);
}
