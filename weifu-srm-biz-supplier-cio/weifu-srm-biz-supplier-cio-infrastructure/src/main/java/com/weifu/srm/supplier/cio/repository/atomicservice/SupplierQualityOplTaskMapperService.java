package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityOplTaskPO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityOplTaskPageReqDTO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualityOplTaskRelationPageReqDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商质量opl任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityOplTaskMapperService extends IService<SupplierQualityOplTaskPO> {

    IPage<SupplierQualityOplTaskPO> listPageByQuery(Page<SupplierQualityOplTaskPO> page, SupplierQualityOplTaskPageReqDTO reqDTO);
    IPage<SupplierQualityOplTaskPO> listPageByQuery(Page<SupplierQualityOplTaskPO> page, SupplierQualityOplTaskRelationPageReqDTO reqDTO);

    List<SupplierQualityOplTaskPO> listByStatusAndPredictCompleteDate(List<String> statusList, Date predictCompleteDate);
}
