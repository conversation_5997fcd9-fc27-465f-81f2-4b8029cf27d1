package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueLogPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决流转日志表 服务类
 * @Version 1.0
 */
public interface SupplierQualityIssueLogMapperService extends IService<SupplierQualityIssueLogPO> {
    /**
     * 根据issueNo查询
     *
     * @param issueNo 问题编号
     * @return 查询结果
     */
    List<SupplierQualityIssueLogPO> listByIssueNo(String issueNo);
}
