package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualitySupplierTaskPO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualitySupplierTaskPageReqDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供应商审核任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualitySupplierTaskMapperService extends IService<SupplierQualitySupplierTaskPO> {
    IPage<SupplierQualitySupplierTaskPO> listPageByQuery(Page<SupplierQualitySupplierTaskPO> page, SupplierQualitySupplierTaskPageReqDTO reqDTO);

    SupplierQualitySupplierTaskPO getByTaskNo(String taskNo);
    List<SupplierQualitySupplierTaskPO> listByTaskNos(List<String> taskNos);

    Map<String,String> listByRelationNoList(List<String> relationNoList,String relationType);
}
