package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualitySupplierTaskSubPO;
import com.weifu.srm.supplier.cio.request.audit.SupplierQualitySupplierTaskSubPageReqDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供应商审核子任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualitySupplierTaskSubMapperService extends IService<SupplierQualitySupplierTaskSubPO> {

    IPage<SupplierQualitySupplierTaskSubPO> listPageByQuery(Page<SupplierQualitySupplierTaskSubPO> page, SupplierQualitySupplierTaskSubPageReqDTO reqDTO);

    Integer statusCountByQuery(SupplierQualitySupplierTaskSubPageReqDTO reqDTO, List<String> statusList);

    List<SupplierQualitySupplierTaskSubPO> listByTaskNo(String tasksNo);

    SupplierQualitySupplierTaskSubPO getBySubTaskNo(String subTaskNo);

    Integer countByTaskNoAndStatus(String tasksNo, String status);

    Map<String,String> statisticsCloseStatus(List<String> taskNoList);


    List<SupplierQualitySupplierTaskSubPO> listByStatusAndPredictCompleteDate(List<String> statusList, Date predictCompleteDate);

    Integer countBySupplier(String supplierCode);
}
