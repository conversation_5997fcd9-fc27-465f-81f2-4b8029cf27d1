package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityIssueSubstitutePO;
import com.weifu.srm.supplier.cio.request.SupplierQualityIssueSubstitutePageReqDTO;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决表-代供应商录入 服务类
 * @Version 1.0
 */
public interface SupplierQualityIssueSubstituteMapperService extends IService<SupplierQualityIssueSubstitutePO> {
    IPage<SupplierQualityIssueSubstitutePO> listPageByQuery(Page<SupplierQualityIssueSubstitutePO> page, SupplierQualityIssueSubstitutePageReqDTO reqDTO);
}
