package com.weifu.srm.supplier.cio.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.cio.repository.po.SupplierQualityOplAdjustmentLogPO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商质量opl任务计划完成时间调整记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityOplAdjustmentLogMapperService extends IService<SupplierQualityOplAdjustmentLogPO> {

    void saveLog(String oplNo, Date predictCompleteDate, Long operatorId, String operatorName);

    List<SupplierQualityOplAdjustmentLogPO> listByOplNo(String oplNo);
}
