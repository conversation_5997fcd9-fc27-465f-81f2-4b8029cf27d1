<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.weifu</groupId>
        <artifactId>weifu-srm-biz-supplier-cio</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>weifu-srm-biz-supplier-cio-domain</artifactId>
    <packaging>jar</packaging>

    <name>weifu-srm-biz-supplier-cio-domain</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common-util</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-supplier-cio-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-supplier-cio-infrastructure</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-audit-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-masterdata-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-communication-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-integration-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-user-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-composite-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-supplier-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
