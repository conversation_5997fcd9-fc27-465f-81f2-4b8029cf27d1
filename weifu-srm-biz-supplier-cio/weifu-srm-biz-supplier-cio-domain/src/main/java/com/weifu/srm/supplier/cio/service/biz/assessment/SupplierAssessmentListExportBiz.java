package com.weifu.srm.supplier.cio.service.biz.assessment;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.supplier.cio.convert.SupplierAssessmentConvert;
import com.weifu.srm.supplier.cio.manager.DataPermissionManager;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierAssessmentMapperService;
import com.weifu.srm.supplier.cio.repository.enums.SupplierAssessmentInvoiceStatusEnum;
import com.weifu.srm.supplier.cio.repository.enums.SupplierAssessmentStatusEnum;
import com.weifu.srm.supplier.cio.repository.po.SupplierAssessmentPO;
import com.weifu.srm.supplier.cio.request.assessment.SupplierAssessmentPageReqDTO;
import com.weifu.srm.supplier.cio.response.assessment.SupplierAssessmentPageRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/9/18 13:51
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAssessmentListExportBiz {
    private final SupplierAssessmentMapperService assessmentMapperService;
    private final DataPermissionManager dataPermissionManager;
    private final SupplierAssessmentConvert supplierAssessmentConvert;

    public List<SupplierAssessmentPageRespDTO> listExport(SupplierAssessmentPageReqDTO req) {
        DataPermissionRespDTO permissionRespDTO = new DataPermissionRespDTO();
        if (ObjectUtils.isEmpty(req.getSupplierCode())) {
            permissionRespDTO = dataPermissionManager.queryUserDataPermission(req.getUserId(), "PC_INTERNAL_PAGE_SUPPLIER_QUALITY_ASSESSMENT");
            if (permissionRespDTO.isNo()) {
                return new ArrayList<>();
            }
        }
        List<SupplierAssessmentPO> poList = assessmentMapperService.queryAssessmentListExport(req,permissionRespDTO);
        if (CollUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<SupplierAssessmentPageRespDTO> respDTOList = supplierAssessmentConvert.toRespList(poList);
        respDTOList.forEach(r->{
            SupplierAssessmentStatusEnum statusEnum = SupplierAssessmentStatusEnum.getByCode(r.getStatus());
            Optional.ofNullable(statusEnum).ifPresent(j->r.setStatusDesc(j.getChineseName()));
            SupplierAssessmentInvoiceStatusEnum invoiceStatusEnum = SupplierAssessmentInvoiceStatusEnum.getByCode(r.getInvoiceStatus());
            Optional.ofNullable(invoiceStatusEnum).ifPresent(j->r.setInvoiceStatusDesc(j.getChineseName()));
            SupplierAssessmentInvoiceStatusEnum redInvoiceStatusEnum = SupplierAssessmentInvoiceStatusEnum.getByCode(r.getEliminationInvoiceStatus());
            Optional.ofNullable(redInvoiceStatusEnum).ifPresent(j->r.setEliminationInvoiceStatusDesc(j.getChineseName()));
        });
        return respDTOList;
    }
}
