package com.weifu.srm.supplier.cio.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.cio.constants.ServiceConstants;
import com.weifu.srm.supplier.cio.convert.SupplierQualitySupplierTaskConvert;
import com.weifu.srm.supplier.cio.convert.SupplierQualitySupplierTaskSubConvert;
import com.weifu.srm.supplier.cio.enums.*;
import com.weifu.srm.supplier.cio.manager.DictDataManager;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualitySupplierTaskMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualitySupplierTaskSubMapperService;
import com.weifu.srm.supplier.cio.repository.atomicservice.SupplierQualityTimerMapperService;
import com.weifu.srm.supplier.cio.repository.constants.SupplierCioCommonConstants;
import com.weifu.srm.supplier.cio.repository.enums.BizNoRuleEnum;
import com.weifu.srm.supplier.cio.repository.po.*;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.cio.response.audit.*;
import com.weifu.srm.supplier.cio.service.SupplierQualitySupplierTaskService;
import com.weifu.srm.supplier.cio.service.biz.AttachmentRecordBiz;
import com.weifu.srm.supplier.cio.service.biz.SupplierQualitySupplierTaskMessageBiz;
import com.weifu.srm.supplier.cio.utils.BizNoUtil;
import com.weifu.srm.supplier.enums.SupplierContactTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商质量审核供应商任务-服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierQualitySupplierTaskServiceImpl implements SupplierQualitySupplierTaskService {

    private final SupplierQualitySupplierTaskMapperService supplierQualitySupplierTaskMapperService;
    private final SupplierQualitySupplierTaskSubMapperService supplierQualitySupplierTaskSubMapperService;
    private final SupplierQualityTimerMapperService supplierQualityTimerMapperServices;
    private final SupplierQualitySupplierTaskConvert supplierQualitySupplierTaskConvert;
    private final SupplierQualitySupplierTaskSubConvert supplierQualitySupplierTaskSubConvert;
    private final AttachmentRecordBiz attachmentRecordBiz;
    private final SupplierQualitySupplierTaskMessageBiz supplierQualitySupplierTaskMessageBiz;
    private final DictDataManager dictDataManager;

    @Override
    public PageResponse<SupplierQualitySupplierTaskRespDTO> page(SupplierQualitySupplierTaskPageReqDTO reqDTO) {
        Page<SupplierQualitySupplierTaskPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierQualitySupplierTaskPO> pageResult = supplierQualitySupplierTaskMapperService.listPageByQuery(page, reqDTO);
        List<SupplierQualitySupplierTaskRespDTO> dtoList = supplierQualitySupplierTaskConvert.toSupplierQualitySupplierTaskRespList(pageResult.getRecords());
        List<String> taskNoList = dtoList.stream().map(SupplierQualitySupplierTaskRespDTO::getTaskNo).collect(Collectors.toList());
        Map<String, String> taskNoMap = supplierQualitySupplierTaskSubMapperService.statisticsCloseStatus(taskNoList);
        Map<String, Map<String, String>> dictMap = dictDataManager.listDictValueMap(Lists.newArrayList(
                ServiceConstants.SUPPLIER_TASK_TYPE,
                ServiceConstants.BUSINESS_RECEIPT_TYPE,
                ServiceConstants.SUPPLIER_SUB_TASK_STATUS));
        for (SupplierQualitySupplierTaskRespDTO dto : dtoList) {
            fillTypeName(dictMap, dto);
            dto.setStatistics(taskNoMap.get(dto.getTaskNo()));
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public SupplierQualitySupplierTaskDetailRespDTO detail(String taskNo) {
        Map<String, Map<String, String>> dictMap = dictDataManager.listDictValueMap(Lists.newArrayList(
                ServiceConstants.SUPPLIER_TASK_TYPE,
                ServiceConstants.BUSINESS_RECEIPT_TYPE,
                ServiceConstants.SUPPLIER_SUB_TASK_STATUS));
        SupplierQualitySupplierTaskPO po = supplierQualitySupplierTaskMapperService.getByTaskNo(taskNo);
        SupplierQualitySupplierTaskDetailRespDTO dto = supplierQualitySupplierTaskConvert.toSupplierQualitySupplierTaskDetailResp(po);
        List<AttachmentMessageRespDTO> annexList = attachmentRecordBiz.listByBusinessNoAndType(po.getTaskNo(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_TASK_ANNEX.getCode());
        dto.setAnnexList(annexList);
        fillTypeName(dictMap, dto);
        return dto;
    }


    @Override
    public PageResponse<SupplierQualitySupplierTaskSubRespDTO> pageSubTask(SupplierQualitySupplierTaskSubPageReqDTO reqDTO) {
        Page<SupplierQualitySupplierTaskSubPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierQualitySupplierTaskSubPO> pageResult = supplierQualitySupplierTaskSubMapperService.listPageByQuery(page, reqDTO);
        List<SupplierQualitySupplierTaskSubRespDTO> dtoList = supplierQualitySupplierTaskSubConvert.toSupplierQualitySupplierTaskSubRespList(pageResult.getRecords());
        fillAnnex(dtoList);
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public Integer subTaskCloseCount(SupplierQualitySupplierTaskSubPageReqDTO reqDTO) {
        return supplierQualitySupplierTaskSubMapperService.statusCountByQuery(reqDTO, Lists.newArrayList(SupplierQualitySupplierSubTaskStatusEnum.CLOSED.getCode(), SupplierQualitySupplierSubTaskStatusEnum.WITHDRAWN.getCode()));
    }


    @Override
    public SupplierQualitySupplierTaskSubDetailRespDTO subTaskDetail(String subTaskNo) {
        SupplierQualitySupplierTaskSubPO sub = supplierQualitySupplierTaskSubMapperService.getBySubTaskNo(subTaskNo);
        SupplierQualitySupplierTaskSubDetailRespDTO dto = supplierQualitySupplierTaskSubConvert.toSupplierQualitySupplierTaskSubDetailResp(sub);
        SupplierQualitySupplierTaskPO task = supplierQualitySupplierTaskMapperService.getByTaskNo(sub.getTaskNo());
        Map<String, Map<String, String>> dictMap = dictDataManager.listDictValueMap(Lists.newArrayList(ServiceConstants.SUPPLIER_TASK_TYPE, ServiceConstants.SUPPLIER_SUB_TASK_STATUS));
        Map<String, String> supplierTaskTypeMap = dictMap.get(ServiceConstants.SUPPLIER_TASK_TYPE);
        Map<String, String> supplierSubTaskStatusMap = dictMap.get(ServiceConstants.SUPPLIER_SUB_TASK_STATUS);
        if (task != null) {
            List<AttachmentMessageRespDTO> annexList = attachmentRecordBiz.listByBusinessNoAndType(task.getTaskNo(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_TASK_ANNEX.getCode());
            dto.setAnnexList(annexList);
            dto.setTaskDescription(task.getTaskDescription());
            dto.setTaskType(task.getTaskType());
            dto.setTaskTypeName(getTaskTypeName(supplierTaskTypeMap, task.getTaskType(), task.getTaskTypeOther()));
            dto.setPrincipalId(task.getPrincipalId());
            dto.setPrincipalName(task.getPrincipalName());
            dto.setSupplierContactType(task.getSupplierContactType());
            dto.setSupplierContactTypeName(SupplierContactTypeEnum.getDesc(task.getSupplierContactType()));
            dto.setStatusName(supplierSubTaskStatusMap.get(dto.getStatus()));
        }
        Map<String, List<AttachmentMessageRespDTO>> annexMap = attachmentRecordBiz.listByBusinessNo(subTaskNo);
        for (Map.Entry<String, List<AttachmentMessageRespDTO>> entry : annexMap.entrySet()) {
            String businessType = entry.getKey();
            if (SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_ANNEX.getCode().equals(businessType)) {
                List<AttachmentMessageRespDTO> attachmentMessageRespList = entry.getValue();
                dto.setAnnexResp(CollectionUtils.isEmpty(attachmentMessageRespList) ? null : attachmentMessageRespList.get(0));
            } else if (SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_FEEDBACK.getCode().equals(businessType)) {
                dto.setSupplierFeedbackAnnexList(entry.getValue());
            } else if (SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_AUDIT.getCode().equals(businessType)) {
                dto.setSupplierAuditAnnexList(entry.getValue());
            }
        }
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(SupplierQualitySupplierTaskSaveReqDTO reqDTO) {
        return addTask(reqDTO, SupplierQualitySupplierTaskCreateTypeEnum.DISPOSABLE.getCode());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void timerCreate(Long timerId, SupplierQualitySupplierTaskSaveReqDTO reqDTO) {
        String taskNo = addTask(reqDTO, SupplierQualitySupplierTaskCreateTypeEnum.TIMING_CYCLE.getCode());
        supplierQualityTimerMapperServices.updateTaskResult(timerId, taskNo, SupplierCioCommonConstants.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void appendSubTask(String taskNo, List<SupplierQualitySupplierTaskSubReqDTO> subTaskList, OperatorBaseReqDTO reqDTO) {
        SupplierQualitySupplierTaskPO po = supplierQualitySupplierTaskMapperService.getByTaskNo(taskNo);
        if (po == null) {
            return;
        }
        Integer count = supplierQualitySupplierTaskSubMapperService.countByTaskNoAndStatus(taskNo, null);
        count++;
        Date now = DateUtil.date();
        saveSubTask(po, count, subTaskList, reqDTO, now);
        supplierQualityTimerMapperServices.updateTaskResult(reqDTO.getId(), taskNo, SupplierCioCommonConstants.SUCCESS);
        updateTaskTime(po, reqDTO, now);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void feedback(SupplierQualitySubTaskFeedbackReqDTO reqDTO) {
        SupplierQualitySupplierTaskSubPO old = supplierQualitySupplierTaskSubMapperService.getById(reqDTO.getId());
        SupplierQualitySupplierTaskSubPO po = new SupplierQualitySupplierTaskSubPO();
        po.setId(reqDTO.getId());
        po.setFeedbackExplanation(reqDTO.getFeedbackExplanation());
        po.setStatus(SupplierQualitySupplierSubTaskStatusEnum.FEEDBACK_RECEIVED.getCode());
        Date now = DateUtil.date();
        po.setFeedbackTime(now);
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        supplierQualitySupplierTaskSubMapperService.updateById(po);
        attachmentRecordBiz.saveBatchAttachmentRecord(reqDTO.getSupplierFeedbackAnnexList(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_FEEDBACK.getCode(), old.getSubTaskNo(), reqDTO);
        supplierQualitySupplierTaskMessageBiz.sendMessage(old, SupplierCioSiteMessageTemplateEnum.SUPPLIER_TASK_FEEDBACK_RECEIVED, old.getCreateBy(), old.getCreateName());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approve(SupplierQualitySubTaskApproveReqDTO reqDTO) {
        SupplierQualitySupplierTaskSubPO old = supplierQualitySupplierTaskSubMapperService.getById(reqDTO.getId());
        SupplierQualitySupplierTaskSubPO po = new SupplierQualitySupplierTaskSubPO();
        po.setId(reqDTO.getId());
        po.setAuditResult(reqDTO.getAuditResult());
        po.setAuditExplanation(reqDTO.getAuditExplanation());
        SupplierQualityEmailTemplateEnum templateEnum;
        if (SupplierCioCommonConstants.AUDIT_FAIL.equals(reqDTO.getAuditResult())) {
            po.setStatus(SupplierQualitySupplierSubTaskStatusEnum.APPROVE_FAIL.getCode());
            po.setLastAuditExplanation(reqDTO.getAuditExplanation());
            templateEnum = SupplierQualityEmailTemplateEnum.SUPPLIER_TASK_REJECTED;
        } else {
            po.setEndTime(DateUtil.date());
            po.setStatus(SupplierQualitySupplierSubTaskStatusEnum.CLOSED.getCode());
            templateEnum = SupplierQualityEmailTemplateEnum.SUPPLIER_TASK_CLOSED;
        }
        Date now = DateUtil.date();
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        supplierQualitySupplierTaskSubMapperService.updateById(po);
        attachmentRecordBiz.saveBatchAttachmentRecord(reqDTO.getSupplierAuditAnnexList(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_AUDIT.getCode(), old.getSubTaskNo(), reqDTO);
        SupplierQualitySupplierTaskPO taskPO = supplierQualitySupplierTaskMapperService.getByTaskNo(old.getTaskNo());
        supplierQualitySupplierTaskMessageBiz.sendEmail(Lists.newArrayList(old), templateEnum, getContactCodes(taskPO));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void withdraw(SupplierQualitySubTaskWithdrawReqDTO reqDTO) {
        SupplierQualitySupplierTaskSubPO old = supplierQualitySupplierTaskSubMapperService.getById(reqDTO.getId());
        SupplierQualitySupplierTaskSubPO sub = new SupplierQualitySupplierTaskSubPO();
        Date now = DateUtil.date();
        sub.setEndTime(now);
        sub.setId(reqDTO.getId());
        sub.setStatus(SupplierQualitySupplierSubTaskStatusEnum.WITHDRAWN.getCode());
        sub.setLastAuditExplanation(old.getAuditExplanation());
        sub.setAuditExplanation(reqDTO.getAuditExplanation());
        BaseEntityUtil.setCommonForU(sub, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        supplierQualitySupplierTaskSubMapperService.updateById(sub);
        attachmentRecordBiz.saveBatchAttachmentRecord(reqDTO.getSupplierAuditAnnexList(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_AUDIT.getCode(), old.getSubTaskNo(), reqDTO);
        SupplierQualitySupplierTaskPO taskPO = supplierQualitySupplierTaskMapperService.getByTaskNo(old.getTaskNo());
        supplierQualitySupplierTaskMessageBiz.sendEmail(Lists.newArrayList(old), SupplierQualityEmailTemplateEnum.SUPPLIER_TASK_WITHDRAWN, getContactCodes(taskPO));
    }

    @Override
    public void overdueFeedbackJob(Date date) {
        List<String> statusList = com.google.common.collect.Lists.newArrayList();
        statusList.add(SupplierQualitySupplierSubTaskStatusEnum.SUBMITTED.getCode());
        statusList.add(SupplierQualitySupplierSubTaskStatusEnum.APPROVE_FAIL.getCode());
        Date threeDaysAgo = DateUtil.offsetDay(date, 3);
        //当天
        List<SupplierQualitySupplierTaskSubPO> poList = supplierQualitySupplierTaskSubMapperService.listByStatusAndPredictCompleteDate(statusList, date);
        //离结束还有3天
        List<SupplierQualitySupplierTaskSubPO> poList2 = supplierQualitySupplierTaskSubMapperService.listByStatusAndPredictCompleteDate(statusList, threeDaysAgo);
        if (CollectionUtils.isNotEmpty(poList2)) {
            poList.addAll(poList2);
        }
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        //发送消息通知
        sendMessageBatch(poList);
        //发送系统通知到供应商子任务的威孚负责人
        Map<String, List<SupplierQualitySupplierTaskSubPO>> taskNoMap = poList.stream().collect(Collectors.groupingBy(SupplierQualitySupplierTaskSubPO::getTaskNo));
        List<SupplierQualitySupplierTaskPO>  taskList = supplierQualitySupplierTaskMapperService.listByTaskNos(new ArrayList<>(taskNoMap.keySet()));
        for (SupplierQualitySupplierTaskPO supplierQualitySupplierTaskPO : taskList) {
            supplierQualitySupplierTaskMessageBiz.sendEmail(poList, SupplierQualityEmailTemplateEnum.SUPPLIER_TASK_EXPIRED, getContactCodes(supplierQualitySupplierTaskPO));
        }
    }

    @Override
    public PageResponse<SupplierQualitySupplierTaskReportRespDTO> pageReport(SupplierQualitySupplierTaskSubPageReqDTO reqDTO) {
        Page<SupplierQualitySupplierTaskSubPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierQualitySupplierTaskSubPO> pageResult = supplierQualitySupplierTaskSubMapperService.listPageByQuery(page, reqDTO);
        List<SupplierQualitySupplierTaskSubPO> poList = pageResult.getRecords();
        List<SupplierQualitySupplierTaskReportRespDTO> dtoList;
        if (CollectionUtils.isNotEmpty(poList)) {
            Set<String> taskNos = poList.stream().map(SupplierQualitySupplierTaskSubPO::getTaskNo).collect(Collectors.toSet());
            dtoList = supplierQualitySupplierTaskSubConvert.toSupplierQualitySupplierTaskReportRespList(pageResult.getRecords());
            List<SupplierQualitySupplierTaskPO> taskList = supplierQualitySupplierTaskMapperService.listByTaskNos(new ArrayList<>(taskNos));
            Map<String, SupplierQualitySupplierTaskPO> taskMap = taskList.stream().collect(Collectors.toMap(SupplierQualitySupplierTaskPO::getTaskNo, obj -> obj, (k1, k2) -> k2));
            Map<String, Map<String, String>> dictMap = dictDataManager.listDictValueMap(Lists.newArrayList(
                    ServiceConstants.SUPPLIER_TASK_TYPE,
                    ServiceConstants.SUPPLIER_SUB_TASK_STATUS));
            Map<String, String> supplierTaskTypeMap = dictMap.get(ServiceConstants.SUPPLIER_TASK_TYPE);
            Map<String, String> supplierSubTaskStatusMap = dictMap.get(ServiceConstants.SUPPLIER_SUB_TASK_STATUS);
            for (SupplierQualitySupplierTaskReportRespDTO dto : dtoList) {
                SupplierQualitySupplierTaskPO task = taskMap.get(dto.getTaskNo());
                dto.setStatusName(supplierSubTaskStatusMap.get(dto.getStatus()));
                if (task != null) {
                    dto.setTaskTypeName(getTaskTypeName(supplierTaskTypeMap, task.getTaskType(), task.getTaskTypeOther()));
                    dto.setTaskDescription(task.getTaskDescription());
                }

            }
        } else {
            dtoList = Lists.newArrayList();
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public Integer countBySupplier(String supplierCode) {
        return supplierQualitySupplierTaskSubMapperService.countBySupplier(supplierCode);
    }


    private void updateTaskTime(SupplierQualitySupplierTaskPO po, OperatorBaseReqDTO reqDTO, Date now) {
        if (po == null) {
            return;
        }
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        supplierQualitySupplierTaskMapperService.updateById(po);
    }

    /**
     * 批量发送消息
     */
    private void sendMessageBatch(List<SupplierQualitySupplierTaskSubPO> poList) {
        for (SupplierQualitySupplierTaskSubPO po : poList) {
            supplierQualitySupplierTaskMessageBiz.sendMessage(po, SupplierCioSiteMessageTemplateEnum.SUPPLIER_TASK_NEAR_EXPIRATION_NO_FEEDBACK, po.getCreateBy(), po.getCreateName());
        }
    }

    /**
     * 新增任务
     */
    private String addTask(SupplierQualitySupplierTaskSaveReqDTO reqDTO, String createType) {
        SupplierQualitySupplierTaskPO po = supplierQualitySupplierTaskConvert.toSupplierQualitySupplierTask(reqDTO);
        po.setId(null);
        po.setTaskNo(BizNoUtil.generateNo(BizNoRuleEnum.SUPPLIER_QUALITY_SUPPLIER_TASK));
        po.setCreateType(createType);
        po.setPrincipalId(reqDTO.getOperationBy());
        po.setPrincipalName(reqDTO.getOperationByName());
        Date now = DateUtil.date();
        BaseEntityUtil.setCommon(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        supplierQualitySupplierTaskMapperService.saveOrUpdate(po);
        attachmentRecordBiz.saveBatchAttachmentRecord(reqDTO.getAnnexList(), SupplierQualityAuditTaskAnnexEnum.SUPPLIER_TASK_ANNEX.getCode(), po.getTaskNo(), reqDTO);
        List<SupplierQualitySupplierTaskSubReqDTO> subReqList = reqDTO.getSubTaskList();
        saveSubTask(po, 1, subReqList, reqDTO, now);
        return po.getTaskNo();
    }

    /**
     * 保存子任务
     */
    private void saveSubTask(SupplierQualitySupplierTaskPO taskPO, Integer startNum, List<SupplierQualitySupplierTaskSubReqDTO> subReqList, OperatorBaseReqDTO reqDTO, Date now) {
        List<SupplierQualitySupplierTaskSubPO> subTaskList = Lists.newArrayList();
        List<AttachmentRecordPO> recordList = Lists.newArrayList();
        String taskNo = taskPO.getTaskNo();
        SupplierQualitySupplierTaskPO po = supplierQualitySupplierTaskMapperService.getByTaskNo(taskNo);
        for (int i = 0; i < subReqList.size(); i++) {
            SupplierQualitySupplierTaskSubReqDTO supplierQualitySupplierTaskSubReqDTO = subReqList.get(i);
            SupplierQualitySupplierTaskSubPO sub = supplierQualitySupplierTaskSubConvert.toSupplierQualitySupplierTaskSub(supplierQualitySupplierTaskSubReqDTO);
            sub.setId(null);
            sub.setTaskNo(taskNo);
            sub.setTaskType(po == null ? null : po.getTaskType());
            sub.setStatus(SupplierQualitySupplierSubTaskStatusEnum.SUBMITTED.getCode());
            sub.setSubTaskNo(taskNo + "-" + (startNum + i));
            BaseEntityUtil.setCommon(sub, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
            AttachmentRecordPO attachmentRecord = attachmentRecordBiz.buildAttachmentRecord(supplierQualitySupplierTaskSubReqDTO.getAnnex(),
                    SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_ANNEX.getCode(), sub.getSubTaskNo(), reqDTO, now);
            if (attachmentRecord != null) {
                recordList.add(attachmentRecord);
            }
            subTaskList.add(sub);
        }
        attachmentRecordBiz.saveBatch(recordList);
        supplierQualitySupplierTaskSubMapperService.saveOrUpdateBatch(subTaskList);
        supplierQualitySupplierTaskMessageBiz.sendEmail(subTaskList, SupplierQualityEmailTemplateEnum.SUPPLIER_TASK_CREATED_TO_CONTACT, getContactCodes(taskPO));
    }


    private Set<String> getContactCodes(SupplierQualitySupplierTaskPO taskPO) {
        Set<String> contactTypeCodes = Sets.newHashSet();
        String supplierContactType = taskPO.getSupplierContactType();
        if (StringUtils.isNotBlank(supplierContactType)) {
            contactTypeCodes.add(supplierContactType);
        }
        String additionalNotifier = taskPO.getAdditionalNotifier();
        if (StringUtils.isNotBlank(additionalNotifier)) {
            String[] additionalNotifierArr = additionalNotifier.split(",");
            contactTypeCodes.addAll(Arrays.asList(additionalNotifierArr));
        }
        return contactTypeCodes;
    }

    private void fillTypeName(Map<String, Map<String, String>> dictMap, SupplierQualitySupplierTaskRespDTO dto) {
        Map<String, String> supplierTaskTypeMap = dictMap.get(ServiceConstants.SUPPLIER_TASK_TYPE);
        Map<String, String> relationTypeMap = dictMap.get(ServiceConstants.BUSINESS_RECEIPT_TYPE);
        dto.setCreateTypeName(SupplierQualitySupplierTaskCreateTypeEnum.getNameByCode(dto.getCreateType()));
        dto.setTaskTypeName(getTaskTypeName(supplierTaskTypeMap, dto.getTaskType(), dto.getTaskTypeOther()));
        dto.setRelationTypeName(relationTypeMap.get(dto.getRelationType()));
        dto.setSupplierContactTypeName(SupplierContactTypeEnum.getDesc(dto.getSupplierContactType()));
    }

    /**
     * 补充附件信息
     */
    private void fillAnnex(List<SupplierQualitySupplierTaskSubRespDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        Set<String> taskNoSet = dtoList.stream().map(SupplierQualitySupplierTaskSubRespDTO::getTaskNo).collect(Collectors.toSet());
        List<SupplierQualitySupplierTaskPO> mainTaskList = supplierQualitySupplierTaskMapperService.listByTaskNos(new ArrayList<>(taskNoSet));
        Map<String, SupplierQualitySupplierTaskPO> mainTaskMap = mainTaskList.stream().collect(Collectors.toMap(SupplierQualitySupplierTaskPO::getTaskNo, obj -> obj, (k1, k2) -> k2));
        List<String> taskNoList = dtoList.stream().map(SupplierQualitySupplierTaskSubRespDTO::getSubTaskNo).collect(Collectors.toList());
        Map<String, List<AttachmentMessageRespDTO>> annexMap = attachmentRecordBiz.listByBusinessNoAndBusinessType(taskNoList, SupplierQualityAuditTaskAnnexEnum.SUPPLIER_SUB_TASK_ANNEX.getCode());
        Map<String, Map<String, String>> dictMap = dictDataManager.listDictValueMap(Lists.newArrayList(ServiceConstants.SUPPLIER_TASK_TYPE, ServiceConstants.SUPPLIER_SUB_TASK_STATUS));
        Map<String, String> supplierTaskTypeMap = dictMap.get(ServiceConstants.SUPPLIER_TASK_TYPE);
        Map<String, String> supplierSubTaskStatusMap = dictMap.get(ServiceConstants.SUPPLIER_SUB_TASK_STATUS);
        for (SupplierQualitySupplierTaskSubRespDTO dto : dtoList) {
            List<AttachmentMessageRespDTO> annexList = annexMap.get(dto.getSubTaskNo());
            dto.setAnnexResp(CollectionUtils.isEmpty(annexList) ? null : annexList.get(0));
            dto.setStatusName(supplierSubTaskStatusMap.get(dto.getStatus()));
            SupplierQualitySupplierTaskPO mainTask = mainTaskMap.get(dto.getTaskNo());
            if (mainTask != null) {
                dto.setTaskTypeName(getTaskTypeName(supplierTaskTypeMap, mainTask.getTaskType(), mainTask.getTaskTypeOther()));
                dto.setPrincipalName(mainTask.getPrincipalName());
            }
        }
    }

    private String getTaskTypeName(Map<String, String> supplierTaskTypeMap, String taskType, String taskTypeOther) {
        String taskTypeName = supplierTaskTypeMap.containsKey(taskType) ? supplierTaskTypeMap.get(taskType) : SupplierQualitySupplierTaskTypeEnum.getNameByCode(taskType);
        if (SupplierQualitySupplierTaskTypeEnum.OTHER.getCode().equals(taskType)) {
            taskTypeName = taskTypeName + (StringUtils.isBlank(taskTypeOther) ? "" : "," + taskTypeOther);
        }
        return taskTypeName;
    }
}
