package com.weifu.srm.supplier.cio.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.audit.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 供应商质量审核供应商任务-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualitySupplierTaskService {

    /**
     * 分页查询
     *
     * @param reqDTO 请求入参
     * @return 分页数据
     */
    PageResponse<SupplierQualitySupplierTaskRespDTO> page(SupplierQualitySupplierTaskPageReqDTO reqDTO);

    /**
     * 详情
     *
     * @param taskNo 任务编号
     * @return 详情
     */
    SupplierQualitySupplierTaskDetailRespDTO detail(String taskNo);

    /**
     * 子任务分页查询
     *
     * @param reqDTO 请求入参
     * @return 子任务分页数据
     */
    PageResponse<SupplierQualitySupplierTaskSubRespDTO> pageSubTask(SupplierQualitySupplierTaskSubPageReqDTO reqDTO);

    /**
     * 子任务关闭数量
     */
    Integer subTaskCloseCount(SupplierQualitySupplierTaskSubPageReqDTO reqDTO);

    /**
     * 子任务详情
     *
     * @param subTaskNo 子任务编号
     * @return 子任务详情
     */
    SupplierQualitySupplierTaskSubDetailRespDTO subTaskDetail(String subTaskNo);

    /**
     * 创建
     *
     * @param reqDTO 请求入参
     */
    String create(SupplierQualitySupplierTaskSaveReqDTO reqDTO);

    /**
     * 定时任务创建
     *
     * @param timerId 定时id
     * @param reqDTO  请求入参
     */
    void timerCreate(Long timerId, SupplierQualitySupplierTaskSaveReqDTO reqDTO);

    /**
     * 追加子任务
     *
     * @param taskNo      主任务编号
     * @param subTaskList 子任务
     * @param reqDTO      操作人数据
     */
    void appendSubTask(String taskNo, List<SupplierQualitySupplierTaskSubReqDTO> subTaskList, OperatorBaseReqDTO reqDTO);

    /**
     * 提交反馈
     *
     * @param reqDTO 请求入参
     */
    void feedback(SupplierQualitySubTaskFeedbackReqDTO reqDTO);

    /**
     * 审核反馈
     *
     * @param reqDTO 请求入参
     */
    void approve(SupplierQualitySubTaskApproveReqDTO reqDTO);

    /**
     * 撤回
     *
     * @param reqDTO 请求入参
     */
    void withdraw(SupplierQualitySubTaskWithdrawReqDTO reqDTO);


    /**
     * 逾期反馈提醒job
     */
    void overdueFeedbackJob(Date date);

    /**
     * 报表分页查询
     *
     * @param reqDTO 请求入参
     * @return 分页数据
     */
    PageResponse<SupplierQualitySupplierTaskReportRespDTO> pageReport(SupplierQualitySupplierTaskSubPageReqDTO reqDTO);

    /**
     * 供应商端工作台待处理数量
     */
    Integer countBySupplier(String supplierCode);
}
