package com.weifu.srm.supplier.cio.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.cio.request.audit.*;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditPlanDetailRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditPlanRespDTO;
import com.weifu.srm.supplier.cio.response.audit.SupplierQualityAuditTaskRespDTO;

import java.util.List;


/**
 * <p>
 * 供应商质量审核计划-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface SupplierQualityAuditPlanService {
    /**
     * 分页查询
     */
    PageResponse<SupplierQualityAuditPlanRespDTO> page(SupplierQualityAuditPlanPageReqDTO reqDTO);

    /**
     * 详情
     */
    SupplierQualityAuditPlanDetailRespDTO detail(String auditPlanNo);

    /**
     * 调整详情
     *
     * @param adjustNo 调整编号
     */
    List<SupplierQualityAuditTaskRespDTO> adjustDetail(String adjustNo);

    /**
     * 复制
     */
    String copy(SupplierQualityAuditPlanCopyReqDTO reqDTO);

    /**
     * 保存
     */
    Long save(SupplierQualityAuditPlanSaveReqDTO reqDTO);

    /**
     * 调整任务申请
     */
    void adjustPetition(SupplierQualityAuditPlanAdjustReqDTO reqDTO);


    /**
     * 删除
     */
    void delete(Long id);

    /**
     * 审批流状态变更
     *
     * @param mq 审批流状态
     */
    void updateAuditProgress(TicketStatusChangedMQ mq);

    /**
     * 申请取消
     */
    void cancelApply(SupplierQualityAuditPlanTaskCancelReqDTO reqDTO);

}
