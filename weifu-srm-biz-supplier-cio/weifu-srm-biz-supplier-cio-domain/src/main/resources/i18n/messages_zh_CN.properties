## 公共
common.option.result.failure=操作失败
common.option.result.success=操作成功
common.option.result.version.error=数据已被更新，请刷新重试
common.query.result.failure=查询失败
common.query.result.success=查询成功
common.save.result.success=保存成功
common.submit.result.success=提交成功
common.query.result.not.find=查询的数据不存在
common.query.not.value=暂无数据
common.data.already.exists=记录已存在
common.params.cannot.be.empty={0}不能为空
common.template.is.inconformity=模板不匹配
common.data.is.empty=数据为空
common.data.is.duplicate=数据重复
common.params.failure=参数错误
common.update.result.not.find=修改的数据不存在
common.delete.result.fail=删除数据失败
## 信用额度
credit.limit.params.error=信用额度申请单参数错误
credit.limit.not.found=该信用额度申请单不存在

## 合作伙伴
new.code.not.exist=新编码不存在，请核实后重试
old.code.not.exist=原编码不存在，请核实后重试
new.code.status.not.effective=新编码的状态不是已生效，不能合并
this.partner.not.exist=当前合作伙伴不存在
partner.cannot.be.disabled=请检查合作伙伴{0}在合同、销售提单、供应商发货指令中的使用情况

## 商品主数据
product.data.null=商品主数据不存在，请确认
product.option.result.part.failure=部分数据操作失败，请刷新重试！
product.chinese.name.exists=中文名称已存在，请重新输入！
product.chinese.name.duplicate=中文名称有重复，请重新输入！
product.display.name.exists=显示名称已存在，请重新输入！
product.display.name.duplicate=显示名称有重复，请检查！
product.import.title=商品主数据
product.import.sheet=商品信息
product.excel.empty=excel内容为空，请填写后再导入!
product.history.not.find=未找到操作历史！
product.customs.multiple=海关编码存在多个，请检查！
product.customs.not.find=海关编码不存在，请检查！
product.group.category.not.find=集团产品分类不存在，请检查！
product.basic.unit.not.find=基本单位不存在，请检查！
product.value.added.taxRate.not.find=进项税不存在，请检查！
product.value.added.salesTaxRate.not.find=销项税不存在，请检查！
merge.query.result.not.find=要合并的商品明细编码不存在或状态不是已生效
product.cannot.be.disabled=请检查商品{0}子类状态及在合同、销售提单、业务库存中的使用情况
product.company.or.department.not.find=数据权限公司或部门不存在，请检查！

## 币种
currency.list.empty=币种列表为空
currency.code.cannot.be.empty=币种代码不能为空
currency.name.cannot.be.empty=币种名称不能为空
currency.code.cannot.be.duplicate=币种代码不能重复
currency.code.already.exists=币种代码{0}已经存在
currency.name.already.exists=币种名称{0}已经存在
currency.not.exist=币种不存在
currency.cannot.be.disabled=币种{0}是某个账套的本位币，不允许禁用

## 会计日历
calendar.list.empty=会计日历列表为空
calendar.code.cannot.be.empty=日历代码不能为空
calendar.type.cannot.be.empty=日历类型不能为空
calendar.desc.cannot.be.empty=日历说明不能为空
calendar.init.open.year=初始打开年度不能为空
calendar.code.cannot.be.duplicate=日历代码不能重复
calendar.code.already.exists=日历代码{0}已经存在
calendar.not.exist=日历不存在

## 账套
ledger.code.cannot.be.empty=分类账代码不能为空
ledger.name.cannot.be.empty=分类账名称不能为空
ledger.chart.cannot.be.empty=会计科目表不能为空
ledger.calendar.cannot.be.empty=会计日历不能为空
ledger.currency.code.cannot.be.empty=本位币不能为空
ledger.must.contain.legal=账套必须包含法人
ledger.code.already.exists=分类账代码{0}已经存在
ledger.not.exist=账套不存在
ledger.default.exchange.type.cannot.be.empty=默认汇率类型不能为空
ledger.init.open.period.cannot.be.empty=初始打开期间不能为空
ledger.legal.code.cannot.be.empty=法人主体代码不能为空
ledger.legal.name.cannot.be.empty=法人主体不能为空
ledger.legal.country.cannot.be.empty=国家(地区)不能为空
ledger.legal.credit.code.cannot.be.empty=统一社会信用代码不能为空
ledger.legal.address.cannot.be.empty=注册地址不能为空
ledger.legal.code.cannot.be.duplicate=法人主体不能重复
ledger.legal.code.already.exists=法人主体{0}已经存在

## 人员主数据
person.sequence.too.long=序列优先级不能大于999
person.id.exists=身份证号码重复，请查看对应人员数据

## 导入
excel.import.data.not.found=内容为空，请填写内容后再导入
excel.import.result.success=导入成功
excel.import.template.incorrect=导入的模板不正确
excel.import.analytic.anomaly=解析异常,请使用Excel标准模板
excel.import.result.failure=导入失败

## 权限
account.code.result.save=该员工账号已存在,请重新选择
application.code.result.delete=应用下面含有菜单不能删除
application.code.result.save=应用名称已存在

## 组织主数据
company.code.result.save=当前数据已被其他人修改，请刷新后重试
company.code.result.import.null=请填写数据
company.code.result.order=序列优先级已到最大值，请重新输入
company.code.result.check=公司名称已存在
nationality.not.find=国籍不存在
department.name.repeat=当前公司下部门名称重复
company.prohibit=当前公司下存在人员，不能禁用
storage.code.result.check=仓库名称已存在
department.code.result.check=部门名称已存在
department.code.result.enable=请先启用父级部门
department.prohibit=当前部门或子部门下存在人员，不能禁用
storage.person.result=该账号绑定的人员异常
control.area.existence.department=所选部门已存在其他控制范围，请重新选择
storage.cannot.be.disabled=请检查仓库{0}在供应商发货指令、物流入库、业务入库、物流出库、采购退换货、销售退换货中的使用情况

## 合作伙伴
partner.bank.not.match=银行信息不匹配
partner.person.not.match=人员信息不匹配

## 汇率
currency.exchange.date.repeat = 日期重复
currency.exchange.database.exists = 数据库已存在相同数据，请修改后重试
currency.exchange.rate.query.not.value=请维护当日汇率
currency.exchange.to.cannot.be.empty=币种至不能为空

## 外部价格
external.price.data.repeat = 相同价格日期，相同产品，相同价格类型，只能存在一条数据

## 供应链主体
supplier.status.exception=草稿、已驳回、已生效状态下才能修改
partner.name.exist=合作伙伴名称已存在
approval.error=审批失败，错误码
supplier.cannot.be.disabled=请检查供应链主体{0}在合同中的使用情况

## MDG
data.send.mdg.error=同步MDG失败:{0}
data.send.mdg.return.error=MDG返回报文异常
data.send.mdg.response.error=MDG响应异常
data.send.mdg.response.success=推送MDG成功

## supplier
supplier.type.not.exists.error=供应商类型错误
supplier.admission.status.not.exists.error=准入状态错误
supplier.admission.type.not.exists.error=准入类型错误
supplier.not.exist.error=供应商不存在
supplier.invitation.already.exist=重复的准入邀请

##supplierAssessment
supplier.assessment.no.not.exists=考核单编号单号不存在
supplier.assessment.not.exist=索赔考核单不存在
supplier.assessment.status.not.upload.doc=该考核单的状态不能上传素情材料
supplier.assessment.status.not.turn.to.cpe=该考核单的状态能否流转到CPE
supplier.assessment.not.support.invoice=该考核单不支持开票
supplier.assessment.not.support.close=索赔考核单状态不支持关闭
supplier.assessment.is.invoicing=您进行了开票操作，但发票暂未开具完成，暂不允许关闭考核单！
request.supplier.server.fail=请求供应商服务失败: {0}
invoice.amt.error=开票金额计算有误
supplier.assessment.invoice.record.not.exist=供应商质量考核开票记录不存在
assessment.back.to.cpe.desc.can.not.null=供应商考核单回退至cpe时,原因不能为空
assessment.status.can.not.back.cpe=供应商考核单状态不支持回退到cpe
request.user.server.fail=请求用户服务失败:{0}
request.invoice.server.fail=请求开票服务失败:{0}

#
user.permission.query.error=查询用户数据权限失败
supplier.cio.quality.issue.temporary.measures.null=临时措施为空，不能关闭！
supplier.cio.quality.issue.audit.option.null=审核意见不能为空
supplier.cio.quality.issue.sqe.id.null=负责的sqe不能为空
supplier.cio.quality.issue.complaint.problem.description.null=问题简述未填写，不能关闭！
supplier.cio.quality.issue.invalid.image.null=失效图片未上传，不能关闭！
supplier.cio.quality.issue.temporary.4q.8d.null=临时措施4Q8D报告未上传，不能关闭！
supplier.cio.audit.plan.repeat.valid.error=存在重复数据:{0} 
supplier.cio.audit.plan.adjust.status.error=只有进行中的任务可以进行操作
