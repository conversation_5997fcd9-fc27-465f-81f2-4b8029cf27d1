## 公共
common.option.result.failure=The operation fails
common.option.result.success=Operation is successful
common.option.result.version.error=Data has been updated, please refresh and try again
common.query.result.failure=The query fails
common.query.result.success=The query is successful
common.save.result.success=The save is successful
common.submit.result.success=The submit is successful
common.query.result.not.find=Query data does not exist
common.query.not.value=The query is no value
common.data.already.exists=The data is already exists
common.params.cannot.be.empty={0} cannot be empty
common.template.is.inconformity=template mismatch
common.data.is.empty=data is empty
common.data.is.duplicate=data is duplicate
common.params.failure=Parameter error
common.update.result.not.find=update data does not exist
common.delete.result.fail=delete data fail
## 信用额度
credit.limit.params.error=credit limit request params error
credit.limit.not.found=Credit limit application form does not exist

## 合作伙伴
new.code.not.exist=new code does not exist,please verify and try again
old.code.not.exist=old code does not exist,please verify and try again
new.code.status.not.effective=new code status not effective,refuse merge
this.partner.not.exist=this partner not exist
partner.cannot.be.disabled=Please check partner {0} usage in contract、storageLade、storageShippingInstruction

## 商品主数据
product.data.null=Commodity master data does not exist, please confirm
product.option.result.part.failure=The operation part failed
product.chinese.name.exists=chinese name already exists
product.chinese.name.duplicate=The chinese name is repeated
product.display.name.exists=display name already exists
product.display.name.duplicate=The display is repeated
product.import.title=product
product.import.sheet=product
product.excel.empty=The contents of Excel are empty
product.history.not.find=No operating history found!
product.customs.multiple=there are multiple customs
product.customs.not.find=customs don't exist
product.group.category.not.find=groupCategory customs don't exist
product.basic.unit.not.find=basicUnit don't exist
product.value.added.taxRate.not.find=valueAddedTax don't exist
product.value.added.salesTaxRate.not.find=salesTax don't exist
merge.query.result.not.find=The code to be merged does not exist or the status is not in effect
product.cannot.be.disabled=Please check the condition of the goods {0}
product.company.or.department.not.find=company or department don't exist

## 币种
currency.list.empty=Currency list is empty
currency.code.cannot.be.empty=Currency code cannot be empty
currency.name.cannot.be.empty=Currency name cannot be empty
currency.code.cannot.be.duplicate=Currency code cannot be duplicate
currency.code.already.exists=Currency code {0} already exists
currency.name.already.exists=Currency name {0} already exists
currency.not.exist=Currency does not exist
currency.cannot.be.disabled=Currency {0} is the functional currency of a ledger and cannot be disabled

## 会计日历
calendar.list.empty=calendar list is empty
calendar.code.cannot.be.empty=Calendar code cannot be empty
calendar.type.cannot.be.empty=Calendar type cannot be empty
calendar.desc.cannot.be.empty=Calendar description cannot be empty
calendar.init.open.year=Initial opening year cannot be empty
calendar.code.cannot.be.duplicate=Calendar code cannot be duplicate
calendar.code.already.exists=Calendar code {0} already exists
calendar.not.exist=Calendar does not exist

## 账套
ledger.code.cannot.be.empty=Ledger code cannot be empty
ledger.name.cannot.be.empty=Ledger name cannot be empty
ledger.chart.cannot.be.empty=Chart of accounts cannot be empty
ledger.calendar.cannot.be.empty=Accounting calendar cannot be empty
ledger.currency.code.cannot.be.empty=Functional currency cannot be empty
ledger.must.contain.legal=Ledger must contain legal person
ledger.code.already.exists=Ledger code {0} already exists
ledger.not.exist=Ledger does not exist
ledger.default.exchange.type.cannot.be.empty=Default exchange rate type cannot be empty
ledger.init.open.period.cannot.be.empty=Initial open period cannot be empty
ledger.legal.code.cannot.be.empty=Legal entity code cannot be empty
ledger.legal.name.cannot.be.empty=Legal entity cannot be empty
ledger.legal.country.cannot.be.empty=Country (region) cannot be empty
ledger.legal.credit.code.cannot.be.empty=Unified social credit code cannot be empty
ledger.legal.address.cannot.be.empty=Registered address cannot be empty
ledger.legal.code.cannot.be.duplicate=Legal entity cannot be duplicate
ledger.legal.code.already.exists=Legal entity {0} already exists

## 人员主数据
person.sequence.too.long=sequence priority cannot be greater than 999
person.id.exists=The ID card number is duplicate. Please check the corresponding personnel data

## 导入
excel.import.data.not.found=The content is empty, please fill in the content before importing
excel.import.result.success=Import success
excel.import.template.incorrect=The imported template is incorrect
excel.import.analytic.anomaly=To resolve the exception, use the Excel standard template
excel.import.result.failure=Import failure

## 权限
account.code.result.save=The employee account already exists, please re select
application.code.result.delete=The menu under the application cannot be deleted
application.code.result.save=App name already exists

## 组织主数据
company.code.result.save=The current data has been modified by someone else. Please refresh and try again
company.code.result.import.null=Please fill in the data
company.code.result.order=Sequence priority has reached the maximum value, please re-enter
company.code.result.check=Company name already exists
nationality.not.find=Nationality doesn't exist
department.name.repeat=Current Company name of the next department duplicate
company.prohibit=There are people in the current company, which cannot be disabled
storage.code.result.check=Warehouse name already exists
department.code.result.check=Department name already exists
department.code.result.enable=Please enable parent department first
department.prohibit=There are personnel under the current department or sub department, which cannot be disabled
storage.person.result=The person bound to this account is abnormal
control.area.existence.department=The selected department already has other control range, please re select
storage.cannot.be.disabled=Please check storage {0} usage in ShippingInstruction、distributionInbound、distributionBusinessInbound、distributionOutbound、purchaseReturn、salesReturn

## 合作伙伴
partner.bank.not.match=partner bank not match
partner.person.not.match=partner person not match

## 汇率
currency.exchange.date.repeat = date repeat
currency.exchange.database.exists = The same data exists in the database, please modify and retry
currency.exchange.rate.query.not.value=Currency exchange rate not find
currency.exchange.to.cannot.be.empty=currency exchange to cannot be empty

## 外部价格
external.price.data.repeat = same priceCreateTime, same product, same product type, only one record

## 供应链主体
supplier.status.exception=Draft, rejected, and effective can only be modified
partner.name.exist=Partner name already exists
approval.error=Approval failed, error code
supplier.cannot.be.disabled=Please check supplier {0} usage in contract

## MDG
data.send.mdg.error=data send mdg error:{0}
data.send.mdg.return.error=data send mdg return error
data.send.mdg.response.error=data send mdg response error
data.send.mdg.response.success=data send mdg response success 

## supplier
supplier.type.not.exists.error=there is no this type of supplier
supplier.admission.status.not.exists.error=there is no this admission status of supplier
supplier.admission.type.not.exists.error=there is no this admission type of supplier
supplier.not.exist.error=supplier not exist
supplier.invitation.already.exist=supplier invitation already exist

##supplierAssessment
supplier.assessment.no.not.exists=this assessment does not exists
supplier.assessment.status.not.upload.doc=this assessment status can not upload claim doc
supplier.assessment.status.not.turn.to.cpe=this assessment status can not turn to cpe
supplier.assessment.not.support.invoice=EN-该考核单不支持开票
supplier.assessment.not.exist=EN-索赔考核单不存在
supplier.assessment.not.support.close=EN-索赔考核单状态不支持关闭
supplier.assessment.is.invoicing=EN-您进行了开票操作，但发票暂未开具完成，暂不允许关闭考核单！
request.supplier.server.fail=request supplier server fail {0}
invoice.amt.error=invoice amount error
supplier.assessment.invoice.record.not.exist=supplier assessment invoice record not exist
assessment.back.to.cpe.desc.can.not.null=assessment back to cpe,the desc can not null
assessment.status.can.not.back.cpe=assessment status can not back to cpe
request.user.server.fail=request user server fail:{0}
request.invoice.server.fail=request invoice server fail:{0}

#
user.permission.query.error=EN-查询用户数据权限失败
supplier.cio.quality.issue.temporary.measures.null=the temporary measure is empty and cannot be closed
supplier.cio.quality.issue.audit.option.null=EN-审核意见不能为空
supplier.cio.quality.issue.sqe.id.null=EN-负责的sqe不能为空
supplier.cio.quality.issue.complaint.problem.description.null=problem description not filled in, cannot be closed
supplier.cio.quality.issue.invalid.image.null=invalid image not uploaded, cannot be closed
supplier.cio.quality.issue.temporary.4q.8d.null=temporary measure 4Q8D report not uploaded, cannot be closed
supplier.cio.audit.plan.repeat.valid.error=EN-存在重复数据:{0}
supplier.cio.audit.plan.adjust.status.error=EN-只有进行中的任务可以进行操作