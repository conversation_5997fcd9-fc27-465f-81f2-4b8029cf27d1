<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.mapper.ReportPermissionMapper">

    <select id="selectByReportId" resultType="com.weifu.srm.supplier.cio.entity.ReportPermission">
        SELECT *
        FROM t_report_permission
        WHERE report_id = #{reportId}
        AND deleted = 0
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_report_permission (
            report_id, permission_type, permission_value, 
            creator, create_time, updater, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.reportId}, #{item.permissionType}, #{item.permissionValue},
            #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <update id="deleteByReportId">
        UPDATE t_report_permission
        SET deleted = 1
        WHERE report_id = #{reportId}
    </update>

</mapper>