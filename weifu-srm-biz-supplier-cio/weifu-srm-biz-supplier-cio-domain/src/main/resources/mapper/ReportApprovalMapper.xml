<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.mapper.ReportApprovalMapper">

    <select id="selectLatestByReportId" resultType="com.weifu.srm.supplier.cio.entity.ReportApproval">
        SELECT *
        FROM t_report_approval
        WHERE report_id = #{reportId}
        AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>