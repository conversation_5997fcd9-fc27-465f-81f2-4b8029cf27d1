<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.cio.mapper.ReportInfoMapper">

    <select id="pageReports" resultType="com.weifu.srm.supplier.cio.entity.ReportInfo">
        SELECT r.*, c.category_name, d.domain_name
        FROM t_report_info r
        LEFT JOIN t_report_category c ON r.report_category_id = c.id
        LEFT JOIN t_business_domain d ON r.business_domain_id = d.id
        WHERE 1=1
        <if test="title != null and title != ''">
            AND r.report_title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="category != null and category != ''">
            AND c.category_name LIKE CONCAT('%', #{category}, '%')
        </if>
        <if test="code != null and code != ''">
            AND r.report_code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="source != null and source != ''">
            AND r.report_source = #{source}
        </if>
        <if test="displayType != null and displayType != ''">
            AND r.display_type = #{displayType}
        </if>
        <if test="status != null and status != ''">
            AND r.report_status = #{status}
        </if>
        ORDER BY r.create_time DESC
    </select>

</mapper>