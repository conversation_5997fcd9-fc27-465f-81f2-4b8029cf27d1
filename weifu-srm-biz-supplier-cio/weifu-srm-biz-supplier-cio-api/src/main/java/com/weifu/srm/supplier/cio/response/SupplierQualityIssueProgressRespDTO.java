package com.weifu.srm.supplier.cio.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-进度response
 * @Version 1.0
 */
@Data
@ApiModel(value = "SupplierQualityIssueProgressRespDTO", description = "供应商质量问题解决-进度response")
public class SupplierQualityIssueProgressRespDTO {
    @ApiModelProperty(value = "节点")
    private Integer progress;

    @ApiModelProperty(value = "问题状态")
    private String issueStatus;

    @ApiModelProperty(value = "是否完成")
    private Boolean isComplete;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



}
