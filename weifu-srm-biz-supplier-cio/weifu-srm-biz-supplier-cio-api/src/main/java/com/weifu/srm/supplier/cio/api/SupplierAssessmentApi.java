package com.weifu.srm.supplier.cio.api;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.cio.constants.ServiceConstants;
import com.weifu.srm.supplier.cio.request.assessment.*;
import com.weifu.srm.supplier.cio.response.assessment.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/13 18:02
 * @Description
 * @Version 1.0
 */
@Api("供应商索赔")
@FeignClient(value = ServiceConstants.APPLICATION_NAME)
public interface SupplierAssessmentApi {
    @ApiOperation("考核单列表")
    @PostMapping("/supplier/assessment/page")
    ApiResponse<PageResponse<SupplierAssessmentPageRespDTO>> queryAssessmentPage(@Valid @RequestBody SupplierAssessmentPageReqDTO req);
    @ApiOperation("考核单保存")
    @PostMapping("/supplier/assessment/save")
    ApiResponse<String> save(@Valid @RequestBody SupplierAssessmentSaveReqDTO reqDTO);
    @ApiOperation("考核单详情")
    @PostMapping("/supplier/assessment/detail/{assessmentNo}")
    ApiResponse<SupplierAssessmentDetailRespDTO> queryByAssessmentNo(@PathVariable("assessmentNo") @NotNull(message = "assessmentNo can not be null") String assessmentNo);
    @ApiOperation("考核单提交")
    @PostMapping("/supplier/assessment/submit")
    ApiResponse<String> submit(@Valid @RequestBody SupplierAssessmentSaveReqDTO reqDTO);

    @ApiOperation("删除考核单")
    @DeleteMapping("/supplier/assessment/delete/{assessmentNo}")
    ApiResponse<Void> delete(@PathVariable("assessmentNo") @NotNull(message = "assessmentNo can not be null") String assessmentNo);
    @ApiOperation("供应商接受或者不接受考核")
    @PostMapping("/supplier/assessment/acceptOrNot")
    ApiResponse<String> acceptOrNot(@Valid @RequestBody SupplierAssessmentAcceptOrNotReqDTO reqDTO);
    @ApiOperation("再次调整考核单提交")
    @PostMapping("/supplier/assessment/adjust/submit")
    ApiResponse<String> submitAgain(@Valid @RequestBody SupplierAssessmentSaveReqDTO reqDTO);
    @ApiOperation("强制考核提交")
    @PostMapping("/supplier/assessment/force/submit")
    ApiResponse<String> forceAssessment(@Valid @RequestBody SupplierAssessmentForceReqDTO reqDTO);
    @ApiOperation("提前结束考核单")
    @PostMapping("/supplier/assessment/earlyEnd/submit")
    ApiResponse<String> earlyEndAssessment(@Valid @RequestBody SupplierAssessmentEarlyEndReqDTO reqDTO);
    @ApiOperation("流转至CPE接口")
    @PostMapping("/supplier/assessment/turnToCpe")
    ApiResponse<String> turnToCpe(@Valid @RequestBody SupplierAssessmentTurnToCPEReqDTO reqDTO);
    @ApiOperation("关闭考核单")
    @PostMapping("/supplier/assessment/close")
    ApiResponse<String> closeAssessment(@Valid @RequestBody SupplierAssessmentCloseReqDTO reqDTO);

    @ApiOperation("考核单列表导出查询")
    @PostMapping("/supplier/assessment/listExport")
    ApiResponse<List<SupplierAssessmentPageRespDTO>> queryAssessmentListExport(@Valid @RequestBody SupplierAssessmentPageReqDTO req);
    @ApiOperation("考核单历史记录")
    @GetMapping("/supplier/assessment/log/{assessmentNo}")
    ApiResponse<List<SupplierAssessmentLogRespDTO>> queryAssessmentLog(@PathVariable("assessmentNo") @NotNull(message = "assessmentNo can not be null") String assessmentNo);
    @ApiOperation("考核单历史记录详情")
    @GetMapping("/supplier/assessment/log/detail/{assessmentLogId}")
    ApiResponse<SupplierAssessmentLogDetailRespDTO> queryAssessmentLogDetail(@PathVariable("assessmentLogId") @NotNull(message = "assessmentLogId can not be null") String assessmentLogId);
    @ApiOperation("上传索赔材料及索赔协议")
    @PostMapping("/supplier/assessment/upload/claimDoc")
    ApiResponse<String> uploadClaimDoc(@Valid @RequestBody SupplierAssessmentUploadClaimDocReqDTO reqDTO);
    @ApiOperation("CPE回退供应商考核至SQE")
    @PostMapping("/supplier/assessment/returnToSqe")
    ApiResponse<String> returnToSqe(@Valid @RequestBody SupplierAssessmentReturnToSQEReqDTO reqDTO);

    @ApiOperation("供应商质量考核-蓝票开具接口")
    @PostMapping("/supplier/assessment/blueInvoiceOpen")
    ApiResponse<Void> assessmentBlueInvoiceOpen(@Valid @RequestBody SupplierAssessmentBlueInvoiceOpenReqDTO req);

    @ApiOperation("供应商质量考核-蓝票开具结果通知接口")
    @PostMapping("/supplier/assessment/blueInvoiceResult")
    ApiResponse<Void> assessmentBlueInvoiceOpenResult(@Valid @RequestBody SupplierAssessmentBlueInvoiceOpenResultReqDTO req);

    @ApiOperation("供应商质量考核-开票记录查询")
    @GetMapping("/supplier/assessment/invoiceHistory")
    ApiResponse<List<SupplierAssessmentInvoiceListRespDTO>> assessmentInvoiceHistory(@RequestParam("assessmentNo") String assessmentNo);

    @ApiOperation("供应商质量考核-开票记录详情")
    @GetMapping("/supplier/assessment/invoiceFullDetail")
    ApiResponse<SupplierAssessmentInvoiceFullDetailRespDTO> invoiceDetail(@RequestParam("invoiceId")Long invoiceId);

    @ApiOperation("供应商索赔首页展示数据统计")
    @GetMapping("/supplier/assessment/statistics")
    ApiResponse<SupplierAssessmentStatisticsRespDTO> queryAssessmentStatistics(@RequestParam(value = "userId",required = false) Long userId,
                                                                               @RequestParam(value = "supplierCode",required = false)String  supplierCode);

}
