package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商定时任务-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityTimerStatusEnum {
    ENABLE("ENABLE", "启用"),
    DISABLE("DISABLE", "禁用"),
    STOP("STOP", "结束");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityTimerStatusEnum statusEnum : SupplierQualityTimerStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
