package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-附件
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AttachmentMessageReqDTO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "附件真实名称")
    @NotNull(message = "附件真实名称 不能为空")
    private String fileOriginalName;

    @NotNull(message = "附件URL 不能为空")
    @ApiModelProperty(value = "附件URL")
    private String fileUrl;

    @NotNull(message = "附件Name 不能为空")
    @ApiModelProperty(value = "附件Name")
    private String fileName;
}
