package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-供应商提交长期措施request
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierQualitySupplierMeasureReqDTO", description = "供应商质量问题解决-供应商提交长期措施request")
public class SupplierQualitySupplierMeasureReqDTO extends SupplierQualityIdReqDTO {

    @NotBlank(message = "supplierMeasureActual不能为空")
    @ApiModelProperty(value = "供应商提交长期措施-实际实施的长期措施", required = true)
    private String supplierMeasureActual;

    @NotBlank(message = "supplierMeasurePrevention不能为空")
    @ApiModelProperty(value = "供应商提交长期措施-预防再发生措施", required = true)
    private String supplierMeasurePrevention;

    @ApiModelProperty(value = "供应商提交长期措施-审核意见")
    private String supplierMeasureAuditOpinion;

    @ApiModelProperty(value = "供应商提交长期措施-4q8d报告及其它附件")
    private List<AttachmentMessageReqDTO> supplierMeasureList;

}
