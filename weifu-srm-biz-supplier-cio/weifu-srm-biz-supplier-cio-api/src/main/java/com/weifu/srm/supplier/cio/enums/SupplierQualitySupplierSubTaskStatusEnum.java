package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量供应商子任务-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualitySupplierSubTaskStatusEnum {
    SUBMITTED("SUBMITTED","待反馈"),
    FEEDBACK_RECEIVED("FEEDBACK_RECEIVED","已反馈"),
    APPROVE_FAIL("APPROVE_FAIL","审核未通过"),
    CLOSED("CLOSED","已关闭"),
    WITHDRAWN("WITHDRAWN","已撤回");
    private final String code;
    private final String name;
    public static String getNameByCode(String code) {
        for (SupplierQualitySupplierSubTaskStatusEnum statusEnum : SupplierQualitySupplierSubTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
