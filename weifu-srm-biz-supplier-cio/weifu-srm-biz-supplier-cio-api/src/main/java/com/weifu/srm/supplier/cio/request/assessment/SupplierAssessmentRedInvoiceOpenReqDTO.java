package com.weifu.srm.supplier.cio.request.assessment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SupplierAssessmentRedInvoiceOpenReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    @NotNull(message = "考核单编号不能为空")
    private String assessmentNo ;
    /** 蓝票票号 当开具红冲发票时需要该信息 */
    @ApiModelProperty(value = "蓝票票号",notes = "")
    private String blueInvoiceNo ;
    /** 开具蓝票时，FSSC返回的业务单号 */
    @ApiModelProperty(value = "FSSC蓝票单号",notes = "")
    private String blueFsscInvoiceNo ;
    /**归属部门-开票人所在二级部门ID*/
    @ApiModelProperty(value = "归属部门",notes = "")
    @NotNull(message = "归属部门不能为空")
    private Long departmentId;
    /**制单人-开票人*/
    @ApiModelProperty(value = "制单人",notes = "")
    @NotNull(message = "制单人不能为空")
    private String invoiceOpenUserName;
    /** 业务大类 */
    @ApiModelProperty(value = "业务大类",notes = "")
    @NotNull(message = "业务大类不能为空")
    private String bizCategory ;
    /** 业务小类 */
    @ApiModelProperty(value = "业务小类",notes = "")
    @NotNull(message = "业务小类不能为空")
    private String bizSubcategory ;
    /** 红冲类型 */
    @ApiModelProperty(value = "红冲类型",notes = "")
    @NotNull(message = "红冲类型不能为空")
    private String redInvoiceType;
    /** 红冲通知单号 */
    @ApiModelProperty(value = "红冲通知单号",notes = "")
    @NotNull(message = "红冲通知单号不能为空")
    @Length(max = 2000,message = "红冲通知单号长度不能超过2000")
    private String redNoticeNo;
    /** 红冲原因 */
    @ApiModelProperty(value = "红冲原因",notes = "")
    @NotNull(message = "红冲原因不能为空")
    private String redReason;
    /** 开票说明 */
    @ApiModelProperty(value = "开票说明",notes = "")
    private String invoiceDesc ;
    /**操作人*/
    private Long userId;
    /**操作人*/
    private String userName;


}
