package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商任务-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商审核供应商任务-response")
public class SupplierQualitySupplierTaskRespDTO {
    @ExcelIgnore
    @ApiModelProperty("主键")
    private Long id;

    @ExcelProperty(value = "任务编号", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("任务编号")
    private String taskNo;

    @ExcelProperty(value = "任务名称", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty("任务名称")
    private String taskName;

    @ExcelProperty(value = "创建类型", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("创建类型名称")
    private String createTypeName;

    @ExcelProperty(value = "任务类型", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    @ExcelProperty(value = "威孚负责人", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty("威孚负责人名称")
    private String principalName;

    @ExcelProperty(value = "已关闭任务数/总任务数", index = 5)
    @ColumnWidth(40)
    @ApiModelProperty("已关闭任务数")
    private String statistics;

    @ExcelProperty(value = "单据类型", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty("单据类型名称")
    private String relationTypeName;

    @ExcelProperty(value = "关联单据号", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ExcelProperty(value = "创建时间", index = 8)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelProperty(value = "更新时间", index = 9)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("结束日期")
    private Date timerEndDate;

    @ExcelIgnore
    @ApiModelProperty("单据类型")
    private String relationType;

    @ExcelIgnore
    @ApiModelProperty("任务类型")
    private String taskType;

    @ExcelIgnore
    @ApiModelProperty("创建类型")
    private String createType;

    @ExcelIgnore
    @ApiModelProperty("任务类型其它")
    private String taskTypeOther;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("实际完成日期")
    private Date practicalCompleteDate;

    @ExcelIgnore
    @ApiModelProperty("负责人id")
    private Long principalId;

    @ExcelIgnore
    @ApiModelProperty("供应商联系人类型")
    private String supplierContactType;

    @ExcelIgnore
    @ApiModelProperty("供应商联系人类型名称")
    private String supplierContactTypeName;

    @ExcelIgnore
    @ApiModelProperty("供应商基础表ID")
    private Long supplierBasicMsgId;

    @ExcelIgnore
    @ApiModelProperty("附加通知人名称")
    private String additionalNotifier;

    @ExcelIgnore
    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ExcelIgnore
    @ApiModelProperty("创建人id")
    private Long createBy;

    @ExcelIgnore
    @ApiModelProperty("创建人姓名")
    private String createName;

}
