package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-issueNo-request
 * @Version 1.0
 */
@Data
@ApiModel(value = "供应商质量问题解决-issueNo-request", description = "供应商质量问题解决-issueNo-request")
public class SupplierQualityIssueNoReqDTO {

    @NotBlank(message = "issueNo不能为空")
    @ApiModelProperty(value = "问题编号",required = true)
    private String issueNo;

}
