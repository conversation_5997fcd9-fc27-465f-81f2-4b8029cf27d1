package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核计划-审核类型小类
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityAuditPlanSubclassTypeEnum {
    PROCESS_AUDIT("PROCESS_AUDIT", "过程审核"),
    PRODUCT_AUDIT("PRODUCT_AUDIT", "产品审核"),
    SYSTEM_AUDIT("SYSTEM_AUDIT", "体系审核"),
    POTENTIAL_SUPPLIER_AUDIT("POTENTIAL_SUPPLIER_AUDIT", "潜在供应商审核"),
    FULL_SIZE_FUNCTIONAL_TESTING("FULL_SIZE_FUNCTIONAL_TESTING", "全尺寸和功能性试验"),
    SUPPLIER_AUDIT("SUPPLIER_AUDIT", "分供方审核"),
    SPECIAL_REVIEW("SPECIAL_REVIEW", "专项审核"),
    TYPE_TEST("TYPE_TEST", "型式试验"),
    OTHER("OTHER", "其他");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityAuditPlanSubclassTypeEnum statusEnum : SupplierQualityAuditPlanSubclassTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
