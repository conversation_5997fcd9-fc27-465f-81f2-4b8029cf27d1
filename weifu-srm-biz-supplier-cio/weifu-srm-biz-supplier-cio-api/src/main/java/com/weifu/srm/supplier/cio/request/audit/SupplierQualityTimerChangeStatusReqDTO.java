package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核子任务-修改状态request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualityTimerChangeStatusReqDTO", description = "供应商审核子任务-修改状态request")
public class SupplierQualityTimerChangeStatusReqDTO extends OperatorBaseReqDTO {
    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("状态")
    private String status;


}
