package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-撤回request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualityOplTaskWithdrawReqDTO", description = "供应商质量opl任务-撤回request")
public class SupplierQualityOplTaskWithdrawReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("说明")
    private String auditExplanation;

    @ApiModelProperty(value = "撤回说明附件")
    private List<AttachmentMessageReqDTO> oplWithdrawAnnexList;
}
