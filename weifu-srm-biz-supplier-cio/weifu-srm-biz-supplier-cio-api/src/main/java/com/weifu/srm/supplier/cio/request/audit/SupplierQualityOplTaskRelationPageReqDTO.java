package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量opl任务-关联单据分页查询request")
@Data
public class SupplierQualityOplTaskRelationPageReqDTO extends PageRequest implements Serializable {

    @NotBlank(message = "relationType is required.")
    @ApiModelProperty(value = "单据类型", required = true)
    private String relationType;

    @NotBlank(message = "relationNo is required.")
    @ApiModelProperty(value = "关联单据号", required = true)
    private String relationNo;
}
