package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-调整计划时间request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualityOplTaskAdjustmentReqDTO", description = "供应商质量opl任务-调整计划时间request")
public class SupplierQualityOplTaskAdjustmentReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;
}
