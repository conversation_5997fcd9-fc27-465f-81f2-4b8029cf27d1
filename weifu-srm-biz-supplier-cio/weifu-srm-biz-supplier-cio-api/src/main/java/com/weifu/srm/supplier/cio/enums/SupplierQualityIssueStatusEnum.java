package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-问题状态状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityIssueStatusEnum {
    DRAFT("DRAFT",0,"草稿"),
    PROPOSE_ISSUE("PROPOSE_ISSUE",0,"提出问题"),
    SUBMITTED_TEMPORARY_MEASURES("SUBMITTED_TEMPORARY_MEASURES",1,"已提交临时措施"),
    CONFIRMED_BY_SQE("CONFIRMED_BY_SQE",1,"待SQE确认"),
    SQE_RETURN("SQE_RETURN",1,"SQE退回"),
    SUPPLIER_PROPOSED_CAUSE_ANALYSIS("SUPPLIER_PROPOSED_CAUSE_ANALYSIS",2,"供应商已提出根因分析"),
    CAUSE_ANALYSIS_VERIFICATION_FAILED("CAUSE_ANALYSIS_VERIFICATION_FAILED",2,"根因分析验证不通过"),
    CAUSE_ANALYSIS_VERIFIED("CAUSE_ANALYSIS_VERIFIED",2,"已验证根因分析"),
    SUPPLIER_SUBMITTED_MEASURES("SUPPLIER_SUBMITTED_MEASURES",3,"供应商已提交长期措施"),
    MEASURES_VERIFICATION_FAILED("MEASURES_VERIFICATION_FAILED",3,"长期措施验证不通过"),
    MEASURES_VERIFIED("MEASURES_VERIFIED",3,"已验证长期措施"),
    ISSUE_CLOSED("ISSUE_CLOSED",4,"问题关闭"),
    WITHDRAWN("WITHDRAWN",5,"已撤回");

    private final String code;
    private final Integer progress;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityIssueStatusEnum statusEnum : SupplierQualityIssueStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
    public static Integer getProgressByCode(String code) {
        for (SupplierQualityIssueStatusEnum statusEnum : SupplierQualityIssueStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getProgress();
            }
        }
        return null;
    }
    public static SupplierQualityIssueStatusEnum getByCode(String code) {
        for (SupplierQualityIssueStatusEnum statusEnum : SupplierQualityIssueStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
