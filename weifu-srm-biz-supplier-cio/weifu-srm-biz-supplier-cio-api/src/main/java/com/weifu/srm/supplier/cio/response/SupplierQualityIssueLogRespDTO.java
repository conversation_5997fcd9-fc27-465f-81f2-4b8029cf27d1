package com.weifu.srm.supplier.cio.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-操作日志-response
 * @Version 1.0
 */
@Data
@ApiModel(value = "供应商质量问题解决-操作日志-response", description = "供应商质量问题解决-操作日志-response")
public class SupplierQualityIssueLogRespDTO {
    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "问题编号")
    private String issueNo;
    @ApiModelProperty(value = "问题状态")
    private String issueStatus;
    @ApiModelProperty(value = "审核结果：PASS-通过 ，FAIL-不通过")
    private String auditResult;
    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;
    @ApiModelProperty(value = "操作人id")
    private Long createBy;
    @ApiModelProperty(value = "操作人名称")
    private String createName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作人时间")
    private Date createTime;
}
