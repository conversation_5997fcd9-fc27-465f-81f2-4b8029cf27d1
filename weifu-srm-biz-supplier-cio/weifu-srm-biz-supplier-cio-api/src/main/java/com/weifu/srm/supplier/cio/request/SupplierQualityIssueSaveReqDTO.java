package com.weifu.srm.supplier.cio.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-新增request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierQualityIssueSaveReqDTO", description = "供应商质量问题解决-新增request")
@Data
public class SupplierQualityIssueSaveReqDTO extends OperatorBaseReqDTO {

    @ApiModelProperty(value = "是否提交，1：是（提交），2：否（保存）")
    private Boolean isSubmit = false;

    @NotBlank(message = "divisionCode不能为空")
    @ApiModelProperty(value = "业部/子公司编码", required = true)
    private String divisionCode;

    @ApiModelProperty(value = "事业部/子公司名称", required = true)
    private String divisionName;

    @NotBlank(message = "supplierCode不能为空")
    @ApiModelProperty(value = "供应商编码", required = true)
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称", required = true)
    private String supplierName;

    @ApiModelProperty(value = "供应商英文名称")
    private String supplierNameEn;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @NotNull(message = "supplierContactsId不能为空")
    @ApiModelProperty(value = "供应商联系人id", required = true)
    private Long supplierContactsId;

    @NotBlank(message = "supplierContactsType不能为空")
    @ApiModelProperty(value = "供应商联系人类型")
    private String supplierContactsType;

    @ApiModelProperty(value = "供应商联系人邮箱")
    private String supplierContactsEmail;

    @ApiModelProperty(value = "供应商联系人名称", required = true)
    private String supplierContactsName;

    @NotBlank(message = "partSapNo不能为空")
    @ApiModelProperty(value = "问题零件信息-零件sap号", required = true)
    private String partSapNo;

    @NotBlank(message = "complaintPhase不能为空")
    @ApiModelProperty(value = "投诉阶段", required = true)
    private String complaintPhase;

    @NotBlank(message = "analysisApproach不能为空")
    @ApiModelProperty(value = "分析方式,：4Q 、8D", required = true)
    private String analysisApproach;

    @ApiModelProperty(value = "分析方式-其它手动输入")
    private String analysisApproachOther;

    @ApiModelProperty(value = "问题零件信息-产品系列")
    private String partProductSeriesName;

    @ApiModelProperty(value = "问题零件信息-总成号")
    private String partAssemblyNo;

    @ApiModelProperty(value = "问题零件信息-零件名称", required = true)
    private String partName;

    @NotBlank(message = "partModel不能为空")
    @ApiModelProperty(value = "问题零件信息-零件型号", required = true)
    private String partModel;

    @ApiModelProperty(value = "问题零件信息-零件品类名称", required = true)
    private String partCategoryName;

    @NotBlank(message = "partCategoryCode不能为空")
    @ApiModelProperty(value = "问题零件信息-零件品类编码", required = true)
    private String partCategoryCode;

    @ApiModelProperty(value = "问题零件信息-投诉数量")
    private String partComplaintQty;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "问题零件信息-生产日期")
    private Date partProductionDate;

    @ApiModelProperty(value = "问题零件信息-生产批次")
    private String partProductionBatch;

    @NotBlank(message = "complaintTitle不能为空")
    @ApiModelProperty(value = "投诉信息-投诉标题", required = true)
    private String complaintTitle;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "complaintDate不能为空")
    @ApiModelProperty(value = "投诉信息-投诉日期", required = true)
    private Date complaintDate;

    @ApiModelProperty(value = "投诉信息-客户代码")
    private String complaintCustomCode;

    @ApiModelProperty(value = "投诉信息-客户名称")
    private String complaintCustomName;

    @ApiModelProperty(value = "投诉信息-投诉类型")
    private String complaintType;

    @NotBlank(message = "complaintFailureCategory不能为空")
    @ApiModelProperty(value = "投诉信息-失效类别", required = true)
    private String complaintFailureCategory;

    @ApiModelProperty(value = "投诉信息-失效类别-其它手动输入")
    private String complaintFailureCategoryOther;

    @ApiModelProperty(value = "投诉信息-发现地点/工位")
    private String complaintDiscoverySite;

    @ApiModelProperty(value = "投诉信息-失效现象简述")
    private String complaintFailureSituationResume;

    @NotBlank(message = "complaintProblemDescription不能为空")
    @ApiModelProperty(value = "投诉信息-问题简述", required = true)
    private String complaintProblemDescription;

    @NotNull(message = "reasonAnalysisFeedbackTerm不能为空")
    @ApiModelProperty(value = "其它信息-原因分析及长期反馈期限", required = true)
    private Integer reasonAnalysisFeedbackTerm;

    @NotNull(message = "preventRecurFeedbackTerm不能为空")
    @ApiModelProperty(value = "其它信息-预防再发生反馈期限", required = true)
    private Integer preventRecurFeedbackTerm;

    @ApiModelProperty(value = "投诉信息-失效图片")
    private List<AttachmentMessageReqDTO> invalidImageList;

    @ApiModelProperty(value = "其它信息-4Q/8D附件")
    private List<AttachmentMessageReqDTO> other4q8dList;

    @ApiModelProperty(value = "其他说明")
    private String otherDescription;
}
