package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-长期措施验证request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-长期措施验证request", description = "供应商质量问题解决-长期措施验证request")
public class SupplierQualityIssueMeasureReqDTO extends SupplierQualityIdReqDTO {

    @NotBlank(message = "measureAuditResult不能为空")
    @ApiModelProperty(value = "长期措施验证-审核结果：PASS-通过 ，FAIL-不通过")
    private String measureAuditResult;

    @ApiModelProperty(value = "长期措施验证-审核意见")
    private String measureAuditOpinion;

    @NotBlank(message = "measureQ11Benchmarking不能为空")
    @ApiModelProperty(value = "长期措施验证-Q11对标:", required = true)
    private String measureQ11Benchmarking;

    @ApiModelProperty(value = "长期措施验证-Q11对标:手动输入")
    private String measureQ11BenchmarkingOther;


}
