package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-复制request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-复制request")
@Data
public class SupplierQualityAuditPlanCopyReqDTO extends OperatorBaseReqDTO {
    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

}
