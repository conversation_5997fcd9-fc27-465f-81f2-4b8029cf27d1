package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商任务-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商审核供应商任务-分页查询request")
@Data
public class SupplierQualitySupplierTaskPageReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty("任务编码")
    private String taskNo;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("负责人id")
    private Long principalId;

    @ApiModelProperty("负责人名称")
    private String principalName;

    @ApiModelProperty("单据类型")
    private String relationType;

    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建任务类型")
    private String createType;

    @ApiModelProperty(value = "创建时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "计划完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateStart;

    @ApiModelProperty(value = "计划完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateEnd;

    @ApiModelProperty(value = "操作人", hidden = true)
    private Long operationBy;
}
