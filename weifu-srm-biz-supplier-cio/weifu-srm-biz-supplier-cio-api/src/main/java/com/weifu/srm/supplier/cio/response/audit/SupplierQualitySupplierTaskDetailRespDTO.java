package com.weifu.srm.supplier.cio.response.audit;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商任务-详情response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商审核供应商任务-详情response")
public class SupplierQualitySupplierTaskDetailRespDTO extends SupplierQualitySupplierTaskRespDTO {

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageRespDTO> annexList;

}
