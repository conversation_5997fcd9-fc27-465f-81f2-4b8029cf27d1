package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量opl任务-分页查询request")
@Data
public class SupplierQualityOplTaskPageReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty("OPL编号")
    private String oplNo;

    @ApiModelProperty("OPL名称")
    private String oplName;

    @ApiModelProperty("状态")
    private List<String> statusList;

    @ApiModelProperty("OPL类型")
    private List<String> oplTypeList;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建人名称")
    private String createName;

    @ApiModelProperty("负责人id")
    private Long principalId;

    @ApiModelProperty("负责人名称")
    private String principalName;

    @ApiModelProperty("单据类型")
    private String relationType;

    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ApiModelProperty("一级品类编码")
    private String firstCategoryCode;

    @ApiModelProperty(value = "创建时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "计划完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateStart;

    @ApiModelProperty(value = "计划完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateEnd;

    @ApiModelProperty(value = "实际完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date practicalCompleteDateStart;

    @ApiModelProperty(value = "实际完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date practicalCompleteDateEnd;

    @ApiModelProperty("关闭时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeStart;

    @ApiModelProperty("关闭时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeEnd;

    @ApiModelProperty(value = "操作人", hidden = true)
    private Long operationBy;
}
