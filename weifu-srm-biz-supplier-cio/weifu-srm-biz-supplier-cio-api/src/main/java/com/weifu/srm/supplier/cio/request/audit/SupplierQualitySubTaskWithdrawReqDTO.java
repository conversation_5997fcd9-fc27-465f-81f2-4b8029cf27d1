package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-撤回request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualitySubTaskWithdrawReqDTO", description = "供应商任务-撤回request")
public class SupplierQualitySubTaskWithdrawReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id is required.")
    @ApiModelProperty(value = "主键",required = true)
    private Long id;

    @ApiModelProperty("说明")
    private String auditExplanation;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageReqDTO> supplierAuditAnnexList;

}
