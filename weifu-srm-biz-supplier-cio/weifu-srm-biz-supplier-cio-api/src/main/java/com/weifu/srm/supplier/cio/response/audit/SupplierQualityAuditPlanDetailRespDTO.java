package com.weifu.srm.supplier.cio.response.audit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-详情response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商质量审核计划-详情response")
public class SupplierQualityAuditPlanDetailRespDTO extends SupplierQualityAuditPlanRespDTO {

    @ApiModelProperty("更新人id")
    private Long updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更新人姓名")
    private String updateName;

    @ApiModelProperty("审核计划任务列表")
    private List<SupplierQualityAuditTaskRespDTO> taskList;

}
