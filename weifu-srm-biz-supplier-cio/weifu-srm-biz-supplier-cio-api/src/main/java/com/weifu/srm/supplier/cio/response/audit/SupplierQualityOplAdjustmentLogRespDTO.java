package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务计划完成时间调整记录-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商质量opl任务计划完成时间调整记录-response")
public class SupplierQualityOplAdjustmentLogRespDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("OPL编号")
    private String oplNo;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人姓名")
    private String createName;

}
