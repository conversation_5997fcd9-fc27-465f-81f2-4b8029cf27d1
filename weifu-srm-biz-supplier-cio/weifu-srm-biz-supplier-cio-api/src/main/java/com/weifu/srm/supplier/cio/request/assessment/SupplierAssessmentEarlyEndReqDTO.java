package com.weifu.srm.supplier.cio.request.assessment;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/14 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentEarlyEndReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 提前结束考核单申请说明 */
    @ApiModelProperty(value = "提前结束考核单申请说明",notes = "")
    private String earlyEndDesc ;
    /*** 提前结束考核单申请说明附件 */
    @ApiModelProperty(value = "提前结束考核单申请说明附件",notes = "")
    private List<AttachmentMessageReqDTO> earlyEndAttachment ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;

}
