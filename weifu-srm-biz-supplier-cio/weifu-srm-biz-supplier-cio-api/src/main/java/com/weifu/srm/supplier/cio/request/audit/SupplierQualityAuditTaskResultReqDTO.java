package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-提交结果request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-提交结果request")
@Data
public class SupplierQualityAuditTaskResultReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id is required.")
    @ApiModelProperty("主键")
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("实际完成日期")
    private Date practicalCompleteDate;

    @ApiModelProperty("审核结论")
    private String auditConclusion;

    @ApiModelProperty("审核得分")
    private String auditScore;

    @ApiModelProperty("审核结果备注")
    private String auditRemark;

    @ApiModelProperty("创建任务：NO不需要创建，OPL创建OPL任务，SUPPLIER供应商任务")
    private String createTask;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageReqDTO> annexList;


}
