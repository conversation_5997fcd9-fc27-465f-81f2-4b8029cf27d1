package com.weifu.srm.supplier.cio.request.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/13 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentPageReqDTO extends PageRequest implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 供应商编码 */
    @ApiModelProperty(value = "供应商编码",notes = "")
    private String supplierCode ;
    /** 状态 */
    @ApiModelProperty(value = "状态",notes = "")
    private String status ;
    @ApiModelProperty(value = "状态，供应商端检索考核单状态，代供应商反馈:WAITING_SUPPLIER_FEEDBACK；进行中:DOING；提前结束:END_EARLY，已完成:COMPLETED",notes = "")
    private String statusFromSupplier;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态",notes = "")
    private String invoiceStatus ;
    /** 公司代码 */
    @ApiModelProperty(value = "公司代码",notes = "")
    private String division ;
    /** 考核金额（含税） */
    @ApiModelProperty(value = "考核金额从",notes = "")
    private BigDecimal assessmentTaxAmtFrom ;
    @ApiModelProperty(value = "考核金额止",notes = "")
    private BigDecimal assessmentTaxAmtTo ;
    /** 品类编码 */
    @ApiModelProperty(value = "品类编码",notes = "")
    private String categoryCode ;
    /** 到期日 */
    @ApiModelProperty(value = "到期日从",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date deadlineTimeFrom ;
    @ApiModelProperty(value = "到期日止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date deadlineTimeTo;
    /** 质量负责人ID */
    @ApiModelProperty(value = "质量负责人ID",notes = "")
    private Long sqeId ;
    /** 品类工程师ID */
    @ApiModelProperty(value = "品类工程师ID",notes = "")
    private Long cpeId ;
    /** 质量负责人 */
    @ApiModelProperty(value = "质量负责人名称",notes = "")
    private String sqeName ;
    /** 品类工程师 */
    @ApiModelProperty(value = "品类工程师名称",notes = "")
    private String cpeName ;
    /** 当前操作人 */
    @ApiModelProperty(value = "当前操作人",notes = "")
    private Long currentOperationBy ;
    /** 当前操作人名称 */
    @ApiModelProperty(value = "当前操作人名称",notes = "")
    private String currentOperationByName ;
    @ApiModelProperty(value = "创建人ID",notes = "")
    private Long createBy;
    @ApiModelProperty(value = "创建时间从",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeFrom;
    @ApiModelProperty(value = "创建时间止",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeTo;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;
}
