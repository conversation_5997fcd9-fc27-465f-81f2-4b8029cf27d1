package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商任务定时任务配置-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商任务定时任务配置-保存request")
@Data
public class SupplierQualityTimerSaveReqDTO extends OperatorBaseReqDTO {

    @ApiModelProperty("定时任务编号")
    private String timerNo;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("任务类型其它")
    private String taskTypeOther;

    @ApiModelProperty("单据类型")
    private String relationType;

    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ApiModelProperty("负责人id")
    private Long principalId;

    @ApiModelProperty("负责人名称")
    private String principalName;

    @ApiModelProperty("供应商联系人类型")
    private String supplierContactType;

    @ApiModelProperty("附加通知人")
    private String additionalNotifier;

    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty("定时类型：WEEK周，MONTH月,  QUARTER季")
    private String timerType;

    @ApiModelProperty("定时详情，天")
    private Integer timerDay;

    @ApiModelProperty("要求供应商反馈天数")
    private Integer feedbackDay;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("定时任务结束日期")
    private Date timerEndDate;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageReqDTO> annexList;

    @ApiModelProperty("接受供应商")
    private List<SupplierQualityTimerReceiveReqDTO> receiveList;
}
