package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-供应商根因分析request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualitySupplierAnalysisReqDTO", description = "供应商质量问题解决-供应商根因分析request")
public class SupplierQualitySupplierAnalysisReqDTO extends SupplierQualityIdReqDTO {

    @NotBlank(message = "supplierAnalysisDescription不能为空")
    @ApiModelProperty(value = "供应商根因分析-根因简述", required = true)
    private String supplierAnalysisDescription;

    @NotBlank(message = "supplierAnalysisActionPlan不能为空")
    @ApiModelProperty(value = "供应商根因分析-长期措施计划", required = true)
    private String supplierAnalysisActionPlan;

    @ApiModelProperty(value = "供应商根因分析-4q8d报告及其它附件")
    private List<AttachmentMessageReqDTO> supplierAnalysisList;


}
