package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-sqe接收-request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-sqe接收-request", description = "供应商质量问题解决-sqe接收-request")
public class SupplierQualityIssueReceiveReqDTO extends SupplierQualityIdReqDTO {


    @NotNull(message = "receiveResult不能为空")
    @ApiModelProperty(value = "接收/退回",required = true)
    private Boolean receiveResult;

    @ApiModelProperty(value = "接收/退回 意见")
    private String receiveOpinion;

}
