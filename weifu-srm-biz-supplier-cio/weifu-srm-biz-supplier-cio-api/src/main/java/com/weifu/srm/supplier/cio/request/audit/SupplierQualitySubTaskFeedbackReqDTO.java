package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商任务-提交反馈request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商审核供应商子任务-提交反馈request")
@Data
public class SupplierQualitySubTaskFeedbackReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("反馈说明")
    private String feedbackExplanation;

    @ApiModelProperty(value = "反馈附件")
    private List<AttachmentMessageReqDTO> supplierFeedbackAnnexList;
}
