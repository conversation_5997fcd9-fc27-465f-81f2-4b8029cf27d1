package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核子任务-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商审核子任务-response")
public class SupplierQualitySupplierTaskSubRespDTO {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("任务编码")
    private String taskNo;

    @ApiModelProperty("子任务编码")
    private String subTaskNo;

    @NotBlank(message = "supplierCode is required.")
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @NotBlank(message = "supplierShortName is required.")
    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("任务名称")
    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ApiModelProperty("任务状态")
    private String status;

    @ApiModelProperty("任务状态名称")
    private String statusName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("供应商反馈时间")
    private Date feedbackTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("关闭时间")
    private Date endTime;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人姓名")
    private String createName;

    @ApiModelProperty(value = "附件")
    private AttachmentMessageRespDTO annexResp;

    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    @ApiModelProperty("威孚负责人名称")
    private String principalName;
}
