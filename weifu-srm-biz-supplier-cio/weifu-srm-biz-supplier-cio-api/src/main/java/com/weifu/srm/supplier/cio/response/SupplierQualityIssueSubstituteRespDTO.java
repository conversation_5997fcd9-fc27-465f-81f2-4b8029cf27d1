package com.weifu.srm.supplier.cio.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-代供应商录入-response
 * @Version 1.0
 */
@Data
@ApiModel(value = "供应商质量问题解决-代供应商录入-response", description = "供应商质量问题解决-代供应商录入-response")
public class SupplierQualityIssueSubstituteRespDTO {

    @ApiModelProperty(value = "问题编号")
    private String issueNo;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "代替录入类型")
    private String substituteInputType;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
