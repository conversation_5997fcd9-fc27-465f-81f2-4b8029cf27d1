package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-新增request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierQualityIssueEditReqDTO", description = "供应商质量问题解决-编辑request")
@Data
public class SupplierQualityIssueEditReqDTO extends SupplierQualityIdReqDTO {

    @ApiModelProperty(value = "提交临时措施-临时措施", required = true)
    private String temporaryMeasures;

    @NotEmpty(message = "complaintProblemDescription is required.")
    @ApiModelProperty(value = "投诉信息-问题简述")
    private String complaintProblemDescription;

    @ApiModelProperty(value = "投诉信息-失效图片")
    private List<AttachmentMessageReqDTO> invalidImageList;

    @ApiModelProperty(value = "提交临时措施-4Q/8D报告")
    private List<AttachmentMessageReqDTO> temporary4q8dList;

    @ApiModelProperty(value = "投诉信息-投诉类型")
    private String complaintType;
}
