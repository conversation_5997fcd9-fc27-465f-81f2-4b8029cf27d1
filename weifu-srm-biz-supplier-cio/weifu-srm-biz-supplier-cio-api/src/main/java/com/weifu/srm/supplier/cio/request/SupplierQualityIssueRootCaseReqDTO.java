package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-根因分析验证request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-根因分析验证request", description = "供应商质量问题解决-根因分析验证request")
public class SupplierQualityIssueRootCaseReqDTO extends SupplierQualityIdReqDTO {

    @ApiModelProperty(value = "根因分析验证-是否重复发生 1：是、0：否")
    private Integer rootCauseIsRecurring;

    @NotBlank(message = "rootCauseAuditResult不能为空")
    @ApiModelProperty(value = "根因分析验证-审核结果： PASS-通过 ，FAIL-不通过",required = true)
    private String rootCauseAuditResult;

    @ApiModelProperty("根因分析验证-审核意见")
    private String rootCauseAuditOpinion;

}
