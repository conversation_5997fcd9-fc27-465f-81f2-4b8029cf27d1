package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-报表response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商质量opl任务-报表response")
public class SupplierQualityOplTaskReportRespDTO extends SupplierQualityOplTaskRespDTO {


    @ExcelProperty(value = "OPL描述", index = 11)
    @ColumnWidth(20)
    @ApiModelProperty("OPL描述")
    private String oplDescription;

    @ExcelProperty(value = "OPL反馈", index = 12)
    @ColumnWidth(20)
    @ApiModelProperty("OPL反馈")
    private String feedbackExplanation;

    @ExcelProperty(value = "审核说明", index = 13)
    @ColumnWidth(20)
    @ApiModelProperty("审核说明")
    private String auditExplanation;

    @ExcelProperty(value = "OPL关闭时间", index = 14)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("撤回-关闭时间")
    private Date endTime;


}
