package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-创建request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualityOplTaskCreateReqDTO", description = "供应商质量opl任务-创建request")
public class SupplierQualityOplTaskCreateReqDTO extends OperatorBaseReqDTO {

    @NotBlank(message = "oplName is required.")
    @ApiModelProperty("OPL名称")
    private String oplName;

    @NotNull(message = "principalId is required.")
    @ApiModelProperty("负责人id")
    private Long principalId;

    @ApiModelProperty("负责人名称")
    private String principalName;

    @NotNull(message = "predictCompleteDate is required.")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @NotBlank(message = "oplType is required.")
    @ApiModelProperty("OPL类型")
    private String oplType;

    @ApiModelProperty("OPL类型其它")
    private String oplTypeOther;

    @ApiModelProperty("单据类型")
    private String relationType;

    @ApiModelProperty("关联单据号")
    private String relationNo;

    @NotBlank(message = "oplDescription is required.")
    @ApiModelProperty("OPL描述")
    private String oplDescription;

    @ApiModelProperty(value = "OPL附件")
    private List<AttachmentMessageReqDTO> oplAnnexList;

}
