package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商任务定时任务配置-接收供应商request
 * @Version 1.0
 */
@ApiModel(description = "供应商任务定时任务配置-接收供应商request")
@Data
public class SupplierQualityTimerReceiveReqDTO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("定时任务编码")
    private String timerNo;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("供应商状态")
    private String supplierStatus;

    @ApiModelProperty("任务名称")
    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ApiModelProperty(value = "附件")
    private AttachmentMessageReqDTO annex;


}
