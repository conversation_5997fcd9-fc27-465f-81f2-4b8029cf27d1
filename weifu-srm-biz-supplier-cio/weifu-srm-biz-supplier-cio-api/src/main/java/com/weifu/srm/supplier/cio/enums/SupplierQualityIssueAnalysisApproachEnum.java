package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-分析报告枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityIssueAnalysisApproachEnum {

    ANALYSIS_APPROACH_4Q("4Q", "4Q"),

    ANALYSIS_APPROACH_8D("8D", "8D"),

    ANALYSIS_APPROACH_OTHER("OTHER", "其他");
    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityIssueAnalysisApproachEnum statusEnum : SupplierQualityIssueAnalysisApproachEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
