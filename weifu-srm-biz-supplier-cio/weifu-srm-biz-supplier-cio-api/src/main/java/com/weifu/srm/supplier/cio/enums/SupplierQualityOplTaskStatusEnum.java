package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量OPL任务-状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityOplTaskStatusEnum {
    SUBMITTED("SUBMITTED","待反馈"),
    FEEDBACK_RECEIVED("FEEDBACK_RECEIVED","已反馈"),
    APPROVE_FAIL("APPROVE_FAIL","审核未通过"),
    CLOSED("CLOSED","已关闭"),
    WITHDRAWN("WIT<PERSON>RAWN","已撤回");

    private final String code;
    private final String name;
    public static String getNameByCode(String code) {
        for (SupplierQualityOplTaskStatusEnum statusEnum : SupplierQualityOplTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
