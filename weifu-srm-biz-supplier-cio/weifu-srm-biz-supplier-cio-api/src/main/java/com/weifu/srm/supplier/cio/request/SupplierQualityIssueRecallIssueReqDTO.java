package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-撤回质量问题request
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "供应商质量问题解决-撤回质量问题request", description = "供应商质量问题解决-撤回质量问题request")
public class SupplierQualityIssueRecallIssueReqDTO extends SupplierQualityIdReqDTO {

    @ApiModelProperty(value = "撤回质量问题-撤回原因描述")
    @NotBlank(message = "recallIssueReasonDescription不能为空")
    private String recallIssueReasonDescription;

    @ApiModelProperty(value = "撤回质量问题-撤回证明附件")
    private List<AttachmentMessageReqDTO> recallIssueList;
}
