package com.weifu.srm.supplier.cio.response.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SupplierAssessmentInvoiceFullDetailRespDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 发票号 */
    @ApiModelProperty(value = "发票号",notes = "")
    private String invoiceNo ;
    /**发票归属公司*/
    @ApiModelProperty(value = "归属公司",notes = "")
    @NotNull(message = "归属公司不能为空")
    private String division;
    /**归属部门-开票人所在二级部门ID*/
    @ApiModelProperty(value = "归属部门",notes = "")
    @NotNull(message = "归属部门不能为空")
    private Long departmentId;
    /**制单人-开票人*/
    @ApiModelProperty(value = "制单人",notes = "")
    @NotNull(message = "制单人不能为空")
    private String invoiceOpenUserName;
    /** 发票种类;发票、红冲发票 */
    @ApiModelProperty(value = "发票种类",notes = "发票、红冲发票")
    private String type;
    /** 业务大类 */
    @ApiModelProperty(value = "业务大类",notes = "")
    private String bizCategory ;
    /** 业务小类 */
    @ApiModelProperty(value = "业务小类",notes = "")
    private String bizSubcategory ;
    /** 客户代码 */
    @ApiModelProperty(value = "客户代码",notes = "")
    private String customerCode ;
    /** 客户名称 */
    @ApiModelProperty(value = "客户名称",notes = "")
    private String customerName ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态",notes = "")
    private String status ;
    /** 开票总额（含税） */
    @ApiModelProperty(value = "开票总额（含税）",notes = "")
    private BigDecimal taxTotalAmt ;
    /** 税额 */
    @ApiModelProperty(value = "税额",notes = "")
    private BigDecimal taxAmt ;
    /** 是否转成本;0 否 1 是 */
    @ApiModelProperty(value = "是否转成本",notes = "")
    private String isTurnToCost ;
    /** 制单日期 */
    @ApiModelProperty(value = "制单日期",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    @NotNull(message = "制单日期不能为空")
    private Date invoiceOpenTime ;
    /** 入账日期 */
    @ApiModelProperty(value = "入账日期",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date entryTime ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    private String currency ;
    /** 汇率 */
    @ApiModelProperty(value = "汇率",notes = "")
    private BigDecimal exchangeRate ;
    /** 发票类型 */
    @ApiModelProperty(value = "发票类型",notes = "")
    private String invoiceType ;
    /** 折扣分摊类型 */
    @ApiModelProperty(value = "折扣分摊类型",notes = "")
    private String discountSharingType ;
    /** 是否进一步展示;0 否 1 是 */
    @ApiModelProperty(value = "是否进一步展示",notes = "0 否 1 是")
    private String isShowMore ;
    /** 交付邮箱 */
    @ApiModelProperty(value = "交付邮箱",notes = "")
    private String deliveryMail ;
    /** 是否为特定业务 */
    @ApiModelProperty(value = "是否为特定业务",notes = "")
    private String isSpecialBiz ;
    /** 开票联系人phone */
    @ApiModelProperty(value = "开票联系人",notes = "")
    @NotNull(message = "开票联系人不能为空")
    private String contactPhone;
    /** 开票备注 */
    @ApiModelProperty(value = "开票备注",notes = "")
    private String remark ;
    /** FSSC开票单据号 */
    @ApiModelProperty(value = "FSSC开票单据号",notes = "")
    private String fsscInvoiceNo ;
    /**SAP凭证编号*/
    @ApiModelProperty(value = "SAP凭证编号",notes = "")
    private String sapBizNo;
    /** FSSC开票失败返回备注 */
    @ApiModelProperty(value = "FSSC开票失败返回备注",notes = "")
    private String fsscRemark ;
    /** 篮字票号-红冲专属 */
    @ApiModelProperty(value = "篮字票号-红冲专属",notes = "")
    private String blueInvoiceNo ;
    /** 篮字票号对应的FSSC单据号-红冲专属 */
    @ApiModelProperty(value = "篮字票号对应的FSSC单据号-红冲专属",notes = "")
    private String blueFsscInvoiceNo ;
    /** 红冲类型-红冲专属 */
    @ApiModelProperty(value = "红冲类型-红冲专属",notes = "")
    private String redInvoiceType ;
    /** 红冲原因-红冲专属 */
    @ApiModelProperty(value = "红冲原因-红冲专属",notes = "")
    private String redReason ;
    /** 红冲通知单号-红冲专属 */
    @ApiModelProperty(value = "红冲通知单号-红冲专属",notes = "")
    private String redNoticeNo ;
    /** 开票说明-红冲专属 */
    @ApiModelProperty(value = "开票说明-红冲专属",notes = "")
    private String invoiceDesc ;

    /** 开票明细 */
    @ApiModelProperty(value = "开票说明-红冲专属",notes = "")
    private List<InvoiceDetail> invoiceDetails ;

    @Data
    public static class InvoiceDetail implements Serializable{

        /** 考核单编号 */
        @ApiModelProperty(value = "考核单编号",notes = "")
        private String assessmentNo ;
        /** 发票号 */
        @ApiModelProperty(value = "发票号",notes = "")
        private String invoiceNo ;

        @ApiModelProperty(value = "开票类型")
        private String invoiceType ;
        /** 商品 */
        @ApiModelProperty(value = "商品",notes = "")
        private String product ;
        /** 规格型号 */
        @ApiModelProperty(value = "规格型号",notes = "")
        private String itemDesc ;
        /** 计量单位 */
        @ApiModelProperty(value = "计量单位", notes = "UOM_CODE")
        private String uomCode;
        /** 数量 */
        @ApiModelProperty(value = "数量",notes = "")
        private BigDecimal quantity ;
        /** 开票总额（不含税） */
        @ApiModelProperty(value = "开票总额（不含税）",notes = "")
        private BigDecimal unExcludeTaxTotalAmt ;
        /** 开票总额（含税） */
        @ApiModelProperty(value = "开票总额（含税）",notes = "")
        private BigDecimal unIncludeTaxTotalAmt ;
        /** 单价（不含税） */
        @ApiModelProperty(value = "单价（不含税）",notes = "")
        private BigDecimal unTaxPrice ;
        /** 税码 */
        @ApiModelProperty(value = "税码",notes = "")
        private String taxCode ;
        /** 税率 */
        @ApiModelProperty(value = "税率",notes = "")
        private BigDecimal taxRate ;
        /** 税额 */
        @ApiModelProperty(value = "税额",notes = "")
        private BigDecimal taxAmt ;
        /** 交货单号 */
        @ApiModelProperty(value = "交货单号",notes = "")
        private String deliveryOrder ;
    }
}
