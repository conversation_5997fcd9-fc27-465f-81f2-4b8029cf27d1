package com.weifu.srm.supplier.cio.request.assessment;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @Date 2024/9/23 13:41
 * @Description 流转至CPE
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentCloseReqDTO implements Serializable {
    @ApiModelProperty(value = "考核单编号",notes = "")
    @NotNull(message = "assessmentNo can not be null")
    private String assessmentNo ;
    /** 关闭说明 */
    @ApiModelProperty(value = "关闭说明",notes = "")
    private String closeReason ;
    /*** 关闭说明附件 */
    @ApiModelProperty(value = "关闭说明附件",notes = "")
    private List<AttachmentMessageReqDTO> closeReasonAttachment ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;

    private Locale locale;
}
