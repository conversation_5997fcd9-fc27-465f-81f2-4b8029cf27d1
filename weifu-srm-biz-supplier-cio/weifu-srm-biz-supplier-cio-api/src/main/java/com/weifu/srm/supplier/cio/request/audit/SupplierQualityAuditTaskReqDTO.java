package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务-request
 * @Version 1.0
 */
@ApiModel(description = "供应商质量审核任务-request")
@Data
public class SupplierQualityAuditTaskReqDTO  {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("质量审核计划编号")
    private String auditPlanNo;

    @ApiModelProperty("审核计划任务编号")
    private String auditTaskNo;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ApiModelProperty("审核产品范围")
    private String auditProductRange;

    @ApiModelProperty("三级品类编码")
    private String threeCategoryCode;

    @ApiModelProperty("三级品类名称")
    private String threeCategoryName;

    @ApiModelProperty("sqe负责id")
    private Long sqeId;

    @ApiModelProperty("sqe负责名称")
    private String sqeName;

    @ApiModelProperty("业务所属事业部/子公司编码")
    private String divisionCode;

    @ApiModelProperty("业务所属事业部/子公司名称")
    private String divisionName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;
}
