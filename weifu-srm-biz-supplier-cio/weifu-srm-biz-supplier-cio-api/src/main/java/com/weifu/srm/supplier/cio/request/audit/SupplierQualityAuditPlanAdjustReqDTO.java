package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-调整request")
@Data
public class SupplierQualityAuditPlanAdjustReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id is required.")
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

    @NotEmpty(message = "taskList is required.")
    @ApiModelProperty(value = "审核计划任务列表", required = true)
    private List<SupplierQualityAuditTaskAdjustReqDTO> taskList;

    @ApiModelProperty(value = "调整说明")
    private String adjustDescription;
}
