package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核计划-审核类型大类
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityAuditPlanCategoryTypeEnum {
    INSIDE("INSIDE", "计划内"),
    OUTSIDE("OUTSIDE", "计划外");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityAuditPlanCategoryTypeEnum statusEnum : SupplierQualityAuditPlanCategoryTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
