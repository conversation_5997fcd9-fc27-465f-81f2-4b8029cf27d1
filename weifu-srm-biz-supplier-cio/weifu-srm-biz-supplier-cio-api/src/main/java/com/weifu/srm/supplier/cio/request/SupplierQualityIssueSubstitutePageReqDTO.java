package com.weifu.srm.supplier.cio.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-供应商代录入-分页查询request", description = "供应商质量问题解决-供应商代录入-分页查询request")
public class SupplierQualityIssueSubstitutePageReqDTO extends PageRequest {

    @ApiModelProperty(value = "供应商编码")
    private String supplierCodeLike;

    @ApiModelProperty(value = "供应商名称")
    private String supplierNameLike;

    @ApiModelProperty(value = "问题编号")
    private String issueNoLike;

    @ApiModelProperty(value = "操作人id")
    private Long createBy;

    @ApiModelProperty(value = "操作人名称")
    private String createName;

    @ApiModelProperty(value = "代替录入类型")
    private String substituteInputType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间-起")
    private Date createTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间-止")
    private Date createTimeEnd;
}
