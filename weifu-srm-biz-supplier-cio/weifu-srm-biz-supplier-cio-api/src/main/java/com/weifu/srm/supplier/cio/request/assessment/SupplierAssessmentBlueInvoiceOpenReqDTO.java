package com.weifu.srm.supplier.cio.request.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@Data
public class SupplierAssessmentBlueInvoiceOpenReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    @NotNull(message = "考核单编号不能为空")
    private String assessmentNo ;
    /**发票归属公司*/
    @ApiModelProperty(value = "归属公司",notes = "")
    @NotNull(message = "归属公司不能为空")
    private String division;
    /**归属部门-开票人所在二级部门ID*/
    @ApiModelProperty(value = "归属部门",notes = "")
    @NotNull(message = "归属部门不能为空")
    private Long departmentId;
    /**制单人-开票人*/
    @ApiModelProperty(value = "制单人",notes = "")
    @NotNull(message = "制单人不能为空")
    private String invoiceOpenUserName;
    /** 业务大类 */
    @ApiModelProperty(value = "业务大类",notes = "")
    @NotNull(message = "业务大类不能为空")
    private String bizCategory ;
    /** 业务小类 */
    @ApiModelProperty(value = "业务小类",notes = "")
    @NotNull(message = "业务小类不能为空")
    private String bizSubcategory ;
    /** 客户代码 */
    @ApiModelProperty(value = "客户代码",notes = "")
    @NotNull(message = "客户代码不能为空")
    private String customerCode ;
    /** 客户名称 */
    @ApiModelProperty(value = "客户名称",notes = "")
    private String customerName ;
    /** 开票总额（含税） */
    @ApiModelProperty(value = "开票总额（含税）",notes = "")
    @NotNull(message = "开票总额（含税）不能为空")
    @DecimalMin(value = "0.001",message = "开票金额必须大于0")
    private BigDecimal taxTotalAmt ;
    /** 税额 */
    @ApiModelProperty(value = "税额",notes = "")
    @NotNull(message = "税额不能为空")
    private BigDecimal taxAmt ;
    /** 是否转成本;0 否 1 是 */
    @ApiModelProperty(value = "是否转成本",notes = "")
    @NotNull(message = "是否转成本不能为空")
    private String isTurnToCost ;
    /** 制单日期 */
    @ApiModelProperty(value = "制单日期",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    @NotNull(message = "制单日期不能为空")
    private Date invoiceOpenTime ;
    /** 入账日期 */
    @ApiModelProperty(value = "入账日期",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    @NotNull(message = "入账日期不能为空")
    private Date entryTime ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    @NotNull(message = "币种不能为空")
    private String currency ;
    /** 汇率 */
    @ApiModelProperty(value = "汇率",notes = "")
    @NotNull(message = "汇率不能为空")
    private BigDecimal exchangeRate ;
    /** 发票类型 */
    @ApiModelProperty(value = "发票类型",notes = "")
    @NotNull(message = "发票类型不能为空")
    private String invoiceType ;
    /** 折扣分摊类型 */
    @ApiModelProperty(value = "折扣分摊类型",notes = "")
    @NotNull(message = "折扣分摊类型不能为空")
    private String discountSharingType ;
    /** 是否进一步展示;0 否 1 是 */
    @ApiModelProperty(value = "是否进一步展示",notes = "")
    @NotNull(message = "是否进一步展示不能为空")
    private String isShowMore ;
    /** 交付邮箱 */
    @ApiModelProperty(value = "交付邮箱",notes = "")
    private String deliveryMail ;
    /** 是否为特定业务 */
    @ApiModelProperty(value = "是否为特定业务",notes = "")
    @NotNull(message = "是否为特定业务不能为空")
    private String isSpecialBiz ;
    /** 开票联系人phone */
    @ApiModelProperty(value = "开票联系人",notes = "")
    @NotNull(message = "开票联系人不能为空")
    private String contactPhone;
    /** 开票备注 */
    @ApiModelProperty(value = "开票备注",notes = "")
    private String remark ;
    @Valid
    @ApiModelProperty(value = "开票明细")
    @Size(min = 1)
    private List<SupplierAssessmentInvoiceDetailReqDTO> detailList;
    /**操作人*/
    private Long userId;
    /**操作人*/
    private String userName;

    private Locale locale;

    @Data
    public static class SupplierAssessmentInvoiceDetailReqDTO implements Serializable {

        /** 商品 */
        @ApiModelProperty(value = "商品",notes = "")
        @NotNull(message = "商品不能为空")
        private String product ;
        /** 规格型号 */
        @ApiModelProperty(value = "规格型号",notes = "")
        private String itemDesc ;
        /** 计量单位 */
        @ApiModelProperty(value = "计量单位", notes = "UOM_CODE")
        private String uomCode;
        /** 数量 */
        @ApiModelProperty(value = "数量",notes = "")
        @NotNull(message = "数量不能为空")
        @Min(value = 1,message = "数量不能小于1")
        private BigDecimal quantity ;
        /** 开票总额（不含税） */
        @ApiModelProperty(value = "开票总额（不含税）",notes = "")
        @NotNull(message = "开票总额（不含税）不能为空")
        @DecimalMin(value = "0.001",message = "开票金额必须大于0")
        private BigDecimal unExcludeTaxTotalAmt ;
        /** 开票总额（含税） */
        @ApiModelProperty(value = "开票总额（含税）",notes = "")
        @NotNull(message = "开票总额（含税）不能为空")
        @DecimalMin(value = "0.001",message = "开票金额必须大于0")
        private BigDecimal unIncludeTaxTotalAmt ;
        /** 单价（不含税） */
        @ApiModelProperty(value = "单价（不含税）",notes = "")
        @NotNull(message = "单价（不含税）不能为空")
        private BigDecimal unTaxPrice ;
        /** 税码 */
        @ApiModelProperty(value = "税码",notes = "")
        @NotNull(message = "税码不能为空")
        private String taxCode ;
        /** 税率 */
        @ApiModelProperty(value = "税率",notes = "")
        @NotNull(message = "税率不能为空")
        private BigDecimal taxRate ;
        /** 税额 */
        @ApiModelProperty(value = "税额",notes = "")
        @NotNull(message = "税额不能为空")
        private BigDecimal taxAmt ;
        /** 交货单号 */
        @ApiModelProperty(value = "交货单号",notes = "")
        private String deliveryOrder ;

    }
}
