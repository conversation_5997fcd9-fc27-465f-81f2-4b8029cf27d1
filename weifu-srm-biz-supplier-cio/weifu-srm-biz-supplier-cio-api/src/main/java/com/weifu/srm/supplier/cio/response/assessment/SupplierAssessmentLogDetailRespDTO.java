package com.weifu.srm.supplier.cio.response.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/23 16:16
 * @Description 考核单历史记录
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentLogDetailRespDTO implements Serializable {
    /** 考核单历史记录Id */
    @ApiModelProperty(value = "考核单历史记录Id",notes = "")
    private Long id;
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 供应商编码 */
    @ApiModelProperty(value = "供应商编码",notes = "")
    private String supplierCode ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName ;
    /** 供应商名称-EN */
    @ApiModelProperty(value = "供应商名称-EN",notes = "")
    private String supplierNameEn ;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称",notes = "")
    private String supplierShortName ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态",notes = "")
    private String invoiceStatus ;
    /** 开票状态描述 */
    @ApiModelProperty(value = "开票状态描述",notes = "")
    private String invoiceStatusDesc ;
    /** 红冲开票状态 */
    @ApiModelProperty(value = "红冲开票状态",notes = "")
    private String verificationInvoiceStatus ;
    /** 公司代码 */
    @ApiModelProperty(value = "公司代码",notes = "")
    private String division ;
    /** 考核金额（含税） */
    @ApiModelProperty(value = "考核金额（含税）",notes = "")
    private BigDecimal assessmentTaxAmt ;
    /** 品类编码 */
    @ApiModelProperty(value = "品类编码",notes = "")
    private String categoryCode ;
    /** 品类名称 */
    @ApiModelProperty(value = "品类名称",notes = "")
    private String categoryName ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    private String currency ;
    /** 确认限制天数 */
    @ApiModelProperty(value = "确认限制天数",notes = "")
    private Integer limitDays ;
    /** 到期日 */
    @ApiModelProperty(value = "到期日",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date deadlineTime ;
    /** 质量负责人ID */
    @ApiModelProperty(value = "质量负责人ID",notes = "")
    private Long sqeId ;
    /** 质量负责人名称 */
    @ApiModelProperty(value = "质量负责人名称",notes = "")
    private String sqeName ;
    /** 质量组长ID */
    @ApiModelProperty(value = "质量组长ID",notes = "")
    private Long sqeMasterId ;
    /** 质量组长名称 */
    @ApiModelProperty(value = "质量组长名称",notes = "")
    private String sqeMasterName ;
    /** 质量处长ID */
    @ApiModelProperty(value = "质量处长ID",notes = "")
    private Long sqeDirectorId ;
    /** 质量处长名称 */
    @ApiModelProperty(value = "质量处长名称",notes = "")
    private String sqeDirectorName ;
    /** 品类工程师ID */
    @ApiModelProperty(value = "品类工程师ID",notes = "")
    private Long cpeId ;
    /** 品类工程师名称 */
    @ApiModelProperty(value = "品类工程师名称",notes = "")
    private String cpeName ;
    /** 品类组长ID */
    @ApiModelProperty(value = "品类组长ID",notes = "")
    private Long cpeMasterId ;
    /** 品类组长名称 */
    @ApiModelProperty(value = "品类组长名称",notes = "")
    private String cpeMasterName ;
    /** 品类处长ID */
    @ApiModelProperty(value = "品类处长ID",notes = "")
    private Long cpeDirectorId ;
    /** 品类处长名称 */
    @ApiModelProperty(value = "品类处长名称",notes = "")
    private String cpeDirectorName ;
    /** 内部说明 */
    @ApiModelProperty(value = "内部说明",notes = "")
    private String innerDesc ;
    /** 考核说明 */
    @ApiModelProperty(value = "考核说明",notes = "")
    private String assessmentDesc ;
    /** 供应商反馈时间 */
    @ApiModelProperty(value = "供应商反馈时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date feedbackTime ;
    /** 供应商反馈状态;0，不接受，1 接受 */
    @ApiModelProperty(value = "供应商反馈状态",notes = "0，不接受，1 接受")
    private Integer feedbackStatus ;
    /** 申诉理由 */
    @ApiModelProperty(value = "申诉理由",notes = "")
    private String appealReason ;
    /** 考核提交时间 */
    @ApiModelProperty(value = "考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date assessmentSubmitTime ;
    /** 强制考核提交时间 */
    @ApiModelProperty(value = "强制考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date forceAssessmentSubmitTime ;
    /** 强制考核标识 */
    @ApiModelProperty(value = "强制考核标识",notes = "")
    private Integer forceAssessmentFlag ;
    /** 强制考核说明 */
    @ApiModelProperty(value = "强制考核说明",notes = "")
    private String forceAssessmentDesc ;
    /** 索赔材料说明 */
    @ApiModelProperty(value = "索赔材料说明",notes = "")
    private String claimDocDesc ;
}
