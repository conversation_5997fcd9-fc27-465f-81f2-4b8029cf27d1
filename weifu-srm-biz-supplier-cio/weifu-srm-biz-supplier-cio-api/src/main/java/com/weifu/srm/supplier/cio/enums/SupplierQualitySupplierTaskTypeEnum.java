package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核供应商任务-任务类型枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualitySupplierTaskTypeEnum {
    SUPPLIER_PRODUCT_SELF_CHECK("SUPPLIER_PRODUCT_SELF_CHECK", "供应商产品自审"),
    SUPPLIER_PROCESS_SELF_CHECK("SUPPLIER_PROCESS_SELF_CHECK", "供应商过程自审"),
    SUPPLIER_TYPE_TEST("SUPPLIER_TYPE_TEST", "供应商型式试验"),
    TOPIC_IMPROVEMENT_TASK("TOPIC_IMPROVEMENT_TASK", "主题改进任务"),
    CPK_REPORT("CPK_REPORT", "CPK报告"),
    LEVEL_ONE_TWO_CONTROLLED_SHIPPMENT("LEVEL_ONE_TWO_CONTROLLED_SHIPPMENT", "一/二级受控发运"),
    LESSONS_LEARNED_EXPERIENCE_SHAREING("LESSONS_LEARNED_EXPERIENCE_SHAREING", "LESSONS LEARNED经验分享"),
    OTHER("OTHER", "其他");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualitySupplierTaskTypeEnum statusEnum : SupplierQualitySupplierTaskTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }


}
