package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-id request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "id request", description = "id request")
public class SupplierQualityIdReqDTO extends OperatorBaseReqDTO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty("主键")
    private Long id;

}
