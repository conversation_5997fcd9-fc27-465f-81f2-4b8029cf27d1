package com.weifu.srm.supplier.cio.request.assessment;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/14 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentForceReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 强制考核说明 */
    @ApiModelProperty(value = "强制考核说明",notes = "")
    private String forceAssessmentDesc ;
    /** 反馈供应商说明 */
    @ApiModelProperty(value = "反馈供应商说明",notes = "")
    private String feedBackSupplierDesc ;
    /*** 强制考核说明附件 */
    @ApiModelProperty(value = "强制考核说明附件",notes = "")
    private List<AttachmentMessageReqDTO> forceAssessmentAttachment ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;

}
