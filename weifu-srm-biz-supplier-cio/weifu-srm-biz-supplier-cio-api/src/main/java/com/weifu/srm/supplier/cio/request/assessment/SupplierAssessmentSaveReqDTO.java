package com.weifu.srm.supplier.cio.request.assessment;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/14 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentSaveReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 供应商编码 */
    @ApiModelProperty(value = "供应商编码",notes = "")
    @NotNull(message = "supplierCode can not be null")
    private String supplierCode ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    @NotNull(message = "supplierName can not be null")
    private String supplierName ;
    /** 供应商名称-EN */
    @ApiModelProperty(value = "供应商名称-EN",notes = "")
    @NotNull(message = "supplierNameEn can not be null")
    private String supplierNameEn ;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称",notes = "")
    private String supplierShortName ;
    /** 状态 */
    @ApiModelProperty(value = "状态",notes = "")
    private String status ;
    /** 公司代码 */
    @ApiModelProperty(value = "公司代码",notes = "")
    @NotNull(message = "division can not be null")
    private String division ;
    /** 考核金额（含税） */
    @ApiModelProperty(value = "考核金额（含税）",notes = "")
    @NotNull(message = "assessmentTaxAmt can not be null")
    @DecimalMin(value = "0", message = "amount must be greater than or equal to 0")
    @DecimalMax(value = "99999999.99", message = "amount must be greater than or equal to 99999999.99")
    private BigDecimal assessmentTaxAmt ;
    /** 品类编码 */
    @ApiModelProperty(value = "品类编码",notes = "")
    @NotNull(message = "categoryCode can not be null")
    private String categoryCode ;
    /** 品类名称 */
    @ApiModelProperty(value = "品类名称",notes = "")
    private String categoryName ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    private String currency ;
    /** 确认限制天数 */
    @ApiModelProperty(value = "确认限制天数",notes = "")
    @NotNull(message = "limitDays can not be null")
    @Digits(message = "limitDays must be a valid number", integer = 4, fraction = 0)
    private Integer limitDays ;
    /** 质量负责人ID */
    @ApiModelProperty(value = "质量负责人ID",notes = "")
    @NotNull(message = "质量负责人ID 不能为空")
    private Long sqeId ;
    /** 质量负责人名称 */
    @ApiModelProperty(value = "质量负责人名称",notes = "")
    @NotNull(message = "质量负责人名称 不能为空")
    private String sqeName ;
    /** 质量组长ID */
    @ApiModelProperty(value = "质量组长ID",notes = "")
    @NotNull(message = "质量组长ID 不能为空")
    private Long sqeMasterId ;
    /** 质量组长名称 */
    @ApiModelProperty(value = "质量组长名称",notes = "")
    @NotNull(message = "质量组长名称 不能为空")
    private String sqeMasterName ;
    /** 质量处长ID */
    @ApiModelProperty(value = "质量处长ID",notes = "")
    @NotNull(message = "质量处长ID 不能为空")
    private Long sqeDirectorId ;
    /** 质量处长名称 */
    @ApiModelProperty(value = "质量处长名称",notes = "")
    @NotNull(message = "质量处长名称 不能为空")
    private String sqeDirectorName ;
    /** 品类工程师ID */
    @ApiModelProperty(value = "品类工程师ID",notes = "")
    private Long cpeId ;
    /** 品类工程师名称 */
    @ApiModelProperty(value = "品类工程师名称",notes = "")
    private String cpeName ;
    /** 品类组长ID */
    @ApiModelProperty(value = "品类组长ID",notes = "")
    private Long cpeMasterId ;
    /** 品类组长名称 */
    @ApiModelProperty(value = "品类组长名称",notes = "")
    private String cpeMasterName ;
    /** 品类处长ID */
    @ApiModelProperty(value = "品类处长ID",notes = "")
    private Long cpeDirectorId ;
    /** 品类处长名称 */
    @ApiModelProperty(value = "品类处长名称",notes = "")
    private String cpeDirectorName ;
    /** 内部说明 */
    @ApiModelProperty(value = "内部说明",notes = "")
    private String innerDesc ;
    /** 考核说明 */
    @ApiModelProperty(value = "考核说明",notes = "")
    private String assessmentDesc ;
    /*** 内部说明附件 */
    @ApiModelProperty(value = "内部说明附件",notes = "")
    private List<AttachmentMessageReqDTO> innerAttachment ;
    /*** 内部说明附件 */
    @ApiModelProperty(value = "考核说明附件",notes = "")
    private List<AttachmentMessageReqDTO> assessmentAttachment ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;

}
