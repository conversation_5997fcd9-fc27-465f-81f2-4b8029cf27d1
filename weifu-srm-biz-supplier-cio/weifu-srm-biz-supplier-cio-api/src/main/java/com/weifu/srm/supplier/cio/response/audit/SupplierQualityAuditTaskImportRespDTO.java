package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 质量审核任务-导入response
 * @Version 1.0
 */
@Data
@ApiModel(description = "质量审核任务-导入response")
public class SupplierQualityAuditTaskImportRespDTO {

    @ExcelProperty("供应商编码*")
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ExcelProperty("供应商简称")
    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ExcelProperty("供应商名称")
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty("审核产品系列*")
    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ExcelProperty("审核产品范围*")
    @ApiModelProperty("审核产品范围")
    private String auditProductRange;

    @ExcelProperty("业务所属名称")
    @ApiModelProperty("业务所属")
    private String divisionName;

    @ExcelProperty("业务所属*")
    @ApiModelProperty("业务所属编码")
    private String divisionCode;

    @ExcelProperty("计划完成日期*")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("计划完成日期")
    private String predictCompleteDate;
    @ExcelIgnore
    @ApiModelProperty("是否成功")
    private Boolean success;

}
