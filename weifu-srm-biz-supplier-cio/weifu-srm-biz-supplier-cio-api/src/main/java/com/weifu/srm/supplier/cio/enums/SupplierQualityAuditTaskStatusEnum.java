package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核计划-审核任务状态枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityAuditTaskStatusEnum {
    NOT_STARTED("NOT_STARTED","未开始"),
    IN_PROGRESS("IN_PROGRESS","进行中"),
    ADJUSTING("ADJUSTING","调整中"),
    SUBMITTED_RESULT("SUBMITTED_RESULT","已提交结论"),
    APPROVE_FAIL("APPROVE_FAIL","审核不通过"),
    ENDED("ENDED","已结束"),
    CANCELLED("CANCELLED","已取消");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityAuditTaskStatusEnum statusEnum : SupplierQualityAuditTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }

}
