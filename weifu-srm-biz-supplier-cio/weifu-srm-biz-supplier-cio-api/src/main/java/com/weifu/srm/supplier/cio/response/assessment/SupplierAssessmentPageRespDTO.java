package com.weifu.srm.supplier.cio.response.assessment;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/13 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentPageRespDTO implements Serializable {
    /** 考核单编号 */
    @ExcelProperty(value = "考核单编号",index = 0)
    @ColumnWidth(20)
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 供应商编码 */
    @ApiModelProperty(value = "供应商编码",notes = "")
    @ExcelProperty(value = "供应商编码",index = 1)
    @ColumnWidth(20)
    private String supplierCode ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    @ExcelProperty(value = "供应商名称",index = 2)
    @ColumnWidth(20)
    private String supplierName ;
    /** 供应商名称-EN */
    @ApiModelProperty(value = "供应商名称-EN",notes = "")
    @ExcelIgnore
    private String supplierNameEn ;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称",notes = "")
    @ExcelProperty(value = "供应商简称",index = 3)
    @ColumnWidth(20)
    private String supplierShortName ;
    /** 状态 */
    @ApiModelProperty(value = "状态",notes = "")
    @ExcelIgnore
    private String status ;
    /** 状态 */
    @ApiModelProperty(value = "状态描述",notes = "")
    @ExcelProperty(value = "考核单状态",index = 4)
    @ColumnWidth(20)
    private String statusDesc ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态",notes = "")
    @ExcelIgnore
    private String invoiceStatus ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态描述",notes = "")
    @ExcelProperty(value = "开票状态",index = 5)
    @ColumnWidth(20)
    private String invoiceStatusDesc ;
    /** 红冲开票状态 */
    @ApiModelProperty(value = "红冲开票状态",notes = "")
    @ExcelIgnore
    private String eliminationInvoiceStatus ;
    /** 红冲开票状态 */
    @ApiModelProperty(value = "红冲开票状态描述",notes = "")
    @ExcelIgnore
    private String eliminationInvoiceStatusDesc ;
    /** 公司代码 */
    @ApiModelProperty(value = "公司代码",notes = "")
    @ExcelProperty(value = "公司代码",index = 6)
    @ColumnWidth(20)
    private String division ;
    /** 考核金额（含税） */
    @ApiModelProperty(value = "考核金额（含税）",notes = "")
    @ExcelProperty(value = "考核金额（含税）",index = 7)
    @ColumnWidth(20)
    private BigDecimal assessmentTaxAmt ;
    /** 品类编码 */
    @ApiModelProperty(value = "品类编码",notes = "")
    @ExcelProperty(value = "三级品类编码",index = 8)
    @ColumnWidth(20)
    private String categoryCode ;
    /** 品类名称 */
    @ApiModelProperty(value = "品类名称",notes = "")
    @ExcelProperty(value = "三级品类",index = 9)
    @ColumnWidth(20)
    private String categoryName ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    @ExcelIgnore
    private String currency ;
    /** 确认限制天数 */
    @ApiModelProperty(value = "确认限制天数",notes = "")
    @ExcelIgnore
    private Integer limitDays ;
    /** 到期日 */
    @ApiModelProperty(value = "到期日",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date deadlineTime ;
    /** 质量负责人ID */
    @ApiModelProperty(value = "质量负责人ID",notes = "")
    @ExcelIgnore
    private Long sqeId ;
    /** 质量负责人名称 */
    @ApiModelProperty(value = "质量负责人名称",notes = "")
    @ExcelProperty(value = "负责SQE",index = 10)
    @ColumnWidth(20)
    private String sqeName ;
    /** 质量组长ID */
    @ApiModelProperty(value = "质量组长ID",notes = "")
    @ExcelIgnore
    private Long sqeMasterId ;
    /** 质量组长名称 */
    @ApiModelProperty(value = "质量组长名称",notes = "")
    @ExcelIgnore
    private String sqeMasterName ;
    /** 质量处长ID */
    @ApiModelProperty(value = "质量处长ID",notes = "")
    @ExcelIgnore
    private Long sqeDirectorId ;
    /** 质量处长名称 */
    @ApiModelProperty(value = "质量处长名称",notes = "")
    @ExcelIgnore
    private String sqeDirectorName ;
    /** 品类工程师ID */
    @ApiModelProperty(value = "品类工程师ID",notes = "")
    @ExcelIgnore
    private Long cpeId ;
    /** 品类工程师名称 */
    @ApiModelProperty(value = "品类工程师名称",notes = "")
    @ExcelProperty(value = "负责CPE",index = 11)
    @ColumnWidth(20)
    private String cpeName ;
    /** 品类组长ID */
    @ApiModelProperty(value = "品类组长ID",notes = "")
    @ExcelIgnore
    private Long cpeMasterId ;
    /** 品类组长名称 */
    @ApiModelProperty(value = "品类组长名称",notes = "")
    @ExcelIgnore
    private String cpeMasterName ;
    /** 品类处长ID */
    @ApiModelProperty(value = "品类处长ID",notes = "")
    @ExcelIgnore
    private Long cpeDirectorId ;
    /** 品类处长名称 */
    @ApiModelProperty(value = "品类处长名称",notes = "")
    @ExcelIgnore
    private String cpeDirectorName ;
    /** 内部说明 */
    @ApiModelProperty(value = "内部说明",notes = "")
    @ExcelIgnore
    private String innerDesc ;
    /** 考核说明 */
    @ApiModelProperty(value = "考核说明",notes = "")
    @ExcelIgnore
    private String assessmentDesc ;
    /** 供应商反馈时间 */
    @ApiModelProperty(value = "供应商反馈时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date feedbackTime ;
    /** 供应商反馈状态;0，不接受，1 接受 */
    @ApiModelProperty(value = "供应商反馈状态",notes = "0，不接受，1 接受")
    @ExcelIgnore
    private Integer feedbackStatus ;
    /** 申诉理由 */
    @ApiModelProperty(value = "申诉理由",notes = "")
    @ExcelIgnore
    private String appealReason ;
    /** 考核提交时间 */
    @ApiModelProperty(value = "考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date assessmentSubmitTime ;
    /** 强制考核提交时间 */
    @ApiModelProperty(value = "强制考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date forceAssessmentSubmitTime ;
    /** 强制考核标识 */
    @ApiModelProperty(value = "强制考核标识",notes = "")
    @ExcelIgnore
    private Integer forceAssessmentFlag ;
    /** 强制考核说明 */
    @ApiModelProperty(value = "强制考核说明",notes = "")
    @ExcelIgnore
    private String forceAssessmentDesc ;
    /** 索赔材料说明 */
    @ApiModelProperty(value = "索赔材料说明",notes = "")
    @ExcelIgnore
    private String claimDocDesc ;
    @ApiModelProperty(value = "索赔材料上传人ID",notes = "")
    @ExcelIgnore
    private Long claimDocUploadBy;
    @ApiModelProperty(value = "索赔材料上传人名称",notes = "")
    @ExcelIgnore
    private String claimDocUploadByName;
    @ApiModelProperty(value = "索赔材料上传时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date claimDocUploadTime ;
    /** cpe退回说明 */
    @ApiModelProperty(value = "cpe退回说明",notes = "")
    @ExcelIgnore
    private String cpeReturnDesc ;
    /** cpe退回时间 */
    @ApiModelProperty(value = "cpe退回时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date cpeReturnTime ;
    /** 关闭时间 */
    @ApiModelProperty(value = "关闭时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelIgnore
    private Date closeTime ;
    /** 关闭说明 */
    @ApiModelProperty(value = "关闭说明",notes = "")
    @ExcelIgnore
    private String closeReason ;
    /** 当前操作人 */
    @ApiModelProperty(value = "当前操作人",notes = "")
    @ExcelIgnore
    private Long currentOperationBy ;
    /** 当前操作人名称 */
    @ApiModelProperty(value = "当前操作人名称",notes = "")
    @ExcelIgnore
    private String currentOperationByName ;
    @ApiModelProperty(value = "创建人ID",notes = "")
    @ExcelIgnore
    private Long createBy;
    @ApiModelProperty(value = "创建人名称",notes = "")
    @ExcelIgnore
    private String createName;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ExcelProperty(value = "创建时间",index = 12)
    @ColumnWidth(20)
    private Date createTime;
}
