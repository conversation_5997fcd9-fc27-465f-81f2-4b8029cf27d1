package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 质量审核任务-response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "质量审核任务-response")
public class SupplierQualityAuditTaskRespDTO extends SupplierQualityAuditTaskExcelRespDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("审核结论")
    private String auditConclusion;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核结果备注")
    private String auditRemark;

    @ApiModelProperty("创建任务：NO不需要创建，OPL创建OPL任务，SUPPLIER供应商任务")
    private String createTask;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人姓名")
    private String createName;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageRespDTO> annexList;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "sqe负责id")
    private Long sqeId;

    @ApiModelProperty(value = "三级品类编码")
    private String threeCategoryCode;

    @ApiModelProperty(value = "业务所属事业部-子公司编码")
    private String divisionCode;

    @ApiModelProperty(value = "审核类型大类")
    private String auditCategoryType;

    @ApiModelProperty(value = "审核类型小类")
    private String auditSubclassType;

    @ApiModelProperty(value = "sqe组长id")
    private Long sqeMasterId;

    @ApiModelProperty(value = "sqe组长名称")
    private String sqeMasterName;


}
