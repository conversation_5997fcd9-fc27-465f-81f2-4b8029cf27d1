package com.weifu.srm.supplier.cio.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 分页权限-request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "分页权限-request")
@Data
public class PagePermissionReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty(hidden = true)
    private List<String> categoryCodePermissionList;

    @ApiModelProperty(hidden = true)
    private List<String> divisionIdPermissionList;

    @ApiModelProperty(hidden = true)
    private Long operationBy;

    @ApiModelProperty(hidden = true)
    private String permissionType;
}
