package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商子任务-报表response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商审核供应商任务-报表response")
public class SupplierQualitySupplierTaskReportRespDTO {

    @ExcelIgnore
    @ApiModelProperty("主键")
    private Long id;
    @ExcelIgnore
    @ApiModelProperty("任务编号")
    private String taskNo;

    @ExcelProperty(value = "任务编号", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("任务编号")
    private String subTaskNo;

    @ExcelProperty(value = "任务名称", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty("任务名称")
    private String taskName;

    @ExcelProperty(value = "威孚负责人", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("威孚负责人名称")
    private String createName;

    @ExcelProperty(value = "供应商", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("供应商")
    private String supplierName;

    @ExcelProperty(value = "任务类型", index = 4)
    @ColumnWidth(30)
    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    @ExcelProperty(value = "任务状态", index = 5)
    @ColumnWidth(20)
    @ApiModelProperty("任务状态")
    private String statusName;

    @ExcelProperty(value = "计划完成日期", index = 6)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ExcelProperty(value = "创建时间", index = 7)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelProperty(value = "任务关闭时间", index = 8)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("任务关闭时间")
    private Date endTime;

    @ExcelProperty(value = "任务描述", index = 9)
    @ColumnWidth(30)
    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ExcelProperty(value = "反馈时间", index = 10)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("供应商反馈时间")
    private Date feedbackTime;

    @ExcelProperty(value = "反馈说明", index = 11)
    @ColumnWidth(30)
    @ApiModelProperty("反馈说明")
    private String feedbackExplanation;

    @ExcelProperty(value = "审核说明", index = 12)
    @ColumnWidth(20)
    @ApiModelProperty("审核说明")
    private String auditExplanation;

    @ExcelIgnore
    @ApiModelProperty("任务状态")
    private String status;

    @ExcelIgnore
    @ApiModelProperty("任务类型")
    private String taskType;

    @ExcelIgnore
    @ApiModelProperty("任务类型其它")
    private String taskTypeOther;

}
