package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量OPL任务-类型枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityOplTaskTypeEnum {
    ADMISSION("ADMISSION","供应商准入"),
    PERFORMANCE("PERFORMANCE","供应商绩效"),
    QUALITY_IMPROVEMENT("QUALITY_IMPROVEMENT","质量整改"),
    PPAP_BATCH_RELEASE("PPAP_BATCH_RELEASE","PPAP批产放行"),
    ENGINEERING_CHANGE("ENGINEERING_CHANGE","工程变更"),
    OTHER("OTHER","其他");

    private final String code;
    private final String name;
    public static String getNameByCode(String code) {
        for (SupplierQualityOplTaskTypeEnum statusEnum : SupplierQualityOplTaskTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
