package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核计划-审核结论
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityAuditTaskConclusionEnum {
    CONCLUSION_A("CONCLUSION_A", "A"),
    CONCLUSION_B("CONCLUSION_B", "B"),
    CONCLUSION_C("CONCLUSION_C", "C"),
    QUALIFIED("QUALIFIED", "合格"),
    UNQUALIFIED("UNQUALIFIED", "不合格"),
    PASS("PASS", "通过"),
    NO_PASS("NO_PASS", "不通过"),
    RED("RED", "红"),
    YELLOW("YELLOW", "黄"),
    GREEN("GRE<PERSON>", "绿");


    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityAuditTaskConclusionEnum statusEnum : SupplierQualityAuditTaskConclusionEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }

}
