package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-保存request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-保存request")
@Data
public class SupplierQualityAuditPlanSaveReqDTO extends OperatorBaseReqDTO {

    @ApiModelProperty(value = "是否提交")
    private Boolean isSubmit;

    @ApiModelProperty("审核计划编号")
    private String auditPlanNo;

    @NotBlank(message = "auditCategoryType is required.")
    @ApiModelProperty(value = "审核类型大类", required = true)
    private String auditCategoryType;

    @NotBlank(message = "auditSubclassType is required.")
    @ApiModelProperty("审核类型小类")
    private String auditSubclassType;

    @ApiModelProperty("审核类型小类-其他")
    private String auditSubclassOther;

    @NotBlank(message = "firstCategoryCode is required.")
    @ApiModelProperty("一级品类编码")
    private String firstCategoryCode;

    @ApiModelProperty("一级品类名称")
    private String firstCategoryName;

    @NotNull(message = "sqeMasterId is required.")
    @ApiModelProperty("sqe组长id")
    private Long sqeMasterId;

    @ApiModelProperty("sqe组长名称")
    private String sqeMasterName;

    @NotNull(message = "auditManagerId is required.")
    @ApiModelProperty("审批处长|经理id")
    private Long auditManagerId;

    @ApiModelProperty("审批处长|经理名称")
    private String auditManagerName;

    @ApiModelProperty("审核计划备注")
    private String auditPlanRemark;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核结果：PASS通过，FAIL不通过")
    private String auditResult;

    @ApiModelProperty("审批意见")
    private String auditOpinion;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @NotEmpty(message = "taskList is required.")
    @ApiModelProperty(value = "审核计划任务列表", required = true)
    private List<SupplierQualityAuditTaskReqDTO> taskList;


}
