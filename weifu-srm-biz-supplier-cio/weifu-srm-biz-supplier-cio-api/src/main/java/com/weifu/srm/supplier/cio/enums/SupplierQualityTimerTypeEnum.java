package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商定时任务-定时类型枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualityTimerTypeEnum {
    WEEK("WEEK", "周度"),
    MONTH("MONTH", "月度"),
    QUARTER("QUARTER", "季度");

    private final String code;
    private final String name;

    public static String getNameByCode(String code) {
        for (SupplierQualityTimerTypeEnum statusEnum : SupplierQualityTimerTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
