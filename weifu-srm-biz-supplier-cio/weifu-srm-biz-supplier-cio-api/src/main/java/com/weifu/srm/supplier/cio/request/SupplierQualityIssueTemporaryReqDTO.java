package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-提交临时措施
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-提交临时措施request", description = "供应商质量问题解决-提交临时措施request")
public class SupplierQualityIssueTemporaryReqDTO extends SupplierQualityIdReqDTO {

    @NotBlank(message = "temporaryMeasures不能为空")
    @ApiModelProperty(value = "提交临时措施-临时措施", required = true)
    private String temporaryMeasures;

    @NotNull(message = "temporaryIsUpgrade不能为空")
    @ApiModelProperty(value = "提交临时措施-是否升级 1：是、0：否", required = true)
    private Boolean temporaryIsUpgrade;

    @ApiModelProperty(value = "负责的sqe的id")
    private Long sqeId;

    @ApiModelProperty(value = "负责的sqe名称")
    private String sqeName;

    @NotEmpty(message = "temporary4q8dList不能为空")
    @ApiModelProperty(value = "提交临时措施-4Q/8D报告")
    private List<AttachmentMessageReqDTO> temporary4q8dList;
}
