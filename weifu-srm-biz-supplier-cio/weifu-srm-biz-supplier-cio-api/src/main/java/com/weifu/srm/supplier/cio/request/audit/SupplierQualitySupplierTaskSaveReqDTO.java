package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商任务-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商审核供应商任务-创建request")
@Data
public class SupplierQualitySupplierTaskSaveReqDTO extends OperatorBaseReqDTO {

    @NotBlank(message = "taskName is required.")
    @ApiModelProperty("任务名称")
    private String taskName;

    @NotBlank(message = "taskType is required.")
    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("任务类型其它")
    private String taskTypeOther;

    @NotNull(message = "predictCompleteDate is required.")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @NotBlank(message = "supplierContactType is required.")
    @ApiModelProperty("供应商联系人类型")
    private String supplierContactType;

    @ApiModelProperty("附加通知人")
    private String additionalNotifier;

    @ApiModelProperty("单据类型")
    private String relationType;

    @ApiModelProperty("关联单据号")
    private String relationNo;

    @NotBlank(message = "taskDescription is required.")
    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageReqDTO> annexList;

    @ApiModelProperty(value = "接收供应商")
    private List<SupplierQualitySupplierTaskSubReqDTO> subTaskList;


}
