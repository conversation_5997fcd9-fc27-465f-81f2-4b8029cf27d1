package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-详情response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商质量opl任务-详情response")
public class SupplierQualityOplTaskDetailRespDTO extends SupplierQualityOplTaskRespDTO {

    @ApiModelProperty("OPL描述")
    private String oplDescription;

    @ApiModelProperty("反馈人id")
    private Long feedbackUserId;

    @ApiModelProperty("反馈人名称")
    private String feedbackUserName;

    @ApiModelProperty("OPL反馈")
    private String feedbackExplanation;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("反馈时间")
    private Date feedbackTime;

    @ApiModelProperty("撤回-关闭人id")
    private Long endUserId;

    @ApiModelProperty("撤回-关闭人名称")
    private String endUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("撤回-关闭时间")
    private Date endTime;

    @ApiModelProperty("审核结果：CLOSE关闭, FAIL审核不通过")
    private String auditResult;

    @ApiModelProperty("上一次的审核说明")
    private String lastAuditExplanation;

    @ApiModelProperty("审核说明")
    private String auditExplanation;

    @ApiModelProperty("更新人id")
    private Long updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更新人姓名")
    private String updateName;

    @ApiModelProperty(value = "OPL附件")
    private List<AttachmentMessageRespDTO> oplAnnexList;

    @ApiModelProperty(value = "OPL反馈附件")
    private List<AttachmentMessageRespDTO> oplFeedbackAnnexList;

    @ApiModelProperty(value = "OPL审核反馈附件")
    private List<AttachmentMessageRespDTO> oplAuditAnnexList;
}
