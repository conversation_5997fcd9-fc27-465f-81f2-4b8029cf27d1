package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核子任务-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商审核子任务-request")
public class SupplierQualitySupplierTaskSubReqDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("任务编码")
    private String taskNo;

    @ApiModelProperty("子任务编码")
    private String subTaskNo;

    @NotBlank(message = "supplierCode is required.")
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @NotBlank(message = "supplierShortName is required.")
    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("任务名称")
    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ApiModelProperty(value = "附件")
    private AttachmentMessageReqDTO annex;

}
