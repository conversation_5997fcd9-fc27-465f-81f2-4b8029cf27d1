package com.weifu.srm.supplier.cio.request.audit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务-调整request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核任务-调整request")
@Data
public class SupplierQualityAuditTaskAdjustReqDTO extends SupplierQualityAuditTaskReqDTO {

    @NotNull(message = "id is required.")
    @ApiModelProperty(value = "主键", required = true)
    private Long id;

}
