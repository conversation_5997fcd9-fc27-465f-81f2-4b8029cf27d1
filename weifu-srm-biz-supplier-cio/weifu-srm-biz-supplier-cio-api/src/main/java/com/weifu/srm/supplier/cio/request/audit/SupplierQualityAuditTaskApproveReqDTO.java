package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务-审核结果request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核任务-审核结果request")
@Data
public class SupplierQualityAuditTaskApproveReqDTO extends OperatorBaseReqDTO {
    @NotNull(message = "id is required.")
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("审核结果：PASS审核通过，FAIL审核不通过")
    private String auditResult;

    @ApiModelProperty("审核意见")
    private String auditOpinion;
}
