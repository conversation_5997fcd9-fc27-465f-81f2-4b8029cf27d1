package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/8/31 10:57
 * @Description 供应商任务定时任务配置-任务配置response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商任务定时任务配置-详情response")
public class SupplierQualityTimerSubRespDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("定时任务编码")
    private String timerNo;

    @ApiModelProperty("定时任务详情编码")
    private String timerDetailNo;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人姓名")
    private String createName;
}
