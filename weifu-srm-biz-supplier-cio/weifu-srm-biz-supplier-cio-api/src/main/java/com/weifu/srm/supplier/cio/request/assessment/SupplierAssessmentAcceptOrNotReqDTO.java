package com.weifu.srm.supplier.cio.request.assessment;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/19 10:47
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentAcceptOrNotReqDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    @NotNull(message = "assessmentNo can not ne null")
    private String assessmentNo ;
    /** 供应商反馈状态;0，不接受，1 接受 */
    @ApiModelProperty(value = "供应商反馈状态",notes = "0，不接受，1 接受")
    private Integer feedbackStatus ;
    /** 申诉理由 */
    @ApiModelProperty(value = "申诉理由",notes = "")
    private String appealReason ;
    /*** 内部说明附件 */
    @ApiModelProperty(value = "申诉理由附件",notes = "")
    private List<AttachmentMessageReqDTO> appealReasonAttachment ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;
}
