package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商任务定时任务配置-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商任务定时任务配置-分页查询request")
@Data
public class SupplierQualityTimerPageReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty("定时任务编号")
    private String timerNo;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("状态：ENABLE启用，STOP停用，DISABLE禁用")
    private String status;

    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("负责人名称")
    private String principalName;

    @ApiModelProperty("定时类型：WEEK周，MONTH月,  QUARTER季")
    private String timerType;

    @ApiModelProperty(value = "创建时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "结束日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timerEndDateStart;

    @ApiModelProperty(value = "结束日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timerEndDateEnd;

    @ApiModelProperty(value = "操作人", hidden = true)
    private Long operationBy;

}
