package com.weifu.srm.supplier.cio.response.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/13 15:32
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentDetailRespDTO implements Serializable {
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    /** 供应商编码 */
    @ApiModelProperty(value = "供应商编码",notes = "")
    private String supplierCode ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName ;
    /** 供应商名称-EN */
    @ApiModelProperty(value = "供应商名称-EN",notes = "")
    private String supplierNameEn ;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称",notes = "")
    private String supplierShortName ;
    /** 状态 */
    @ApiModelProperty(value = "状态",notes = "")
    private String status ;
    /** 状态 */
    @ApiModelProperty(value = "状态描述",notes = "")
    private String statusDesc ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态",notes = "")
    private String invoiceStatus ;
    /** 开票状态 */
    @ApiModelProperty(value = "开票状态描述",notes = "")
    private String invoiceStatusDesc ;
    /** 开票最新反馈时间 */
    @ApiModelProperty(value = "开票最新反馈时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date lastModifyTimeForInvoice;
    /** fssc开票业务单号 */
    @ApiModelProperty(value = "fssc开票业务单号",notes = "")
    private String fsscBusinessNo;
    /** 发票号 */
    @ApiModelProperty(value = "发票号",notes = "")
    private String invoiceNo;
    /** 红冲开票状态 */
    @ApiModelProperty(value = "红冲开票状态",notes = "")
    private String eliminationInvoiceStatus ;
    /** 红冲开票状态 */
    @ApiModelProperty(value = "红冲开票状态描述",notes = "")
    private String eliminationInvoiceStatusDesc ;
    /** 公司代码 */
    @ApiModelProperty(value = "公司代码",notes = "")
    private String division ;
    /** 考核金额（含税） */
    @ApiModelProperty(value = "考核金额（含税）",notes = "")
    private BigDecimal assessmentTaxAmt ;
    /** 品类编码 */
    @ApiModelProperty(value = "品类编码",notes = "")
    private String categoryCode ;
    /** 品类名称 */
    @ApiModelProperty(value = "品类名称",notes = "")
    private String categoryName ;
    /** 币种 */
    @ApiModelProperty(value = "币种",notes = "")
    private String currency ;
    /** 确认限制天数 */
    @ApiModelProperty(value = "确认限制天数",notes = "")
    private Integer limitDays ;
    /** 到期日 */
    @ApiModelProperty(value = "到期日",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date deadlineTime ;
    /** 质量负责人ID */
    @ApiModelProperty(value = "质量负责人ID",notes = "")
    private Long sqeId ;
    /** 质量负责人名称 */
    @ApiModelProperty(value = "质量负责人名称",notes = "")
    private String sqeName ;
    /** 质量组长ID */
    @ApiModelProperty(value = "质量组长ID",notes = "")
    private Long sqeMasterId ;
    /** 质量组长名称 */
    @ApiModelProperty(value = "质量组长名称",notes = "")
    private String sqeMasterName ;
    /** 质量处长ID */
    @ApiModelProperty(value = "质量处长ID",notes = "")
    private Long sqeDirectorId ;
    /** 质量处长名称 */
    @ApiModelProperty(value = "质量处长名称",notes = "")
    private String sqeDirectorName ;
    /** 品类工程师ID */
    @ApiModelProperty(value = "品类工程师ID",notes = "")
    private Long cpeId ;
    /** 品类工程师名称 */
    @ApiModelProperty(value = "品类工程师名称",notes = "")
    private String cpeName ;
    /** 品类组长ID */
    @ApiModelProperty(value = "品类组长ID",notes = "")
    private Long cpeMasterId ;
    /** 品类组长名称 */
    @ApiModelProperty(value = "品类组长名称",notes = "")
    private String cpeMasterName ;
    /** 品类处长ID */
    @ApiModelProperty(value = "品类处长ID",notes = "")
    private Long cpeDirectorId ;
    /** 品类处长名称 */
    @ApiModelProperty(value = "品类处长名称",notes = "")
    private String cpeDirectorName ;
    /** 内部说明 */
    @ApiModelProperty(value = "内部说明",notes = "")
    private String innerDesc ;
    /** 考核说明 */
    @ApiModelProperty(value = "考核说明",notes = "")
    private String assessmentDesc ;
    /** 供应商反馈时间 */
    @ApiModelProperty(value = "供应商反馈时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date feedbackTime ;
    /** 供应商反馈状态;0，不接受，1 接受 */
    @ApiModelProperty(value = "供应商反馈状态",notes = "0，不接受，1 接受")
    private Integer feedbackStatus ;
    /** 申诉理由 */
    @ApiModelProperty(value = "申诉理由",notes = "")
    private String appealReason ;
    /** 考核提交时间 */
    @ApiModelProperty(value = "考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date assessmentSubmitTime ;
    /** 强制考核提交时间 */
    @ApiModelProperty(value = "强制考核提交时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date forceAssessmentSubmitTime ;
    /** 强制考核标识 */
    @ApiModelProperty(value = "强制考核标识",notes = "")
    private Integer forceAssessmentFlag ;
    /** 强制考核说明 */
    @ApiModelProperty(value = "强制考核说明",notes = "")
    private String forceAssessmentDesc ;
    /** 是否再次调整过 */
    @ApiModelProperty(value = "是否再次调整过",notes = "")
    private Integer isAdjusted ;
    /** 提前结束考核单申请说明 */
    @ApiModelProperty(value = "提前结束考核单申请说明",notes = "")
    private String earlyEndDesc ;
    @ApiModelProperty(value = "提前结束时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date earlyEndTime ;
    /** 索赔材料说明 */
    @ApiModelProperty(value = "索赔材料说明",notes = "")
    private String claimDocDesc ;
    /** cpe退回说明 */
    @ApiModelProperty(value = "cpe退回说明",notes = "")
    private String cpeReturnDesc ;
    /** cpe退回时间 */
    @ApiModelProperty(value = "cpe退回时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date cpeReturnTime ;
    /** 关闭时间 */
    @ApiModelProperty(value = "关闭时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date closeTime ;
    /** 关闭说明 */
    @ApiModelProperty(value = "关闭说明",notes = "")
    private String closeReason ;
    /** 当前操作人 */
    @ApiModelProperty(value = "当前操作人",notes = "")
    private Long currentOperationBy ;
    /** 当前操作人名称 */
    @ApiModelProperty(value = "当前操作人名称",notes = "")
    private String currentOperationByName ;
    /*** 内部说明附件 */
    @ApiModelProperty(value = "内部说明附件",notes = "")
    private List<AttachmentMessageRespDTO> innerAttachment ;
    /*** 内部说明附件 */
    @ApiModelProperty(value = "考核说明附件",notes = "")
    private List<AttachmentMessageRespDTO> assessmentAttachment ;
    /*** 索赔材料说明附件 */
    @ApiModelProperty(value = "索赔材料说明附件",notes = "")
    private List<AttachmentMessageRespDTO> claimDocAttachment ;
    /*** 申诉理由附件 */
    @ApiModelProperty(value = "强制考核说明附件",notes = "")
    private List<AttachmentMessageRespDTO> forceAssessmentAttachment ;
    /*** 强制考核说明附件 */
    @ApiModelProperty(value = "申诉理由附件",notes = "")
    private List<AttachmentMessageRespDTO> appealReasonAttachment ;
    /*** 索赔协议说明附件 */
    @ApiModelProperty(value = "索赔协议说明附件",notes = "")
    private List<AttachmentMessageRespDTO> claimAgreementAttachment ;
    /** 关闭说明附件 */
    @ApiModelProperty(value = "关闭说明附件",notes = "")
    private List<AttachmentMessageRespDTO> closeReasonAttachment ;
    private Long createBy;
    @ApiModelProperty(value = "创建人名称",notes = "")
    private String createName;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;

    @ApiModelProperty(value = "是否签署单次索赔协议",notes = "")
    private Boolean isSignClaimAgreement;
    @ApiModelProperty(value = "索赔材料上传人",notes = "")
    private Long claimDocOptId;
    @ApiModelProperty(value = "索赔材料上传人",notes = "")
    private String claimDocOptName;
    @ApiModelProperty(value = "索赔材料上传时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date claimDocOptTime;
}
