package com.weifu.srm.supplier.cio.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-response
 * @Version 1.0
 */
@Data
@ApiModel(value = "供应商质量问题解决-response", description = "供应商质量问题解决-response")
public class SupplierQualityIssueRespDTO {
    @ExcelProperty(value = "事业部/子公司", index = 0)
    @ColumnWidth(50)
    @ApiModelProperty(value = "事业部/子公司")
    private String divisionCode;

    @ExcelProperty(value = "投诉阶段", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty(value = "投诉阶段名称")
    private String complaintPhaseName;

    @ExcelProperty(value = "分析方式", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty(value = "分析方式名称")
    private String analysisApproachName;

    @ExcelProperty(value = "产品系列", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题零件信息-产品系列")
    private String partProductSeriesName;


    @ExcelProperty(value = "零件SAP号", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题零件信息-零件sap号")
    private String partSapNo;

    @ExcelProperty(value = "零件名称", index = 5)
    @ColumnWidth(30)
    @ApiModelProperty(value = "问题零件信息-零件名称")
    private String partName;

    @ExcelProperty(value = "零件型号", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题零件信息-零件型号")
    private String partModel;

    @ExcelProperty(value = "零件品类", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题零件信息-零件品类名称")
    private String partCategoryName;

    @ExcelProperty(value = "投诉数量", index = 8)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题零件信息-投诉数量")
    private String partComplaintQty;

    @ExcelProperty(value = "供应商编码", index = 9)
    @ColumnWidth(20)
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商简称", index = 10)
    @ColumnWidth(50)
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ExcelProperty(value = "接到投诉日期", index = 11)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "投诉信息-投诉日期")
    private Date complaintDate;

    @ExcelProperty(value = "客户名称", index = 12)
    @ColumnWidth(30)
    @ApiModelProperty(value = "投诉信息-客户名称")
    private String complaintCustomName;

    @ExcelProperty(value = "投诉类型", index = 13)
    @ColumnWidth(20)
    @ApiModelProperty(value = "投诉信息-投诉类型名称")
    private String complaintTypeName;

    @ExcelProperty(value = "失效类别", index = 14)
    @ColumnWidth(20)
    @ApiModelProperty(value = "投诉信息-失效类别名称")
    private String complaintFailureCategoryName;

    @ExcelProperty(value = "问题/不符合简述", index = 15)
    @ColumnWidth(50)
    @ApiModelProperty(value = "问题简述")
    private String complaintProblemDescription;

    @ExcelProperty(value = "原因分析及长期反馈期限", index = 16)
    @ColumnWidth(45)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "原因分析及长期反馈期限")
    private Date reasonAnalysisFeedbackDate;

    @ExcelProperty(value = "预防再发生反馈期限", index = 17)
    @ColumnWidth(40)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "预防再发生反馈期限")
    private Date preventRecurFeedbackDate;

    @ExcelProperty(value = "原因分析及长期实际反馈时间", index = 18)
    @ColumnWidth(45)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "原因分析及长期实际反馈时间")
    private Date reasonRealFeedbackDate;

    @ExcelProperty(value = "预防再发生实际反馈时间", index = 19)
    @ColumnWidth(45)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty(value = "预防再发生实际反馈时间")
    private Date preventRealFeedbackDate;

    @ExcelProperty(value = "根因简述", index = 20)
    @ColumnWidth(20)
    @ApiModelProperty(value = "根因简述")
    private String supplierAnalysisDescription;

    @ExcelProperty(value = "措施简述", index = 21)
    @ColumnWidth(20)
    @ApiModelProperty(value = "措施简述")
    private String supplierMeasureActual;

    @ExcelProperty(value = "是否重复发生", index = 22)
    @ColumnWidth(20)
    @ApiModelProperty(value = "是否重复发生")
    private String rootCauseIsRecurringName;

    @ExcelIgnore
    @ApiModelProperty(value = "是否重复发生1：是、0：否")
    private Integer rootCauseIsRecurring;


    @ExcelProperty(value = "Q11对标", index = 23)
    @ColumnWidth(20)
    @ApiModelProperty(value = "Q11对标字符串")
    private String measureQ11BenchmarkingStr;

    @ExcelProperty(value = "是否关联质量任务", index = 24)
    @ColumnWidth(40)
    @ApiModelProperty(value = "是否关联质量任务")
    private String isRefTask;

    @ExcelProperty(value = "问题编号", index = 25)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题编号")
    private String issueNo;

    @ExcelProperty(value = "问题状态", index = 26)
    @ColumnWidth(20)
    @ApiModelProperty(value = "问题状态名称")
    private String issueStatusStr;

    @ExcelProperty(value = "是否正常关闭", index = 27)
    @ColumnWidth(20)
    @ApiModelProperty(value = "是否正常关闭")
    private String isNormalClose;

    @ExcelIgnore
    @ApiModelProperty(value = "主键")
    private Long id;

    @ExcelIgnore
    @ApiModelProperty(value = "业部/子公司编码")
    private String divisionName;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商英文名称")
    private String supplierNameEn;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商联系人id")
    private Long supplierContactsId;

    @ExcelIgnore
    @ApiModelProperty(value = "供应商联系人名称")
    private String supplierContactsName;

    @ExcelIgnore
    @ApiModelProperty(value = "投诉阶段")
    private String complaintPhase;

    @ExcelIgnore
    @ApiModelProperty(value = "分析方式,：4Q 、8D")
    private String analysisApproach;

    @ExcelIgnore
    @ApiModelProperty(value = "负责的sqe的id")
    private Long sqeId;

    @ExcelIgnore
    @ApiModelProperty(value = "负责的sqe名称")
    private String sqeName;

    @ExcelIgnore
    @ApiModelProperty(value = "问题状态")
    private String issueStatus;

    @ExcelIgnore
    @ApiModelProperty(value = "问题零件信息-总成号")
    private String partAssemblyNo;

    @ExcelIgnore
    @ApiModelProperty(value = "问题零件信息-零件品类编码")
    private String partCategoryCode;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "问题零件信息-生产日期")
    private Date partProductionDate;

    @ExcelIgnore
    @ApiModelProperty(value = "问题零件信息-生产批次")
    private String partProductionBatch;

    @ExcelIgnore
    @ApiModelProperty(value = "投诉信息-投诉标题")
    private String complaintTitle;

    @ExcelIgnore
    @ApiModelProperty(value = "投诉信息-客户代码")
    private String complaintCustomCode;

    @ExcelIgnore
    @ApiModelProperty(value = "投诉信息-投诉类型")
    private String complaintType;

    @ExcelIgnore
    @ApiModelProperty(value = "投诉信息-失效类别")
    private String complaintFailureCategory;

    @ExcelIgnore
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @ExcelIgnore
    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ExcelIgnore
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    @ApiModelProperty(value = "长期措施验证-Q11对标")
    private String measureQ11Benchmarking;

    @ExcelIgnore
    @ApiModelProperty(value = "长期措施验证-Q11对标:手动输入")
    private String measureQ11BenchmarkingOther;

}
