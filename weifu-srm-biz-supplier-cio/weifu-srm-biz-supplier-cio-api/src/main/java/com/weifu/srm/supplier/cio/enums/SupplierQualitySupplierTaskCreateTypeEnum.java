package com.weifu.srm.supplier.cio.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量供应商任务-创建任务类型枚举
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum SupplierQualitySupplierTaskCreateTypeEnum {
    DISPOSABLE("DISPOSABLE","一次性任务"),
    TIMING_CYCLE("TIMING_CYCLE","定时循环任务");
    private final String code;
    private final String name;
    public static String getNameByCode(String code) {
        for (SupplierQualitySupplierTaskCreateTypeEnum statusEnum : SupplierQualitySupplierTaskCreateTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
