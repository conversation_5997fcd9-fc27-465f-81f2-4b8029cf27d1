package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商质量审核计划-response")
public class SupplierQualityAuditPlanRespDTO {

    @ApiModelProperty("主键")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "计划编号", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("审核计划编号")
    private String auditPlanNo;

    @ExcelProperty(value = "计划状态", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty("计划状态名称")
    private String statusName;

    @ExcelProperty(value = "审核类型大类", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("审核类型大类名称")
    private String auditCategoryTypeName;

    @ExcelProperty(value = "审核类型小类", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("审核类型小类名称")
    private String auditSubclassTypeName;

    @ExcelProperty(value = "创建人", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty("创建人姓名")
    private String createName;

    @ExcelProperty(value = "人员所属公司", index = 5)
    @ColumnWidth(30)
    @ApiModelProperty("业务所属名称")
    private String divisionName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", index = 6)
    @ColumnWidth(30)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelProperty(value = "一级品类", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty("一级品类名称")
    private String firstCategoryName;

    @ExcelProperty(value = "审核计划备注", index = 8)
    @ColumnWidth(50)
    @ApiModelProperty("审核计划备注")
    private String auditPlanRemark;

    @ExcelIgnore
    @ApiModelProperty("审核类型大类")
    private String auditCategoryType;

    @ExcelIgnore
    @ApiModelProperty("审核类型小类")
    private String auditSubclassType;

    @ExcelIgnore
    @ApiModelProperty("一级品类编码")
    private String firstCategoryCode;

    @ExcelIgnore
    @ApiModelProperty("sqe组长id")
    private Long sqeMasterId;

    @ExcelIgnore
    @ApiModelProperty("sqe组长名称")
    private String sqeMasterName;

    @ExcelIgnore
    @ApiModelProperty("审批处长|经理id")
    private Long auditManagerId;

    @ExcelIgnore
    @ApiModelProperty("审批处长|经理名称")
    private String auditManagerName;

    @ExcelIgnore
    @ApiModelProperty("状态")
    private String status;

    @ExcelIgnore
    @ApiModelProperty("审核结果：PASS通过，FAIL不通过")
    private String auditResult;

    @ExcelIgnore
    @ApiModelProperty("审批意见")
    private String auditOpinion;

    @ExcelIgnore
    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ExcelIgnore
    @ApiModelProperty("创建人id")
    private Long createBy;

    @ExcelIgnore
    @ApiModelProperty("业务所属编码")
    private String divisionCode;

    @ExcelIgnore
    @ApiModelProperty("审核类型小类-其他")
    private String auditSubclassOther;

}
