package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-关闭质量问题request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "供应商质量问题解决-关闭质量问题request", description = "供应商质量问题解决-关闭质量问题request")
public class SupplierQualityIssueCloseReqDTO extends SupplierQualityIdReqDTO {

    @ApiModelProperty(value = "关闭质量问题-问题解决确认部门")
    @NotBlank(message = "closeIssueDepartment不能为空")
    private String closeIssueDepartment;

    @ApiModelProperty(value = "关闭质量问题-确认关闭意见")
    @NotBlank(message = "closeIssueOpinion不能为空")
    private String closeIssueOpinion;

    @ApiModelProperty(value = "关闭质量问题-本部门确认附件")
    private List<AttachmentMessageReqDTO> confirmedDepartmentList;

    @NotEmpty(message = "closeIssueList不能为空")
    @ApiModelProperty(value = "关闭质量问题-确认问题关闭附件")
    private List<AttachmentMessageReqDTO> closeIssueList;
}
