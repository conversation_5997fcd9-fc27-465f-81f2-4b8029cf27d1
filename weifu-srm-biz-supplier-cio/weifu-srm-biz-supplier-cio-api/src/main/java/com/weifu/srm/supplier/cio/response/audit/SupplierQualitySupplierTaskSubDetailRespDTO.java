package com.weifu.srm.supplier.cio.response.audit;

import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商审核供应商子任务-详情response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商审核供应商子任务-详情response")
public class SupplierQualitySupplierTaskSubDetailRespDTO extends SupplierQualitySupplierTaskSubRespDTO {

    @ApiModelProperty("负责人id")
    private Long principalId;

    @ApiModelProperty("威孚负责人名称")
    private String principalName;

    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("供应商联系人类型")
    private String supplierContactType;

    @ApiModelProperty("供应商联系人类型名称")
    private String supplierContactTypeName;

    @ApiModelProperty("反馈说明")
    private String feedbackExplanation;

    @ApiModelProperty("审核结果：CLOSE关闭, FAIL审核不通过")
    private String auditResult;

    @ApiModelProperty("上一次的审核说明")
    private String lastAuditExplanation;

    @ApiModelProperty("审核说明")
    private String auditExplanation;

    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty("父任务附件")
    private List<AttachmentMessageRespDTO> annexList;

    @ApiModelProperty("供应商反馈-附件")
    private List<AttachmentMessageRespDTO> supplierFeedbackAnnexList;

    @ApiModelProperty("任务关闭-附件")
    private List<AttachmentMessageRespDTO> supplierAuditAnnexList;
}
