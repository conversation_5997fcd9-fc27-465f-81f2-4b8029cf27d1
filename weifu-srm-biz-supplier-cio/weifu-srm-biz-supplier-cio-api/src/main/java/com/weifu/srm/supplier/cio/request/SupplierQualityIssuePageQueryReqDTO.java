package com.weifu.srm.supplier.cio.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierQualityIssuePageQueryReqDTO", description = "供应商质量问题解决-分页查询request")
public class SupplierQualityIssuePageQueryReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty(name = "当前登录用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "问题编号")
    private String issueNoLike;

    @ApiModelProperty(value = "问题编号集合")
    private List<String> issueNoList;

    @ApiModelProperty(value = "零件sap号")
    private String partSapNoLike;

    @ApiModelProperty(value = "零件名称")
    private String partNameLike;

    @ApiModelProperty(value = "业部/子公司编码")
    private List<String> divisionCodeList;

    @ApiModelProperty(value = "供应商编码")
    private List<String> supplierCodeList;

    @ApiModelProperty(value = "问题状态")
    private List<String> issueStatusList;

    @ApiModelProperty(value = "创建BP")
    private List<Long> createByList;

    @ApiModelProperty(value = "负责SQE")
    private List<Long> sqeIdList;

    @ApiModelProperty(value = "分析方式,：4Q 、8D")
    private List<String> analysisApproachList;

    @ApiModelProperty(value = "投诉时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date complaintDateStart;

    @ApiModelProperty(value = "投诉时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date complaintDateEnd;

    @ApiModelProperty(value = "问题提出时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeStart;

    @ApiModelProperty(value = "问题提出时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeEnd;

    @ApiModelProperty(value = "供应商联系id(供应商id)")
    private Long supplierContactsId;

    @ApiModelProperty(value = "产品系列")
    private String partProductSeriesName;

    @ApiModelProperty(value = "零件品类编码集合")
    private List<String> partCategoryCodeList;

    @ApiModelProperty(value = "客户类编码集合")
    private List<String> complaintCustomCodeList;

    @ApiModelProperty(value = "投诉类型集合")
    private List<String> complaintTypeList;

    @ApiModelProperty(value = "失效类型集合")
    private List<String> complaintFailureCategoryList;

    @ApiModelProperty(value = "是否重复发生 1：是、0：否")
    private Integer rootCauseIsRecurring;

    @ApiModelProperty(value = "Q11对标")
    private String measureQ11Benchmarking;

    @ApiModelProperty(value = "供应商端标识")
    private String supplierMark;

    @ApiModelProperty(value = "负责的sqe名称")
    private String sqeName;

    @ApiModelProperty(value = "质量BP")
    private String createName;

    @ApiModelProperty(value = "投诉阶段")
    private String complaintPhase;

    @ApiModelProperty(hidden = true)
    private List<String> categoryCodePermissionList;

    @ApiModelProperty(hidden = true)
    private List<String> divisionCodePermissionList;

    @ApiModelProperty(hidden = true)
    private Long createBy;


}
