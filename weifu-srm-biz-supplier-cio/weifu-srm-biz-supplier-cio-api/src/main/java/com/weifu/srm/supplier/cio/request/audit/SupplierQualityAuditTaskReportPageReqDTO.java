package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务-报表分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核任务-报表分页查询request")
@Data
public class SupplierQualityAuditTaskReportPageReqDTO  extends PageRequest {


    @ApiModelProperty("审核任务编号")
    private String auditTaskNo;

    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ApiModelProperty("品类")
    private String categoryCode;

    @ApiModelProperty("三级品类")
    private List<String> threeCategoryCodes;

    @ApiModelProperty("业务所属编码")
    private String divisionCode;

    @ApiModelProperty("审核任务状态")
    private String status;

    @ApiModelProperty("sqe负责id")
    private Long sqeId;

    @ApiModelProperty("sqe负责名称")
    private String sqeName;

    @ApiModelProperty(value = "计划完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date predictCompleteDateStart;

    @ApiModelProperty(value = "计划完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date predictCompleteDateEnd;

    @ApiModelProperty(value = "审核结论")
    private String auditConclusion;

    @ApiModelProperty(value = "实际完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date practicalCompleteDateStart;

    @ApiModelProperty(value = "实际完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date practicalCompleteDateEnd;

    @ApiModelProperty(value = "是否逾期")
    private Boolean isOverdue;

    @ApiModelProperty("审核类型大类")
    private String auditCategoryType;

    @ApiModelProperty("审核类型小类")
    private String auditSubclassType;

    @ApiModelProperty("SQE组长")
    private Long sqeMasterId;

    @ApiModelProperty("SQE组长名称")
    private String sqeMasterName;

}
