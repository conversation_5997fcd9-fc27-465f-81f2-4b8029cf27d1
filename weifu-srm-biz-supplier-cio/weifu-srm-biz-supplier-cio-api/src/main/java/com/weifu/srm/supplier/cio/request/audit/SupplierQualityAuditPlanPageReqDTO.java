package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.PagePermissionReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-分页查询request")
@Data
public class SupplierQualityAuditPlanPageReqDTO extends PagePermissionReqDTO implements Serializable {

    @ApiModelProperty("审核计划编号")
    private String auditPlanNo;

    @ApiModelProperty("计划状态")
    private String status;

    @ApiModelProperty("审核类型大类")
    private String auditCategoryType;

    @ApiModelProperty("审核类型小类")
    private String auditSubclassType;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("一级品类编码")
    private String firstCategoryCode;

    @ApiModelProperty(value = "创建时间-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;


    @ApiModelProperty("业务所属编码")
    private String divisionCode;
}
