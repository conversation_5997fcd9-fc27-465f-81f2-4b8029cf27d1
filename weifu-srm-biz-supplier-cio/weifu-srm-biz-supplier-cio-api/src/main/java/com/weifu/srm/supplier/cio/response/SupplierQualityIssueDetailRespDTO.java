package com.weifu.srm.supplier.cio.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决-详情response
 * @Version 1.0
 */
@Data
@ApiModel(value = "供应商质量问题解决-详情response", description = "供应商质量问题解决-详情response")
public class SupplierQualityIssueDetailRespDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "问题编号")
    private String issueNo;

    @ApiModelProperty(value = "业部/子公司编码")
    private String divisionCode;

    @ApiModelProperty(value = "事业部/子公司名称")
    private String divisionName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商英文名称")
    private String supplierNameEn;

    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName;

    @ApiModelProperty(value = "供应商联系人id")
    private Long supplierContactsId;

    @ApiModelProperty(value = "供应商联系人名称")
    private String supplierContactsName;

    @ApiModelProperty(value = "供应商联系人类型")
    private String supplierContactsType;

    @ApiModelProperty(value = "供应商联系人邮箱")
    private String supplierContactsEmail;

    @ApiModelProperty(value = "问题零件信息-零件sap号")
    private String partSapNo;

    @ApiModelProperty(value = "投诉阶段")
    private String complaintPhase;

    @ApiModelProperty(value = "分析方式,：4Q 、8D")
    private String analysisApproach;

    @ApiModelProperty(value = "分析方式-其它手动输入")
    private String analysisApproachOther;

    @ApiModelProperty(value = "负责的sqe的id")
    private Long sqeId;

    @ApiModelProperty(value = "负责的sqe名称")
    private String sqeName;

    @ApiModelProperty(value = "问题状态")
    private String issueStatus;

    @ApiModelProperty(value = "问题零件信息-产品系列")
    private String partProductSeriesName;

    @ApiModelProperty(value = "问题零件信息-总成号")
    private String partAssemblyNo;

    @ApiModelProperty(value = "问题零件信息-零件名称")
    private String partName;

    @ApiModelProperty(value = "问题零件信息-零件型号")
    private String partModel;

    @ApiModelProperty(value = "问题零件信息-零件品类名称")
    private String partCategoryName;

    @ApiModelProperty(value = "问题零件信息-零件品类编码")
    private String partCategoryCode;

    @ApiModelProperty(value = "问题零件信息-投诉数量")
    private String partComplaintQty;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "问题零件信息-生产日期")
    private Date partProductionDate;

    @ApiModelProperty(value = "问题零件信息-生产批次")
    private String partProductionBatch;

    @ApiModelProperty(value = "投诉信息-投诉标题")
    private String complaintTitle;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "投诉信息-投诉日期")
    private Date complaintDate;

    @ApiModelProperty(value = "投诉信息-客户代码")
    private String complaintCustomCode;

    @ApiModelProperty(value = "投诉信息-客户名称")
    private String complaintCustomName;

    @ApiModelProperty(value = "投诉信息-投诉类型")
    private String complaintType;

    @ApiModelProperty(value = "投诉信息-失效类别")
    private String complaintFailureCategory;

    @ApiModelProperty(value = "投诉信息-失效类别-其它手动输入")
    private String complaintFailureCategoryOther;

    @ApiModelProperty(value = "投诉信息-发现地点/工位")
    private String complaintDiscoverySite;

    @ApiModelProperty(value = "投诉信息-失效现象简述")
    private String complaintFailureSituationResume;

    @ApiModelProperty(value = "投诉信息-问题简述")
    private String complaintProblemDescription;

    @ApiModelProperty(value = "其它信息-原因分析及长期反馈期限")
    private Integer reasonAnalysisFeedbackTerm;

    @ApiModelProperty(value = "其它信息-预防再发生反馈期限")
    private Integer preventRecurFeedbackTerm;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "接收/退回 1：是、0：否")
    private Boolean receiveResult;

    @ApiModelProperty(value = "接收/退回 意见")
    private String receiveOpinion;

    @ApiModelProperty(value = "提交临时措施-临时措施")
    private String temporaryMeasures;

    @ApiModelProperty(value = "提交临时措施-是否升级 1：是、0：否")
    private Boolean temporaryIsUpgrade;

    @ApiModelProperty(value = "根因分析验证-是否重复发生 1：是、0：否")
    private Integer rootCauseIsRecurring;

    @ApiModelProperty(value = "根因分析验证-审核结果： PASS-通过 ，FAIL-不通过")
    private String rootCauseAuditResult;

    @ApiModelProperty(value = "根因分析验证-审核意见")
    private String rootCauseAuditOpinion;

    @ApiModelProperty(value = "长期措施验证-审核结果：PASS-通过 ，FAIL-不通过")
    private String measureAuditResult;

    @ApiModelProperty(value = "长期措施验证-审核意见")
    private String measureAuditOpinion;

    @ApiModelProperty(value = "长期措施验证-Q11对标:")
    private String measureQ11Benchmarking;

    @ApiModelProperty(value = "长期措施验证-Q11对标:手动输入")
    private String measureQ11BenchmarkingOther;

    @ApiModelProperty(value = "关闭质量问题-问题解决确认部门")
    private String closeIssueDepartment;

    @ApiModelProperty(value = "关闭质量问题-确认关闭意见")
    private String closeIssueOpinion;

    @ApiModelProperty(value = "撤回质量问题-撤回原因描述")
    private String recallIssueReasonDescription;

    @ApiModelProperty(value = "供应商根因分析-根因简述")
    private String supplierAnalysisDescription;

    @ApiModelProperty(value = "供应商根因分析-长期措施计划")
    private String supplierAnalysisActionPlan;

    @ApiModelProperty(value = "供应商提交长期措施-实际实施的长期措施")
    private String supplierMeasureActual;

    @ApiModelProperty(value = "供应商提交长期措施-预防再发生措施")
    private String supplierMeasurePrevention;

    @ApiModelProperty(value = "供应商提交长期措施-审核意见")
    private String supplierMeasureAuditOpinion;

    @ApiModelProperty(value = "投诉信息-失效图片")
    private List<AttachmentMessageRespDTO> invalidImageList;

    @ApiModelProperty(value = "其它信息-4Q/8D附件")
    private List<AttachmentMessageRespDTO> other4q8dList;

    @ApiModelProperty(value = "关闭质量问题-本部门确认附件")
    private List<AttachmentMessageRespDTO> confirmedDepartmentList;

    @ApiModelProperty(value = "关闭质量问题-确认问题关闭附件")
    private List<AttachmentMessageRespDTO> closeIssueList;

    @ApiModelProperty(value = "撤回质量问题-撤回证明附件")
    private List<AttachmentMessageRespDTO> recallIssueList;

    @ApiModelProperty(value = "提交临时措施-4Q/8D报告")
    private List<AttachmentMessageRespDTO> temporary4q8dList;

    @ApiModelProperty(value = "供应商根因分析-4q8d报告及其它附件")
    private List<AttachmentMessageRespDTO> supplierAnalysisList;

    @ApiModelProperty(value = "供应商提交长期措施-4q8d报告及其它附件")
    private List<AttachmentMessageRespDTO> supplierMeasureList;

    @ApiModelProperty(value = "创建人id")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "负责处理人的id")
    private Long handleId;

    @ApiModelProperty(value = "其他说明")
    private String otherDescription;
}
