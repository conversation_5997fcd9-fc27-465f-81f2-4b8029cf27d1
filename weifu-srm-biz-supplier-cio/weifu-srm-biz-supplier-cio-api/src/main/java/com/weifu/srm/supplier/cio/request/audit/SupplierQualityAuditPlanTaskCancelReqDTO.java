package com.weifu.srm.supplier.cio.request.audit;

import com.weifu.srm.supplier.cio.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.cio.request.OperatorBaseReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核计划-申请取消request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核计划-申请取消request")
@Data
public class SupplierQualityAuditPlanTaskCancelReqDTO extends SupplierQualityAuditPlanAdjustReqDTO {

    @NotEmpty(message = "cancelDescription is required.")
    @ApiModelProperty(value = "取消说明", required = true)
    private String cancelDescription;

}
