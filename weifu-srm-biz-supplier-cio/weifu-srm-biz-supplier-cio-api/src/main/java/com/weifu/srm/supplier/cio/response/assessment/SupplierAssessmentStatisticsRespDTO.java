package com.weifu.srm.supplier.cio.response.assessment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupplierAssessmentStatisticsRespDTO implements Serializable {

    @ApiModelProperty(value = "当前统计的供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "隶属于当前供应商的，等待供应商反馈的索赔单数量")
    private Integer todoCount;

    public SupplierAssessmentStatisticsRespDTO(String supplierCode, Integer todoCount) {
        this.supplierCode = supplierCode;
        this.todoCount = todoCount;
    }

    public SupplierAssessmentStatisticsRespDTO() {
    }
}
