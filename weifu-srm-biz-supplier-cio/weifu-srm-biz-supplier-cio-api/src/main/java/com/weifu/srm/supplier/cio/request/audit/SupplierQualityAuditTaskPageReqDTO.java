package com.weifu.srm.supplier.cio.request.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.request.PagePermissionReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量审核任务-分页查询request
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "供应商质量审核任务-分页查询request")
@Data
public class SupplierQualityAuditTaskPageReqDTO extends PagePermissionReqDTO implements Serializable {

    @ApiModelProperty("供应商编码简称")
    private String supplierCodeAndShortName;

    @ApiModelProperty("业务所属编码")
    private String divisionCode;

    @ApiModelProperty("sqe负责id")
    private Long sqeId;

    @ApiModelProperty("sqe负责名称")
    private String sqeName;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核计划编号")
    private String auditPlanNo;

    @ApiModelProperty("审核任务编号")
    private String auditTaskNo;

    @ApiModelProperty("品类")
    private String categoryCode;

    @ApiModelProperty("三级品类")
    private List<String> threeCategoryCodes;

    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ApiModelProperty(value = "计划完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateStart;

    @ApiModelProperty(value = "计划完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date predictCompleteDateEnd;

    @ApiModelProperty(value = "实际完成日期-起")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date practicalCompleteDateStart;

    @ApiModelProperty(value = "实际完成日期-止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date practicalCompleteDateEnd;

}
