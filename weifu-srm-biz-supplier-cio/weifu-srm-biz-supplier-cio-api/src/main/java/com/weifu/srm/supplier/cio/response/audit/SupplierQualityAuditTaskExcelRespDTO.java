package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 质量审核任务-导出response
 * @Version 1.0
 */
@Data
@ApiModel(description = "质量审核任务-导出response")
public class SupplierQualityAuditTaskExcelRespDTO {
    @ExcelProperty(value = "任务编号", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("审核计划任务编号")
    private String auditTaskNo;

    @ExcelProperty(value = "审核计划编号", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty("质量审核计划编号")
    private String auditPlanNo;

    @ExcelProperty(value = "供应商编码", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商简称", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ExcelProperty(value = "审核产品系列", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ExcelProperty(value = "审核产品范围", index = 5)
    @ColumnWidth(20)
    @ApiModelProperty("审核产品范围")
    private String auditProductRange;

    @ExcelProperty(value = "三级品类", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty("三级品类名称")
    private String threeCategoryName;

    @ExcelProperty(value = "负责SQE", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty("sqe负责名称")
    private String sqeName;

    @ExcelProperty(value = "审核计划大类", index = 8)
    @ColumnWidth(20)
    @ApiModelProperty("审核类型大类名称")
    private String auditCategoryTypeName;

    @ExcelProperty(value = "审核类型小类", index = 9)
    @ColumnWidth(20)
    @ApiModelProperty("审核类型小类名称")
    private String auditSubclassTypeName;

    @ExcelProperty(value = "业务所属", index = 10)
    @ColumnWidth(20)
    @ApiModelProperty("业务所属")
    private String divisionName;

    @ExcelProperty(value = "审核任务状态", index = 11)
    @ColumnWidth(20)
    @ApiModelProperty("任务状态")
    private String statusName;

    @ExcelProperty(value = "计划完成日期", index = 12)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ExcelProperty(value = "审核结论", index = 13)
    @ColumnWidth(20)
    @ApiModelProperty("审核结论名称")
    private String auditConclusionName;

    @ExcelProperty(value = "审核得分", index = 14)
    @ColumnWidth(20)
    @ApiModelProperty("审核得分")
    private String auditScore;

    @ExcelProperty(value = "实际完成日期", index = 15)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("实际完成日期")
    private Date practicalCompleteDate;

    @ExcelProperty(value = "是否逾期", index = 16)
    @ColumnWidth(20)
    @ApiModelProperty("是否逾期")
    private String isOverdue;

    @ExcelProperty(value = "OPL/供应商任务", index = 17)
    @ColumnWidth(20)
    @ApiModelProperty("OPL-供应商任务")
    private String createTaskStr;

    @ExcelProperty(value = "审核意见", index = 18)
    @ColumnWidth(20)
    @ApiModelProperty("审核意见")
    private String auditOpinion;

    @ExcelIgnore
    @ApiModelProperty("审核结论")
    private String auditConclusion;

    @ExcelIgnore
    @ApiModelProperty("状态")
    private String status;

    @ExcelIgnore
    @ApiModelProperty("审核类型小类-其他")
    private String auditSubclassOther;

}
