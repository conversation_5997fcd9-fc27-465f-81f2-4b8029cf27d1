package com.weifu.srm.supplier.cio.response.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class SupplierAssessmentInvoiceListRespDTO implements Serializable {
    @ApiModelProperty(value = "发票号ID",notes = "查询详情时使用")
    private Long id;
    @ApiModelProperty(value = "申请开票时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    @ApiModelProperty(value = "发票类型",notes = "")
    private String type;
    @ApiModelProperty(value = "发票号",notes = "")
    private String invoiceNo;
    @ApiModelProperty(value = "FSSC单据号",notes = "")
    private String fsscInvoiceNo;
    @ApiModelProperty(value = "SAP凭证号",notes = "")
    private String sapBizNo;
    @ApiModelProperty(value = "开票状态",notes = "")
    private String status;
    @ApiModelProperty(value = "开票人",notes = "")
    private String createName;
    @ApiModelProperty(value = "开票失败的备注",notes = "")
    private String failRemark;
}
