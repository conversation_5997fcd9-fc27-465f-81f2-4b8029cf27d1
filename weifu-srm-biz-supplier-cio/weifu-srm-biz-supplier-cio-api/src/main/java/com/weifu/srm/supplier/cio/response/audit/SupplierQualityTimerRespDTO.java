package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商任务定时任务配置-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商任务定时任务配置-response")
public class SupplierQualityTimerRespDTO {

    @ExcelIgnore
    @ApiModelProperty("主键")
    private Long id;

    @ExcelProperty(value = "ID", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("定时任务编号")
    private String timerNo;

    @ExcelProperty(value = "任务名称", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty("任务名称")
    private String taskName;

    @ExcelProperty(value = "任务类型", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("任务类型名称")
    private String taskTypeName;

    @ExcelProperty(value = "定时类型", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("定时类型名称")
    private String timerTypeName;

    @ExcelProperty(value = "定时详情", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty("定时详情")
    private String timerDayName;

    @ExcelProperty(value = "要求供应商反馈天数", index = 5)
    @ColumnWidth(20)
    @ApiModelProperty("要求供应商反馈天数")
    private Integer feedbackDay;

    @ExcelProperty(value = "定时状态", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty("状态名称")
    private String statusName;

    @ExcelProperty(value = "威孚负责人", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty("负责人名称")
    private String principalName;

    @ExcelProperty(value = "创建时间", index = 8)
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelProperty(value = "结束日期", index = 9)
    @ApiModelProperty("定时任务结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date timerEndDate;

    @ExcelIgnore
    @ApiModelProperty("任务类型")
    private String taskType;

    @ExcelIgnore
    @ApiModelProperty("任务类型其它")
    private String taskTypeOther;

    @ExcelIgnore
    @ApiModelProperty("单据类型")
    private String relationType;

    @ExcelIgnore
    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ExcelIgnore
    @ApiModelProperty("负责人id")
    private Long principalId;

    @ExcelIgnore
    @ApiModelProperty("供应商联系人类型")
    private String supplierContactType;

    @ExcelIgnore
    @ApiModelProperty("附加通知人")
    private String additionalNotifier;

    @ExcelIgnore
    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ExcelIgnore
    @ApiModelProperty("定时类型：WEEK周，MONTH月,  QUARTER季")
    private String timerType;

    @ExcelIgnore
    @ApiModelProperty("定时详情，天")
    private Integer timerDay;



    @ExcelIgnore
    @ApiModelProperty("状态")
    private String status;

}
