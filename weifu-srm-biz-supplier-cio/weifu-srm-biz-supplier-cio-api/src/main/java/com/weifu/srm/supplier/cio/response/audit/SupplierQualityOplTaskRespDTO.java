package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量opl任务-response
 * @Version 1.0
 */
@Data
@ApiModel(description = "供应商质量opl任务-response")
public class SupplierQualityOplTaskRespDTO {

    @ExcelIgnore
    @ApiModelProperty("主键")
    private Long id;

    @ExcelProperty(value = "OPL编号", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty("OPL编号")
    private String oplNo;

    @ExcelProperty(value = "OPL名称", index = 1)
    @ColumnWidth(50)
    @ApiModelProperty("OPL名称")
    private String oplName;

    @ExcelProperty(value = "OPL状态", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty("OPL状态名称")
    private String statusName;

    @ExcelProperty(value = "OPL类型", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("OPL类型名称")
    private String oplTypeName;

    @ExcelProperty(value = "创建人", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty("创建人姓名")
    private String createName;

    @ExcelProperty(value = "创建时间", index = 5)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ExcelProperty(value = "单据类型", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty("单据类型")
    private String relationTypeName;

    @ExcelProperty(value = "关联单据号", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty("关联单据号")
    private String relationNo;

    @ExcelProperty(value = "负责人", index = 8)
    @ColumnWidth(20)
    @ApiModelProperty("负责人名称")
    private String principalName;

    @ExcelProperty(value = "计划完成时间", index = 9)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ExcelProperty(value = "实际完成日期", index = 10)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("实际完成日期")
    private Date practicalCompleteDate;

    @ExcelIgnore
    @ApiModelProperty("负责人id")
    private Long principalId;

    @ExcelIgnore
    @ApiModelProperty("OPL类型")
    private String oplType;

    @ExcelIgnore
    @ApiModelProperty("OPL类型其它")
    private String oplTypeOther;

    @ExcelIgnore
    @ApiModelProperty("单据类型")
    private String relationType;

    @ExcelIgnore
    @ApiModelProperty("状态")
    private String status;

    @ExcelIgnore
    @ApiModelProperty("创建人id")
    private Long createBy;


}
