package com.weifu.srm.supplier.cio.response.audit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.cio.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/31 10:57
 * @Description 供应商任务定时任务配置-详情response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "供应商任务定时任务配置-详情response")
public class SupplierQualityTimerDetailRespDTO extends SupplierQualityTimerRespDTO {

    @ApiModelProperty("状态：ENABLE启用，STOP停用，DISABLE禁用")
    private String status;

    @ApiModelProperty("创建人id")
    private Long createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("create_name")
    private String createName;

    @ApiModelProperty("接收供应商")
    List<SupplierQualityTimerReceiveRespDTO> receiveList;

    @ApiModelProperty(value = "附件")
    private List<AttachmentMessageRespDTO> annexList;
}
