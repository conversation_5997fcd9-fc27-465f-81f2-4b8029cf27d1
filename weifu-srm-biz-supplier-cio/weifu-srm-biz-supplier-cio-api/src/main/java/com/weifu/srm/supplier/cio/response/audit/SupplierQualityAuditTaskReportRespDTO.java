package com.weifu.srm.supplier.cio.response.audit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 质量审核任务-导入response
 * @Version 1.0
 */
@Data
@ApiModel(description = "质量审核任务-报表response")
public class SupplierQualityAuditTaskReportRespDTO {
    @ExcelProperty(value = "审核任务编号")
    @ColumnWidth(25)
    @ApiModelProperty("审核计划任务编号")
    private String auditTaskNo;

    @ExcelProperty(value = "供应商编码")
    @ColumnWidth(20)
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "供应商简称")
    @ColumnWidth(20)
    @ApiModelProperty("供应商简称")
    private String supplierShortName;

    @ExcelProperty(value = "审核产品系列")
    @ColumnWidth(20)
    @ApiModelProperty("审核产品系列")
    private String auditProductSeries;

    @ExcelProperty(value = "审核产品范围")
    @ColumnWidth(20)
    @ApiModelProperty("审核产品范围")
    private String auditProductRange;

    @ExcelProperty(value = "三级品类")
    @ColumnWidth(20)
    @ApiModelProperty("三级品类名称")
    private String threeCategoryName;

    @ExcelProperty(value = "业务所属")
    @ColumnWidth(20)
    @ApiModelProperty("业务所属")
    private String divisionCode;

    @ExcelProperty(value = "审核任务状态")
    @ColumnWidth(20)
    @ApiModelProperty("任务状态")
    private String statusName;

    @ExcelProperty(value = "负责SQE")
    @ColumnWidth(20)
    @ApiModelProperty("sqe负责名称")
    private String sqeName;

    @ExcelProperty(value = "计划完成日期")
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd")
    @ApiModelProperty("计划完成日期")
    private Date predictCompleteDate;

    @ExcelProperty(value = "审核结论")
    @ColumnWidth(20)
    @ApiModelProperty("审核结论名称")
    private String auditConclusionName;

    @ExcelProperty(value = "审核得分")
    @ColumnWidth(20)
    @ApiModelProperty("审核得分")
    private String auditScore;

    @ExcelProperty(value = "实际完成日期")
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat( "yyyy-MM-dd")
    @ApiModelProperty("实际完成日期")
    private Date practicalCompleteDate;

    @ExcelProperty(value = "是否逾期")
    @ColumnWidth(20)
    @ApiModelProperty("是否逾期")
    private String isOverdue;

    @ExcelProperty(value = "审核类型大类")
    @ColumnWidth(30)
    @ApiModelProperty("审核类型大类名称")
    private String auditCategoryTypeName;

    @ExcelProperty(value = "审核类型小类")
    @ColumnWidth(30)
    @ApiModelProperty("审核类型小类名称")
    private String auditSubclassTypeName;

    @ExcelProperty(value = "SQE组长")
    @ColumnWidth(20)
    @ApiModelProperty("SQE组长")
    private String sqeMasterName;

    @ExcelProperty(value = "处长/事业部经理")
    @ColumnWidth(30)
    @ApiModelProperty("处长|事业部经理")
    private String auditManagerName;

    @ExcelIgnore
    @ApiModelProperty("审核类型大类")
    private String auditCategoryType;

    @ExcelIgnore
    @ApiModelProperty("审核类型小类")
    private String auditSubclassType;

    @ExcelIgnore
    @ApiModelProperty("审核类型小类")
    private String auditSubclassOther;

    @ExcelIgnore
    @ApiModelProperty("状态编码")
    private String status;

    @ExcelIgnore
    @ApiModelProperty("审核结论")
    private String auditConclusion;

    @ExcelIgnore
    @ApiModelProperty("审核意见")
    private String auditOpinion;

}
