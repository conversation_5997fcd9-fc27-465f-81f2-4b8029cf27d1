package com.weifu.srm.supplier.cio.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description base request
 * @Version 1.0
 */
@Data
@ApiModel(value = "operator request", description = "operator request")
public class OperatorReqDTO {

    @ApiModelProperty(value = "操作人", hidden = true)
    private Long operationBy;

    @ApiModelProperty(value = "操作人名称", hidden = true)
    private String operationByName;


}
