package com.weifu.srm.requirement.service.biz.upload;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.masterdata.response.CostCenterRespDTO;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.masterdata.response.MaterialResultDTO;
import com.weifu.srm.requirement.enums.AnalysisExelFileTypeEnum;
import com.weifu.srm.requirement.enums.PurchaseBizEnum;
import com.weifu.srm.requirement.enums.RecommendSupplierTypeEnum;
import com.weifu.srm.requirement.manager.remote.masterdata.CostCenterManager;
import com.weifu.srm.requirement.manager.remote.masterdata.DictDataManager;
import com.weifu.srm.requirement.manager.remote.masterdata.MaterialManager;
import com.weifu.srm.requirement.manager.remote.user.SysFactoryManager;
import com.weifu.srm.requirement.response.upload.RequirementAnalysisBaseResDTO;
import com.weifu.srm.requirement.response.upload.RequirementAnalysisResultResDTO;
import com.weifu.srm.requirement.utils.AnalysisExelFileUtils;
import com.weifu.srm.user.request.SysFactoryQueryReqDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class RequirementAnalysisExelBiz {
    private final SysFactoryManager sysFactoryManager;
    private final DictDataManager dictDataManager;
    private final MaterialManager materialManager;
    private final CostCenterManager costCenterManager;
    private static final String DICT_CODE = "MEASUREMENT_UNIT";
    private static final String LAG_TYPE = "zh_CN";
    private static final Set<String> RECOMMENDED_SUPPLIER_TYPE_SET = RecommendSupplierTypeEnum.getChineseNameSet();
    private static final Set<String> PURCHASE_BIZ_TYPE_SET = PurchaseBizEnum.getChineseNameSet();

    public RequirementAnalysisResultResDTO analysisExelFile(MultipartFile file, String fileType) {
        AnalysisExelFileTypeEnum fileTypeEnum = AnalysisExelFileTypeEnum.statOf(fileType);
        RequirementAnalysisResultResDTO result = new RequirementAnalysisResultResDTO();
        List<MaterialResultDTO> materialList = new ArrayList<>();
        // 解析导入的文件
        List<String[]> rowList = AnalysisExelFileUtils.analysisExelFile(file, fileTypeEnum);
        Map<String, List<DictDataShowResDTO>> map = dictDataManager.getDictDataList(DICT_CODE, LAG_TYPE);
        List<DictDataShowResDTO> dictDataShowResDTOS = map.get(DICT_CODE);
        Set<String> unitSet = dictDataShowResDTOS.stream()
                .map(DictDataShowResDTO::getDataShowName).collect(Collectors.toSet());

        Map<String, DictDataShowResDTO> dictMap = dictDataShowResDTOS
                .stream().collect(Collectors.toMap(DictDataShowResDTO::getDataShowName, v -> v , (v1,v2) -> v2));

        // 校验导入文件内容
        List<Map<Integer, String>> errorMapList = checkRowList(rowList, fileTypeEnum, materialList, unitSet);

        // 存在未通过的项，则不在封装返回值；
        if(CollUtil.isNotEmpty(errorMapList)){
            result.setErrorMapList(errorMapList);
            result.setStatus(YesOrNoEnum.NO.getCode());
        }else {
            result.setResDTO(packageRowResult(rowList, fileTypeEnum, materialList, dictMap, dictDataShowResDTOS));
            result.setStatus(YesOrNoEnum.YES.getCode());
        }
        log.info("{}", errorMapList);
        return result;
    }

    private List<RequirementAnalysisBaseResDTO> packageRowResult(List<String[]> rowList, AnalysisExelFileTypeEnum fileTypeEnum,
                                                                 List<MaterialResultDTO> materialList, Map<String, DictDataShowResDTO> dictMap,
                                                                 List<DictDataShowResDTO> dictDataShowResDTOS) {
        Map<String, MaterialResultDTO> materialMap = materialList.stream()
                .collect(Collectors.toMap(MaterialResultDTO::getMaterialCode, v -> v));
        Map<String, DictDataShowResDTO> dataValueMap = dictDataShowResDTOS
                .stream().collect(Collectors.toMap(DictDataShowResDTO::getDataValue, v -> v , (v1,v2) -> v2));

        List<RequirementAnalysisBaseResDTO> resultList = new ArrayList<>(rowList.size());
        for (int i = 0; i < rowList.size(); i++) {
            RequirementAnalysisBaseResDTO result = new RequirementAnalysisBaseResDTO();
            result.setCustomerSpecifiedSupplierDesc(CollUtil.newArrayList());
            result.setOtherTechAttachment(CollUtil.newArrayList());
            result.setLogisticsAndPackagingDesc(CollUtil.newArrayList());
            String[] row = rowList.get(i);
            // 赋值公共字段
            int index = 0;
            result.setFactory(row[index++]);
            String materialCode = row[index++];
            result.setMaterialCode(materialCode);
            String materialDesc = row[index++];
            setMaterialDesc(materialCode, result, materialDesc, materialMap, dataValueMap);
            result.setRequirementQty(row[index++]);

            switch (fileTypeEnum) {
                case CRITERION:
                    result.setRequirementDate(row[index++]);
                    String unitDesc = row[index++];
                    if (StringUtils.isEmpty(result.getUnit()) && ObjectUtil.isNotEmpty(unitDesc)) {
                        result.setUnit(dictMap.get(unitDesc).getDataValue());
                        result.setUnitDesc(unitDesc);
                    }
                    result.setRecommendedSupplierName(row[index++]);
                    String recommendedSupplierTypeDesc = row[index++];
                    result.setRecommendedSupplierTypeDesc(recommendedSupplierTypeDesc);
                    result.setRecommendedSupplierType(RecommendSupplierTypeEnum.getSupplierTypeCode(recommendedSupplierTypeDesc));
                    result.setLogisticsDestination(row[index++]);
                    String purchaseBizTypeDesc = row[index++];
                    result.setPurchaseBizTypeDesc(purchaseBizTypeDesc);
                    result.setPurchaseBizType(PurchaseBizEnum.getPurchaseBizCode(purchaseBizTypeDesc));
                    result.setTechContactPerson(row[index++]);
                    result.setPurchaseExpectQty(row[index++]);
                    result.setTargetUntaxedPrice(row[index++]);
                    result.setMaterialType(row[index++]);
                    result.setBatchProductPlm(row[index++]);

                    result.setTrialProductionAppNo(row[index]);
                    break;
                case EXPENSE_ORDER:
                case EXPENSE:
                    setInfo(dictMap, result, row, index);
                    break;
                case INQUIRY:
                    String unitDesc1 = row[index++];
                    if (StringUtils.isEmpty(result.getUnit()) && ObjectUtil.isNotEmpty(unitDesc1)) {
                        result.setUnit(dictMap.get(unitDesc1).getDataValue());
                        result.setUnitDesc(unitDesc1);
                    }
                    result.setRecommendedSupplierName(row[index++]);
                    String recommendedSupplierTypeDesc2 = row[index++];
                    result.setRecommendedSupplierTypeDesc(recommendedSupplierTypeDesc2);
                    result.setRecommendedSupplierType(RecommendSupplierTypeEnum.getSupplierTypeCode(recommendedSupplierTypeDesc2));
                    result.setLogisticsDestination(row[index++]);
                    String purchaseBizTypeDesc2 = row[index++];
                    result.setPurchaseBizTypeDesc(purchaseBizTypeDesc2);
                    result.setPurchaseBizType(PurchaseBizEnum.getPurchaseBizCode(purchaseBizTypeDesc2));
                    result.setTechContactPerson(row[index++]);
                    result.setRelateMaterialCode(row[index++]);
                    String num = row[index++];
                    result.setProductionRequireMthQty(ObjectUtil.isEmpty(num)?null:Integer.valueOf(num));
                    result.setPurchaseExpectQty(row[index++]);
                    result.setTargetUntaxedPrice(row[index++]);
                    result.setMaterialType(row[index++]);
                    result.setBatchProductPlm(row[index++]);
                    result.setRequirementDate(row[index]);
                    break;
            }
            resultList.add(result);
        }

        // 填充额外信息（成本中心描述）
        fillExtendInfo(resultList);
        return resultList;
    }

    private void fillExtendInfo(List<RequirementAnalysisBaseResDTO> resultList) {
        List<String> costCenterCodes = resultList.stream().filter(record->StringUtils.isNotEmpty(record.getCostCenter()))
                .map(RequirementAnalysisBaseResDTO::getCostCenter).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(costCenterCodes)) {
            return;
        }

        List<CostCenterRespDTO> costCenters = costCenterManager.queryCostCenter(costCenterCodes);
        Map<String, String> costCenterMap = costCenters.stream().collect(Collectors.toMap(
                CostCenterRespDTO::getCostCenterCode,
                CostCenterRespDTO::getCostCenterDesc,
                (oldVal, newVal) -> newVal
        ));
        for (RequirementAnalysisBaseResDTO record:resultList) {
            record.setCostCenterDesc(costCenterMap.get(record.getCostCenter()));
        }
    }

    private static void setMaterialDesc(String materialCode, RequirementAnalysisBaseResDTO result, String materialDesc, Map<String, MaterialResultDTO> materialMap, Map<String, DictDataShowResDTO> dataValueMap) {
        if(ObjectUtil.isEmpty(materialCode)){
            result.setMaterialDesc(materialDesc);
        } else {
            MaterialResultDTO material = materialMap.get(materialCode);
            result.setMaterialDesc(material.getMaterialDesc());
            result.setUnit(material.getUnit());
            result.setUnitDesc(dataValueMap.get(material.getUnit()).getDataShowName());
        }
    }

    private void setInfo(Map<String, DictDataShowResDTO> dictMap, RequirementAnalysisBaseResDTO result, String[] row, int index) {
        result.setRequirementDate(row[index++]);
        String unitDesc1 = row[index++];
        if (StringUtils.isEmpty(result.getUnit()) && ObjectUtil.isNotEmpty(unitDesc1)) {
            result.setUnit(dictMap.get(unitDesc1).getDataValue());
            result.setUnitDesc(unitDesc1);
        }
        result.setRecommendedSupplierName(row[index++]);
        String recommendedSupplierTypeDesc = row[index++];
        result.setRecommendedSupplierTypeDesc(recommendedSupplierTypeDesc);
        result.setRecommendedSupplierType(RecommendSupplierTypeEnum.getSupplierTypeCode(recommendedSupplierTypeDesc));
        result.setLogisticsDestination(row[index++]);
        String purchaseBizTypeDesc = row[index++];
        result.setPurchaseBizTypeDesc(purchaseBizTypeDesc);
        result.setPurchaseBizType(PurchaseBizEnum.getPurchaseBizCode(purchaseBizTypeDesc));
        result.setTechContactPerson(row[index++]);
        result.setPurchaseExpectQty(row[index++]);
        result.setTargetUntaxedPrice(row[index++]);
        result.setMaterialType(row[index++]);
        result.setBatchProductPlm(row[index++]);
        result.setCostCenter(row[index++]);
        result.setProjectOrderNo(row[index++]);
        result.setInnerOrderNo(row[index++]);
        result.setTrialProductionAppNo(row[index]);
    }

    // ii.导入时，校验物料号不可以重复；同一个需求中物料号不可重复；同一个项目关联的正式寻源需求物料号不可以重复
    //1、校验必填字段是否填写，具体以各自的模板为准（注意 如物料描述，系统中为必填，但当物料号填写时，导入时可不填，系统自动根据物料号带出）
    //2、数据枚举值校验：工厂，物料号，基本单位，推荐供应商属性，采购业务类型，成本中心 都需要符合系统中字段的枚举值（收货地址待讨论：不校验）
    //3、数据格式校验：样件需求数量（数字），样件需求交付时间（日期），推荐供应商名称，技术联系人，预计年采购量，目标价格（未税），
    // 材质，批产后生命周期（预测），项目订单号（研发类）,内部订单号，试制申请单号 ，关联物料号，每月产能需求，首批样件需求交付时间，
    // 等输入值需要符合系统中对应字段的基本限制，如字数限制，格式限制
    public List<Map<Integer,String>> checkRowList(List<String[]> rowList, AnalysisExelFileTypeEnum fileTypeEnum,
                                                  List<MaterialResultDTO> materialList, Set<String> unitSet){
        // 错误数据记录
        Map<Integer, String> rowMap = new HashMap<>();

        // 物料号去重缓存
        Set<String> materialCodeSet = new HashSet<>();
        // 查询工厂枚举
        List<SysFactoryRespDTO> factoryList = sysFactoryManager.getFactoryList(new SysFactoryQueryReqDTO());
        Set<String> factorySet = factoryList.stream()
                .map(SysFactoryRespDTO::getFactoryCode)
                .collect(Collectors.toSet());
        // 待校验的物料号
        String[] notCheckMaterialCodeSet = new String[rowList.size()];
        // 待校验的成本中心
        String[] notCheckCostCenterSet = new String[rowList.size()];
        // 枚举校验：工厂，物料号，基本单位，推荐供应商属性，采购业务类型，成本中心
        int[] enumCheckIndex = fileTypeEnum.getEnumCheckIndex();

        for (int i = 0; i < rowList.size(); i++) {
            String[] row = rowList.get(i);

            // 尝试转换样件需求交付时间格式
            convertRequirementDateFormat(fileTypeEnum, row);

            String reason = "";
            reason = checkMaterialCode(fileTypeEnum, row, materialCodeSet, reason);

            // 校验必填
            reason = checkNotNull(fileTypeEnum, row, reason);

            // 校验样件需求数量、预计年采购量、目标价格
            reason = checkNumber(fileTypeEnum, row, reason);

            // 枚举校验：工厂，基本单位，推荐供应商属性，采购业务类型
            reason = checkEnumList(fileTypeEnum, row, enumCheckIndex, factorySet, reason, unitSet);
            // 样件需求交付时间格式校验
            reason = checkDataFormat(fileTypeEnum,row,reason);
            // 缓存物料号，成本中心
            cacheMaterialCodeAndCostCenter(enumCheckIndex, notCheckMaterialCodeSet, i, row, notCheckCostCenterSet);

            rowMap.put(i,reason);
        }
        if(-1 == enumCheckIndex[5]){
            notCheckCostCenterSet = null;
        }

        checkMaterialCodeAndCostCenter(notCheckMaterialCodeSet, notCheckCostCenterSet,rowMap, materialList);
        return getErrorMaps(rowMap);
    }

    private String checkNumber(AnalysisExelFileTypeEnum fileTypeEnum, String[] row, String reason) {
        // 校验样件需求数量
        String requirementQty = row[fileTypeEnum.getRequirementQtyIndex()];
        if (StringUtils.isNotEmpty(requirementQty) && !NumberUtil.isNumber(requirementQty)) {
            reason = jointReason(reason, ERROR_TEST[13], ERROR_TEST[10]);
        }

        // 校验预计年采购量
        String purchaseExpectQty = row[fileTypeEnum.getPurchaseExpectQtyIndex()];
        if (StringUtils.isNotEmpty(purchaseExpectQty) && !NumberUtil.isNumber(purchaseExpectQty)) {
            reason = jointReason(reason, ERROR_TEST[14], ERROR_TEST[10]);
        }

        // 校验目标价格
        String targetUntaxedPrice = row[fileTypeEnum.getTargetUntaxedPriceIndex()];
        if (StringUtils.isNotEmpty(targetUntaxedPrice) && !NumberUtil.isNumber(targetUntaxedPrice)) {
            reason = jointReason(reason, ERROR_TEST[15], ERROR_TEST[10]);
        }
        return reason;
    }

    private void convertRequirementDateFormat(AnalysisExelFileTypeEnum fileTypeEnum, String[] row) {
        String requirementDate = row[fileTypeEnum.getRequirementDateIndex()];
        if (!NumberUtil.isNumber(requirementDate)) {
            return;
        }
        Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(Double.valueOf(requirementDate));
        row[fileTypeEnum.getRequirementDateIndex()] = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
    }

    private String checkDataFormat(AnalysisExelFileTypeEnum fileTypeEnum, String[] row, String reason) {
        String requirementDate = row[fileTypeEnum.getRequirementDateIndex()];
        if (StringUtil.isBlank(requirementDate)) {
            return reason;
        }
        try {
            DateUtil.parse(requirementDate, DatePattern.NORM_DATE_PATTERN);
        } catch (Exception e) {
            log.error("date transfer error{}.{}",e.getMessage(),e);
            //提示物料号重复
            reason = jointReason(reason,ERROR_TEST[12],ERROR_TEST[10]);
        }
        return reason;
    }

    private String checkMaterialCode(AnalysisExelFileTypeEnum fileTypeEnum, String[] row, Set<String> materialCodeSet, String reason) {
        String materialCode = row[fileTypeEnum.getMaterialCodeIndex()];
        if(materialCodeSet.contains(materialCode)){
            //提示物料号重复
            reason = jointReason(reason,ERROR_TEST[0],ERROR_TEST[10]);
        }else {
            if(ObjectUtil.isNotEmpty(materialCode)){
                materialCodeSet.add(materialCode);
            }
        }
        return reason;
    }

    private String checkNotNull(AnalysisExelFileTypeEnum fileTypeEnum, Object[] row, String reason) {
        int[] notNullIndex = fileTypeEnum.getNotNullIndex();
        String[] notNullName = fileTypeEnum.getNotNullName();
        for (int j = 0; j < notNullIndex.length; j++) {
            Object notnull = row[notNullIndex[j]];
            if(ObjectUtil.isEmpty(notnull)){
                // 记录为null的角标
                reason = jointReasonByType(reason,ERROR_TEST[1],notNullName[j],ERROR_TEST[9]);
            }
        }
        return reason;
    }

    private void cacheMaterialCodeAndCostCenter(int[] enumCheckIndex, String[] notCheckMaterialCodeSet, int i,
                                                String[] row, String[] notCheckCostCenterSet) {
        // 物料号 单独校验
        if(isCheck(enumCheckIndex[1])){
            // 记录未通过校验的基本单位
            notCheckMaterialCodeSet[i] =  row[enumCheckIndex[1]];
        }

        // 校验成本中心
        if(isCheck(enumCheckIndex[5])){
            // 记录未通过校验的成本中心
            notCheckCostCenterSet[i] =  row[enumCheckIndex[5]];
        }
    }

    private String checkEnumList(AnalysisExelFileTypeEnum fileTypeEnum, String[] row, int[] enumCheckIndex,
                                 Set<String> factorySet, String reason, Set<String> unitSet) {
        // 校验工厂
        reason = oneEnumCell(row[enumCheckIndex[0]],0,factorySet,reason,ERROR_TEST[3]);
        // 物料编码为空时，校验基本单位
        if(StringUtils.isEmpty(row[fileTypeEnum.getMaterialCodeIndex()]) && ObjectUtil.isNotEmpty(row[enumCheckIndex[2]])) {
            reason = oneEnumCell(row[enumCheckIndex[2]],2,unitSet,reason,ERROR_TEST[5]);
        }
        reason = oneEnumCell(row[enumCheckIndex[3]],3,RECOMMENDED_SUPPLIER_TYPE_SET,reason,ERROR_TEST[6]);
        // 校验采购业务类型
        reason = oneEnumCell(row[enumCheckIndex[4]],4,PURCHASE_BIZ_TYPE_SET,reason,ERROR_TEST[7]);
        return reason;
    }

    private String oneEnumCell(Object obj,int enumCheckIndex, Set<String> set, String reason,String type){
        if(isCheck(enumCheckIndex) && ObjectUtil.isNotEmpty(obj) && !set.contains(String.valueOf(obj))){
            // 记录未通过校验的采购业务类型
            reason = jointReasonByType(reason,ERROR_TEST[2],type,ERROR_TEST[9]);
        }
        return reason;
    }

    private boolean isCheck(int i){
        return -1 != i;
    }

    private static List<Map<Integer, String>> getErrorMaps(Map<Integer, String> rowMap) {
        Set<Integer> integers = rowMap.keySet();
        List<Integer> collect = integers.stream().sorted().collect(Collectors.toList());
        List<Map<Integer,String>> errorMapList = new ArrayList<>();
        for (Integer integer : collect) {
            String reason = rowMap.get(integer);
            if(ObjectUtil.isEmpty(reason)){
                continue;
            }
            Map<Integer, String> tempMap = new HashMap<>();
            //tempMap.put(integer, reason);
            tempMap.put(integer + 2, reason);
            errorMapList.add(tempMap);
        }
        return errorMapList;
    }

    private void checkMaterialCodeAndCostCenter(String[] notCheckMaterialCodeSet, String[] notCheckCostCenterSet,
                                                Map<Integer, String> rowMap,  List<MaterialResultDTO> materialList) {
        // 校验的物料号
        ArrayList<String> codeList = CollUtil.newArrayList(new HashSet<>(Arrays.asList(notCheckMaterialCodeSet)));
        codeList.remove(null);
        Set<String> findMaterialCodes = getMaterialCode(codeList, materialList);

        for (int i = 0; i < notCheckMaterialCodeSet.length; i++) {
            String materialCode = notCheckMaterialCodeSet[i];
            if(ObjectUtil.isNotEmpty(materialCode) && !findMaterialCodes.contains(materialCode)){
                // 记录未通过校验的物料号
                String reason = rowMap.get(i);
                if(ObjectUtil.isEmpty(reason)){
                    reason = "";
                }
                reason = jointReasonByType(reason,ERROR_TEST[2],ERROR_TEST[4],ERROR_TEST[9]);
                rowMap.put(i,reason);
            }
        }

        // 校验的成本中心
        if(ObjectUtil.isEmpty(notCheckCostCenterSet)){
            return;
        }
        Set<String> findCostCenterCodeSet = costCenterManager.getCostCenterCodeSet(notCheckCostCenterSet);
        for (int i = 0; i < notCheckCostCenterSet.length; i++) {
            String costCenter = notCheckCostCenterSet[i];
            if(ObjectUtil.isNotEmpty(costCenter) && !findCostCenterCodeSet.contains(costCenter)){
                // 记录未通过校验的成本中心
                String reason = rowMap.get(i);
                if(ObjectUtil.isEmpty(reason)){
                    reason = "";
                }
                reason = jointReasonByType(reason,ERROR_TEST[2],ERROR_TEST[8],ERROR_TEST[9]);
                rowMap.put(i,reason);
            }
        }
    }

    public Set<String> getMaterialCode(List<String> materialCodes, List<MaterialResultDTO> materialList){
        List<MaterialResultDTO> materialResultDTOS = materialManager.listByMaterialCodes(materialCodes);
        if(CollUtil.isEmpty(materialResultDTOS)){
            return CollUtil.newHashSet();
        }
        materialList.addAll(materialResultDTOS);
        return materialResultDTOS.stream().map(MaterialResultDTO::getMaterialCode).collect(Collectors.toSet());
    }

    // ,=9 ;=10
    private static final String[] ERROR_TEST = {"物料号重复","必填字段未填写:","与系统参数不符:","工厂","物料号","基本单位",
            "推荐供应商属性", "采购业务类型","成本中心",",",";\n\r",":","样件需求交付时间格式错误","样件需求数量须为数字",
            "预计年采购量须为数字","目标价格须为数字"};

    private String jointReasonByType(String reason, String type, String value, String jointType){
        if(!reason.contains(type)){
            if(ObjectUtil.isEmpty(reason)){
                reason = type;
            }else {
                reason = reason + ERROR_TEST[10] + type;
            }
        }
        return jointReason(reason,value,jointType);
    }

    private String jointReason(String reason, String value, String jointType){
        if(ObjectUtil.isNotEmpty(reason)){
            if(reason.endsWith(ERROR_TEST[11])){
                reason = reason + value;
            }else {
                reason = reason + jointType + value;
            }
        }else {
            reason = value;
        }
        return reason;
    }
}
