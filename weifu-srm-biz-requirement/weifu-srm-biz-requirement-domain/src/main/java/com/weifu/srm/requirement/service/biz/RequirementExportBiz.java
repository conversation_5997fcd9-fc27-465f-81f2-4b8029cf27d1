package com.weifu.srm.requirement.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.poi.excel.model.PoiSheet;
import com.weifu.srm.common.poi.excel.parser.PoiSheetParser;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.requirement.atomicservice.ProjectMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsPurchasePlanMapperService;
import com.weifu.srm.requirement.atomicservice.impl.RequirementMapperServiceImpl;
import com.weifu.srm.requirement.convert.RequirementConvert;
import com.weifu.srm.requirement.enums.RequirementExportFileTypeEnum;
import com.weifu.srm.requirement.enums.*;
import com.weifu.srm.requirement.enums.project.ProjectSubmitTypeEnum;
import com.weifu.srm.requirement.manager.remote.masterdata.DictDataManager;
import com.weifu.srm.requirement.manager.remote.user.DataPermissionManager;
import com.weifu.srm.requirement.manager.remote.user.SysDivisionManager;
import com.weifu.srm.requirement.manager.remote.user.SysUserManager;
import com.weifu.srm.requirement.po.ProjectPO;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.po.RequirementPartsPurchasePlanPO;
import com.weifu.srm.requirement.request.RequirementQueryPageReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.division.SysDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/14 14:40
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementExportBiz {

    private final SysUserManager sysUserManager;
    private final SysDivisionManager sysDivisionManager;
    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService partsMapperService;
    private final RequirementPartsPurchasePlanMapperService partsPurchasePlanMapperService;
    private final ProjectMapperService projectMapperService;
    private final RequirementConvert requirementConvert;
    private final DataPermissionManager dataPermissionManager;
    private final DictDataManager dictDataManager;

    private static final String PC_INTERNAL_PAGE_REQUIREMENT_LIST = "PC_INTERNAL_PAGE_REQUIREMENT_LIST";
    private static final String DICT_CODE = "MEASUREMENT_UNIT";
    private static final String LAG_TYPE = "zh_CN";


    public List<PoiSheet> exportRequirement(RequirementQueryPageReqDTO reqDTO) {
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(reqDTO.getOperationBy(), PC_INTERNAL_PAGE_REQUIREMENT_LIST);
        if (dataPermission.isNo()) {
            return CollUtil.newArrayList();
        }
        LambdaQueryWrapper<RequirementPO> requirementPOLambdaQueryWrapper
                = RequirementMapperServiceImpl.getRequirementPOLambdaQueryWrapper(reqDTO, dataPermission);

        // 查询需求
        List<RequirementPO> poList = requirementMapperService.list(requirementPOLambdaQueryWrapper);
        if(CollUtil.isEmpty(poList)){
            return CollUtil.newArrayList();
        }

        Set<String> requirementNos = Optional.ofNullable(poList).map(r-> r.stream().map(RequirementPO::getRequirementNo)
                .collect(Collectors.toSet())).orElse(new HashSet<>());

        // 零件需求
        List<RequirementPartsPO> requirementPartsPOList = partsMapperService.lambdaQuery()
                .in(RequirementPartsPO::getRequirementNo, requirementNos)
                .eq(RequirementPartsPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        if(CollUtil.isEmpty(requirementPartsPOList)){
            requirementPartsPOList = CollUtil.newArrayList();
        }
        Set<String> partNoSet = requirementPartsPOList.stream()
                .map(RequirementPartsPO::getRequirementPartsNo).collect(Collectors.toSet());

        // 查询外购建
        Map<String, Long> partsCountMap = requirementPartsPOList.stream()
                .filter(r->!RequirementPartsStatusEnum.RECREATED.getCode().equals(r.getStatus()))
                .collect(Collectors.groupingBy(RequirementPartsPO::getRequirementNo,Collectors.counting()));

        // 查询外购件总成本
        Set<String> projectNoSet = Optional.ofNullable(poList).map(r-> r.stream().map(RequirementPO::getProjectNo)
                .collect(Collectors.toSet())).orElse(new HashSet<>());
        Map<String, ProjectPO> projectMap = new HashMap<>();
        if(CollUtil.isNotEmpty(projectNoSet)){
            List<ProjectPO> projectList = projectMapperService.lambdaQuery()
                    .in(ProjectPO::getProjectNo, projectNoSet).list();
            if(CollUtil.isNotEmpty(projectList)){
                projectMap = projectList.stream()
                        .collect(Collectors.toMap(ProjectPO::getProjectNo, v -> v));
            }
        }
        // 查询事业部/子公司
        Map<String, SysDivisionRespDTO> sysDivisionMap = sysDivisionManager.getSysDivisionMap();

        List<PoiSheet> sheets = new ArrayList<>();
        // 总需求
        List<Object[]> requirementBody = getRequirementList(poList, partsCountMap, projectMap, sysDivisionMap);
        sheets.add(PoiSheetParser.parse(RequirementExportFileTypeEnum.ALL_REQUIREMENT.getSheetName(),
                            null, RequirementExportFileTypeEnum.ALL_REQUIREMENT.getHead(), requirementBody));
        // 零件需求
        List<Object[]> requirementPartBody = getRequirementPartList(requirementPartsPOList);
        sheets.add(PoiSheetParser.parse(RequirementExportFileTypeEnum.MATERIAL_REQUIREMENT.getSheetName(),
                null, RequirementExportFileTypeEnum.MATERIAL_REQUIREMENT.getHead(), requirementPartBody));

        // 零件采购计划
        List<RequirementPartsPurchasePlanPO> purchasePlanPOS = CollUtil.newArrayList();
        if(CollUtil.isNotEmpty(partNoSet)){
            purchasePlanPOS = partsPurchasePlanMapperService.lambdaQuery()
                    .in(RequirementPartsPurchasePlanPO::getRequirementPartsNo, partNoSet)
                    .list();
            if (CollUtil.isEmpty(purchasePlanPOS)){
                purchasePlanPOS = CollUtil.newArrayList();
            }
        }
        List<Object[]> purchasePlanBody = getPurchasePlanList(purchasePlanPOS );
        sheets.add(PoiSheetParser.parse(RequirementExportFileTypeEnum.MATERIAL_PURCHASE_REQUIREMENT.getSheetName(),
                null, RequirementExportFileTypeEnum.MATERIAL_PURCHASE_REQUIREMENT.getHead(), purchasePlanBody));

        return sheets;
    }

    private List<Object[]> getPurchasePlanList(List<RequirementPartsPurchasePlanPO> list ) {
        List<Object[]> reList = new ArrayList<>(list.size());
        for (RequirementPartsPurchasePlanPO po : list) {
            Object[] row = {
                    //"需求编号","零件需求编号","项目启动时间","新供应商准入开始时间","新供应商准入完成间","寻源开始时间",
                    po.getRequirementNo(),
                    po.getRequirementPartsNo(),
                    DateUtil.formatDate(po.getProjectStartPlanTime()),
                    DateUtil.formatDate(po.getNewSupplierAdmissionStartPlanTime()),
                    DateUtil.formatDate(po.getNewSupplierAdmissionEndPlanTime()),
                    DateUtil.formatDate(po.getSourcingStartPlanTime()),
                    //"A样外购件提交时间","商务定点完成时间","首次样件预计交付时间","B样外购件提交时间","OTS外购件提交",
                    DateUtil.formatDate(po.getSampASubmitPlanTime()),
                    DateUtil.formatDate(po.getBusinessDesignatedCompletePlanTime()),
                    DateUtil.formatDate(po.getFirstSampPlanDeliveryPlanTime()),
                    DateUtil.formatDate(po.getSampBSubmitPlanTime()),
                    DateUtil.formatDate(po.getOtsOutsourceSubmitPlanTime()),
                    //"供应商PPAP计划制订完成","供应商PPAP提交","供应商PPAP放行","量产SOP","批产早期控制退出"
                    DateUtil.formatDate(po.getSupplierPpapCompletePlanTime()),
                    DateUtil.formatDate(po.getSupplierPpapSubmitPlanTime()),
                    DateUtil.formatDate(po.getSupplierPpapCompletionPlanTime()),
                    DateUtil.formatDate(po.getManufactureSopPlanTime()),
                    DateUtil.formatDate(po.getEarlyControlExitPlanTime())
            };
            reList.add(row);
        }
        return reList;
    }

    private List<Object[]> getRequirementPartList(List<RequirementPartsPO> list){
        List<Object[]> reList = new ArrayList<>(list.size());
        Map<String, List<DictDataShowResDTO>> map = dictDataManager.getDictDataList(DICT_CODE, LAG_TYPE);
        List<DictDataShowResDTO> dictDataShowResDTOS = map.get(DICT_CODE);
        for (RequirementPartsPO po : list) {
            PurchaseBizEnum purchaseBizEnum = PurchaseBizEnum.getByCode(po.getPurchaseBizType());
            RequirementPartsStatusEnum requirementPartsStatusEnum = RequirementPartsStatusEnum.getByCode(po.getStatus());
            RecommendSupplierTypeEnum recommendSupplierTypeEnum = RecommendSupplierTypeEnum.getByCode(po.getRecommendedSupplierType());
            Object[] row = {
                    // "需求编号","零件需求编号","状态","工厂","物料号","物料描述","一级品类","二级品类","三级品类","采购BP",
                    po.getRequirementNo(),
                    po.getRequirementPartsNo(),
                    requirementPartsStatusEnum == null ? null : requirementPartsStatusEnum.getChineseName(),
                    po.getFactory(),
                    po.getMaterialCode(),
                    po.getMaterialDesc(),
                    po.getCategoryFirstName(),
                    po.getCategorySecondName(),
                    po.getCategoryThirdName(),
                    po.getBpName(),
                    //"品类组长","品类工程师","质量负责人","样件需求数量","样件需求交付时间","基本单位","推荐供应商名称","推荐供应商属性",
                    po.getCpeMasterName(),
                    po.getCpeName(),
                    po.getSqeName(),
                    po.getRequirementQty(),
                    DateUtil.formatDate(po.getRequirementDate()),
                    Optional.ofNullable(dictDataShowResDTOS)
                            .flatMap(r -> r.stream().filter(k -> k.getDataValue().equals(po.getUnit()))
                                    .findFirst().map(DictDataShowResDTO::getDataShowName))
                            .orElse(null),
                    po.getRecommendedSupplierName(),
                    ObjectUtil.isNull(recommendSupplierTypeEnum) ? null : recommendSupplierTypeEnum.getChineseName(),
                    //"收货地址","采购业务类型","技术联系人","关联物料号","每月产能需求","预计年采购量","目标价格（未税）","材质",
                    po.getLogisticsDestination(),
                    ObjectUtil.isEmpty(purchaseBizEnum)?null:purchaseBizEnum.getChineseName(),
                    po.getTechContactPerson(),
                    po.getRelateMaterialCode(),
                    po.getProductionRequireMthQty(),
                    po.getPurchaseExpectQty(),
                    po.getTargetUntaxedPrice(),
                    po.getMaterialType(),
                    //"批产后生命周期（预测）","成本中心","项目订单号（研发类）","内部订单号","试制申请单号","首批样件需求交付时间",
                    po.getBatchProductPlm(),
                    po.getCostCenter(),
                    po.getProjectOrderNo(),
                    po.getInnerOrderNo(),
                    po.getTrialProductionAppNo(),
                    //首批样件需求交付时间
                    DateUtil.formatDate(po.getRequirementDate()),
                    //"推荐品类工程师","推荐质量负责人","需求创建人","需求创建时间","关闭原因","询价单号","订单号"
                    po.getRecommendedCpeName(),
                    po.getRecommendedSqeName(),
                    po.getCreateName(),
                    DateUtil.formatDate(po.getCreateTime()),
                    po.getCloseReason(),
                    po.getInquiryOrderNo(),
                    po.getOrderNo()
            };
            reList.add(row);
        }
        return reList;
    }
    private List<Object[]> getRequirementList(List<RequirementPO> list, Map<String, Long> partsCountMap,
                                              Map<String, ProjectPO> projectMap,
                                              Map<String, SysDivisionRespDTO> sysDivisionMap) {
        List<Object[]> reList = new ArrayList<>(list.size());

        for (RequirementPO po : list) {
            SysDivisionRespDTO sysDivisionRespDTO = sysDivisionMap.get(po.getSubsidiaryCode());
            ProjectPO projectPO = projectMap.get(po.getProjectNo());
            String outsourceTotalTargetCost = getTargetCost(projectPO);
            Object[] row = {
                po.getRequirementNo(),
                po.getRequirementDesc(),
                ObjectUtil.isEmpty(po.getStatus())?"":RequirementStatusEnum.getByCode(po.getStatus()).getChineseName(),
                ObjectUtil.isEmpty(po.getRequirementType())?"":RequirementTypeEnum.getByCode(po.getRequirementType()).getChineseName(),
                ObjectUtil.isEmpty(po.getPurchaseRequirementType())?"":PurchaseRequirementTypeEnum.getByCode(po.getPurchaseRequirementType()).getChineseName(),
                this.getIsName(po.getIsRelateProject()),
                po.getProjectName(),
                ObjectUtil.isEmpty(sysDivisionRespDTO)?"":sysDivisionRespDTO.getDivisionId() + "_" + sysDivisionRespDTO.getDivisionName(),
                po.getPurchaseOrgCode(),
                ObjectUtil.isEmpty(po.getRequirementOriented())?"":RequirementOrientedEnum.getByCode(po.getRequirementOriented()).getChineseName(),
                // 外购件总目标成本
                outsourceTotalTargetCost,
                po.getCustomerName(),
                po.getProductName(),
                po.getProductModel(),
                partsCountMap.get(po.getRequirementNo()),
                ObjectUtil.isEmpty(po.getOperationByRole())?"": ProjectSubmitTypeEnum.statOf(po.getOperationByRole()).getValue(),
                po.getOperationByThreeCategoryName(),
                po.getCreateName(),
                DateUtil.formatDate(po.getCreateTime())
            };
            reList.add(row);
        }
        return reList;
    }

    private static String getTargetCost(ProjectPO projectPO) {
        String outsourceTotalTargetCost;
        if (ObjectUtil.isEmpty(projectPO)) {
            outsourceTotalTargetCost = BigDecimal.ZERO.toString();
        } else {
            if (ObjectUtil.isEmpty(projectPO.getOutsourceTotalTargetCost()))
                outsourceTotalTargetCost = BigDecimal.ZERO.toString();
            else outsourceTotalTargetCost = projectPO.getOutsourceTotalTargetCost().toString();
        }
        return outsourceTotalTargetCost;
    }

    private String getIsName(Integer i){
        if(ObjectUtil.equals(YesOrNoEnum.YES.getCode(), i)){
            return YesOrNoEnum.YES.getDesc();
        }
        return YesOrNoEnum.NO.getDesc();
    }

}
