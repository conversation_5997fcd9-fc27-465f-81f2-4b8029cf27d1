package com.weifu.srm.requirement.enums;

import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import lombok.Getter;

@Getter
public enum TodoTemplateEnum {

    /** 0 零件采购计划延期代办 */
    PURCHASE_PLAN_DELAY_TODO("PURCHASE_PLAN_DELAY_TODO", TodoClsEnum.PART_PROCUREMENT_PLAN_DELAY,
            IconTypeEnum.GENERAL,"零件采购计划延期处理","零件需求编号：${0}，${1}零件采购计划延期了，请录入延期原因！");


    private String key;

    private TodoClsEnum todoClsEnum;

    private IconTypeEnum iconTypeEnum;

    private String title;

    private String content;



    private TodoTemplateEnum(String key, TodoClsEnum todoClsEnum, IconTypeEnum iconTypeEnum, String title, String content) {
        this.key = key;
        this.todoClsEnum = todoClsEnum;
        this.iconTypeEnum = iconTypeEnum;
        this.title = title;
        this.content = content;
    }
}
