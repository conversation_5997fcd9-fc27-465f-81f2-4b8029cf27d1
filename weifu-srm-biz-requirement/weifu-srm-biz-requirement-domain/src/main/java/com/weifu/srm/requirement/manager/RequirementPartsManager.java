package com.weifu.srm.requirement.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.masterdata.response.CostCenterRespDTO;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsDrawingMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.constants.MQProjectTopicConstants;
import com.weifu.srm.requirement.convert.AttachmentRecordConvert;
import com.weifu.srm.requirement.convert.RequirementPartsConvert;
import com.weifu.srm.requirement.enums.*;
import com.weifu.srm.requirement.manager.remote.masterdata.CostCenterManager;
import com.weifu.srm.requirement.manager.remote.masterdata.DictDataManager;
import com.weifu.srm.requirement.manager.remote.user.SysDivisionManager;
import com.weifu.srm.requirement.manager.remote.user.SysFactoryManager;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsDrawingPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.response.RequirementPartsQueryByNoRespDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import com.weifu.srm.user.response.division.SysDivisionFindByDivisionResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/12 22:03
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementPartsManager {

    public static final String RELEASE_TYPE_INQUIRY_ORDER = "INQUIRY_ORDER"; // 释放类型-询价单
    public static final String RELEASE_TYPE_SAMPLE_ORDER = "SAMPLE_ORDER"; // 释放类型-样件订单
    public static final String OCCUPY_TYPE_INQUIRY_ORDER = "INQUIRY_ORDER"; // 占用类型-询价单
    public static final String OCCUPY_TYPE_SAMPLE_ORDER = "SAMPLE_ORDER"; // 占用类型-样件订单

    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementPartsConvert partsConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final RequirementManager requirementManager;
    private final SysFactoryManager factoryManager;
    private final DictDataManager dictDataManager;
    private final SysDivisionManager sysDivisionManager;
    private final CostCenterManager costCenterManager;
    private final RequirementPartsDrawingMapperService requirementPartsDrawingMapperService;
    private final MqManager mqManager;
    private final AttachmentRecordManager attachmentRecordManager;
    private final AttachmentRecordConvert attachmentRecordConvert;

    private static final String DICT_CODE = "MEASUREMENT_UNIT";
    private static final String LAG_TYPE = "zh_CN";

    public RequirementPartsQueryByNoRespDTO queryRequirementPartsByNo(String requirementPartsNo) {
        RequirementPartsPO partsPO = requirementPartsMapperService.lambdaQuery()
                .eq(RequirementPartsPO::getRequirementPartsNo,requirementPartsNo).one();
        if (ObjectUtil.isNull(partsPO)) {
           throw new BizFailException("this requirement parts does not exists");
        }
        RequirementPartsQueryByNoRespDTO respDTO = partsConvert.toDTO(partsPO);

        // 补充非图纸附件
        List<String> partNos = List.of(respDTO.getRequirementPartsNo());
        if (!CollUtil.isEmpty(partNos)) {
            List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.getAttachments(partNos);
            //设置附件
            requirementManager.setAttachments(List.of(respDTO), attachmentRecordPOList);
        }

        // 补充图纸附件
        if (respDTO.getRequirementDrawingId() != null) {
            RequirementPartsDrawingPO drawing = requirementPartsDrawingMapperService.lambdaQuery()
                    .eq(RequirementPartsDrawingPO::getId, respDTO.getRequirementDrawingId()).one();
            if (drawing != null) {
                respDTO.setDrawingNo(drawing.getDrawingNo());
                respDTO.setDrawingVersion(drawing.getDrawingVersion());
                respDTO.setDrawingUrl(drawing.getDrawingUrl());
                respDTO.setDrawingName(drawing.getDrawingName());
            }
        }

        //设置工厂名称
        List<String> factory = List.of(respDTO.getFactory());
        if (!CollUtil.isEmpty(factory)) {
            List<SysFactoryRespDTO> factoryRespDTOS = factoryManager.listByFactory(factory);
            respDTO.setFactoryName(Optional.ofNullable(factoryRespDTOS)
                    .flatMap(k -> k.stream()
                            .filter(j -> !StringUtil.isNullOrEmpty(respDTO.getFactory())
                                    && respDTO.getFactory().equals(j.getFactoryCode()))
                            .findFirst().map(SysFactoryRespDTO::getFactoryName))
                    .orElse(null));
        }
        //设置成本中心名称
        if (!StringUtil.isNullOrEmpty(respDTO.getCostCenter())) {
            List<CostCenterRespDTO> costCenters = costCenterManager.queryCostCenter(List.of(respDTO.getCostCenter()));
            respDTO.setCostCenterDesc(Optional.ofNullable(costCenters).flatMap(r -> r.stream().findFirst()
                    .map(CostCenterRespDTO::getCostCenterDesc)).orElse(null));
        }
        //设置单位
        Map<String, List<DictDataShowResDTO>> respMap = dictDataManager.getDictDataList(DICT_CODE,LAG_TYPE);
        List<DictDataShowResDTO> showResDTOS = respMap.get(DICT_CODE);
        respDTO.setUnitDesc(Optional.ofNullable(showResDTOS)
                .flatMap(r -> r.stream().filter(j -> respDTO.getUnit().equals(j.getDataValue()))
                        .findFirst().map(DictDataShowResDTO::getDataShowName))
                .orElse(null));
        //设置事业部名称
        //设置头部信息
        RequirementPO requirementPO = requirementMapperService.lambdaQuery()
                .eq(RequirementPO::getRequirementNo,partsPO.getRequirementNo())
                .eq(RequirementPO::getIsDelete,YesOrNoEnum.NO.getCode()).one();
        respDTO.setRequirementType(requirementPO.getRequirementType());
        respDTO.setPurchaseRequirementType(requirementPO.getPurchaseRequirementType());
        respDTO.setIsRelateProject(requirementPO.getIsRelateProject());
        respDTO.setProjectName(requirementPO.getProjectName());
        respDTO.setPurchaseOrgCode(requirementPO.getPurchaseOrgCode());
        respDTO.setSubsidiaryCode(requirementPO.getSubsidiaryCode());
        respDTO.setOriRequirementNo(requirementPO.getOriRequirementNo());
        respDTO.setRequirementDesc(requirementPO.getRequirementDesc());
        respDTO.setCustomerName(requirementPO.getCustomerName());
        respDTO.setProjectId(requirementPO.getProjectId());
        respDTO.setProjectNo(requirementPO.getProjectNo());
        respDTO.setProductName(requirementPO.getProductName());
        respDTO.setProductModel(requirementPO.getProductModel());
        respDTO.setRequirementRemark(requirementPO.getRemark());

        // 补充总需求上的BOM附件
        List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordManager
                .getAttachments(requirementPO.getRequirementNo(), AttachmentRecordEnum.REQUIREMENT_BOM_ATTACHMENT.getKey());
        Optional.ofNullable(attachmentRecordPOList)
                .map(attachmentRecordConvert::toDTOs)
                .ifPresent(respDTO::setBomAttachment);

        if (!StringUtil.isNullOrEmpty(requirementPO.getSubsidiaryCode())) {
            SysDivisionFindByDivisionResDTO divisionResDTO = sysDivisionManager.findByDivisionId(requirementPO.getSubsidiaryCode());
            Optional.ofNullable(divisionResDTO).ifPresent(r-> respDTO.setSubsidiaryName(r.getDivisionId()+r.getDivisionName()));
        }
        PurchaseBizEnum purchaseBizEnum = PurchaseBizEnum.getByCode(respDTO.getPurchaseBizType());
        Optional.ofNullable(purchaseBizEnum).ifPresent(k-> respDTO.setPurchaseBizTypeDesc(k.getChineseName()));
        RecommendSupplierTypeEnum recommendSupplierTypeEnum = RecommendSupplierTypeEnum.getByCode(respDTO.getRecommendedSupplierType());
        Optional.ofNullable(recommendSupplierTypeEnum).ifPresent(k-> respDTO.setRecommendedSupplierTypeDesc(k.getChineseName()));
        RequirementTypeEnum requirementTypeEnum = RequirementTypeEnum.getByCode(respDTO.getRequirementType());
        Optional.ofNullable(requirementTypeEnum).ifPresent(j-> respDTO.setRequirementTypeDesc(j.getChineseName()));
        PurchaseRequirementTypeEnum purchaseRequirementTypeEnum = PurchaseRequirementTypeEnum.getByCode(respDTO.getPurchaseRequirementType());
        Optional.ofNullable(purchaseRequirementTypeEnum).ifPresent(j-> respDTO.setPurchaseRequirementTypeDesc(j.getChineseName()));
        RequirementPartsStatusEnum statusEnum = RequirementPartsStatusEnum.getByCode(respDTO.getStatus());
        Optional.ofNullable(statusEnum).ifPresent(j-> respDTO.setStatusDesc(j.getChineseName()));
        return respDTO;
    }

    /**
     * 计划当前最大的零件需求编号
     */
    public Integer calcCurrentMaxNo(List<RequirementPartsPO> oldParts) {
        if (CollectionUtils.isEmpty(oldParts)) {
            return 0;
        }

        Integer maxNo = oldParts.stream()
                .map(parts -> {
                    String partNo = parts.getRequirementPartsNo();
                    int underscoreIndex = partNo.indexOf('_');
                    if (underscoreIndex != -1) {
                        // 截取下划线之后的部分
                        return partNo.substring(underscoreIndex + 1);
                    }
                    return null; // 或者抛出异常处理
                })
                .filter(ObjectUtil::isNotNull) // 过滤掉可能的 null 值
                .map(Integer::parseInt) // 转换为 Integer
                .max(Comparator.naturalOrder()).orElse(0); // 求最大值;
        return maxNo;
    }

    /**
     * 解除零件需求与图纸关系
     */
    public void clearOldPartDrawingRel(List<RequirementPartsPO> oldParts) {
        List<Long> drawingIds = new ArrayList<>();
        for (RequirementPartsPO part:oldParts) {
            if (part.getRequirementDrawingId() != null) {
                drawingIds.add(part.getRequirementDrawingId());
            }
        }

        if (CollectionUtils.isEmpty(drawingIds)) {
            return;
        }

        LambdaUpdateWrapper<RequirementPartsDrawingPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(RequirementPartsDrawingPO::getRequirementPartsNo, null);
        wrapper.in(RequirementPartsDrawingPO::getId, drawingIds);
        requirementPartsDrawingMapperService.update(wrapper);
    }

    /**
     * 发送零件需求状态变化MQ
     */
    public void sendStatusChangedMq(RequirementPartsPO oldPart, String status, Date current, Long operateBy, String operateName) {
        // 构建发送状态变化MQ
        RequirementPartStatusChangedMq statusChangedMq = buildStatusChangedMq(oldPart, status, current, operateBy, operateName);
        // 发送状态变化MQ
        sendStatusChangedMq(statusChangedMq);
    }

    /**
     * 构建发送状态变化MQ
     */
    public RequirementPartStatusChangedMq buildStatusChangedMq(RequirementPartsPO oldPart, String status, Date current, Long operateBy, String operateName) {
        RequirementPartStatusChangedMq statusChangedMq = new RequirementPartStatusChangedMq();
        statusChangedMq.setRequirementPartsNo(oldPart.getRequirementPartsNo());
        statusChangedMq.setPreStatus(oldPart.getStatus());
        statusChangedMq.setStatus(status);
        statusChangedMq.setChangeTime(current);
        statusChangedMq.setOperateBy(operateBy);
        statusChangedMq.setOperateName(operateName);
        return statusChangedMq;
    }

    /**
     * 发送状态变化MQ
     */
    public void sendStatusChangedMq(RequirementPartStatusChangedMq statusChangedMq) {
        mqManager.sendTopic(MQProjectTopicConstants.REQUIREMENT_PARTS_STATUS_CHANGED, JacksonUtil.bean2Json(statusChangedMq));
    }

    /**
     * 占用零件需求，返回状态发生变化的零件需求
     */
    public List<RequirementPartsPO> occupy(List<String> partNos, String occupyType,
                                           Date current, Long operateBy, String operateName,
                                           String inquiryOrderNo, String sampleOrderNo) {
        if (CollectionUtils.isEmpty(partNos)) {
            return new ArrayList<>();
        }

        List<RequirementPartsPO> oldParts = requirementPartsMapperService.listByNo(partNos,
                List.of(RequirementPartsStatusEnum.PENDING.getCode(), RequirementPartsStatusEnum.PROCESSING.getCode()));

        // 设置零件需求上的询价单号或设置零件需求上的样件订单号
        List<Long> ids = oldParts.stream().map(RequirementPartsPO::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<RequirementPartsPO> updateWrapper = Wrappers.lambdaUpdate(RequirementPartsPO.class)
                .in(RequirementPartsPO::getId, ids)
                .set(RequirementPartsPO::getUpdateTime, current)
                .set(RequirementPartsPO::getUpdateBy, operateBy)
                .set(RequirementPartsPO::getUpdateName, operateName);
        if (OCCUPY_TYPE_INQUIRY_ORDER.equals(occupyType)) {
            updateWrapper.set(RequirementPartsPO::getInquiryOrderNo, inquiryOrderNo);
        } else if (OCCUPY_TYPE_SAMPLE_ORDER.equals(occupyType)) {
            updateWrapper.set(RequirementPartsPO::getOrderNo, sampleOrderNo);
        }
        requirementPartsMapperService.update(updateWrapper);

        // 计算出会改变状态的零件需求
        Map<String, RequirementPO> requirementMap = requirementManager.queryRequirementMap(oldParts);
        List<RequirementPartsPO> statusChangeParts = new ArrayList<>();
        List<Long> statusChangePartIds = new ArrayList<>();
        for (RequirementPartsPO oldPart:oldParts) {
            RequirementPO requirement = requirementMap.get(oldPart.getRequirementNo());
            String requirementType = requirement.getRequirementType();
            // 判断是否可以从“待执行”前进至“执行中”
            if (canChangeToProcessing(oldPart, requirementType, occupyType)) {
                statusChangeParts.add(oldPart);
                statusChangePartIds.add(oldPart.getId());
            }
        }

        // 更新零件需求状态
        if (CollectionUtils.isEmpty(statusChangeParts)) {
            return statusChangeParts;
        }
        requirementPartsMapperService.lambdaUpdate()
                .in(RequirementPartsPO::getId, statusChangePartIds)
                .set(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.PROCESSING.getCode())
                .set(RequirementPartsPO::getUpdateTime, current)
                .set(RequirementPartsPO::getUpdateBy, operateBy)
                .set(RequirementPartsPO::getUpdateName, operateName)
                .update();
        return statusChangeParts;
    }

    /**
     * 计算零件需求是否可以从“待执行”前进至“执行中”
     * 1.寻源零件需求：
     *  1）被询价单占用
     * 2.采购零件需求
     *  1）被询价单、样件订单任一占用
     *
     * @param occupyType 占用类型（询价单：INQUIRY_ORDER；样件订单：SAMPLE_ORDER）
     */
    private boolean canChangeToProcessing(RequirementPartsPO part, String requirementType, String occupyType) {
        // 已经处于执行中的，无需改变状态
        if (RequirementPartsStatusEnum.PROCESSING.equalsCode(part.getStatus())) {
            return false;
        }

        if (RequirementTypeEnum.SOURCING.equalsCode(requirementType)) {
            if (OCCUPY_TYPE_INQUIRY_ORDER.equals(occupyType)) {
                return true;
            }
        } else if (RequirementTypeEnum.PURCHASE.equalsCode(requirementType)) {
            return true;
        }
        return false;
    }

    /**
     * 释放需求
     */
    public List<RequirementPartsPO> releaseByNo(List<String> partNos, String releaseType,
                                            Date current, Long operateBy, String operateName) {
        if (CollectionUtils.isEmpty(partNos)) {
            return new ArrayList<>();
        }
        List<RequirementPartsPO> oldParts = requirementPartsMapperService.listByNo(partNos);
        return release(oldParts, releaseType, current, operateBy, operateName);
    }
    
    /**
     * 询价单、样件订单释放零件需求，返回发生状态变化的零件需求
     *
     * 释放类型为询价单时
     * 1.清除零件需求上的询价单号
     * 2.将零件需求状态从“执行中”改为“待执行”
     *   1）寻源零件需求是直接修改
     *   2）采购零件需求需要看样件订单的占用情况
     *
     * 释放类型为样件订单时
     * 1.清除零件需求上的样件订单号
     * 2.将零件需求状态从“执行中”改为“待执行”
     *   1）寻源零件需求状态不变
     *   2）采购零件需求需要看询价单的占用情况
     */
    public List<RequirementPartsPO> release(List<RequirementPartsPO> oldParts, String releaseType,
                                            Date current, Long operateBy, String operateName) {
        if (CollectionUtils.isEmpty(oldParts)) {
            return new ArrayList<>();
        }

        // 清空零件需求上的询价单号或清空零件需求上的样件订单号
        List<Long> ids = oldParts.stream().map(RequirementPartsPO::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<RequirementPartsPO> updateWrapper = Wrappers.lambdaUpdate(RequirementPartsPO.class)
                .in(RequirementPartsPO::getId, ids)
                .set(RequirementPartsPO::getUpdateTime, current)
                .set(RequirementPartsPO::getUpdateBy, operateBy)
                .set(RequirementPartsPO::getUpdateName, operateName);
        if (RELEASE_TYPE_INQUIRY_ORDER.equals(releaseType)) {
            updateWrapper.set(RequirementPartsPO::getInquiryOrderNo, null);
        } else if (RELEASE_TYPE_SAMPLE_ORDER.equals(releaseType)) {
            updateWrapper.set(RequirementPartsPO::getOrderNo, null);
        }
        requirementPartsMapperService.update(updateWrapper);

        // 计算出会改变状态的零件需求
        Map<String, RequirementPO> requirementMap = requirementManager.queryRequirementMap(oldParts);
        List<RequirementPartsPO> statusChangeParts = new ArrayList<>();
        List<Long> statusChangePartIds = new ArrayList<>();
        for (RequirementPartsPO oldPart:oldParts) {
            RequirementPO requirement = requirementMap.get(oldPart.getRequirementNo());
            String requirementType = requirement.getRequirementType();
            // 判断是否可以退回到“待执行”状态
            if (canChangeToPending(oldPart, requirementType, releaseType)) {
                statusChangeParts.add(oldPart);
                statusChangePartIds.add(oldPart.getId());
            }
        }

        // 更新零件需求状态
        if (CollectionUtils.isEmpty(statusChangeParts)) {
            return statusChangeParts;
        }
        requirementPartsMapperService.lambdaUpdate()
                .in(RequirementPartsPO::getId, statusChangePartIds)
                .set(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.PENDING.getCode())
                .set(RequirementPartsPO::getUpdateTime, current)
                .set(RequirementPartsPO::getUpdateBy, operateBy)
                .set(RequirementPartsPO::getUpdateName, operateName)
                .update();
        return statusChangeParts;
    }

    /**
     * 计算零件需求是否可以从“执行中”回退至“待执行”
     * 1.寻源零件需求：没有被询价单占用则退回
     *  1）只要释放类型是询价单，则退回
     * 2.采购零件需求：同时没有被询价单、样件订单占用，则退回
     *  1）释放类型是询价单时，判断占用的样件订单号是否为空
     *  2）释放类型是样件订单时，判断占用的询价单号是否为空
     *
     * @param releaseType 释放类型（询价单：INQUIRY_ORDER；样件订单：SAMPLE_ORDER）
     */
    private boolean canChangeToPending(RequirementPartsPO part, String requirementType, String releaseType) {
        if (RequirementTypeEnum.SOURCING.equalsCode(requirementType)) {
            if (RELEASE_TYPE_INQUIRY_ORDER.equals(releaseType)) {
                return true;
            }
        } else if (RequirementTypeEnum.PURCHASE.equalsCode(requirementType)) {
            if (RELEASE_TYPE_INQUIRY_ORDER.equals(releaseType)) {
                return StringUtils.isEmpty(part.getOrderNo());
            } else if (RELEASE_TYPE_SAMPLE_ORDER.equals(releaseType)) {
                return StringUtils.isEmpty(part.getInquiryOrderNo());
            }
        }
        return false;
    }

}