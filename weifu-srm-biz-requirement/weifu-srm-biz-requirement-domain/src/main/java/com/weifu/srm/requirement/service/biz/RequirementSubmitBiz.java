package com.weifu.srm.requirement.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.mq.CreateTodoMQ;
import com.weifu.srm.integration.api.SAPApi;
import com.weifu.srm.integration.request.sap.MaterialSlowFlowStatusCheckReqDTO;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.api.MaterialApi;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.request.CategoryUserQueryReqDTO;
import com.weifu.srm.masterdata.response.*;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.requirement.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsDrawingMapperService;
import com.weifu.srm.requirement.atomicservice.RequirementPartsMapperService;
import com.weifu.srm.requirement.constants.RequirementBizConstants;
import com.weifu.srm.requirement.convert.RequirementConvert;
import com.weifu.srm.requirement.convert.RequirementPartsConvert;
import com.weifu.srm.requirement.enums.*;
import com.weifu.srm.requirement.enums.project.ProjectSubmitTypeEnum;
import com.weifu.srm.requirement.enums.requirement.RequirementToDoTemplateEnum;
import com.weifu.srm.requirement.manager.RequirementManager;
import com.weifu.srm.requirement.manager.RequirementPartsManager;
import com.weifu.srm.requirement.manager.remote.category.CategoryManager;
import com.weifu.srm.requirement.manager.remote.masterdata.MaterialManager;
import com.weifu.srm.requirement.manager.remote.user.SysDivisionManager;
import com.weifu.srm.requirement.manager.remote.user.SysUserManager;
import com.weifu.srm.requirement.po.AttachmentRecordPO;
import com.weifu.srm.requirement.po.RequirementPO;
import com.weifu.srm.requirement.po.RequirementPartsDrawingPO;
import com.weifu.srm.requirement.po.RequirementPartsPO;
import com.weifu.srm.requirement.request.RequirementPartsSaveReqDTO;
import com.weifu.srm.requirement.request.RequirementSaveReqDTO;
import com.weifu.srm.user.api.SysDivisionApi;
import com.weifu.srm.user.api.SysFactoryApi;
import com.weifu.srm.user.response.BaseGroupedSysUserRespDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import com.weifu.srm.user.response.division.SysDivisionFindByDivisionResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/14 14:40
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementSubmitBiz {

    private static final String COMMA = ",";

    private final LocaleMessage localeMessage;
    private final RequirementConvert requirementConvert;
    private final RequirementPartsConvert partsConvert;
    private final SysUserManager sysUserManager;
    private final RequirementManager requirementManager;
    private final RequirementPartsManager requirementPartsManager;
    private final SysDivisionManager sysDivisionManager;
    private final SysDivisionApi sysDivisionApi;
    private final CategoryManager categoryManager;
    private final MqManager mqManager;
    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementPartsDrawingMapperService requirementPartsDrawingMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final MaterialApi materialApi;
    private final CategoryApi categoryApi;
    private final SysFactoryApi sysFactoryApi;
    private final SAPApi sapApi;
    private final MaterialManager materialManager;
    private final TransactionTemplate transactionTemplate;
    
    private static final String REQUIREMENT_CATEGORY = "requirement.submit.material.no.relate.category";

    public String execute(RequirementSaveReqDTO req) {
        log.info("提交总需求开始，operationBy={}，requirementNo={}，req={}", req.getOperationBy(), req.getRequirementNo(), JacksonUtil.bean2Json(req));

        // 以品类管理员、品类组长角色提交时，转换出品类编码
        if (ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.equalsKey(req.getOperationByRole())
                || ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.equalsKey(req.getOperationByRole())) {
            CategoryResultDTO category = categoryManager.categoryIdToCategoryCode(req.getOperationByThreeCategory());
            if (category != null) {
                req.setOperationByThreeCategoryCode(category.getCategoryCode());
                req.setOperationByThreeCategoryName(category.getCategoryName());
            }
        }

        // 校验
        checkParam(req);

        String requirementNo = null;
        if (StringUtils.isEmpty(req.getRequirementNo())) {
            requirementNo = addRequirement(req);
        } else {
            requirementNo = updateRequirement(req);
        }
        log.info("提交总需求完成，operationBy={}，requirementNo={}", req.getOperationBy(), req.getRequirementNo());
        return requirementNo;
    }

    private String addRequirement(RequirementSaveReqDTO req) {
        String requirementNo = requirementManager.generateRequirementNo();
        req.setRequirementNo(requirementNo);

        Date current = new Date();
        RequirementPO requirement = requirementConvert.toRequirementEntity(req);
        requirement.setRequirementNo(requirementNo);
        requirement.setStatus(RequirementStatusEnum.APPROVING.getCode());
        requirement.setSubmitTime(current);
        BaseEntityUtil.setCommon(requirement, req.getOperationBy(), req.getOperationByName(), current);

        Map<String, CategoryWithAllLevelDTO> materialToCategoryMap = materialManager.materialToCategory(req.getRequirementParts());

        AtomicInteger counter = new AtomicInteger(1);
        List<RequirementPartsPO> partList = new ArrayList<>();
        for (RequirementPartsSaveReqDTO partReq:req.getRequirementParts()) {
            partReq.setRequirementNo(requirementNo);
            partReq.setRequirementPartsNo(requirementNo+"_"+counter.getAndIncrement());
            partReq.setStatus(RequirementPartsStatusEnum.APPROVING.getCode());

            RequirementPartsPO part = partsConvert.toRequirementEntity(partReq);
            part.setBpId(req.getBpId());
            part.setBpName(req.getBpName());
            BaseEntityUtil.setCommon(part, req.getOperationBy(), req.getOperationByName(), current);
            partList.add(part);

            // 补充物料行的品类信息
            String materialCode = partReq.getMaterialCode();
            if (StringUtils.isNotEmpty(materialCode)) {
                CategoryWithAllLevelDTO category = materialToCategoryMap.get(materialCode);
                if (category != null) {
                    part.setCategoryFirst(category.getOneLevelCategoryCode());
                    part.setCategoryFirstName(category.getOneLevelCategoryName());
                    part.setCategorySecond(category.getThreeLevelCategoryCode());
                    part.setCategorySecondName(category.getTwoLevelCategoryName());
                    part.setCategoryThird(category.getThreeLevelCategoryCode());
                    part.setCategoryThirdName(category.getThreeLevelCategoryName());
                }
            }
        }

        List<AttachmentRecordPO> attachmentRecords = requirementManager.buildAttachment(req, req.getRequirementNo(), current);
        transactionTemplate.execute(r->{
            requirementMapperService.save(requirement); // 保存总需求
            requirementPartsMapperService.saveBatch(partList); // 保存零件需求
            attachmentRecordMapperService.saveBatch(attachmentRecords); // 保存附件
            // 绑定零件需求与图纸的关系
            requirementPartsDrawingMapperService.updateBatchById(buildPartsDrawingUpdate(req, partList, current));
            // 发送创建工单MQ
            sendCreateTicketMq(req);
            return null;
        });
        return requirementNo;
    }

    private String updateRequirement(RequirementSaveReqDTO req) {
        RequirementPO requirement = requirementMapperService.getByNo(req.getRequirementNo());

        Date current = new Date();
        BeanUtil.copyProperties(req, requirement);
        requirement.setStatus(RequirementStatusEnum.APPROVING.getCode());
        requirement.setSubmitTime(current);
        BaseEntityUtil.setCommonForU(requirement, req.getOperationBy(), req.getOperationByName(), current);

        Map<String, CategoryWithAllLevelDTO> materialToCategoryMap = materialManager.materialToCategory(req.getRequirementParts());

        List<RequirementPartsPO> oldParts = requirementPartsMapperService.listByRequirementNo(req.getRequirementNo());

        // 查找最大的零件需求编号
        Integer maxNo = requirementPartsManager.calcCurrentMaxNo(oldParts);
        AtomicInteger counter = new AtomicInteger(maxNo + 1);
        List<RequirementPartsPO> partList = new ArrayList<>();
        for (RequirementPartsSaveReqDTO partReq:req.getRequirementParts()) {
            partReq.setRequirementNo(req.getRequirementNo());
            partReq.setStatus(RequirementPartsStatusEnum.APPROVING.getCode());
            if (StringUtil.isNullOrEmpty(partReq.getRequirementPartsNo())) {
                partReq.setRequirementPartsNo(req.getRequirementNo() + "_" + counter.getAndIncrement());
            }

            RequirementPartsPO part = partsConvert.toRequirementEntity(partReq);
            part.setBpId(req.getBpId());
            part.setBpName(req.getBpName());
            BaseEntityUtil.setCommon(part, req.getOperationBy(), req.getOperationByName(), current);
            partList.add(part);

            // 补充物料行的品类信息
            String materialCode = partReq.getMaterialCode();
            if (StringUtils.isNotEmpty(materialCode)) {
                CategoryWithAllLevelDTO category = materialToCategoryMap.get(materialCode);
                if (category != null) {
                    part.setCategoryFirst(category.getOneLevelCategoryCode());
                    part.setCategoryFirstName(category.getOneLevelCategoryName());
                    part.setCategorySecond(category.getThreeLevelCategoryCode());
                    part.setCategorySecondName(category.getTwoLevelCategoryName());
                    part.setCategoryThird(category.getThreeLevelCategoryCode());
                    part.setCategoryThirdName(category.getThreeLevelCategoryName());
                }
            }
        }

        CreateTodoMQ createTodoMq = buildCreateTodoMq(req, requirement);
        transactionTemplate.execute(r -> {
            // 更新总需求
            requirementMapperService.updateById(requirement);
            if (!(ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.equalsKey(req.getOperationByRole())
                || ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.equalsKey(req.getOperationByRole()))) {
                // 非品类管理员、品类组长提交时，清空总需求上的品类信息
                requirementMapperService.lambdaUpdate()
                        .eq(RequirementPO::getId, requirement.getId())
                        .set(RequirementPO::getOperationByThreeCategory, null)
                        .set(RequirementPO::getOperationByThreeCategoryCode, null)
                        .set(RequirementPO::getOperationByThreeCategoryName, null)
                        .update();
            }

            // 保存新的零件需求
            requirementPartsMapperService.deleteByRequirementNo(req.getRequirementNo());
            requirementPartsMapperService.saveBatch(partList);

            // 清除旧总需求附件、旧零件需求附件（其他技术附件、客户指定供应商说明、物流及包装说明）
            // 保存新总需求附件、新零件需求附件
            requirementManager.refreshAttachment(req, requirement.getRequirementNo(), oldParts, current);

            // 解除原有零件需求与图纸关系并建立新零件需求与图纸关系（最终受影响零件需求为：已删除以及做过修改的）
            requirementPartsManager.clearOldPartDrawingRel(oldParts);
            requirementPartsDrawingMapperService.updateBatchById(buildPartsDrawingUpdate(req, partList, current));

            // 发送创建工单MQ
            sendCreateTicketMq(req);
            return null;
        });
        return req.getRequirementNo();
    }

    private List<RequirementPartsDrawingPO> buildPartsDrawingUpdate(RequirementSaveReqDTO req, List<RequirementPartsPO> partList, Date current) {
        List<RequirementPartsDrawingPO> updateDraingList = new ArrayList<>();
        for (RequirementPartsPO part:partList) {
            RequirementPartsDrawingPO updateDrawing = new RequirementPartsDrawingPO();
            updateDrawing.setId(part.getRequirementDrawingId());
            updateDrawing.setRequirementPartsNo(part.getRequirementPartsNo());
            BaseEntityUtil.setCommonForU(updateDrawing, req.getOperationBy(), req.getOperationByName(), current);
            updateDraingList.add(updateDrawing);
        }
        return updateDraingList;
    }

    /**
     * 发送创建工单MQ
     */
    private void sendCreateTicketMq(RequirementSaveReqDTO req) {
        List<CreateTicketMQ.ApprovalProcessVar> processVars = getApprovalProcessVars(req);

        RequirementTypeEnum requirementTypeEnum = RequirementTypeEnum.getByCode(req.getRequirementType());
        String submitDesc = RequirementBizConstants.SUBMIT_DESC
                .replace(RequirementBizConstants.REQUIREMENT_DESC_RP, req.getRequirementDesc())
                .replace(RequirementBizConstants.REQUIREMENT_TYPE_RP, requirementTypeEnum.getChineseName());

        CreateTicketMQ message = new CreateTicketMQ();
        message.setBusinessNo(req.getRequirementNo());
        message.setTicketType(TicketTypeEnum.PROCUREMENT_SOURCING_REQUIREMENT.getCode());
        message.setSubmitBy(req.getOperationBy());
        message.setSubmitName(req.getOperationByName());
        message.setSubmitDesc(submitDesc);
        message.setProcessVars(processVars);
        mqManager.sendTopic(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(message));
    }

    /**
     * 构建创建待办MQ
     */
    private CreateTodoMQ buildCreateTodoMq(RequirementSaveReqDTO req, RequirementPO requirement) {
        Long operationBy = requirement.getOperationBy();
        BaseSysUserRespDTO user = sysUserManager.getUserDetailById(operationBy);

        String content = RequirementToDoTemplateEnum.PART_REQUIREMENT_SYSTEM_DISTRIBUTION.getContent();
        content = content.replace("${applicant}", user.getRealName() + "（" + user.getDomain() + "）")
                .replace("${requirementNo}", requirement.getRequirementNo())
                .replace("${num}", String.valueOf(req.getRequirementParts().size()));

        CreateTodoMQ message = new CreateTodoMQ();
        message.setBusinessType(TodoClsEnum.PART_REQUIREMENT_SYSTEM_DISTRIBUTION.getCode());
        message.setBusinessNo(requirement.getRequirementNo());
        message.setUserId(requirement.getBpId());
        message.setIconType(IconTypeEnum.GENERAL.getCode());
        message.setContent(content);
        return message;
    }

    private List<CreateTicketMQ.ApprovalProcessVar> getApprovalProcessVars(RequirementSaveReqDTO req) {
        List<CreateTicketMQ.ApprovalProcessVar> processVarList = new ArrayList<>();
        if (StringUtils.equalsAny(req.getOperationByRole(),
                ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE.getKey(),
                ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE_LEADER.getKey())) {
            setBPApprovers(req, processVarList);
        } else if (StringUtils.equalsAny(req.getOperationByRole(),
                ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.getKey(),
                ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.getKey())) {
            setCPEApprovers(req, processVarList);
        }
        return processVarList;
    }

    private void setCPEApprovers(RequirementSaveReqDTO reqDTO, List<CreateTicketMQ.ApprovalProcessVar> processVarList) {
        CreateTicketMQ.ApprovalProcessVar processVar0 = new CreateTicketMQ.ApprovalProcessVar();
        processVar0.setKey(RequirementBizConstants.JSLX);
        processVar0.setV(RequirementBizConstants.QT);
        processVarList.add(processVar0);
        List<CategoryEngineerResultDTO> resultDTOList =  categoryManager.queryCategoryEngineerByCategoryId(reqDTO.getOperationByThreeCategory());
        Optional<CategoryEngineerResultDTO> categoryEngineerResultDTO = resultDTOList.stream().filter(r-> CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.equalsCode(r.getRoleId())).findFirst();
        AtomicReference<Long> zzsp= new AtomicReference<>();
        zzsp.set(-1L);
        Optional<CategoryEngineerResultDTO> cpeMaster = resultDTOList.stream().filter(r-> CategoryRoleEnum.CPE_MASTER.equalsCode(r.getRoleId())).findFirst();
        cpeMaster.ifPresent(r->{
            if (!r.getUserId().equals(reqDTO.getOperationBy())) {
                CreateTicketMQ.ApprovalProcessVar processVar = new CreateTicketMQ.ApprovalProcessVar();
                processVar.setKey(RequirementBizConstants.ZZSP);
                processVar.setV(r.getUserDomain());
                zzsp.set(r.getUserId());
                processVarList.add(processVar);
            }else {
                categoryEngineerResultDTO.ifPresent(k->{
                    CreateTicketMQ.ApprovalProcessVar processVar = new CreateTicketMQ.ApprovalProcessVar();
                    processVar.setKey(RequirementBizConstants.ZZSP);
                    processVar.setV(k.getUserDomain());
                    zzsp.set(k.getUserId());
                    processVarList.add(processVar);
                });
            }
        });

        categoryEngineerResultDTO.ifPresent(r->{
            if (zzsp.get().equals(r.getUserId())) {
                BaseGroupedSysUserRespDTO sysUserRespDTO = sysUserManager.findGroupedUserByRoleId(RequirementBizConstants.PURCHASE_MINISTER);
                sysUserRespDTO.getUsers().stream().findFirst().ifPresent(k->{
                    CreateTicketMQ.ApprovalProcessVar processVar = new CreateTicketMQ.ApprovalProcessVar();
                    processVar.setKey(RequirementBizConstants.CZSP);
                    processVar.setV(k.getDomain());
                    processVarList.add(processVar);
                });
            } else {
                CreateTicketMQ.ApprovalProcessVar processVar = new CreateTicketMQ.ApprovalProcessVar();
                processVar.setKey(RequirementBizConstants.CZSP);
                processVar.setV(r.getUserDomain());
                processVarList.add(processVar);
            }

        });
    }

    private void setBPApprovers(RequirementSaveReqDTO reqDTO, List<CreateTicketMQ.ApprovalProcessVar> processVarList) {
        CreateTicketMQ.ApprovalProcessVar processVar0 = new CreateTicketMQ.ApprovalProcessVar();
        processVar0.setKey(RequirementBizConstants.JSLX);
        processVar0.setV(RequirementBizConstants.QT);
        processVarList.add(processVar0);
        SysDivisionFindByDivisionResDTO data = sysDivisionApi.findByDivisionId(reqDTO.getSubsidiaryCode()).getData();
        log.info("SysDivisionFindByDivisionResDTO data,{}",data);
        Long zzsp = null;
        if (reqDTO.getOperationBy().equals(data.getProjectBpMasterUserId())) {
            CreateTicketMQ.ApprovalProcessVar r = new CreateTicketMQ.ApprovalProcessVar();
            r.setKey(RequirementBizConstants.ZZSP);
            r.setV(data.getProjectDirectorUserDomain());
            zzsp = data.getProjectDirectorUserId();
            processVarList.add(r);
        }else{
            CreateTicketMQ.ApprovalProcessVar r = new CreateTicketMQ.ApprovalProcessVar();
            r.setKey(RequirementBizConstants.ZZSP);
            r.setV(data.getProjectBpMasterUserDomain());
            zzsp = data.getProjectBpMasterUserId();
            processVarList.add(r);
        }

        if (zzsp.equals(data.getProjectDirectorUserId())) {
            BaseGroupedSysUserRespDTO sysUserRespDTO = sysUserManager.findGroupedUserByRoleId(RequirementBizConstants.PURCHASE_MINISTER);
            sysUserRespDTO.getUsers().stream().findFirst().ifPresent(k->{
                CreateTicketMQ.ApprovalProcessVar r2 = new CreateTicketMQ.ApprovalProcessVar();
                r2.setKey(RequirementBizConstants.CZSP);
                r2.setV(k.getDomain());
                processVarList.add(r2);
            });
        } else {
            CreateTicketMQ.ApprovalProcessVar r2 = new CreateTicketMQ.ApprovalProcessVar();
            r2.setKey(RequirementBizConstants.CZSP);
            r2.setV(data.getProjectDirectorUserDomain());
            processVarList.add(r2);
        }
    }

    /**
     * ----------------------------------------------------------------------
     */

    public void checkParam(RequirementSaveReqDTO req) {
        // 校验项目BP不能为空
        if (req.getBpId() == null || StringUtils.isEmpty(req.getBpName())) {
            throw new BizFailException(localeMessage.getMessage("requirement.submit.bp.not.empty"));
        }

        // 判断提交人的角色
        ProjectSubmitTypeEnum projectSubmitTypeEnum = ProjectSubmitTypeEnum.statOf(req.getOperationByRole());
        if (ObjectUtil.isNull(projectSubmitTypeEnum)) {
            throw new BizFailException(localeMessage.getMessage("requirement.submit.role.not.correct"));
        }

        RequirementTypeEnum requirementTypeEnum =RequirementTypeEnum.getByCode(req.getRequirementType());
        if (ObjectUtil.isNull(requirementTypeEnum)) {
            throw new BizFailException(localeMessage.getMessage("requirement.type.error"));
        }

        // 品类管理员、品类组长提交的需求一定要选择三级品类
        if (ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.equalsKey(req.getOperationByRole())
                || ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.equalsKey(req.getOperationByRole())) {
            if (req.getOperationByThreeCategory() == null) {
                throw new BizFailException(localeMessage.getMessage("requirement.submit.category.not.empty"));
            }
        }

        // 校验同一总需求下物料是否重复
        requirementManager.checkMaterialDuplicate(req);

        //寻源类需求 - 校验相同项目下 物料 是否有重复 （编制中，已重新发起的除外）
        checkProjectMaterialDuplicate(req, requirementTypeEnum);
        checkProjectAndPurchaseAndSubsidiaryCode(req);

        PurchaseRequirementTypeEnum purchaseRequirementTypeEnum = PurchaseRequirementTypeEnum.getByCode(req.getPurchaseRequirementType());
        if (RequirementTypeEnum.PURCHASE.equals(requirementTypeEnum) && (ObjectUtil.isNull(purchaseRequirementTypeEnum))) {
            throw new BizFailException(localeMessage.getMessage("requirement.purchase.type.error"));
        }

        List<RequirementPartsSaveReqDTO> partsPOList = req.getRequirementParts();
        if (CollUtil.isEmpty(partsPOList)) {
            throw new BizFailException("there is no requirement parts");
        }

        // 通过事业部找对应的项目BP
        SysDivisionFindByDivisionResDTO division = sysDivisionManager.findByDivisionId2(req.getSubsidiaryCode());
        checkUserToDivision(req, division);

        List<String> materialCodes = partsPOList.stream()
                .map(RequirementPartsSaveReqDTO::getMaterialCode)
                .filter(r->!StringUtil.isNullOrEmpty(r))
                .collect(Collectors.toList());

        List<MaterialResultDTO> materials = queryMaterials(materialCodes);
        Map<String, MaterialResultDTO> materialMap = new HashMap<>();
        for (MaterialResultDTO material:materials) {
            materialMap.put(material.getMaterialCode(), material);
        }

        //零件需求
        checkPartsPOList(req, partsPOList, requirementTypeEnum, purchaseRequirementTypeEnum, materialMap);

        // 检验物料与工厂关系
        checkFactory(materialCodes, partsPOList);

        if ((ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.equals(projectSubmitTypeEnum)
                || ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.equals(projectSubmitTypeEnum))) {
            //检查是否有三级品类 以及  该三级品类是否有挂组长，组长用于后面的组长分发
            checkHasCategoryAndCpeMaster(materials, req);
        }

        // 校验事业部与工厂的关系
        checkDivisionAndFactoryRel(req);

        // 慢流动状态校验
        checkSlowFlow(req);
    }

    private void checkProjectAndPurchaseAndSubsidiaryCode(RequirementSaveReqDTO req) {
        if (YesOrNoEnum.YES.equalsCode(req.getIsRelateProject()) && StringUtil.isNullOrEmpty(req.getProjectNo())) {
            throw new BizFailException(localeMessage.getMessage("requirement.project.org.error"));
        }
        if (StringUtil.isNullOrEmpty(req.getPurchaseOrgCode())) {
            throw new BizFailException(localeMessage.getMessage("requirement.purchase.type.error"));
        }
        RequirementOrientedEnum orientedEnum = RequirementOrientedEnum.getByCode(req.getRequirementOriented());
        if (!StringUtil.isNullOrEmpty(req.getRequirementOriented()) && ObjectUtil.isNull(orientedEnum)) {
            throw new BizFailException(localeMessage.getMessage("requirement.subsidiaryCode.error"));
        }
        if (StringUtil.isNullOrEmpty(req.getSubsidiaryCode())) {
            throw new BizFailException(localeMessage.getMessage("requirement.subsidiaryCode.error"));
        }
    }

    private static void checkUserToDivision(RequirementSaveReqDTO req, SysDivisionFindByDivisionResDTO division) {
        if (ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE.equalsKey(req.getOperationByRole())) {
            if (!StringUtils.contains(division.getProjectBpUserId(), COMMA + req.getOperationBy() + COMMA)) {
                throw new BizFailException("当前登录用户不是事业部【" + req.getSubsidiaryCode() + "】的项目BP");
            }
        } else if (ProjectSubmitTypeEnum.PROJECT_SUBMIT_PURCHASE_LEADER.equalsKey(req.getOperationByRole())) {
            if (!req.getOperationBy().equals(division.getProjectBpMasterUserId())) {
                throw new BizFailException("当前登录用户不是事业部【" + req.getSubsidiaryCode() + "】的项目BP组长");
            }
        }
    }

    private void checkPartsPOList(RequirementSaveReqDTO req, List<RequirementPartsSaveReqDTO> partsPOList, RequirementTypeEnum requirementTypeEnum, PurchaseRequirementTypeEnum purchaseRequirementTypeEnum, Map<String, MaterialResultDTO> materialMap) {
        partsPOList.forEach(r->{
            if (RequirementTypeEnum.PURCHASE.equals(requirementTypeEnum)
                    && !purchaseRequirementTypeEnum.equals(PurchaseRequirementTypeEnum.EXPENSE_REIMBURSEMENT)
                    && StringUtil.isNullOrEmpty(r.getMaterialCode())) {
                throw new BizFailException(localeMessage.getMessage("requirement.materialCode.error"));
            }
            if (!StringUtil.isNullOrEmpty(r.getRecommendedSupplierName()) && StringUtil.isNullOrEmpty(r.getRecommendedSupplierType())) {
                throw new BizFailException("Recommended Supplier Type must be selected when Recommended Supplier Name is not null");
            }
            // 校验物料所属品类与需求头上的品类是否一致（1.提交角色是品类管理员或品类组长；2.物料编码不为空）
            if ((ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY.equalsKey(req.getOperationByRole())
                    || ProjectSubmitTypeEnum.PROJECT_SUBMIT_CATEGORY_LEADER.equalsKey(req.getOperationByRole()))
                && StringUtils.isNotEmpty(r.getMaterialCode())) {
                MaterialResultDTO material = materialMap.get(r.getMaterialCode());
                if (material == null || (!StringUtils.equals(material.getCategoryCode(), req.getOperationByThreeCategoryCode()))) {
                    throw new BizFailException(localeMessage.getMessage("requirement.submit.material.no.belong.category", new Object[]{r.getMaterialCode(), req.getOperationByThreeCategoryCode()}));
                }
            }
        });
    }

    /**
     * 查询物料详情
     */
    private List<MaterialResultDTO> queryMaterials(List<String> materialCodes) {
        if (CollectionUtils.isEmpty(materialCodes)) {
            return new ArrayList<>();
        }

        ApiResponse<List<MaterialResultDTO>> apiResponse = materialApi.listByMaterialCodes(materialCodes);
        if (Boolean.FALSE.equals(apiResponse.getSucc())) {
            throw new BizFailException("get material category error"+apiResponse.getMsg());
        }
        return apiResponse.getData();
    }

    private void checkDivisionAndFactoryRel(RequirementSaveReqDTO reqDTO) {
        Set<String> factoryCodes = reqDTO.getRequirementParts().stream()
                .map(RequirementPartsSaveReqDTO::getFactory).collect(Collectors.toSet());
        ApiResponse<List<SysFactoryRespDTO>> res = sysFactoryApi.listByFactory(new ArrayList<>(factoryCodes));
        if (!res.getSucc()) {
            throw new BizFailException("查询工厂信息失败");
        }

        Map<String, SysFactoryRespDTO> factoryMap = res.getData().stream().collect(
                Collectors.toMap(SysFactoryRespDTO::getFactoryCode, Function.identity(), (oldVal, newVal) -> newVal));
        for (String factoryCode:factoryCodes) {
            SysFactoryRespDTO factory = factoryMap.get(factoryCode);
            if (factory == null || !StringUtils.equals(factory.getDivisionCode(), reqDTO.getSubsidiaryCode())) {
                throw new BizFailException(localeMessage.getMessage("requirement.submit.factory.no.belong.division", new Object[]{factoryCode, reqDTO.getSubsidiaryCode()}));
            }
        }
    }

    private void checkFactory(List<String> materialCodes, List<RequirementPartsSaveReqDTO> partsPOList) {
        if (CollUtil.isEmpty(materialCodes)) {
            return;
        }

        List<MaterialFactoryResultDTO> factoryResultDTOS = materialManager.listFactories(materialCodes);

        Map<String, String> materialFactoryMap = new HashMap<>();
        for (MaterialFactoryResultDTO materialFactory:factoryResultDTOS) {
            String key = materialFactory.getMaterialCode() + "_" + materialFactory.getFactory();
            materialFactoryMap.put(key, StringUtils.EMPTY);
        }

        List<String> wrongFactoryMaterials = new ArrayList<>();
        for (RequirementPartsSaveReqDTO part:partsPOList) {
            if (StringUtils.isEmpty(part.getMaterialCode())) {
                continue;
            }

            String key = part.getMaterialCode() + "_" + part.getFactory();
            if (materialFactoryMap.containsKey(key)) {
                continue;
            }
            wrongFactoryMaterials.add(part.getMaterialCode());
        }
        if (!CollUtil.isEmpty(wrongFactoryMaterials)) {
            throw new BizFailException(localeMessage.getMessage("requirement.submit.material.no.belong.factory", new Object[]{wrongFactoryMaterials}));
        }
    }

    private void checkSlowFlow(RequirementSaveReqDTO reqDTO) {
        reqDTO.getRequirementParts().forEach(r->{
            if (!StringUtil.isNullOrEmpty(r.getFactory()) && !StringUtil.isNullOrEmpty(r.getMaterialCode())) {
                MaterialSlowFlowStatusCheckReqDTO slowFlowStatusCheckReqDTO = new MaterialSlowFlowStatusCheckReqDTO();
                slowFlowStatusCheckReqDTO.setFactory(r.getFactory());
                slowFlowStatusCheckReqDTO.setMaterialCode(r.getMaterialCode());
                ApiResponse<String> response = sapApi.slowFlowStatus(slowFlowStatusCheckReqDTO);
                if (Boolean.FALSE.equals(response.getSucc())) {
                    throw new BizFailException("slow flow status check error");
                }
                String responseCode = Optional.ofNullable(response.getData()).orElse("");
                if (!"S".equals(responseCode)) {
                    log.error("slow flow status check error:{}",response.getMsg());
                    throw new BizFailException(localeMessage.getMessage("requirement.submit.slow.flow.check.failed", new String[] {r.getMaterialCode()}));
                }
            }
        });
    }

    private void checkProjectMaterialDuplicate(RequirementSaveReqDTO reqDTO, RequirementTypeEnum requirementTypeEnum) {
        if (RequirementTypeEnum.PURCHASE.equals(requirementTypeEnum)) {
            return;
        }
        List<String> submitMaterials = reqDTO.getRequirementParts().stream()
                .map(RequirementPartsSaveReqDTO::getMaterialCode)
                .filter(i->!StringUtil.isNullOrEmpty(i))
                .collect(Collectors.toList());
        List<String> exsitsMaterialCodes = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(reqDTO.getProjectNo())) {
            List<String> requirementNos = requirementMapperService.lambdaQuery()
                    .eq(RequirementPO::getProjectNo, reqDTO.getProjectNo())
                    .ne(!StringUtil.isNullOrEmpty(reqDTO.getRequirementNo()),RequirementPO::getRequirementNo, reqDTO.getRequirementNo())
                    .eq(RequirementPO::getIsDelete,YesOrNoEnum.NO.getCode())
                    .list().stream().map(RequirementPO::getRequirementNo)
                    .collect(Collectors.toList());
            if (!CollUtil.isEmpty(requirementNos)) {
                exsitsMaterialCodes = requirementPartsMapperService.lambdaQuery()
                        .in(RequirementPartsPO::getRequirementNo, requirementNos)
                        .eq(RequirementPartsPO::getIsDelete,YesOrNoEnum.NO.getCode())
                        .ne(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.DRAFT.getCode())
                        .ne(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.RECREATED.getCode())
                        .list().stream().map(RequirementPartsPO::getMaterialCode)
                        .filter(materialCode ->!StringUtil.isNullOrEmpty(materialCode))
                        .collect(Collectors.toList());
            }
            for (String submitMaterial : submitMaterials) {
                if (exsitsMaterialCodes.contains(submitMaterial)) {
                    String message = localeMessage.getMessage("requirement.submit.check.duplicate", new Object[] {submitMaterial});
                    throw new BizFailException(message);
                }
            }
        }
    }

    private void checkHasCategoryAndCpeMaster(List<MaterialResultDTO> materials, RequirementSaveReqDTO reqDTO) {
        if (CollUtil.isEmpty(materials)) {
            return;
        }

        List<String> materialCodes = materials.stream().map(MaterialResultDTO::getMaterialCode).collect(Collectors.toList());
        //检查是否有三级品类 以及  该三级品类是否有挂组长，组长用于后面的组长分发
        if (CollUtil.isEmpty(materials)) {
            throw new BizFailException(localeMessage.getMessage(REQUIREMENT_CATEGORY, new Object[]{materialCodes}));
        }
        List<String> hasDifferenceCategories = materials.stream()
                .filter(r->!r.getCategoryCode().equals(reqDTO.getOperationByThreeCategoryCode()))
                .map(MaterialResultDTO::getMaterialCode).collect(Collectors.toList());
        if (!CollUtil.isEmpty(hasDifferenceCategories)) {
            throw new BizFailException(localeMessage.getMessage("requirement.submit.material.no.relate.required.category",
                    new Object[]{materialCodes, reqDTO.getOperationByThreeCategoryCode()}));
        }
        List<String> noCategories = materials.stream()
                .filter(r->StringUtil.isNullOrEmpty(r.getCategoryCode()))
                .map(MaterialResultDTO::getMaterialCode).collect(Collectors.toList());
        if (!CollUtil.isEmpty(noCategories)) {
            throw new BizFailException(localeMessage.getMessage(REQUIREMENT_CATEGORY, new Object[]{noCategories}));
        }
        List<String> categoryCodes = materials.stream().map(MaterialResultDTO::getCategoryCode)
                .filter(categoryCode ->!StringUtil.isNullOrEmpty(categoryCode))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(categoryCodes)) {
            throw new BizFailException(localeMessage.getMessage(REQUIREMENT_CATEGORY, new Object[]{materialCodes}));
        }
        CategoryUserQueryReqDTO categoryUserQueryReqDTO = new CategoryUserQueryReqDTO();
        categoryUserQueryReqDTO.setCategoryCodes(categoryCodes);
        categoryUserQueryReqDTO.setRoleIds(List.of(CategoryRoleEnum.CPE_MASTER.getCode()));
        ApiResponse<List<CategoryEngineerResultDTO>> roleResponse = categoryApi.queryUserByCategoryAndRole(categoryUserQueryReqDTO);
        if (Boolean.FALSE.equals(roleResponse.getSucc())) {
            throw new BizFailException("get material category master error"+roleResponse.getMsg());
        }
        List<CategoryEngineerResultDTO> engineerRespS = roleResponse.getData();
        if (CollUtil.isEmpty(engineerRespS)){
            throw new BizFailException(localeMessage.getMessage("requirement.submit.material.no.cpe.master"));
        }
        List<String> hasMasterCategories =  engineerRespS.stream().map(CategoryEngineerResultDTO::getCategoryCode).collect(Collectors.toList());
        List<String> hasMasterMaterials = materials.stream()
                .filter(r->hasMasterCategories.contains(r.getCategoryCode()))
                .map(MaterialResultDTO::getMaterialCode).collect(Collectors.toList());
        List<String> noCategorMasterMaterials = materialCodes.stream()
                .filter(element -> !hasMasterMaterials.contains(element))
                .collect(Collectors.toList());
        if (!CollUtil.isEmpty(noCategorMasterMaterials)) {
            throw new BizFailException(localeMessage.getMessage("requirement.submit.material.no.cpe.master2", new Object[]{materialCodes}));
        }
    }

}
