package com.weifu.srm.requirement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.masterdata.mq.MaterialCategoryRelChangedMQ;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.purchase.mq.SampleOrderChangeMq;
import com.weifu.srm.requirement.atomicservice.*;
import com.weifu.srm.requirement.convert.RequirementPartsConvert;
import com.weifu.srm.requirement.enums.*;
import com.weifu.srm.requirement.enums.project.ProjecTimeStageEnum;
import com.weifu.srm.requirement.manager.RequirementManager;
import com.weifu.srm.requirement.manager.RequirementPartsManager;
import com.weifu.srm.requirement.manager.remote.masterdata.MaterialManager;
import com.weifu.srm.requirement.manager.remote.user.DataPermissionManager;
import com.weifu.srm.requirement.manager.remote.user.SysDivisionManager;
import com.weifu.srm.requirement.manager.remote.user.SysFactoryManager;
import com.weifu.srm.requirement.mq.PartsPurchasePlanChangeMq;
import com.weifu.srm.requirement.mq.RequirementPartStatusChangedMq;
import com.weifu.srm.requirement.po.*;
import com.weifu.srm.requirement.request.*;
import com.weifu.srm.requirement.response.RequirementPartsQueryByNoRespDTO;
import com.weifu.srm.requirement.response.RequirementSimpleRespDTO;
import com.weifu.srm.requirement.service.RequirementPartsService;
import com.weifu.srm.requirement.service.biz.*;
import com.weifu.srm.sourcing.mq.InquiryOrderMQ;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.SysFactoryRespDTO;
import com.weifu.srm.user.response.division.SysDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/12 23:07
 * @Description
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequirementPartsServiceImpl implements RequirementPartsService {

    private final RequirementPartsMapperService requirementPartsMapperService;
    private final RequirementPartsConvert partsConvert;
    private final RequirementMapperService requirementMapperService;
    private final RequirementPartsTransferBiz transferBiz;
    private final RequirementPartsReturnBiz returnBiz;
    private final RequirementPartsCloseBiz requirementPartsCloseBiz;
    private final RequirementPartsDistributeBiz distributeBiz;
    private final RequirementPartsReCreateBiz requirementPartsReCreateBiz;
    private final RequirementPartsBorrowedBiz borrowedBiz;
    private final RequirementPartsManager partsManager;
    private final RequirementManager requirementManager;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SysFactoryManager factoryManager;
    private final ProjectTimeMapperService projectTimeMapperService;
    private final SysDivisionManager sysDivisionManager;
    private final RequirementPartsDrawingMapperService drawingMapperService;
    private final DataPermissionManager dataPermissionManager;
    private final ProjectMapperService projectMapperService;
    private final RequirementPartsDrawingMapperService requirementPartsDrawingMapperService;
    private final HandleRequirementPartStatusChangedBiz handleRequirementPartStatusChangedBiz;
    private final HandleSampleOrderChangedBiz handleSampleOrderChangedBiz;
    private final HandleMaterialCategoryRelChangedBiz handleMaterialCategoryRelChangedBiz;
    private final HandlePartsPurchasePlanChangeBiz handlePartsPurchasePlanChangeBiz;
    private final HandleInquiryOrderChangeBiz handleInquiryOrderChangeBiz;
    private final MaterialManager materialManager;

    private static final String PC_INTERNAL_PAGE_SUBREQUIREMENT_LIST = "PC_INTERNAL_PAGE_SUBREQUIREMENT_LIST";

    @Override
    public RequirementPartsQueryByNoRespDTO queryRequirementPartsByNo(String requirementPartsNo) {
        return partsManager.queryRequirementPartsByNo(requirementPartsNo);
    }

    @Override
    public PageResponse<RequirementPartsQueryByNoRespDTO> queryPage(RequirementPartsQueryPageReqDTO reqDTO) {
        Page<RequirementPartsPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(reqDTO.getOperationBy(), PC_INTERNAL_PAGE_SUBREQUIREMENT_LIST);
        if (dataPermission.isNo()) {
            return PageResponse.toResult(reqDTO.getPageNum(),
                    reqDTO.getPageSize(),
                    0L,
                    new ArrayList<>());
        }

        // 不展示状态为编制中、审核中的零件需求
        Page<RequirementPartsQueryByNoRespDTO> pageResp = requirementPartsMapperService.queryPage(page, reqDTO, dataPermission);
        List<RequirementPartsQueryByNoRespDTO> pageList = pageResp.getRecords();
        if (CollectionUtils.isEmpty(pageList)) {
            new PageResponse<RequirementPartsQueryByNoRespDTO>();
        }
        List<String> partNos = pageList.stream().map(RequirementPartsQueryByNoRespDTO::getRequirementPartsNo).collect(Collectors.toList());
        List<String> projectNos = pageList.stream().map(RequirementPartsQueryByNoRespDTO::getProjectNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(partNos)) {
            List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.getAttachments(partNos);
            //设置附件及
            requirementManager.setAttachments(pageList, attachmentRecordPOList);
        }

        // 补充图纸附件
        List<Long> requirementDrawingIds = pageList.stream().map(RequirementPartsQueryByNoRespDTO::getRequirementDrawingId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        setDrawing(requirementDrawingIds, pageList);

        //量产采购时间 + 项目编码获取
        setProjectNo(projectNos, pageList);
        //设置事业部
        setDivision(pageList);
        setEnums(pageList);
        // 设置物料品类关联状态
        setRelatedCategoryStatus(pageList);
        return PageResponse.toResult(reqDTO.getPageNum(),
                reqDTO.getPageSize(),
                pageResp.getTotal(),
                pageList);
    }

    private void setDivision(List<RequirementPartsQueryByNoRespDTO> pageList) {
        List<SysDivisionRespDTO> sysDivisionRespDTOS = sysDivisionManager.getSysDivisionRespDTO();
        pageList.forEach(r -> r.setSubsidiaryName(Optional.ofNullable(sysDivisionRespDTOS)
                .flatMap(j -> j.stream().filter(i -> i.getDivisionId().equals(r.getSubsidiaryCode()))
                        .findFirst().map(SysDivisionRespDTO::getDivisionName))
                .orElse(null)));
        List<String> factory = pageList.stream().map(RequirementPartsQueryByNoRespDTO::getFactory).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(factory)) {
            List<SysFactoryRespDTO> factoryRespDTOS = factoryManager.listByFactory(factory);
            pageList.forEach(r -> r.setFactoryName(Optional.ofNullable(factoryRespDTOS)
                    .flatMap(k -> k.stream()
                            .filter(j -> !StringUtil.isNullOrEmpty(r.getFactory())
                                    && r.getFactory().equals(j.getFactoryCode()))
                            .findFirst().map(SysFactoryRespDTO::getFactoryName))
                    .orElse(null)));
        }
    }

    private void setProjectNo(List<String> projectNos, List<RequirementPartsQueryByNoRespDTO> pageList) {
        if (CollectionUtils.isNotEmpty(projectNos)) {
            List<ProjectTimePO> planPOS = projectTimeMapperService.lambdaQuery()
                    .in(ProjectTimePO::getProjectNo, projectNos)
                    .eq(ProjectTimePO::getStageType, ProjecTimeStageEnum.TIME_STAGE_PLAN.getKey())
                    .eq(ProjectTimePO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            List<ProjectPO> projectList = projectMapperService.lambdaQuery()
                    .in(ProjectPO::getProjectNo, projectNos)
                    .list();
            pageList.forEach(k -> {
                        k.setManufactureSopPlanTime(Optional.ofNullable(planPOS)
                                .flatMap(r -> r.stream().filter(o -> !StringUtil.isNullOrEmpty(k.getProjectNo())
                                                && k.getProjectNo().equals(o.getProjectNo()))
                                        .findFirst().map(ProjectTimePO::getManufactureSopDate))
                                .orElse(null));
                        k.setProjectCode(projectList.stream().filter(r -> r.getProjectNo().equals(k.getProjectNo())).findFirst().map(ProjectPO::getProjectCode).orElse(null));
                    }
            );

        }
    }

    private void setDrawing(List<Long> requirementDrawingIds, List<RequirementPartsQueryByNoRespDTO> pageList) {
        if (CollectionUtils.isNotEmpty(requirementDrawingIds)) {
            List<RequirementPartsDrawingPO> drawings = requirementPartsDrawingMapperService.lambdaQuery()
                    .in(RequirementPartsDrawingPO::getId, requirementDrawingIds).list();
            for (RequirementPartsQueryByNoRespDTO part : pageList) {
                for (RequirementPartsDrawingPO drawing : drawings) {
                    if (drawing.getId().equals(part.getRequirementDrawingId())) {
                        part.setDrawingUrl(drawing.getDrawingUrl());
                        part.setDrawingName(drawing.getDrawingName());
                    }
                }
            }
        }
    }

    /**
     * 设置物料品类关联状态
     */
    private void setRelatedCategoryStatus(List<RequirementPartsQueryByNoRespDTO> pageList) {
        List<String> materialCodes = new ArrayList<>();
        for (RequirementPartsQueryByNoRespDTO part:pageList) {
            if (!RequirementPartsStatusEnum.WAITING_BP_DISTRIBUTE.equalsCode(part.getStatus())) {
                part.setRelatedCategoryStatus(RequirementPartsQueryByNoRespDTO.RELATED_CATEGORY_NOT_NEED_RELATED);
                continue;
            }

            if (StringUtils.isEmpty(part.getMaterialCode())) {
                part.setRelatedCategoryStatus(RequirementPartsQueryByNoRespDTO.RELATED_CATEGORY_NOT_NEED_RELATED);
            } else if (StringUtils.isNotEmpty(part.getCategoryThird())) {
                part.setRelatedCategoryStatus(RequirementPartsQueryByNoRespDTO.RELATED_CATEGORY_RELATED);
            } else {
                // 可能会因为存在正在审核中的物料挂品类申请而覆盖成“审核中”
                part.setRelatedCategoryStatus(RequirementPartsQueryByNoRespDTO.RELATED_CATEGORY_UN_RELATED);
                materialCodes.add(part.getMaterialCode());
            }
        }

        if (CollectionUtils.isEmpty(materialCodes)) {
            return;
        }

        // 覆盖部分“未关联”数据为“审核中”
        List<String> filterMaterialCodes = materialManager.listApprovingMaterial(materialCodes);
        for (RequirementPartsQueryByNoRespDTO part:pageList) {
            if (filterMaterialCodes.contains(part.getMaterialCode())) {
                part.setRelatedCategoryStatus(RequirementPartsQueryByNoRespDTO.RELATED_CATEGORY_APPROVING);
            }
        }
    }

    @Override
    public List<RequirementPartsQueryByNoRespDTO> queryByPartsNos(RequirementPartsQueryByNosReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getRequirementPartsNo())) {
            return new ArrayList<>();
        }
        Optional<List<RequirementPartsPO>> poListOptional = Optional.ofNullable(reqDTO.getRequirementPartsNo())
                .map(r -> requirementPartsMapperService.lambdaQuery()
                        .in(RequirementPartsPO::getRequirementPartsNo, r)
                        .eq(RequirementPartsPO::getIsDelete, YesOrNoEnum.NO.getCode())
                        .list());
        List<String> requirementNos = poListOptional.map(r -> r.stream().map(RequirementPartsPO::getRequirementNo).collect(Collectors.toList())).orElse(new ArrayList<>());
        List<RequirementPartsQueryByNoRespDTO> pageList = poListOptional.map(partsConvert::toQuerySumByNo).orElse(null);
        if (CollectionUtils.isEmpty(pageList)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(requirementNos)) {
            List<RequirementPO> requirementPOS = requirementMapperService.lambdaQuery()
                    .in(RequirementPO::getRequirementNo, requirementNos)
                    .eq(RequirementPO::getIsDelete, YesOrNoEnum.NO.getCode()).list();
            pageList.forEach(r -> {
                Optional<RequirementPO> requirementPO = requirementPOS.stream().filter(j -> j.getRequirementNo().equals(r.getRequirementNo())).findFirst();
                requirementPO.ifPresent(j -> {
                    r.setPurchaseOrgCode(j.getPurchaseOrgCode());
                    r.setSubsidiaryCode(j.getSubsidiaryCode());
                    r.setProjectNo(j.getProjectNo());
                    r.setProjectName(j.getProjectName());
                    r.setProjectId(j.getProjectId());
                    r.setRequirementType(j.getRequirementType());
                    r.setPurchaseRequirementType(j.getPurchaseRequirementType());
                });
            });
        }
        // 添加客户相关信息
        setOtherInfo(pageList);

        //设置事业部
        List<SysDivisionRespDTO> sysDivisionRespDTOS = sysDivisionManager.getSysDivisionRespDTO();
        pageList.forEach(r -> r.setSubsidiaryName(Optional.ofNullable(sysDivisionRespDTOS)
                .flatMap(j -> j.stream().filter(i -> i.getDivisionId().equals(r.getSubsidiaryCode()))
                        .findFirst().map(SysDivisionRespDTO::getDivisionName))
                .orElse(null)));
        List<String> partNos = Optional.of(pageList).map(r -> r.stream().map(RequirementPartsQueryByNoRespDTO::getRequirementPartsNo).collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isNotEmpty(partNos)) {
            List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.getAttachments(partNos);
            //设置附件
            requirementManager.setAttachments(pageList, attachmentRecordPOList);
        }
        //设置图纸
        List<Long> drawingIds = pageList.stream()
                .map(RequirementPartsQueryByNoRespDTO::getRequirementDrawingId)
                .filter(j -> !ObjectUtil.isNull(j)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(drawingIds)) {
            List<RequirementPartsDrawingPO> drawingPOS = drawingMapperService.lambdaQuery()
                    .in(RequirementPartsDrawingPO::getId, drawingIds)
                    .eq(RequirementPartsDrawingPO::getIsDelete, YesOrNoEnum.NO.getCode()).list();
            pageList.forEach(r -> {
                RequirementPartsDrawingPO drawingPO = Optional.ofNullable(drawingPOS).flatMap(j -> j.stream().filter(k -> k.getId().equals(r.getRequirementDrawingId())).findFirst()).orElse(null);
                r.setDrawingName(Optional.ofNullable(drawingPO).map(RequirementPartsDrawingPO::getDrawingName).orElse(null));
                r.setDrawingUrl(Optional.ofNullable(drawingPO).map(RequirementPartsDrawingPO::getDrawingUrl).orElse(null));
                r.setFileName(Optional.ofNullable(drawingPO).map(RequirementPartsDrawingPO::getFileName).orElse(null));
            });
        }
        //设置枚举
        setEnums(pageList);
        return pageList;
    }

    @Override
    public void returnByPartsNos(RequirementPartsReturnReqDTO reqDTO) {
        returnBiz.returnByPartsNo(reqDTO);
    }

    @Override
    public void closeByPartsNos(RequirementPartsCloseReqDTO reqDTO) {
        try {
            requirementPartsCloseBiz.execute(reqDTO);
        } catch (BizFailException e) {
            throw e;
        } catch (Exception e) {
            log.error("关闭零件需求异常，requirementPartsNo={}", reqDTO.getRequirementPartsNo(), e);
            throw e;
        }
    }

    @Override
    public void transferByPartsNo(RequirementPartsTransferReqDTO reqDTO) {
        transferBiz.transferByPartsNo(reqDTO);
    }

    @Override
    public String distribute(RequirementPartsDistributeReqDTO reqDTO) {
        return distributeBiz.distribute(reqDTO);
    }

    @Override
    public String reCreateRequirementParts(RequirementPartsReCreateReqDTO reqDTO) {
        return requirementPartsReCreateBiz.reCreateRequirementParts(reqDTO);
    }

    @Override
    public String borrowedRequirementParts(RequirementPartsBorrowReqDTO reqDTO) {
        return borrowedBiz.borrowRequirementParts(reqDTO);
    }

    @Override
    public List<RequirementSimpleRespDTO> queryRequirements(RequirementPartsQueryReqDTO param) {
        LambdaQueryWrapper<RequirementPartsPO> query = new LambdaQueryWrapper<>();
        query.select(RequirementPartsPO::getId, RequirementPartsPO::getRequirementNo,
                RequirementPartsPO::getRequirementPartsNo, RequirementPartsPO::getMaterialCode);
        query.eq(RequirementPartsPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.eq(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.PENDING.getCode());

        query.eq(RequirementPartsPO::getMaterialCode, param.getMaterialCode());
        if (CharSequenceUtil.isNotEmpty(param.getRequirementPartsNo())) {
            if (CharSequenceUtil.equalsIgnoreCase("Y", param.getAccurateQuery())) {
                query.eq(RequirementPartsPO::getRequirementPartsNo, param.getRequirementPartsNo());
            } else {
                query.like(CharSequenceUtil.isNotEmpty(param.getRequirementPartsNo()), RequirementPartsPO::getRequirementPartsNo, param.getRequirementPartsNo());
            }
        }

        List<RequirementPartsPO> list = requirementPartsMapperService.list(query);
        if (CollectionUtils.isEmpty(list)) { return Collections.emptyList(); }

        //K:requirementNo V:RequirementPO
        Map<String, RequirementPO> requirementMap = null;
        List<String> requirementNos = list.stream().map(RequirementPartsPO::getRequirementNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(requirementNos)) {
            LambdaQueryWrapper<RequirementPO> query1 = new LambdaQueryWrapper<>();
            query1.select(RequirementPO::getId, RequirementPO::getRequirementNo,
                    RequirementPO::getRequirementType, RequirementPO::getPurchaseOrgCode);
            query1.eq(RequirementPO::getIsDelete, YesOrNoEnum.NO.getCode());
            query1.in(RequirementPO::getRequirementNo, requirementNos);
            query1.eq(RequirementPO::getPurchaseOrgCode, param.getPurchaseOrgCode());

            List<RequirementPO> requirements = requirementMapperService.list(query1);
            if (CollectionUtils.isNotEmpty(requirements)) {
                requirementMap = requirements.stream()
                        .collect(Collectors.toMap(RequirementPO::getRequirementNo, Function.identity(), (v1, v2) -> v1));
            }
        }

        if (MapUtils.isEmpty(requirementMap)) { return Collections.emptyList(); }

        Map<String, RequirementPO> finalRequirementMap = requirementMap;
        return BeanUtil.copyToList(list, RequirementSimpleRespDTO.class).stream()
                .filter(v -> finalRequirementMap.containsKey(v.getRequirementNo()))
                .peek(v -> {
                    RequirementPO requirementPO = finalRequirementMap.get(v.getRequirementNo());
                    v.setRequirementType(requirementPO.getRequirementType());
                    v.setPurchaseOrgCode(requirementPO.getPurchaseOrgCode());
                }).collect(Collectors.toList());
    }

    @Override
    public List<RequirementSimpleRespDTO> queryPendingByNos(List<String> requirementPartsNos) {
        if (CollectionUtils.isEmpty(requirementPartsNos)) { return Collections.emptyList(); }
        LambdaQueryWrapper<RequirementPartsPO> query = new LambdaQueryWrapper<>();
        query.select(RequirementPartsPO::getId, RequirementPartsPO::getRequirementNo,
                RequirementPartsPO::getRequirementPartsNo, RequirementPartsPO::getMaterialCode, RequirementPartsPO::getStatus);
        query.eq(RequirementPartsPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.eq(RequirementPartsPO::getStatus, RequirementPartsStatusEnum.PENDING.getCode());
        query.in(RequirementPartsPO::getRequirementPartsNo, requirementPartsNos);

        List<RequirementPartsPO> list = requirementPartsMapperService.list(query);
        if (CollectionUtils.isEmpty(list)) { return Collections.emptyList(); }
        return BeanUtil.copyToList(list, RequirementSimpleRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSqe(RequirementPartsSqeUpdateReqDTO reqDTO) {
        List<RequirementPartsPO> poList = requirementPartsMapperService.list(Wrappers.<RequirementPartsPO>lambdaQuery().eq(RequirementPartsPO::getRequirementPartsNo, reqDTO.getRequirementPartsNo()));
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        Date now = DateUtil.date();
        for (RequirementPartsPO po : poList) {
            po.setSqeId(reqDTO.getSqeId());
            po.setSqeName(reqDTO.getSqeName());
            BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), now);
        }
        requirementPartsMapperService.updateBatchById(poList);
    }

    @Override
    public void handleRequirementPartStatusChanged(RequirementPartStatusChangedMq mq) {
        handleRequirementPartStatusChangedBiz.execute(mq);
    }

    @Override
    public void handleSampleOrderChanged(SampleOrderChangeMq mq) {
        handleSampleOrderChangedBiz.execute(mq);
    }

    @Override
    public void handleMaterialCategoryRelChanged(MaterialCategoryRelChangedMQ mq) {
        handleMaterialCategoryRelChangedBiz.execute(mq);
    }

    @Override
    public void handlePartsPurchasePlanChange(PartsPurchasePlanChangeMq mq) {
        handlePartsPurchasePlanChangeBiz.execute(mq);
    }

    @Override
    public void handleInquiryOrderChange(InquiryOrderMQ mq) {
        handleInquiryOrderChangeBiz.execute(mq);
    }

    private void setOtherInfo(List<RequirementPartsQueryByNoRespDTO> pageList) {
        Map<String, String> requirementNoToProjectNoMap = pageList.stream()
                .filter(dto -> dto.getProjectNo() != null)
                .collect(Collectors.toMap(RequirementPartsQueryByNoRespDTO::getRequirementNo,
                        RequirementPartsQueryByNoRespDTO::getProjectNo, (v1, v2) -> v1));
        List<String> projectNoList = pageList.stream()
                .map(RequirementPartsQueryByNoRespDTO::getProjectNo).distinct().collect(Collectors.toList());

        List<ProjectPO> projectPOS = projectMapperService.lambdaQuery()
                .in(ProjectPO::getProjectNo, projectNoList)
                .list();
        Map<String, ProjectPO> projectPOMap = projectPOS.stream()
                .collect(Collectors.toMap(ProjectPO::getProjectNo, v -> v, (v1, v2) -> v1));

        for (RequirementPartsQueryByNoRespDTO respDTO : pageList) {
            respDTO.setSqeUserId(respDTO.getRecommendedSqe());
            respDTO.setSqeUserName(respDTO.getRecommendedSqeName());
            String projectNO = requirementNoToProjectNoMap.get(respDTO.getRequirementNo());
            if (ObjectUtil.isEmpty(projectNO)) {
                continue;
            }
            ProjectPO projectPO = projectPOMap.get(projectNO);
            // 客户名编码
            respDTO.setCustomerCode(null);
            // 客户名称
            respDTO.setCustomerName(projectPO.getCustomerName());
            // 需求方
            respDTO.setRequirementSide(projectPO.getDepartmentName());
        }
    }

    private void setEnums(List<RequirementPartsQueryByNoRespDTO> pageList) {
        Optional.ofNullable(pageList).ifPresent(k -> k.forEach(r -> {
            r.setIsDone(YesOrNoEnum.NO.getCode());
            if (RequirementPartsStatusEnum.COMPLETED.getCode().equals(r.getStatus())
                    || RequirementPartsStatusEnum.PENDING.getCode().equals(r.getStatus())
                    || RequirementPartsStatusEnum.CLOSED.getCode().equals(r.getStatus())
                    || RequirementPartsStatusEnum.RECREATED.getCode().equals(r.getStatus())
                    || RequirementPartsStatusEnum.PROCESSING.getCode().equals(r.getStatus())) {
                r.setIsDone(YesOrNoEnum.YES.getCode());
            }
            RequirementTypeEnum requirementTypeEnum = RequirementTypeEnum.getByCode(r.getRequirementType());
            Optional.ofNullable(requirementTypeEnum).ifPresent(j -> r.setRequirementTypeDesc(j.getChineseName()));
            PurchaseRequirementTypeEnum purchaseRequirementTypeEnum = PurchaseRequirementTypeEnum.getByCode(r.getPurchaseRequirementType());
            Optional.ofNullable(purchaseRequirementTypeEnum).ifPresent(j -> r.setPurchaseRequirementTypeDesc(j.getChineseName()));
            RequirementPartsStatusEnum statusEnum = RequirementPartsStatusEnum.getByCode(r.getStatus());
            Optional.ofNullable(statusEnum).ifPresent(j -> r.setStatusDesc(j.getChineseName()));
            PurchaseBizEnum purchaseBizEnum = PurchaseBizEnum.getByCode(r.getPurchaseBizType());
            Optional.ofNullable(purchaseBizEnum).ifPresent(j -> r.setPurchaseBizTypeDesc(j.getChineseName()));
            RecommendSupplierTypeEnum recommendSupplierTypeEnum = RecommendSupplierTypeEnum.getByCode(r.getRecommendedSupplierType());
            Optional.ofNullable(recommendSupplierTypeEnum).ifPresent(j -> r.setRecommendedSupplierTypeDesc(j.getChineseName()));
        }));
    }

}
