package com.weifu.srm.requirement.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "requirement_parts_purchase_plan")
public class RequirementPartsPurchasePlanPO extends BaseEntity implements Serializable {
    /** 需求编号取于requirement */
    @ApiModelProperty(name = "需求编号取于requirement_parts")
    private String requirementNo ;
    /** 零件需求编号取于requirement_parts */
    @ApiModelProperty(name = "零件需求编号取于requirement_parts")
    private String requirementPartsNo ;
    /** 零件采购计划编号 */
    @ApiModelProperty(name = "零件采购计划编号")
    private String planNo ;
    /** 零件需求类型（项目类非项目类） */
    @ApiModelProperty(value = "零件需求类型（0: 非项目类型 1: 项目类型）")
    private Integer planType ;
    /** SRM项目编号取于project表 */
    @ApiModelProperty(name = "SRM项目编号取于project表")
    private String projectNo ;
    /** 项目计划启动时间 */
    @ApiModelProperty(name = "项目计划启动时间")
    private Date projectStartPlanTime ;
    /** 项目启动完成时间 */
    @ApiModelProperty(name = "项目启动完成时间")
    private Date projectStartCompleteTime ;
    /** 项目启动状态(灰灯，蓝灯，绿灯，黄灯，红灯 */
    @ApiModelProperty(name = "项目启动状态(待开始，进行中，正常完成，延期，严重延期)")
    private String projectStartStatus ;
    /** 新供应商开始准入计划时间 */
    @ApiModelProperty(name = "新供应商开始准入计划时间")
    private Date newSupplierAdmissionStartPlanTime ;
    /** 新供应商供开始准入完成时间 */
    @ApiModelProperty(name = "新供应商供开始准入完成时间")
    private Date newSupplierAdmissionStartCompleteTime ;
    /** 新供应商开始准入完成状态 */
    @ApiModelProperty(name = "新供应商开始准入完成状态")
    private String newSupplierAdmissionStartStatus ;
    /** 新供应商准入完成计划时间 */
    @ApiModelProperty(name = "新供应商准入完成计划时间")
    private Date newSupplierAdmissionEndPlanTime ;
    /** 新供应商准入完成时间 */
    @ApiModelProperty(name = "新供应商准入完成时间")
    private Date newSupplierAdmissionEndCompleteTime ;
    /** 新供应商准入完成状态 */
    @ApiModelProperty(name = "新供应商准入完成状态")
    private String newSupplierAdmissionEndStatus;
    /** 寻源计划开始时间 */
    @ApiModelProperty(name = "寻源计划开始时间")
    private Date sourcingStartPlanTime ;
    /** 寻源开始完成时间 */
    @ApiModelProperty(name = "寻源开始完成时间")
    private Date sourcingStartCompleteTime ;
    /** 寻源开始完成状态 */
    @ApiModelProperty(name = "寻源开始完成状态")
    private String sourcingStartStatus ;
    /** a样件计划提交时间 */
    @ApiModelProperty(name = "a样件计划提交时间")
    private Date sampASubmitPlanTime ;
    /** a样件提交完成时间 */
    @ApiModelProperty(name = "a样件提交完成时间")
    private Date sampASubmitCompleteTime ;
    /** a样件提交完成状态 */
    @ApiModelProperty(name = "a样件提交完成状态")
    private String sampASubmitStatus ;
    /** 商务定点计划完成时间 */
    @ApiModelProperty(name = "商务定点计划完成时间")
    private Date businessDesignatedCompletePlanTime ;
    /** 商务定点完成时间 */
    @ApiModelProperty(name = "商务定点完成时间")
    private Date businessDesignatedCompleteCompleteTime ;
    /** 商务定点完成状态 */
    @ApiModelProperty(name = "商务定点完成状态")
    private String businessDesignatedCompleteStatus ;
    /** b样件计划提交时间 */
    @ApiModelProperty(name = "b样件计划提交时间")
    private Date sampBSubmitPlanTime ;
    /** b样件提交完成时间 */
    @ApiModelProperty(name = "b样件提交完成时间")
    private Date sampBSubmitCompleteTime ;
    /** b样件提交完成状态 */
    @ApiModelProperty(name = "b样件提交完成状态")
    private String sampBSubmitStatus ;
    /** OTS外购件计划提交时间 */
    @ApiModelProperty(name = "OTS外购件计划提交时间")
    private Date otsOutsourceSubmitPlanTime ;
    /** OTS外购件提交完成时间 */
    @ApiModelProperty(name = "OTS外购件提交完成时间")
    private Date otsOutsourceSubmitCompleteTime ;
    /** OTS外购件提交完成状态 */
    @ApiModelProperty(name = "OTS外购件提交完成状态")
    private String otsOutsourceSubmitStatus;
    /** ppap计划制定计划完成时间 */
    @ApiModelProperty(name = "ppap计划制定计划完成时间")
    private Date supplierPpapCompletePlanTime ;
    /** ppap计划制定完成时间 */
    @ApiModelProperty(name = "ppap计划制定完成时间")
    private Date supplierPpapCompleteCompleteTime ;
    /** ppap计划制定完成状态 */
    @ApiModelProperty(name = "ppap计划制定完成状态")
    private String supplierPpapCompleteStatus ;
    /** ppap计划提交时间 */
    @ApiModelProperty(name = "ppap计划提交时间")
    private Date supplierPpapSubmitPlanTime ;
    /** ppap提交完成时间 */
    @ApiModelProperty(name = "ppap提交完成时间")
    private Date supplierPpapSubmitCompleteTime ;
    /** ppap提交完成状态 */
    @ApiModelProperty(name = "ppap提交完成状态")
    private String supplierPpapSubmitStatus;
    /** ppap计划放行时间 */
    @ApiModelProperty(name = "ppap计划放行时间")
    private Date supplierPpapCompletionPlanTime ;
    /** ppap放行完成时间 */
    @ApiModelProperty(name = "ppap放行完成时间")
    private Date supplierPpapCompletionCompleteTime ;
    /** ppap放行完成状态 */
    @ApiModelProperty(name = "ppap放行完成状态")
    private String supplierPpapCompletionStatus ;
    /** 量产SOP计划时间 */
    @ApiModelProperty(name = "量产SOP计划时间")
    private Date manufactureSopPlanTime ;
    /** 量产SOP完成时间 */
    @ApiModelProperty(name = "量产SOP完成时间")
    private Date manufactureSopCompleteTime ;
    /** 量产SOP状态 */
    @ApiModelProperty(name = "量产SOP状态")
    private String manufactureSopStatus ;
    /** 批产早期退出计划时间 */
    @ApiModelProperty(name = "批产早期退出计划时间")
    private Date earlyControlExitPlanTime ;
    /** 批产早期退出完成时间 */
    @ApiModelProperty(name = "批产早期退出完成时间")
    private Date earlyControlExitCompleteTime ;
    /** 批产早期退出状态 */
    @ApiModelProperty(name = "批产早期退出状态")
    private String earlyControlExitStatus ;
    /** 首次样件计划预计交付时间 */
    @ApiModelProperty(name = "首次样件计划预计交付时间")
    private Date firstSampPlanDeliveryPlanTime ;
    /** 首次样件完成交付时间 */
    @ApiModelProperty(name = "首次样件完成交付时间")
    private Date firstSampPlanDeliveryCompleteTime ;
    /** 首次样件完成状态 */
    @ApiModelProperty(name = "首次样件完成状态")
    private String firstSampPlanDeliveryStatus ;
    /** 计划完成时间 */
    @ApiModelProperty(name = "计划完成时间")
    private Date planCompleteTime ;
    /** 原始计划Json */
    @ApiModelProperty(name = "原始计划时间json")
    private String orgPlanJson;
    /** 是否有过计划修改 0: 否 1: 是 */
    @ApiModelProperty(name = "是否有过计划修改 0: 否 1: 是")
    private Integer isUpdatePlan;
    /** 计划状态 */
    @ApiModelProperty(name = "计划状态")
    private String status ;
}
