<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.requirement.mapper.AttachmentRecordMapper">
    <delete id="deleteByBizNos" parameterType="java.util.List">
        delete from attachment_record where 1 = 1
        and business_no in
        <foreach collection="bizNos" item="bizNo" open="(" separator="," close=")">
            #{bizNo}
        </foreach>
        <if test="bizTypes != null and bizTypes.size > 0">
            and business_type in
            <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
                #{bizType}
            </foreach>
        </if>
    </delete>
</mapper>