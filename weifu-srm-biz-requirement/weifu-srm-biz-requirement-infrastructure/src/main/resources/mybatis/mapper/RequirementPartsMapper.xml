<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.requirement.mapper.RequirementPartsMapper">
    <select id="queryPartsPage" parameterType="com.baomidou.mybatisplus.core.conditions.query.QueryWrapper" resultType="com.weifu.srm.requirement.response.RequirementPartsQueryByNoRespDTO">
        <!-- 定义复杂的 SQL 查询 -->
        SELECT
            rp.requirement_no,
            rp.requirement_parts_no,
            rp.material_code,
            rp.material_desc,
            r.requirement_type ,
            r.purchase_requirement_type ,
            r.is_relate_project ,
            r.project_id ,
            r.project_no ,
            r.project_name ,
            r.requirement_desc ,
            r.customer_name,
            r.product_name,
            r.product_model,
            r.subsidiary_code,
            r.purchase_org_code,
            rp.plan_no,
            rp.category_first,
            rp.category_first_name,
            rp.category_second,
            rp.category_second_name,
            rp.category_third,
            rp.category_third_name,
            rp.requirement_drawing_id,
            rp.drawing_no,
            rp.drawing_version,
            rp.requirement_qty,
            rp.requirement_date,
            rp.unit,
            rp.status,
            rp.is_borrow_parts,
            rp.recommended_supplier_name,
            rp.recommended_supplier_type,
            rp.factory,
            rp.logistics_destination,
            rp.purchase_biz_type,
            rp.tech_contact_person,
            rp.purchase_expect_qty,
            rp.target_untaxed_price,
            rp.material_type,
            rp.batch_product_plm,
            rp.cost_center,
            rp.project_order_no,
            rp.inner_order_no,
            rp.trial_production_app_no,
            rp.slow_flow_status,
            rp.bp_id,
            rp.bp_name,
            rp.cpe_id,
            rp.cpe_name,
            rp.cpe_master_id,
            rp.cpe_master_name,
            rp.sqe_id,
            rp.sqe_name,
            rp.distribution_time,
            rp.submit_time,
            rp.inquiry_order_no,
            rp.order_no,
            rp.create_by,
            rp.create_time,
            rp.create_name,
            rp.recommended_cpe,
            rp.recommended_cpe_name,
            rp.recommended_sqe,
            rp.recommended_sqe_name,
            rp.current_operation_by,
            rp.current_operation_by_name,
            rp.update_by,
            rp.update_name,
            rp.update_time,
            rp.is_delete
        from requirement_parts rp
        left join requirement r on r.requirement_no = rp.requirement_no
        <if test="_parameter != null">
            ${ew.customSqlSegment}
        </if>
    </select>

</mapper>