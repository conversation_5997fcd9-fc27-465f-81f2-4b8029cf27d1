<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.requirement.mapper.ProjectTimeMapper">
    <select id="selectProjectTimeComplete"  resultType="com.weifu.srm.requirement.vo.ProjectTimeVo">
        SELECT
            pt.`project_no` as projectNo,
            pt.`stage_type` as stageType,
            pt.`start_date` as startDate,
            pt.`new_supplier_admission_completion_date` as newSupplierAdmissionCompletionDate,
            pt.`business_designated_completion_date` as businessDesignatedCompletionDate,
            pt.`ots_outsource_submit_date` as otsOutsourceSubmitDate,
            pt.`supplier_ppap_submit_date` as supplierPpapSubmitDate,
            pt.`supplier_ppap_completion_date` as supplierPpapCompletionDate,
            pt.`early_control_exit_date` as earlyControlExitDate,
            pt.`manufacture_sop_date` as manufactureSopDate,
            pt.`manufacture_sop_date` as manufactureSopDate,
            pt.`manufacture_sop_date` as manufactureSopDate,
            p.project_name as projectName,
            p.project_leader_id as projectLeaderId,
            p.project_leader_name as projectLeaderName
        FROM project_time pt, project p
        WHERE
            p.project_no = pt.project_no
            AND p.status in ('IMPLEMENT','STOPPING')
            AND pt.stage_type = 'COMPLETE'
            AND p.is_delete = 0
            AND pt.is_delete = 0
    </select>

    <select id="findNotStartTIme"  resultType="com.weifu.srm.requirement.po.ProjectTimePO">
        SELECT
            p.project_no AS projectNo
        FROM project p
        WHERE
            (p.status = 'IMPLEMENT' or p.status = 'STOPPING')
            AND p.is_delete = 0
            AND p.project_no not in (SELECT
                                        pt.project_no
                                    FROM project_time pt
                                    WHERE
                                        pt.stage_type = 'COMPLETE'
                                        AND pt.start_date is NOT NULL)
    </select>
    <select id="findPlanAndChange"  resultType="com.weifu.srm.requirement.po.ProjectTimePO">
        SELECT *
        FROM project_time pt
        WHERE
        pt.project_no = #{projectNo}
        and pt.stage_type IN ('PLAN','CHANGE');
    </select>
</mapper>