<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.weifu</groupId>
        <artifactId>weifu-srm-biz-requirement</artifactId>
        <version>1.0.1</version>
    </parent>

    <artifactId>weifu-srm-biz-requirement-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-requirement-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mybatis</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-id-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-integration-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq-sender</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-user-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-masterdata-api</artifactId>
            <version>1.0.2</version>
        </dependency>

    </dependencies>
</project>