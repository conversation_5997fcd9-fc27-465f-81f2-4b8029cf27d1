package com.weifu.srm.requirement.response.project;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class ProjectPageResDTO {
    /** id */
    private Long id;
    /** SRM项目编号 */
    private String projectNo ;
    /** 项目延期状态 */
    private String projectLightStatus ;
    /** 项目名称 */
    private String projectName ;
    /** 事业部/子公司名称 */
    private String subsidiaryName ;
    /** 需求部门名称 */
    private String departmentName ;
    /** 需求部门id */
    private String departmentCode ;
    /** 需求项目经理名称 */
    private String projectManagerName ;
    /** 客户名称 */
    private String customerName ;
    /** 项目负责人名称 */
    private String projectLeaderName ;
    /** 量产SOP时间 */
    private String sopDate;
    /** 外购件数量 */
    private Long outNum;
    /** 完成商务定点 */
    private Long outBusinessNum;
    /** 完成OTS样件提交 */
    private Long otsNum;
    /** n)	完成PPAP提交 */
    private Long submitPPAPNum;
    /** o)	完成PPAP放行 */
    private Long complainPPAPNum;
    /** 创建时间 */
    private String createTime;
    /** 项目状态 :编制中，审核中，执行中，终止中，已终止，已完成 */
    private String status;
}
