package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequirementPartsQueryReqDTO {
    @ApiModelProperty("是否精确查询 Y:精确查询 N:模糊查询")
    private String accurateQuery;

    @ApiModelProperty("零件需求编号 子需求号")
    private String requirementPartsNo;

    @ApiModelProperty("物料号 根据requirementPartsNo 查询需传")
    private String materialCode;
    @ApiModelProperty("采购组织编码 根据requirementPartsNo 查询需传")
    private String purchaseOrgCode ;

    public RequirementPartsQueryReqDTO(String accurateQuery,String requirementPartsNo){
        this.accurateQuery = accurateQuery;
        this.requirementPartsNo = requirementPartsNo;
    }
}
