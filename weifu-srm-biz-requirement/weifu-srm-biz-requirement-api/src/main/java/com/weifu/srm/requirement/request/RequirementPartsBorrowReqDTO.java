package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/15 16:44
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsBorrowReqDTO {

    @ApiModelProperty(value = "零件需求编号")
    @NotNull(message = "零件需求编号不能为空")
    private String requirementPartsNo;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName;
    /** 附件 */
    @ApiModelProperty("附件")
    private List<AttachmentMessageReqDTO> attachments;

}
