package com.weifu.srm.requirement.response.upload;

import com.weifu.srm.requirement.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class RequirementAnalysisBaseResDTO {
    @ApiModelProperty(value = "工厂")
    private String factory;
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    @ApiModelProperty(value = "物料描述")
    private String materialDesc;
    @ApiModelProperty(value = "样件需求数量")
    private String requirementQty;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "单位描述")
    private String unitDesc;
    @ApiModelProperty(value = "推荐供应商名称")
    private String recommendedSupplierName;
    @ApiModelProperty(value = "推荐供应商属性")
    private String recommendedSupplierType;
    @ApiModelProperty(value = "推荐供应商属性描述")
    private String recommendedSupplierTypeDesc;
    @ApiModelProperty(value = "收货地址")
    private String logisticsDestination;
    @ApiModelProperty(value = "采购业务类型")
    private String purchaseBizType;
    @ApiModelProperty(value = "采购业务类型描述")
    private String purchaseBizTypeDesc;
    @ApiModelProperty(value = "技术联系人")
    private String techContactPerson;
    @ApiModelProperty(value = "预计年采购量")
    private String purchaseExpectQty;
    @ApiModelProperty(value = "目标未税价格")
    private String targetUntaxedPrice;
    @ApiModelProperty(value = "材质")
    private String materialType;
    @ApiModelProperty(value = "批产后生命周期")
    private String batchProductPlm;
    @ApiModelProperty(value = "需求时间 - 首批样件需求交付时间; 在寻源类型下：指首批样件需求交付时间")
    private String requirementDate;

    @ApiModelProperty(value = "试制申请单号")
    private String trialProductionAppNo;
    @ApiModelProperty(value = "成本中心")
    private String costCenter;
    @ApiModelProperty(value = "成本中心描述")
    private String costCenterDesc;
    @ApiModelProperty(value = "项目订单号")
    private String projectOrderNo;
    @ApiModelProperty(value = "内部订单号")
    private String innerOrderNo;

    @ApiModelProperty(value = "每月产能需求")
    private Integer productionRequireMthQty;
    @ApiModelProperty(value = "关联物料号")
    private String relateMaterialCode;

    @ApiModelProperty(value = "图纸编号")
    private String drawingNo;
    @ApiModelProperty(value = "客户指定供应商说明")
    private List<AttachmentMessageReqDTO> customerSpecifiedSupplierDesc;
    @ApiModelProperty(value = "其他技术附件")
    private List<AttachmentMessageReqDTO> otherTechAttachment;
    @ApiModelProperty(value = "物流及包装说明")
    private List<AttachmentMessageReqDTO> logisticsAndPackagingDesc;

}
