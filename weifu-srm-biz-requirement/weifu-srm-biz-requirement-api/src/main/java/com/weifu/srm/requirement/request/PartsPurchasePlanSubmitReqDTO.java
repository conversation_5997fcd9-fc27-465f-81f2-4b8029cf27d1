package com.weifu.srm.requirement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.requirement.request.project.ProjectBaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class PartsPurchasePlanSubmitReqDTO  extends ProjectBaseReqDTO implements Serializable {
    /** 需求编号取于requirement */
    @ApiModelProperty(value = "需求编号取于requirement_parts")
    @NotNull(message = "需求编号不能为空")
    private String requirementNo ;
    /** 零件需求编号取于requirement_parts */
    @ApiModelProperty(value = "零件需求编号取于requirement_parts")
    @NotNull(message = "零件需求编号不能为空")
    private String requirementPartsNo ;
    /** 零件采购计划编号 */
    @ApiModelProperty(value = "零件采购计划编号")
    private String planNo ;
    /** 零件需求类型（项目类非项目类） */
    @ApiModelProperty(value = "零件需求类型（0: 非项目类型 1: 项目类型）")
    private Integer planType ;
    /** SRM项目编号取于project表 */
    @ApiModelProperty(value = "SRM项目编号取于project表")
    private String projectNo ;
    /** 项目计划启动时间 */
    @ApiModelProperty(value = "项目计划启动时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date projectStartPlanTime ;
    /** 新供应商开始准入计划时间 */
    @ApiModelProperty(value = "新供应商开始准入计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date newSupplierAdmissionStartPlanTime ;
    /** 新供应商准入完成计划时间 */
    @ApiModelProperty(value = "新供应商准入完成计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date newSupplierAdmissionEndPlanTime ;
    /** 寻源计划开始时间 */
    @ApiModelProperty(value = "寻源计划开始时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date sourcingStartPlanTime ;
    /** a样件计划提交时间 */
    @ApiModelProperty(value = "a样件计划提交时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date sampASubmitPlanTime ;
    /** 商务定点计划完成时间 */
    @ApiModelProperty(value = "商务定点计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date businessDesignatedCompletePlanTime ;
    /** b样件计划提交时间 */
    @ApiModelProperty(value = "b样件计划提交时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date sampBSubmitPlanTime ;
    /** OTS外购件计划提交时间 */
    @ApiModelProperty(value = "OTS外购件计划提交时间")
    @NotNull(message = "OTS外购件计划提交时间不能为空")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date otsOutsourceSubmitPlanTime ;
    /** ppap计划制定计划完成时间 */
    @ApiModelProperty(value = "ppap计划制定计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date supplierPpapCompletePlanTime ;
    /** ppap计划提交时间 */
    @ApiModelProperty(value = "ppap计划提交时间")
    @NotNull(message = "ppap计划提交时间不能为空")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date supplierPpapSubmitPlanTime ;
    /** ppap计划放行时间 */
    @ApiModelProperty(value = "ppap计划放行时间")
    @NotNull(message = "ppap计划放行时间不能为空")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date supplierPpapCompletionPlanTime ;
    /** 量产SOP计划时间 */
    @ApiModelProperty(value = "量产SOP计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date manufactureSopPlanTime ;
    /** 批产早期退出计划时间 */
    @ApiModelProperty(value = "批产早期退出计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date earlyControlExitPlanTime ;
    /** 首次样件计划预计交付时间 */
    @ApiModelProperty(value = "首次样件计划预计交付时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date firstSampPlanDeliveryPlanTime ;
    /** 计划修改原因类型 */
    @ApiModelProperty(value = "计划修改原因类型")
    private String planUpdateReasonType ;
    /** 计划修改原因 */
    @ApiModelProperty(value = "计划修改原因")
    private String planUpdateReason ;
    /** 计划修改备注 */
    @ApiModelProperty(value = "计划修改备注")
    private String remark ;
    /**修改原因附件*/
    @ApiModelProperty(value = "计划修改附件")
    private List<AttachmentMessageReqDTO> updatePlanAttachment;

}
