package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementSaveReqDTO implements Serializable {

    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNo;
    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求类型")
    @NotNull(message = "需求类型不能为空")
    private String requirementType;
    /**
     * 是否关联项目
     */
    @ApiModelProperty(value = "是否关联项目")
    @NotNull(message = "是否关联项目不能为空")
    private Integer isRelateProject;
    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id", notes = "项目Id")
    private Long projectId;
    /**
     * 项目编号;项目表业务主键
     */
    @ApiModelProperty(value = "项目编号", notes = "项目表业务主键")
    private String projectNo;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", notes = "项目名称")
    private String projectName;
    /**
     * 需求导向;1.质量优先 2.成本优先 3.交付优先
     */
    @ApiModelProperty(value = "需求导向", notes = "1.质量优先 2.成本优先 3.交付优先")
    private String requirementOriented;
    /**
     * 采购需求类型;当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单
     */
    @ApiModelProperty(value = "采购需求类型", notes = "当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单")
    private String purchaseRequirementType;
    /**
     * 外购件总需求成本;概览页会显示此字段；一个项目会存在多个需求，以第一次获取到输入为准（审批通过的）
     */
    @ApiModelProperty(value = "外购件总需求成本", notes = "概览页会显示此字段；一个项目会存在多个需求，以第一次获取到输入为准（审批通过的）")
    private BigDecimal costTotalAmt;
    /**
     * 事业部/子公司;1.非项目类支持用户手动选择 项目类，可从项目中带入，可修改
     */
    @ApiModelProperty(value = "事业部/子公司", notes = "1.非项目类支持用户手动选择 项目类，可从项目中带入，可修改")
    private String subsidiaryCode;
    /**
     * 采购组织编码;1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改
     */
    @ApiModelProperty(value = "采购组织编码", notes = "1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改")
    private String purchaseOrgCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    private String productModel;
    /**
     * 需求概述;1.默认带入项目名称，可修改； 限制输入50字符
     */
    @ApiModelProperty(value = "需求概述", notes = "1.默认带入项目名称，可修改； 限制输入50字符")
    private String requirementDesc;
    @ApiModelProperty(value = "备注")
    private String remark;
    /** 操作人名称 */
    @ApiModelProperty("当前操作人")
    private Long operationBy;
    /** 操作人名称 */
    @ApiModelProperty("当前操作人名称")
    private String operationByName;
    /** 操作人名称 */
    @ApiModelProperty("提交人角色")
    private String operationByRole;
    /** 提交人挂的品类ID */
    @ApiModelProperty("提交人挂的品类ID")
    private Long operationByThreeCategory;
    /** 提交人挂的品类代码 */
    @ApiModelProperty("提交人挂的品类代码")
    private String operationByThreeCategoryCode;
    /** 提交人挂的品类名称 */
    @ApiModelProperty("提交人挂的品类名称")
    private String operationByThreeCategoryName;
    /** 项目BP（也有人叫采购BP） */
    @ApiModelProperty("项目BP（也有人叫采购BP）")
    private Long bpId;
    @ApiModelProperty("项目BP名称（也有人叫采购BP名称）")
    private String bpName;
    /*** 零件需求*/
    @ApiModelProperty(value = "零件需求")
    private List<RequirementPartsSaveReqDTO> requirementParts;
    /*** BOM文件 */
    @ApiModelProperty(value = "BOM文件",notes = "")
    private List<AttachmentMessageReqDTO> bomAttachment;

}
