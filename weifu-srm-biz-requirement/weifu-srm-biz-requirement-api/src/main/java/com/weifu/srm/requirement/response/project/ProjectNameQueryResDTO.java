package com.weifu.srm.requirement.response.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class ProjectNameQueryResDTO {
    /** SRM项目Id */
    private Long projectId ;
    /** SRM项目编号 */
    private String projectNo ;
    /** 项目编编码 */
    private String projectCode ;
    /** 项目类型 */
    private String projectType ;
    /** 项目名称 */
    private String projectName ;
    /** 事业部/子公司id */
    private String subsidiaryCode ;
    /** 事业部/子公司名称 */
    private String subsidiaryName ;
    /** 三级品类 */
    private String operationByThreeCategory ;
    /** 三级品类名称 */
    private String operationByThreeCategoryName ;
    /** 外购件总目标成本 */
    private BigDecimal costTotalAmt;
}
