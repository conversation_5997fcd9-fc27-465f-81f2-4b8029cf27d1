package com.weifu.srm.requirement.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PartsPurchaseChangeLogRespDTO implements Serializable {

    @ExcelProperty("序号")
    @ColumnWidth(20)
    private Long id;
    /** 修改对象类型：项目采购计划，零件采购计划 */
    @ExcelProperty("修改对象")
    @ColumnWidth(20)
    private String type ;
    /** 编号：项目编号，零件计划编号 */
    @ExcelProperty("编号")
    @ColumnWidth(20)
    private String no ;
    /** 修改人id */
    @ExcelIgnore
    private Long modifyId ;
    /** 修改人名称 */
    @ExcelProperty("修改人")
    @ColumnWidth(20)
    private String modifyName ;
    /** 项目修改时间 */
    @ExcelProperty("修改时间")
    @ColumnWidth(20)
    private String modifyDate ;
    /** 修改原因:需求项目计划调整，图纸变动导致计划变动，采购计划管理需要，其他 */
    @ExcelProperty("修改原因")
    @ColumnWidth(20)
    private String modifyReason ;
    /** 备注 */
    @ExcelProperty("备注")
    @ColumnWidth(20)
    private String remark ;
}
