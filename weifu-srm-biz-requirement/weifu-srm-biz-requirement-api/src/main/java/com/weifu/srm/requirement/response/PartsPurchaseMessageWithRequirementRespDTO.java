package com.weifu.srm.requirement.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class PartsPurchaseMessageWithRequirementRespDTO implements Serializable {

    /** 需求编号取于requirement */
    @ApiModelProperty(name = "需求编号取于requirement_parts")
    private String requirementNo ;
    /** 零件需求编号取于requirement_parts */
    @ApiModelProperty(name = "零件需求编号取于requirement_parts")
    private String requirementPartsNo ;
    /** 零件需求类型（项目类非项目类） */
    @ApiModelProperty(value = "零件需求类型（0: 非项目类型 1: 项目类型）")
    private Integer planType ;
    /** SRM项目编号取于project表 */
    @ApiModelProperty(name = "SRM项目编号取于project表")
    private String projectNo ;
    /** 项目计划启动原始时间 */
    @ApiModelProperty(name = "项目计划启动原始时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date projectStartPlanTime ;
    @ApiModelProperty(name = "新供应商准入计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date newSupplierAdmissionEndPlanTime ;
    @ApiModelProperty(name = "商务定点计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date businessDesignatedCompletePlanTime ;
    @ApiModelProperty(name = "OTS外购件计划提交时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date otsOutsourceSubmitPlanTime ;
    @ApiModelProperty(name = "供应商PPAP计划提交时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date supplierPpapSubmitPlanTime ;
    @ApiModelProperty(name = "供应商PPAP放行计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date supplierPpapCompletionPlanTime ;
    @ApiModelProperty(name = "供应商批产早期控制退出计划完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date earlyControlExitPlanTime ;
    /** 量产SOP计划时间 */
    @ApiModelProperty(name = "量产SOP计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date manufactureSopPlanTime ;
    @ApiModelProperty(name = "零件需求CPEId")
    private Long cpeId;
}
