package com.weifu.srm.requirement.response.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class ProjectResDTO {
    /** SRM项目id */
    private Long id ;
    /** SRM项目编号 */
    private String projectNo ;
    /** 项目编编码 */
    private String projectCode ;
    /** 项目状态 :编制中，审核中，执行中，终止中，已终止，已完成 */
    private String status;
    /** 事业部/子公司id */
    private String subsidiaryCode ;
    /** 事业部/子公司名称 */
    private String subsidiaryName ;
    /** 需求部门id */
    private String departmentCode ;
    /** 需求部门名称 */
    private String departmentName ;
    /** 需求项目经理id */
    private Long projectManagerId ;
    /** 需求项目经理名称 */
    private String projectManagerName ;
    /** 项目类型 */
    private String projectType ;
    /** 项目名称 */
    private String projectName ;
    /** 客户名称 */
    private String customerName ;
    /** 产品名称 */
    private String productName ;
    /** 产品型号 */
    private String productModel ;
    /** 平台名称 */
    private String platformName ;
    /** 发起阶段（概念、A阶段、B阶段、C阶段、D阶段、批产阶段） */
    private String initiationPhase ;
    /** 项目定级 */
    private String projectClassification ;
    /** 项目负责人id */
    private Long projectLeaderId ;
    /** 项目负责人名称 */
    private String projectLeaderName ;
    /** 计划时间 */
    private ProjectTimeResDTO times;
    /** 附件id集合 */
    private List<ProjectFileResDTO> fileList;
    /** 备注 */
    private String remark;
    /** 项目提交人角色 */
    private String submitByRole ;
    /** 项目提交人三级品类 */
    private Long submitByThreeCategory ;
    /** 项目提交人三级品类名称 */
    private String submitByThreeCategoryName ;
    /** 项目终止原因及附件 */
    private ProjectTerminationResDTO projectTerminationResDTO;
    @ApiModelProperty("外购件总目标成本")
    private BigDecimal outsourceTotalTargetCost;
}
