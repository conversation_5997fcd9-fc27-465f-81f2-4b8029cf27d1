package com.weifu.srm.requirement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsQueryPageReqDTO extends PageRequest implements Serializable {

    /** 0未处理 1已处理 */
    @ApiModelProperty(value = "已处理/未处理")
    private Integer isDone ;
    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键,更新时必传")
    private String requirementNo ;
    /** 零件需求编号 */
    @ApiModelProperty(value = "零件需求编号",notes = "零件需求编号,更新时必传")
    private String requirementPartsNo ;
    /** 物料编码;费用化报销时可不填写 */
    @ApiModelProperty(value = "物料编码",notes = "费用化报销时可不填写")
    private String materialCode ;
    /** 物料描述;1.支持手动输入 支持以填入的物料号带出 */
    @ApiModelProperty(value = "物料描述",notes = "1.支持手动输入 支持以填入的物料号带出")
    @NotNull(message = "物料描述不能为空")
    private String materialDesc ;
    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求类型")
    private String requirementType;
    /**
     * 需求概述;
     */
    @ApiModelProperty(value = "需求概述")
    private String requirementDesc;
    /**
     * 是否关联项目
     */
    @ApiModelProperty(value = "是否关联项目", notes = "")
    private Integer isRelateProject;
    /**
     * 项目名称;
     */
    @ApiModelProperty(value = "项目名称", notes = "项目名称")
    private String projectNane;
    /** 三级品类 */
    @ApiModelProperty(value = "三级品类",notes = "")
    private String categoryThird ;

    /** 状态;编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭 */
    @ApiModelProperty(value = "状态",notes = "编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭")
    private String status ;
    /** 借用件 */
    @ApiModelProperty(value = "借用件",notes = "")
    private Integer isBorrowParts ;
    /** 推荐供应商名称 */
    @ApiModelProperty(value = "推荐供应商名称",notes = "")
    private String recommendedSupplierName ;
    /** 工厂 */
    @ApiModelProperty(value = "工厂",notes = "")
    private List<String> factory ;
    @ApiModelProperty(value = "采购需求类型")
    private String purchaseRequirementType;
    @ApiModelProperty(value = "采购业务类型",notes = "1.零件采购 2.工序协作")
    private String purchaseBizType ;
    /** 采购BP */
    @ApiModelProperty(value = "采购BP",notes = "")
    private Long bpId ;
    /** 品类工程师 */
    @ApiModelProperty(value = "品类工程师",notes = "")
    private Long cpeId ;
    /** 品类组长 */
    @ApiModelProperty(value = "品类组长",notes = "")
    private Long cpeMasterId ;
    /** 质量工程师 */
    @ApiModelProperty(value = "质量工程师",notes = "")
    private Long sqeId ;
    /** 询价单号 */
    @ApiModelProperty(value = "询价单号",notes = "")
    private String inquiryOrderNo ;
    /** 订单号 */
    @ApiModelProperty(value = "订单号",notes = "")
    private String orderNo ;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeStart ;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeEnd ;
    /** 创建人 */
    @ApiModelProperty(value = "创建人",notes = "")
    private Long createBy ;
    /** 创建人名称 */
    @ApiModelProperty(value = "创建人名称",notes = "")
    private Long createName ;
    /** 关键字 */
    @ApiModelProperty("关键字")
    private String keyWord ;
    /** 当前操作人 */
    @ApiModelProperty(value = "当前操作人",notes = "")
    private Long currentOperationBy ;

    private Long operationBy ;
}
