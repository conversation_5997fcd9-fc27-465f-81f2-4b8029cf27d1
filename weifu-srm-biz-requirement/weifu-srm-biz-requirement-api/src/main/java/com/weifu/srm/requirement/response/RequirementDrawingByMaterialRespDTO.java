package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/15 14:39
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementDrawingByMaterialRespDTO implements Serializable {
    /** 零件需求编号 */
    @ApiModelProperty(name = "零件需求编号",notes = "")
    private String requirementPartsNo ;
    /** 物料号 */
    @ApiModelProperty(name = "物料号",notes = "")
    private String materialCode;
    /** 图纸编号 */
    @ApiModelProperty(name = "图纸编号",notes = "")
    private String drawingNo ;
    /** 图纸版本号 */
    @ApiModelProperty(name = "图纸版本号",notes = "")
    private String drawingVersion ;
}
