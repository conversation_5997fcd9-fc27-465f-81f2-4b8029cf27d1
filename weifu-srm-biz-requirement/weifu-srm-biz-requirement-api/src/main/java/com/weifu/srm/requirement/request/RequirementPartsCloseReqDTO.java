package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/15 16:44
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsCloseReqDTO {

    @ApiModelProperty(value = "零件需求编号", notes = "零件需求编号 == 批量")
    @NotNull(message = "零件需求编号不能为空")
    private List<String> requirementPartsNo;
    @ApiModelProperty("操作人")
    private Long operationBy;
    @ApiModelProperty("操作人名称")
    private String operationByName;
    @ApiModelProperty("关闭原因")
    private String returnReason;
    @ApiModelProperty("关闭附件")
    private List<AttachmentMessageReqDTO> attachments;

}
