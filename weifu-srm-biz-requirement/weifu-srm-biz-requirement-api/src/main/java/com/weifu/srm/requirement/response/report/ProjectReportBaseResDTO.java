package com.weifu.srm.requirement.response.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectReportBaseResDTO {

    @ApiModelProperty("已关闭")
    private Long closeNum;

    @ApiModelProperty("不涉及")
    private Long notInvolvedNum;

    @ApiModelProperty("未完成")
    private Long incompleteNum;

    @ApiModelProperty("已完成")
    private Long completedNum;

    @ApiModelProperty("借用件")
    private Long borrowNum;

    @ApiModelProperty("总数")
    private Long totalNum;
}
