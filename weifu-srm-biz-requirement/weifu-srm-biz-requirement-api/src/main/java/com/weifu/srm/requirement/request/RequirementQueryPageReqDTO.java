package com.weifu.srm.requirement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementQueryPageReqDTO extends PageRequest implements Serializable {

    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNo;
    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求类型")
    private String requirementType;
    /**
     * 是否关联项目
     */
    @ApiModelProperty(value = "是否关联项目", notes = "")
    private Integer isRelateProject;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 采购需求类型;当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单
     */
    @ApiModelProperty(value = "采购需求类型", notes = "当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单")
    private String purchaseRequirementType;
    /**
     * 采购组织编码;1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改
     */
    @ApiModelProperty(value = "采购组织编码", notes = "1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改")
    private List<String> purchaseOrgCode;

    @ApiModelProperty(value = "需求概述", notes = "1.默认带入项目名称，可修改； 限制输入50字符")
    private String requirementDesc;
    @ApiModelProperty(value = "状态", notes = "状态")
    private String status;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeStart;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTimeEnd;
    @ApiModelProperty(value = "需求申请人名称(模糊检索)")
    private String requirementApplyName;

    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy ;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName ;
    /** 关键字 */
    @ApiModelProperty("关键字")
    private String keyWord ;
}
