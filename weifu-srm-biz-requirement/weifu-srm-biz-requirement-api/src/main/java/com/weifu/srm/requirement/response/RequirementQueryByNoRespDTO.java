package com.weifu.srm.requirement.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.requirement.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementQueryByNoRespDTO {
    @ApiModelProperty(value = "需求编号")
    private String requirementNo;
    @ApiModelProperty(value = "需求类型")
    private String requirementType;
    @ApiModelProperty(value = "需求类型描述")
    private String requirementTypeDesc;
    @ApiModelProperty(value = "是否关联项目", notes = "")
    private Integer isRelateProject;
    @ApiModelProperty(value = "项目Id")
    private Long projectId;
    @ApiModelProperty(value = "项目编号")
    private String projectNo;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "需求导向", notes = "1.质量优先 2.成本优先 3.交付优先")
    private String requirementOriented;
    @ApiModelProperty(value = "需求导向描述", notes = "1.质量优先 2.成本优先 3.交付优先")
    private String requirementOrientedDesc;
    @ApiModelProperty(value = "采购需求类型", notes = "当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单")
    private String purchaseRequirementType;
    @ApiModelProperty(value = "采购需求类型描述", notes = "当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单")
    private String purchaseRequirementTypeDesc;
    @ApiModelProperty(value = "外购件总需求成本", notes = "概览页会显示此字段；一个项目会存在多个需求，以第一次获取到输入为准（审批通过的）")
    private BigDecimal costTotalAmt;
    @ApiModelProperty(value = "事业部/子公司", notes = "1.非项目类支持用户手动选择 项目类，可从项目中带入，可修改")
    private String subsidiaryCode;
    @ApiModelProperty(value = "采购组织编码", notes = "1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改")
    private String purchaseOrgCode;
    @ApiModelProperty(value = "客户名称", notes = "")
    private String customerName;
    @ApiModelProperty(value = "产品名称", notes = "")
    private String productName;
    @ApiModelProperty(value = "产品型号", notes = "")
    private String productModel;
    @ApiModelProperty(value = "需求概述", notes = "1.默认带入项目名称，可修改； 限制输入50字符")
    private String requirementDesc;
    @ApiModelProperty(value = "备注", notes = "")
    private String remark;
    @ApiModelProperty(value = "状态", notes = "")
    private String status;
    @ApiModelProperty(value = "状态描述", notes = "")
    private String statusDesc;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private String operationByRole;
    /** 操作人名称 */
    @ApiModelProperty("操作人挂的品类")
    private String operationByThreeCategory;
    /** 操作人名称 */
    @ApiModelProperty("操作人挂的品类名称")
    private String operationByThreeCategoryName;
    /** 项目BP（也有人叫采购BP） */
    @ApiModelProperty("项目BP（也有人叫采购BP）")
    private Long bpId;
    @ApiModelProperty("项目BP名称（也有人叫采购BP名称）")
    private String bpName;
    /** 原零件编号 */
    @ApiModelProperty(name = "原零件编号",notes = "")
    private String oriPartsNo;
    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long createBy;
    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    private String createName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    /*** 零件需求*/
    @ApiModelProperty(value = "零件需求", notes = "")
    List<RequirementPartsQueryByNoRespDTO> requirementParts;
    /*** BOM文件 */
    @ApiModelProperty(value = "BOM文件",notes = "")
    private List<AttachmentMessageReqDTO> bomAttachment;
}
