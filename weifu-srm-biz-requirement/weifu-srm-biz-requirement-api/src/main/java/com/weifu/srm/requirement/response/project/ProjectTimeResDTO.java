package com.weifu.srm.requirement.response.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class ProjectTimeResDTO {

    @ApiModelProperty("SRM项目编号")
    private String projectNo ;
    @ApiModelProperty("阶段类型（计划时间，最新计划时间，最终完成时间）")
    private String stageType ;
    @ApiModelProperty("项目启动时间")
    private String startDate ;
    @ApiModelProperty("新供应商准入完成时间")
    private String newSupplierAdmissionCompletionDate ;
    @ApiModelProperty("商务定点完成时间")
    private String businessDesignatedCompletionDate ;
    @ApiModelProperty("OTS外购件提交时间")
    private String otsOutsourceSubmitDate ;
    @ApiModelProperty("供应商PPAP提交时间")
    private String supplierPpapSubmitDate ;
    @ApiModelProperty("供应商PPAP放行完成时间")
    private String supplierPpapCompletionDate ;
    @ApiModelProperty("批产早期控制退出时间")
    private String earlyControlExitDate ;
    @ApiModelProperty("量产SOP时间")
    private String manufactureSopDate ;
}
