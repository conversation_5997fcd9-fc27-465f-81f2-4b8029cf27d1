package com.weifu.srm.requirement.response.report;

import com.weifu.srm.requirement.response.RequirementQueryByNoRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectOverviewCorrRequirementResDTO {

    @ApiModelProperty(name = "需求编号",notes = "")
    private String requirementNo ;

    @ApiModelProperty(name = "需求详情",notes = "")
    private RequirementQueryByNoRespDTO requirementDto;
}
