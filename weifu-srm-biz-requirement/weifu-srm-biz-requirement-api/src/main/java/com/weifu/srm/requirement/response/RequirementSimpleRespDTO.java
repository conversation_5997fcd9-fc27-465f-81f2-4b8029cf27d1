package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RequirementSimpleRespDTO {
    @ApiModelProperty("需求编号 主需求号")
    private String requirementNo ;

    @ApiModelProperty("需求类型")
    private String requirementType ;

    @ApiModelProperty("零件需求编号 子需求号")
    private String requirementPartsNo ;

    @ApiModelProperty("物料编码")
    private String materialCode ;

    @ApiModelProperty(name = "采购组织编码")
    private String purchaseOrgCode ;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;
    @ApiModelProperty(value = "客户名编码")
    private String customerCode;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "需求方")
    private String requirementSide;
    @ApiModelProperty(value = "质量工程师UserId")
    private Long sqeUserId;
    @ApiModelProperty(value = "质量工程师RealName")
    private String sqeUserName;
}
