package com.weifu.srm.requirement.response.upload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 标准订单
 */
@Data
@NoArgsConstructor
public class RequirementAnalysisResultResDTO{

    @ApiModelProperty("导入的信息")
    private List<RequirementAnalysisBaseResDTO> resDTO;
    @ApiModelProperty("导入是否成功，1：成功，0：不成功")
    private Integer status;
    @ApiModelProperty("错误信息：Key：row 行号，value：错误信息（每种错误信息结尾有换行符号）")
    private List<Map<Integer, String>> errorMapList;
}
