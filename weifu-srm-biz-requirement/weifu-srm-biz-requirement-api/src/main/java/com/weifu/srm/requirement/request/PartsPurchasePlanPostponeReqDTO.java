package com.weifu.srm.requirement.request;

import com.weifu.srm.requirement.request.project.ProjectBaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class PartsPurchasePlanPostponeReqDTO extends ProjectBaseReqDTO {

    /** 零件采购计划编号 */
    @ApiModelProperty(value = "零件采购计划编号")
    @NotNull(message = "零件采购计划编号不能为空")
    private String planNo ;
    /** 零件采购计划延期节点 */
    @ApiModelProperty(value = "零件采购计划延期节点")
    @NotNull(message = "零件采购计划延期节点不能为空")
    private String postPoneNode ;
    /** 零件采购计划延期原因类型 */
    @ApiModelProperty(value = "零件采购计划延期原因类型")
    private String postPoneType ;
    /** 零件采购计划延期原因 */
    @ApiModelProperty(value = "零件采购计划延期原因")
    @NotNull(message = "零件采购计划延期原因不能为空")
    private String postPoneReason ;


}
