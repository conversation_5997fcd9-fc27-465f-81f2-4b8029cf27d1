package com.weifu.srm.requirement.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementQueryPageRespDTO implements Serializable {

    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNo;

    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求类型")
    private String requirementType;
    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String ticketNo;
    /**
     * 需求类型描述
     */
    @ApiModelProperty(value = "需求类型描述")
    private String requirementTypeDesc;
    /**
     * 是否关联项目
     */
    @ApiModelProperty(value = "是否关联项目", notes = "")
    private Integer isRelateProject;
    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private Long projectId;
    /**
     * 项目编号;项目表业务主键
     */
    @ApiModelProperty(value = "项目编号", notes = "项目表业务主键")
    private String projectNo;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", notes = "项目名称")
    private String projectName;
    /**
     * 需求导向;1.质量优先 2.成本优先 3.交付优先
     */
    @ApiModelProperty(value = "需求导向", notes = "1.质量优先 2.成本优先 3.交付优先")
    private String requirementOriented;
    /**
     * 需求导向;1.质量优先 2.成本优先 3.交付优先
     */
    @ApiModelProperty(value = "需求导向描述")
    private String requirementOrientedDesc;
    /**
     * 采购需求类型;当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单
     */
    @ApiModelProperty(value = "采购需求类型", notes = "当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单")
    private String purchaseRequirementType;
    /**
     * 采购需求类型;当requirementType是采购需求时必填1.费用报销 2.标准订单 3. 费用化订单
     */
    @ApiModelProperty(value = "采购需求类型描述")
    private String purchaseRequirementTypeDesc;
    /**
     * 外购件总需求成本;概览页会显示此字段；一个项目会存在多个需求，以第一次获取到输入为准（审批通过的）
     */
    @ApiModelProperty(value = "外购件总需求成本", notes = "概览页会显示此字段；一个项目会存在多个需求，以第一次获取到输入为准（审批通过的）")
    private BigDecimal costTotalAmt;
    /**
     * 事业部/子公司;1.非项目类支持用户手动选择 项目类，可从项目中带入，可修改
     */
    @ApiModelProperty(value = "事业部/子公司", notes = "1.非项目类支持用户手动选择 项目类，可从项目中带入，可修改")
    private String subsidiaryCode;
    /**
     * 采购组织编码;1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改
     */
    @ApiModelProperty(value = "采购组织编码", notes = "1.非项目类，支持用户手动选择 项目类，根据项目需求方信息，可自动带出采购组织编码，并且可修改")
    private String purchaseOrgCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", notes = "")
    private String customerName;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", notes = "")
    private String productName;
    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号", notes = "")
    private String productModel;
    /**
     * 需求概述;1.默认带入项目名称，可修改； 限制输入50字符
     */
    @ApiModelProperty(value = "需求概述", notes = "1.默认带入项目名称，可修改； 限制输入50字符")
    private String requirementDesc;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", notes = "")
    private String remark;
    @ApiModelProperty(value = "状态", notes = "状态")
    private String status;
    @ApiModelProperty(value = "状态描述", notes = "状态描述")
    private String statusDesc;
    /**
     * 分发时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    private String createName;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy ;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName ;

    /** 外购件数量 */
    @ApiModelProperty("外购件数量")
    private Long materialQty ;
}
