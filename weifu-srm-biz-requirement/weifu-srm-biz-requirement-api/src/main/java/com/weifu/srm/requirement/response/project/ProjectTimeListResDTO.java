package com.weifu.srm.requirement.response.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProjectTimeListResDTO {
    @ApiModelProperty("计划时间")
    private ProjectTimeResDTO planTimes;
    @ApiModelProperty("最新计划时间")
    private ProjectTimeResDTO changeTimes;
    @ApiModelProperty("最终完成时间")
    private ProjectTimeResDTO completeTimes;
    @ApiModelProperty("SOP完成时间附件")
    private ProjectFileResDTO sopAttachmentRecord;
    @ApiModelProperty("时间轴状态")
    private ProjectTimeAxisStatusResDTO projectTimeAxisStatusResDTO;
}
