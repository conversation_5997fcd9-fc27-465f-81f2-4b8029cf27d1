package com.weifu.srm.requirement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsSaveReqDTO  implements Serializable {
    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键,更新时必传")
    private String requirementNo;
    /** 零件需求编号 */
    @ApiModelProperty(value = "零件需求编号",notes = "零件需求编号,更新时必传")
    private String requirementPartsNo;
    /** 物料编码;费用化报销时可不填写 */
    @ApiModelProperty(value = "物料编码",notes = "费用化报销时可不填写")
    private String materialCode;
    /** 物料描述;1.支持手动输入 支持以填入的物料号带出 */
    @ApiModelProperty(value = "物料描述",notes = "1.支持手动输入 支持以填入的物料号带出")
    @NotNull(message = "物料描述不能为空")
    private String materialDesc;
    /** 一级品类 */
    @ApiModelProperty(value = "一级品类")
    private String categoryFirst;
    /** 二级品类 */
    @ApiModelProperty(value = "二级品类")
    private String categorySecond;
    /** 三级品类 */
    @ApiModelProperty(value = "三级品类")
    private String categoryThird;
    @ApiModelProperty(value = "图纸表Id")
    private Long requirementDrawingId;
    /** 图纸编号;windchill/用户上传 */
    @ApiModelProperty(value = "图纸编号",notes = "windchill/用户上传")
    private String drawingNo;
    /** 图纸版本号 */
    @ApiModelProperty(value = "图纸版本号")
    private String drawingVersion;
    /** 需求数量;重量类单位需保留小数 */
    @ApiModelProperty(value = "需求数量",notes = "重量类单位需保留小数")
    private BigDecimal requirementQty;
    /** 需求时间 */
    @ApiModelProperty(value = "需求时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date requirementDate;
    /** 单位;从物料表带出 */
    @ApiModelProperty(value = "单位",notes = "从物料表带出")
    private String unit ;
    /** 状态;编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭 */
    @ApiModelProperty(value = "状态",notes = "编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭")
    private String status;
    /** 借用件 */
    @ApiModelProperty(value = "借用件")
    private Integer isBorrowParts;
    /** 推荐供应商名称 */
    @ApiModelProperty(value = "推荐供应商名称")
    private String recommendedSupplierName;
    /** 推荐供应商属性;1.普通推荐 2.客户指定 */
    @ApiModelProperty(value = "推荐供应商属性",notes = "1.普通推荐 2.客户指定")
    private String recommendedSupplierType;
    /** 工厂 */
    @ApiModelProperty(value = "工厂")
    private String factory;
    /** 物流目的地 */
    @ApiModelProperty(value = "物流目的地")
    private String logisticsDestination;
    /** 采购业务类型;1.零件采购 2.工序协作 */
    @ApiModelProperty(value = "采购业务类型",notes = "1.零件采购 2.工序协作")
    private String purchaseBizType;
    /** 技术联系人;用户手动输入限制10字符 */
    @ApiModelProperty(value = "技术联系人",notes = "用户手动输入限制10字符")
    private String techContactPerson;
    /** 预计年采购量 */
    @ApiModelProperty(value = "预计年采购量")
    private BigDecimal purchaseExpectQty;
    /** 目标未税价格 */
    @ApiModelProperty(value = "目标未税价格")
    private BigDecimal targetUntaxedPrice;
    /** 材质 */
    @ApiModelProperty(value = "材质")
    private String materialType;
    /** 批产后生命周期 */
    @ApiModelProperty(value = "批产后生命周期")
    private String batchProductPlm;
    /** 成本中心;下拉框取cost_center_info表 */
    @ApiModelProperty(value = "成本中心",notes = "下拉框取cost_center_info表")
    private String costCenter;
    /** 项目订单号 */
    @ApiModelProperty(value = "项目订单号")
    private String projectOrderNo;
    /** 内部订单号 */
    @ApiModelProperty(value = "内部订单号")
    private String innerOrderNo;
    /** 试制申请单号 */
    @ApiModelProperty(value = "试制申请单号")
    private String trialProductionAppNo;
    /** 慢流动状态;从sap中获取 */
    @ApiModelProperty(value = "慢流动状态",notes = "从sap中获取")
    private String slowFlowStatus;
    /** 采购BP */
    @ApiModelProperty(value = "采购BP")
    private Long bpId;
    /** 品类工程师;操作人 */
    @ApiModelProperty(value = "品类工程师",notes = "操作人")
    private Long cpeId;
    /** 品类组长 */
    @ApiModelProperty(value = "品类组长")
    private Long cpeMasterId;
    /** 质量工程师 */
    @ApiModelProperty(value = "质量工程师")
    private Long sqeId;
    /** 推荐品类工程师 */
    @ApiModelProperty(value = "推荐品类工程师")
    private Long recommendedCpe;
    /** 推荐品类工程师名称 */
    @ApiModelProperty(value = "推荐品类工程师名称")
    private String recommendedCpeName;
    /** 推荐质量工程师id */
    @ApiModelProperty(value = "推荐质量工程师id")
    private Long recommendedSqe;
    /** 推荐质量工程师名称 */
    @ApiModelProperty(value = "推荐质量工程师名称")
    private String recommendedSqeName;
    /** 关联物料号 */
    @ApiModelProperty(value = "关联物料号")
    private String relateMaterialCode;
    /** 每月产能需求 */
    @ApiModelProperty(value = "每月产能需求")
    @Max(value = 999999)
    @Min(value = 0)
    private Integer productionRequireMthQty;
    /** 科目分配类别 */
    @ApiModelProperty(value = "科目分配类别")
    private String accountCategory;
    /** 询价单号 */
    @ApiModelProperty(value = "询价单号")
    private String inquiryOrderNo;
    /** 订单号 */
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /** 客户指定供应商说明 */
    @ApiModelProperty(value = "客户指定供应商说明")
    private List<AttachmentMessageReqDTO> customerSpecifiedSupplierDesc;
    /** 物流及包装说明 */
    @ApiModelProperty(value = "物流及包装说明")
    private List<AttachmentMessageReqDTO> logisticsAndPackagingDesc;
    /** 其他技术附件 */
    @ApiModelProperty(value = "其他技术附件")
    private List<AttachmentMessageReqDTO> otherTechAttachment;
}
