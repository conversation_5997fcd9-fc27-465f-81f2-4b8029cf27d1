package com.weifu.srm.requirement.response;

import com.weifu.srm.requirement.response.project.ProjectFileResDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PartsPurchasePlanChangeLogResDTO {
    private Long id;
    /** 编号：计划编号 */
    private String no ;
    /** 修改对象类型：零件需求采购计划 */
    private String type ;
    /** 修改人id */
    private Long modifyId ;
    /** 修改人名称 */
    private String modifyName ;
    /** 项目修改时间 */
    private String modifyDate ;
    /** 修改原因:需求项目计划调整，图纸变动导致计划变动，采购计划管理需要，其他 */
    private String modifyReason ;
    /** 其他原因 */
    private String otherReason ;
    /** 备注 */
    private String remark ;
    /** 修改项目日志附件 */
    private List<ProjectFileResDTO> projectFileResDTOs;
}
