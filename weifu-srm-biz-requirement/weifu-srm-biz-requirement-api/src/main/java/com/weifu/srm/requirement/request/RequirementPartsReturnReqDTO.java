package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/15 16:44
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsReturnReqDTO {

    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键")
    private String requirementNo;
    /** 零件需求编号 */
    @ApiModelProperty(value = "零件需求编号",notes = "零件需求编号 == 批量")
    @NotNull(message = "零件需求编号不能为空")
    private List<String> requirementPartsNo;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName;
    /** 退回原因 */
    @ApiModelProperty("退回原因")
    private String returnReason;
    /** 退回附件 */
    @ApiModelProperty("退回附件")
    private List<AttachmentMessageReqDTO> attachments;

}
