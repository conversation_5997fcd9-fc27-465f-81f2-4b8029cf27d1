package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PartsPurchasePlanNodeRespDTO implements Serializable {

    /** 零件需求/采购计划编号 */
    @ApiModelProperty(name = "零件需求/采购计划编号")
    private String planNo ;
    /** 计划节点 */
    @ApiModelProperty(value = "计划节点")
    private String planNode ;
    /** 是否存在待办 */
    @ApiModelProperty(value = "是否存在待办 0: 不存在 1:存在")
    private Integer existTodo = 0;
    /** 是否存在可展示的延期原因 */
    @ApiModelProperty(value = "是否存在可展示的延期原因 0: 不存在 1:存在")
    private Integer existPostponeReason = 0;;
    /** 是否存在可展示的登记附件 */
    @ApiModelProperty(value = "是否存在可展示的登记附件 0: 不存在 1: 存在")
    private Integer existAttachment = 0;;

    public PartsPurchasePlanNodeRespDTO(String planNo, String planNode) {
        this.planNo = planNo;
        this.planNode = planNode;
    }
}
