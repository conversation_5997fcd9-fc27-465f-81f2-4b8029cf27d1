package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/8 10:06
 * @Description
 * @Version 1.0
 */
@Data
public class MaterialDrawingFileListSaveReqDTO implements Serializable {
    @ApiModelProperty("图纸list")
    private List<MaterialDrawingFileSaveReqDTO> pictureList;
    @ApiModelProperty("操作人")
    private Long operationBy ;
    @ApiModelProperty("操作人名称")
    private String operationByName ;
}
