package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/8/15 16:44
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsTransferReqDTO {

    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键")
    private String requirementNo;
    @ApiModelProperty(value = "零件需求编号")
    @NotNull(message = "零件需求编号不能为空")
    private String requirementPartsNo;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName;
    /** 转派对像不能为空 */
    @ApiModelProperty("被转派人员ID")
    @NotNull(message = "转派对像不能为空")
    private Long transferTo;
    /** 被转派人员名称 */
    @ApiModelProperty("被转派人员名称")
    private String transferToName;

}
