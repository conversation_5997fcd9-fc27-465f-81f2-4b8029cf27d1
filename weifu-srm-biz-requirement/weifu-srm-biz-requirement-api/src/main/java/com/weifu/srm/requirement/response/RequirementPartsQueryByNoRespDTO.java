package com.weifu.srm.requirement.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.requirement.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsQueryByNoRespDTO {

    public static final String RELATED_CATEGORY_UN_RELATED = "UN_RELATED"; // 物料关联品类状态-未关联
    public static final String RELATED_CATEGORY_APPROVING = "APPROVING"; // 物料关联品类状态-审核中
    public static final String RELATED_CATEGORY_RELATED = "RELATED"; // 物料关联品类状态-已关联
    public static final String RELATED_CATEGORY_NOT_NEED_RELATED = "NOT_NEED_RELATED"; // 物料关联品类状态-无需关联

    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键,更新时必传")
    private String requirementNo;
    /** 原需求编号 */
    @ApiModelProperty(name = "原需求编号",notes = "")
    private String oriRequirementNo;
    /** 零件需求编号 */
    @ApiModelProperty(value = "零件需求编号",notes = "零件需求编号,更新时必传")
    private String requirementPartsNo;
    /** 零件采购计划编号 */
    @ApiModelProperty(value = "零件采购计划编号",notes = "")
    private String planNo;
    /** 物料编码;费用化报销时可不填写 */
    @ApiModelProperty(value = "物料编码",notes = "费用化报销时可不填写")
    private String materialCode;
    /** 物料描述;1.支持手动输入 支持以填入的物料号带出 */
    @ApiModelProperty(value = "物料描述",notes = "1.支持手动输入 支持以填入的物料号带出")
    @NotNull(message = "物料描述不能为空")
    private String materialDesc;
    /** 是否关联项目 */
    @ApiModelProperty(value = "是否关联项目",notes = "")
    private Integer isRelateProject;
    @ApiModelProperty(value = "事业部/子公司")
    private String subsidiaryCode;
    @ApiModelProperty(value = "事业部/子公司名称")
    private String subsidiaryName;
    /**
     * 需求类型;寻源需求、采购需求
     */
    @ApiModelProperty(value = "需求类型")
    private String requirementType;
    @ApiModelProperty(value = "需求类型描述")
    private String requirementTypeDesc;
    @ApiModelProperty(value = "采购组织编码")
    private String purchaseOrgCode;
    @ApiModelProperty(value = "采购需求类型")
    private String purchaseRequirementType;
    @ApiModelProperty(value = "采购需求类型描述")
    private String purchaseRequirementTypeDesc;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "产品型号")
    private String productModel;
    @ApiModelProperty(name = "总需求上的备注")
    private String requirementRemark;
    @ApiModelProperty(value = "BOM文件")
    private List<AttachmentMessageReqDTO> bomAttachment;
    /**
     * 需求概述;
     */
    @ApiModelProperty(value = "需求概述")
    private String requirementDesc;
    /**
     * 项目id;
     */
    @ApiModelProperty(value = "项目Id", notes = "项目id")
    private Long projectId;
    /**
     * 项目编号;
     */
    @ApiModelProperty(value = "项目编号", notes = "项目编号")
    private String projectNo;
    /**
     * 非SRM系统项目编号
     */
    @ApiModelProperty(value = "项目编码（非SRM系统项目编号）", notes = "项目编码")
    private String projectCode;
    /**
     * 项目名称;
     */
    @ApiModelProperty(value = "项目名称", notes = "项目名称")
    private String projectName;
    /** 一级品类 */
    @ApiModelProperty(name = "一级品类",notes = "")
    private String categoryFirst;
    /** 一级品类名称 */
    @ApiModelProperty(name = "一级品类名称",notes = "")
    private String categoryFirstName;
    /** 二级品类 */
    @ApiModelProperty(name = "二级品类",notes = "")
    private String categorySecond;
    /** 二级品类名称 */
    @ApiModelProperty(name = "二级品类名称",notes = "")
    private String categorySecondName;
    /** 三级品类 */
    @ApiModelProperty(name = "三级品类",notes = "")
    private String categoryThird;
    /** 三级品类 */
    @ApiModelProperty(name = "三级品类名称",notes = "")
    private String categoryThirdName;
    private Long requirementDrawingId;
    /** 图纸编号;windchill/用户上传 */
    @ApiModelProperty(value = "图纸编号",notes = "windchill/用户上传")
    private String drawingNo;
    /** 图纸版本号 */
    @ApiModelProperty(value = "图纸版本号",notes = "")
    private String drawingVersion;
    /** 图纸版本号 */
    @ApiModelProperty(value = "图纸地址",notes = "")
    private String drawingUrl;
    /** 文件命名称 */
    @ApiModelProperty(value = "文件命名称",notes = "")
    private String drawingName;
    /** 图纸版本号 */
    @ApiModelProperty(value = "文件命名称",notes = "")
    private String fileName;
    /** 需求数量;重量类单位需保留小数 */
    @ApiModelProperty(value = "需求数量",notes = "重量类单位需保留小数")
    private BigDecimal requirementQty;
    /** 需求时间 */
    @ApiModelProperty(value = "需求时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date requirementDate;
    /** 量产SOP计划时间 */
    @ApiModelProperty(name = "量产SOP计划时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date manufactureSopPlanTime;
    /** 单位;从物料表带出 */
    @ApiModelProperty(value = "单位",notes = "从物料表带出")
    private String unit;
    /** 单位;从物料表带出单位描述 */
    @ApiModelProperty(value = "单位描述",notes = "单位描述")
    private String unitDesc;
    /** 状态;编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭 */
    @ApiModelProperty(value = "状态",notes = "编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭")
    private String status;
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;
    /** 借用件 */
    @ApiModelProperty(value = "借用件",notes = "")
    private Integer isBorrowParts;
    /** 推荐供应商名称 */
    @ApiModelProperty(value = "推荐供应商名称",notes = "")
    private String recommendedSupplierName;
    /** 推荐供应商属性;1.普通推荐 2.客户指定 */
    @ApiModelProperty(value = "推荐供应商属性",notes = "1.普通推荐 2.客户指定")
    private String recommendedSupplierType;
    @ApiModelProperty(value = "推荐供应商属性描述",notes = "1.普通推荐 2.客户指定")
    private String recommendedSupplierTypeDesc;
    /** 工厂 */
    @ApiModelProperty(value = "工厂",notes = "")
    private String factory;
    /** 工厂描述 */
    @ApiModelProperty(value = "工厂描述",notes = "")
    private String factoryName;
    /** 物流目的地 */
    @ApiModelProperty(value = "物流目的地",notes = "")
    private String logisticsDestination;
    /** 采购业务类型;1.零件采购 2.工序协作 */
    @ApiModelProperty(value = "采购业务类型",notes = "PARTS_PURCHASE：零件采购 PROCESS_WRITING：工序协作")
    private String purchaseBizType;
    @ApiModelProperty(value = "采购业务类型描述",notes = "PROCESS_WRITING.零件采购 PROCESS_WRITING.工序协作")
    private String purchaseBizTypeDesc;
    /** 技术联系人;用户手动输入限制10字符 */
    @ApiModelProperty(value = "技术联系人",notes = "用户手动输入限制10字符")
    private String techContactPerson;
    /** 预计年采购量 */
    @ApiModelProperty(value = "预计年采购量",notes = "")
    private BigDecimal purchaseExpectQty;
    /** 目标未税价格 */
    @ApiModelProperty(value = "目标未税价格",notes = "")
    private BigDecimal targetUntaxedPrice;
    /** 材质 */
    @ApiModelProperty(value = "材质",notes = "")
    private String materialType;
    /** 批产后生命周期 */
    @ApiModelProperty(value = "批产后生命周期",notes = "")
    private String batchProductPlm;
    /** 成本中心;下拉框取cost_center_info表 */
    @ApiModelProperty(value = "成本中心",notes = "下拉框取cost_center_info表")
    private String costCenter;
    @ApiModelProperty(value = "成本中心描述")
    private String costCenterDesc;
    /** 项目订单号 */
    @ApiModelProperty(value = "项目订单号",notes = "")
    private String projectOrderNo;
    /** 内部订单号 */
    @ApiModelProperty(value = "内部订单号",notes = "")
    private String innerOrderNo;
    /** 试制申请单号 */
    @ApiModelProperty(value = "试制申请单号",notes = "")
    private String trialProductionAppNo;
    /** 慢流动状态;从sap中获取 */
    @ApiModelProperty(value = "慢流动状态",notes = "从sap中获取")
    private String slowFlowStatus;
    /** 采购BP */
    @ApiModelProperty(name = "采购BP",notes = "")
    private Long bpId;
    /** 采购BP */
    @ApiModelProperty(name = "采购BP",notes = "")
    private String bpName;
    /** 品类工程师;操作人 */
    @ApiModelProperty(name = "品类工程师",notes = "操作人")
    private Long cpeId;
    /** 品类工程师;操作人 */
    @ApiModelProperty(name = "品类工程师名称",notes = "操作人")
    private String cpeName;
    /** 品类组长 */
    @ApiModelProperty(name = "品类组长",notes = "")
    private Long cpeMasterId;
    /** 品类组长 */
    @ApiModelProperty(name = "品类组长名称",notes = "")
    private String cpeMasterName;
    /** 质量工程师 */
    @ApiModelProperty(name = "质量工程师",notes = "")
    private Long sqeId;
    /** 质量工程师 */
    @ApiModelProperty(name = "质量工程师名称",notes = "")
    private String sqeName;
    /** 推荐品类工程师 */
    @ApiModelProperty(value = "推荐品类工程师",notes = "")
    private Long recommendedCpe;
    /** 推荐品类工程师名称 */
    @ApiModelProperty(value = "推荐品类工程师名称",notes = "")
    private String recommendedCpeName;
    /** 推荐质量工程师id */
    @ApiModelProperty(value = "推荐质量工程师id",notes = "")
    private Long recommendedSqe;
    /** 推荐质量工程师名称 */
    @ApiModelProperty(value = "推荐质量工程师名称",notes = "")
    private String recommendedSqeName;
    /** 组长分发时间 */
    @ApiModelProperty(name = "组长分发时间",notes = "组长分发时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date cpeMasterDistributeTime;
    /** bp分发时间 */
    @ApiModelProperty(name = "bp分发时间",notes = "bp分发时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date bpDistributeTime;
    /** 关联物料号 */
    @ApiModelProperty(value = "关联物料号",notes = "")
    private String relateMaterialCode;
    /** 每月产能需求 */
    @ApiModelProperty(value = "每月产能需求",notes = "")
    private Integer productionRequireMthQty;
    /** 科目分配类别 */
    @ApiModelProperty(value = "科目分配类别",notes = "")
    private String accountCategory;
    /** 询价单号 */
    @ApiModelProperty(value = "询价单号",notes = "")
    private String inquiryOrderNo;
    /** 订单号 */
    @ApiModelProperty(value = "订单号",notes = "")
    private String orderNo;
    @ApiModelProperty(value = "创建时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
    @ApiModelProperty(value = "需求申请人ID")
    private Long createBy;
    @ApiModelProperty(value = "需求申请人Name")
    private String createName;
    private Integer isDone;
    /** 当前操作人 */
    @ApiModelProperty("当前操作人")
    private Long currentOperationBy;
    /** 当前操作人名称 */
    @ApiModelProperty("当前操作人名称")
    private String currentOperationByName;
    /** 客户指定供应商说明 */
    @ApiModelProperty(value = "客户指定供应商说明")
    private List<AttachmentMessageReqDTO> customerSpecifiedSupplierDesc;
    /** 物流及包装说明 */
    @ApiModelProperty(value = "物流及包装说明")
    private List<AttachmentMessageReqDTO> logisticsAndPackagingDesc;
    /** 其他技术附件 */
    @ApiModelProperty(value = "其他技术附件")
    private List<AttachmentMessageReqDTO> otherTechAttachment;
    /** 关闭原因 */
    private String closeReason;
    /** 关闭原因附件 */
    @ApiModelProperty(value = "关闭原因附件")
    private List<AttachmentMessageReqDTO> closeReasonAttachment;
    @ApiModelProperty(value = "借用件附件")
    private List<AttachmentMessageReqDTO> borrowAttachment;

    @ApiModelProperty(value = "客户名编码")
    private String customerCode;
    @ApiModelProperty(value = "需求方")
    private String requirementSide;
    @ApiModelProperty(value = "质量工程师UserId")
    private Long sqeUserId;
    @ApiModelProperty(value = "质量工程师RealName")
    private String sqeUserName;
    @ApiModelProperty(value = "物料关联品类的状态")
    private String relatedCategoryStatus;

}
