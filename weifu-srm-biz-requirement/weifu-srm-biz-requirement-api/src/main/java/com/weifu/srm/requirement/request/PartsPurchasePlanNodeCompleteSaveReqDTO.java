package com.weifu.srm.requirement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.requirement.request.project.ProjectBaseReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class PartsPurchasePlanNodeCompleteSaveReqDTO extends ProjectBaseReqDTO implements Serializable {

    /** 零件采购计划编号 */
    @ApiModelProperty(value = "零件采购计划编号")
    @NotNull(message = "零件采购计划编号不能为空")
    private String planNo ;
    /** 零件采购计划节点 */
    @ApiModelProperty(value = "零件采购计划节点")
    @NotNull(message = "零件采购计划节点不能为空")
    private String planNode ;
    /** 零件采购计划节点完成时间 */
    @ApiModelProperty(value = "零件采购计划节点完成时间")
    @NotNull(message = "零件采购计划节点完成时间不能为空")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date completeTime ;
    /** 零件采购计划节点完成附件 */
    @ApiModelProperty(value = "零件采购计划节点完成附件")
    private List<AttachmentMessageReqDTO> completeAttachments ;

}
