package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/8/15 16:44
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsDistributeReqDTO {
    /** 需求编号;需求表业务主键 */
    @ApiModelProperty(value = "需求编号",notes = "需求表业务主键")
    private String requirementNo ;
    @ApiModelProperty(value = "零件需求编号")
    @NotNull(message = "零件需求编号不能为空")
    private String requirementPartsNo ;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy ;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName;
    /** 转派给 */
    @ApiModelProperty("分发对象")
    @NotNull(message = "分发对象不能为空")
    private Long distributeTo ;
    /** 转派给 名称*/
    @ApiModelProperty("转派给 名称")
    private String distributeToName ;
    /** 推荐品类工程师 */
    @ApiModelProperty(name = "推荐品类工程师",notes = "")
    private Long recommendedCpe ;
    /** 推荐品类工程师名称 */
    @ApiModelProperty(name = "推荐品类工程师名称",notes = "")
    private String recommendedCpeName ;
    /** 推荐质量工程师id */
    @ApiModelProperty(name = "推荐质量工程师id",notes = "")
    private Long recommendedSqe ;
    /** 推荐质量工程师名称 */
    @ApiModelProperty(name = "推荐质量工程师名称",notes = "")
    private String recommendedSqeName ;
}
