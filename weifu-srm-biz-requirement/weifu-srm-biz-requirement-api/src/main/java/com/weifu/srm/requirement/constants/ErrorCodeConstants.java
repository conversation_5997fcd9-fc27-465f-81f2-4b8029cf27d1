package com.weifu.srm.requirement.constants;

public class ErrorCodeConstants {

    /** 查询成本中心信息失败 */
    public static final String QUERY_COST_CENTER_FAILED = "query.cost.center.failed";
    /** 查询物料对应品类失败 */
    public static final String QUERY_MATERIAL_CATEGORY_FAILED = "query.material.category.failed";
    /** 查询品类详情失败 */
    public static final String QUERY_CATEGORY_DETAIL_FAILED = "query.category.detail.failed";
    /** 查询审核中的物料失败 */
    public static final String QUERY_MATERIAL_UNDER_REVIEW_FAILED = "query.material.under.review.failed";
    /** 查询事业部项目BP失败 */
    public static final String QUERY_DIVISION_PROJECT_BP_FAILED = "query.division.project.bp.failed";
    /** 查询事业部管控关系失败 */
    public static final String QUERY_DIVISION_CONTROL_RELATION_FAILED = "query.division.control.relation.failed";
    /** 该事业部{0}未配置管控关系 */
    public static final String DIVISION_CONTROL_RELATION_NOT_CONFIGURED = "division.control.relation.not.configured";
    /** 批量关闭时，所有零件需求需要在同一总需求下 */
    public static final String BATCH_CLOSE_REQUIRE_SAME_TOTAL_REQ = "batch.close.require.same.total.req";
    /** 批量重新发起时，所有零件需求需要在同一总需求下 */
    public static final String BATCH_RECREATE_REQUIRE_SAME_TOTAL_REQ = "batch.recreate.require.same.total.req";
    /** 至少有一个零件需求当前状态不允许退回 */
    public static final String AT_LEAST_ONE_REQ_CANNOT_RETURN = "at.least.one.req.cannot.return";
    /** 当前零件需求的状态不能一起退回 */
    public static final String REQ_STATUS_CANNOT_RETURN_TOGETHER = "req.status.cannot.return.together";

}
