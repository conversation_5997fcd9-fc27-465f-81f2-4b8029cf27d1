package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PartsPurchasePlanDetailRespDTO {
    /** 需求编号取于requirement */
    @ApiModelProperty(name = "需求编号取于requirement_parts")
    private String requirementNo ;
    /** 零件需求编号取于requirement_parts */
    @ApiModelProperty(name = "零件需求编号取于requirement_parts")
    private String requirementPartsNo ;
    /** 零件采购计划编号 */
    @ApiModelProperty(name = "零件采购计划编号")
    private String planNo ;
    /** 零件需求类型（项目类非项目类） */
    @ApiModelProperty(value = "零件需求类型（0: 非项目类型 1: 项目类型）")
    private Integer planType ;
    /** SRM项目编号取于project表 */
    @ApiModelProperty(name = "SRM项目编号取于project表")
    private String projectNo ;
    /** 项目计划启动原始时间 */
    @ApiModelProperty(name = "项目计划启动原始时间")
    private String projectStartOrgTime ;
    /** 项目计划启动时间 */
    @ApiModelProperty(name = "项目计划启动时间")
    private String projectStartPlanTime ;
    /** 项目启动完成时间 */
    @ApiModelProperty(name = "项目启动完成时间")
    private String projectStartCompleteTime ;
    /** 项目启动状态(灰灯，蓝灯，绿灯，黄灯，红灯 */
    @ApiModelProperty(name = "项目启动状态(待开始，进行中，正常完成，延期，严重延期)")
    private String projectStartStatus ;
    /** 新供应商开始准入原始时间 */
    @ApiModelProperty(name = "新供应商开始准入原始时间")
    private String newSupplierAdmissionStartOrgTime ;
    /** 新供应商开始准入计划时间 */
    @ApiModelProperty(name = "新供应商开始准入计划时间")
    private String newSupplierAdmissionStartPlanTime ;
    /** 新供应商供开始准入完成时间 */
    @ApiModelProperty(name = "新供应商供开始准入完成时间")
    private String newSupplierAdmissionStartCompleteTime ;
    /** 新供应商开始准入完成状态 */
    @ApiModelProperty(name = "新供应商开始准入完成状态")
    private String newSupplierAdmissionStartStatus ;
    /** 新供应商准入完成原始时间 */
    @ApiModelProperty(name = "新供应商准入完成原始时间")
    private String newSupplierAdmissionEndOrgTime ;
    /** 新供应商准入完成计划时间 */
    @ApiModelProperty(name = "新供应商准入完成计划时间")
    private String newSupplierAdmissionEndPlanTime ;
    /** 新供应商准入完成时间 */
    @ApiModelProperty(name = "新供应商准入完成时间")
    private String newSupplierAdmissionEndCompleteTime ;
    /** 新供应商准入完成状态 */
    @ApiModelProperty(name = "新供应商准入完成状态")
    private String newSupplierAdmissionEndStatus;
    /** 寻源计划原始时间 */
    @ApiModelProperty(name = "寻源计划原始时间")
    private String sourcingStartOrgTime ;
    /** 寻源计划开始时间 */
    @ApiModelProperty(name = "寻源计划开始时间")
    private String sourcingStartPlanTime ;
    /** 寻源开始完成时间 */
    @ApiModelProperty(name = "寻源开始完成时间")
    private String sourcingStartCompleteTime ;
    /** 寻源开始完成状态 */
    @ApiModelProperty(name = "寻源开始完成状态")
    private String sourcingStartStatus ;
    /** a样件计划原始时间 */
    @ApiModelProperty(name = "a样件计划原始时间")
    private String sampASubmitOrgTime ;
    /** a样件计划提交时间 */
    @ApiModelProperty(name = "a样件计划提交时间")
    private String sampASubmitPlanTime ;
    /** a样件提交完成时间 */
    @ApiModelProperty(name = "a样件提交完成时间")
    private String sampASubmitCompleteTime ;
    /** a样件提交完成状态 */
    @ApiModelProperty(name = "a样件提交完成状态")
    private String sampASubmitStatus ;
    /** 商务定点计划原始时间 */
    @ApiModelProperty(name = "商务定点计划原始时间")
    private String businessDesignatedCompleteOrgTime ;
    /** 商务定点计划完成时间 */
    @ApiModelProperty(name = "商务定点计划完成时间")
    private String businessDesignatedCompletePlanTime ;
    /** 商务定点完成时间 */
    @ApiModelProperty(name = "商务定点完成时间")
    private String businessDesignatedCompleteCompleteTime ;
    /** 商务定点完成状态 */
    @ApiModelProperty(name = "商务定点完成状态")
    private String businessDesignatedCompleteStatus ;
    /** b样件计划原始时间 */
    @ApiModelProperty(name = "b样件计划原始时间")
    private String sampBSubmitOrgTime ;
    /** b样件计划提交时间 */
    @ApiModelProperty(name = "b样件计划提交时间")
    private String sampBSubmitPlanTime ;
    /** b样件提交完成时间 */
    @ApiModelProperty(name = "b样件提交完成时间")
    private String sampBSubmitCompleteTime ;
    /** b样件提交完成状态 */
    @ApiModelProperty(name = "b样件提交完成状态")
    private String sampBSubmitStatus ;
    /** OTS外购件计划原始时间 */
    @ApiModelProperty(name = "OTS外购件计划原始时间")
    private String otsOutsourceSubmitOrgTime ;
    /** OTS外购件计划提交时间 */
    @ApiModelProperty(name = "OTS外购件计划提交时间")
    private String otsOutsourceSubmitPlanTime ;
    /** OTS外购件提交完成时间 */
    @ApiModelProperty(name = "OTS外购件提交完成时间")
    private String otsOutsourceSubmitCompleteTime ;
    /** OTS外购件提交完成状态 */
    @ApiModelProperty(name = "OTS外购件提交完成状态")
    private String otsOutsourceSubmitStatus;
    /** ppap计划制定计划原始时间 */
    @ApiModelProperty(name = "ppap计划制定计划原始时间")
    private String supplierPpapCompleteOrgTime ;
    /** ppap计划制定计划完成时间 */
    @ApiModelProperty(name = "ppap计划制定计划完成时间")
    private String supplierPpapCompletePlanTime ;
    /** ppap计划制定完成时间 */
    @ApiModelProperty(name = "ppap计划制定完成时间")
    private String supplierPpapCompleteCompleteTime ;
    /** ppap计划制定完成状态 */
    @ApiModelProperty(name = "ppap计划制定完成状态")
    private String supplierPpapCompleteStatus ;
    /** ppap计划原始时间 */
    @ApiModelProperty(name = "ppap计划原始时间")
    private String supplierPpapSubmitOrgTime ;
    /** ppap计划提交时间 */
    @ApiModelProperty(name = "ppap计划提交时间")
    private String supplierPpapSubmitPlanTime ;
    /** ppap提交完成时间 */
    @ApiModelProperty(name = "ppap提交完成时间")
    private String supplierPpapSubmitCompleteTime ;
    /** ppap提交完成状态 */
    @ApiModelProperty(name = "ppap提交完成状态")
    private String supplierPpapSubmitStatus;
    /** ppap计划放行原始时间 */
    @ApiModelProperty(name = "ppap计划放行原始时间")
    private String supplierPpapCompletionOrgTime ;
    /** ppap计划放行时间 */
    @ApiModelProperty(name = "ppap计划放行时间")
    private String supplierPpapCompletionPlanTime ;
    /** ppap放行完成时间 */
    @ApiModelProperty(name = "ppap放行完成时间")
    private String supplierPpapCompletionCompleteTime ;
    /** ppap放行完成状态 */
    @ApiModelProperty(name = "ppap放行完成状态")
    private String supplierPpapCompletionStatus ;
    /** 量产SOP计划原始时间 */
    @ApiModelProperty(name = "量产SOP计划原始时间")
    private String manufactureSopOrgTime ;
    /** 量产SOP计划时间 */
    @ApiModelProperty(name = "量产SOP计划时间")
    private String manufactureSopPlanTime ;
    /** 量产SOP完成时间 */
    @ApiModelProperty(name = "量产SOP完成时间")
    private String manufactureSopCompleteTime ;
    /** 量产SOP状态 */
    @ApiModelProperty(name = "量产SOP状态")
    private String manufactureSopStatus ;
    /** 批产早期退出计划原始时间 */
    @ApiModelProperty(name = "批产早期退出计划原始时间")
    private String earlyControlExitOrgTime ;
    /** 批产早期退出计划时间 */
    @ApiModelProperty(name = "批产早期退出计划时间")
    private String earlyControlExitPlanTime ;
    /** 批产早期退出完成时间 */
    @ApiModelProperty(name = "批产早期退出完成时间")
    private String earlyControlExitCompleteTime ;
    /** 批产早期退出状态 */
    @ApiModelProperty(name = "批产早期退出状态")
    private String earlyControlExitStatus ;
    /** 首次样件计划预计交付原始时间 */
    @ApiModelProperty(name = "首次样件计划预计交付原始时间")
    private String firstSampPlanDeliveryOrgTime ;
    /** 首次样件计划预计交付时间 */
    @ApiModelProperty(name = "首次样件计划预计交付时间")
    private String firstSampPlanDeliveryPlanTime ;
    /** 首次样件完成交付时间 */
    @ApiModelProperty(name = "首次样件完成交付时间")
    private String firstSampPlanDeliveryCompleteTime ;
    /** 首次样件完成状态 */
    @ApiModelProperty(name = "首次样件完成状态")
    private String firstSampPlanDeliveryStatus ;
    /** 计划完成时间 */
    @ApiModelProperty(name = "计划完成时间")
    private String planCompleteTime ;
    /** 计划状态 */
    @ApiModelProperty(name = "计划状态")
    private String status ;
    /** 品类工程师Id */
    private Long cpeId;
    /** 是否修改过 */
    @ApiModelProperty(name = "是否进行过计划修改 0: 否 1: 是")
    private Integer isUpdatePlan ;
    /** 节点信息 */
    @ApiModelProperty(name = "节点信息-包含节点key 延期，待办，附件")
    private List<PartsPurchasePlanNodeRespDTO> nodeList;
}
