package com.weifu.srm.requirement.request;

import com.weifu.srm.requirement.request.project.ProjectBasePageReqDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class PartsPurchasePlanChangeRecordReqDTO extends ProjectBasePageReqDTO {

    /** 零件需求编号 */
    @NotNull(message = "零件需求编号不能为空")
    private String requirementPartsNo;

    private String startDate;

    private String endDate;
}
