package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/16 11:22
 * @Description 物料图纸
 * @Version 1.0
 */
@ApiModel("物料图纸")
@Data
@NoArgsConstructor
public class MaterialDrawingFileListRespDTO {
    @ApiModelProperty(value = "id",notes = "")
    private Long id;
    @ApiModelProperty("物料编码")
    private String materialCode;
    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("图纸编号")
    private String drawingNo;
    @ApiModelProperty("图纸版本")
    private String drawingVersion;
    @ApiModelProperty("图纸地址")
    private String drawingUrl;
    @ApiModelProperty("图纸名称")
    private String drawingName;
    @ApiModelProperty("文件名称（MinIO）")
    private String fileName;
    @ApiModelProperty(value = "windchill推送的基线ID",notes = "")
    private String windchillId;
    @ApiModelProperty(value = "图纸格式",notes = "")
    private String drawingFormat ;
}
