package com.weifu.srm.requirement.response.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ProjectExportResDTO extends ProjectPageResDTO {
    /** 项目编编码 */
    private String projectCode ;
    /** 事业部/子公司id */
    private String subsidiaryCode ;
    /** 需求部门id */
    private String departmentCode ;
    /** 需求项目经理id */
    private Long projectManagerId ;
    /** 项目类型 */
    private String projectType ;
    /** 产品名称 */
    private String productName ;
    /** 产品型号 */
    private String productModel ;
    /** 平台名称 */
    private String platformName ;
    /** 发起阶段（概念、A阶段、B阶段、C阶段、D阶段、批产阶段） */
    private String initiationPhase ;
    /** 项目定级 */
    private String projectClassification ;
    /** 项目负责人id */
    private Long projectLeaderId ;
    /** 计划时间 */
    private ProjectTimeResDTO times;
    /** 附件id集合 */
    private List<ProjectFileResDTO> fileList;
    /** 备注 */
    private String remark;
    /** 项目提交人角色 */
    private String submitByRole ;
    /** 项目提交人三级品类 */
    private Long submitByThreeCategory ;
    /** 项目提交人三级品类名称 */
    private String submitByThreeCategoryName ;

    @ApiModelProperty("外购件总目标成本")
    private BigDecimal outsourceTotalTargetCost;
}
