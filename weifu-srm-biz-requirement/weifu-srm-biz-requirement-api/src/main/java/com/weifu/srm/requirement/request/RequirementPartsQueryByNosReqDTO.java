package com.weifu.srm.requirement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/13 19:16
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartsQueryByNosReqDTO implements Serializable {

    @NotNull(message = "零件需求编号不能为空")
    @Size(min = 1, message = "零件需求编号不能为空")
    @ApiModelProperty(value = "零件需求编号", required = true)
    private List<String> requirementPartsNo;
}
