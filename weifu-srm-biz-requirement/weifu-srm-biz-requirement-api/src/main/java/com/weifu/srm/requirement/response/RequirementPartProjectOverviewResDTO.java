package com.weifu.srm.requirement.response;

import com.weifu.srm.requirement.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/12 10:59
 * @Description 需求保存DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class RequirementPartProjectOverviewResDTO {
    @ApiModelProperty(value = "需求编号")
    private String requirementNo;
    @ApiModelProperty(value = "零件需求编号")
    private String requirementPartsNo;
    @ApiModelProperty(name = "项目延期状态",notes = "")
    private String requirementPartStatus;
    @ApiModelProperty(name = "物料编码",notes = "费用化报销时可不填写")
    private String materialCode;
    @ApiModelProperty(name = "物料描述",notes = "1.支持手动输入 支持以填入的物料号带出")
    private String materialDesc;
    @ApiModelProperty(name = "询价单号",notes = "")
    private String inquiryOrderNo;
    @ApiModelProperty(name = "一级品类",notes = "")
    private String categoryFirst ;
    @ApiModelProperty(name = "一级品类名称",notes = "")
    private String categoryFirstName ;
    @ApiModelProperty(name = "二级品类",notes = "")
    private String categorySecond ;
    @ApiModelProperty(name = "二级品类名称",notes = "")
    private String categorySecondName ;
    @ApiModelProperty(name = "三级品类",notes = "")
    private String categoryThird ;
    @ApiModelProperty(name = "三级品类名称",notes = "")
    private String categoryThirdName ;
    @ApiModelProperty(name = "品类工程师",notes = "操作人")
    private Long cpeId;
    @ApiModelProperty(name = "品类工程师姓名",notes = "操作人")
    private String cpeName;
    @ApiModelProperty(name = "质量工程师",notes = "")
    private Long sqeId;
    @ApiModelProperty(name = "质量工程师姓名",notes = "")
    private String sqeName;
    /** 推荐品类工程师 */
    @ApiModelProperty(name = "推荐品类工程师",notes = "")
    private Long recommendedCpe ;
    /** 推荐品类工程师名称 */
    @ApiModelProperty(name = "推荐品类工程师名称",notes = "")
    private String recommendedCpeName ;
    /** 推荐质量工程师id */
    @ApiModelProperty(name = "推荐质量工程师id",notes = "")
    private Long recommendedSqe ;
    /** 推荐质量工程师名称 */
    @ApiModelProperty(name = "推荐质量工程师名称",notes = "")
    private String recommendedSqeName ;
    @ApiModelProperty(name = "OTS外购件提交",notes = "显示零件计划中OTS外购件提交计划时间")
    private String otsOutsourceSubmitPlanTime;
    @ApiModelProperty(name = "OTS外购件提交状态",notes = "需求计划时间超过项目计划标红")
    private String otsOutsourceSubmitPlanTimeStatus;
    @ApiModelProperty(name = "供应商PPAP提交",notes = "显示零件计划中PPAP提交节点计划时间")
    private String supplierPpapSubmitPlanTime;
    @ApiModelProperty(name = "供应商PPAP提交状态",notes = "需求计划时间超过项目计划标红")
    private String supplierPpapSubmitPlanTimeStatus;
    @ApiModelProperty(name = "供应商PPAP放行",notes = "显示零件计划中PPAP放行节点计划时间")
    private String supplierPpapCompletionPlanTime;
    @ApiModelProperty(name = "供应商PPAP放行状态",notes = "需求计划时间超过项目计划标红")
    private String supplierPpapCompletionPlanTimeStatus;
    @ApiModelProperty(name = "状态",notes = "编制中，BP退回，待BP分发，待组长分发，组长已退回，待执行，CPE已退回，执行中，已完成，已关闭")
    private String status ;
    @ApiModelProperty(name = "附件")
    private List<AttachmentMessageReqDTO> fileList;
}
