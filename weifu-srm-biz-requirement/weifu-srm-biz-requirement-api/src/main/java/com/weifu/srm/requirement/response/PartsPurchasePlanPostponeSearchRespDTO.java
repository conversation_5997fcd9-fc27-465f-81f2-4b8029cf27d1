package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Data
@NoArgsConstructor
public class PartsPurchasePlanPostponeSearchRespDTO implements Serializable {

    @ApiModelProperty(value = "零件采购计划编号")
    private String planNo ;
    @ApiModelProperty(value = "零件采购计划延期节点")
    private String postponeNode ;
    @ApiModelProperty(value = "零件采购计划延期原因类型")
    private String postponeType ;
    @ApiModelProperty(value = "零件采购计划延期原因")
    private String postponeDesc ;
    @ApiModelProperty(value = "操作人ID")
    private Long recordUserId ;
    @ApiModelProperty(value = "操作人Name")
    private String recordUserName ;
    @ApiModelProperty("记录时间")
    private String recordTime;
}
