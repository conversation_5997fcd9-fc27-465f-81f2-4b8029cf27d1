package com.weifu.srm.requirement.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("根据物料号查询其对应的品类信息")
@Data
@NoArgsConstructor
public class CategoryInfoPresentByMaterialRespDTO {
    /** 物料编码;费用化报销时可不填写 */
    @ApiModelProperty(value= "物料编码",notes = "费用化报销时可不填写")
    private String materialCode ;
    /** 物料描述;1.支持手动输入 支持以填入的物料号带出 */
    @ApiModelProperty(value= "物料描述",notes = "1.支持手动输入 支持以填入的物料号带出")
    private String materialDesc ;
    /** 一级品类 */
    @ApiModelProperty(value= "一级品类",notes = "")
    private String categoryFirst ;
    /** 一级品类名称 */
    @ApiModelProperty(value= "一级品类名称",notes = "")
    private String categoryFirstName ;
    /** 二级品类 */
    @ApiModelProperty(value= "二级品类",notes = "")
    private String categorySecond ;
    /** 二级品类名称 */
    @ApiModelProperty(value= "二级品类名称",notes = "")
    private String categorySecondName ;
    /** 三级品类 */
    @ApiModelProperty(value= "三级品类",notes = "")
    private String categoryThird ;
    /** 三级品类 */
    @ApiModelProperty(value= "三级品类名称",notes = "")
    private String categoryThirdName ;
    /** 品类组长 */
    @ApiModelProperty(value= "品类组长",notes = "")
    private Long cpeMasterId ;
    /** 品类组长 */
    @ApiModelProperty(value= "品类组长名称",notes = "")
    private String cpeMasterName ;
}
