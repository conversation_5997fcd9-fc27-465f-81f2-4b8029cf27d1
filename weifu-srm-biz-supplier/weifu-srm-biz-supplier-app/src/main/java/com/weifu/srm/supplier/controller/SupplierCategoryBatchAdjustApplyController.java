package com.weifu.srm.supplier.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.supplier.api.SupplierCategoryBatchAdjustApplyApi;
import com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO;
import com.weifu.srm.supplier.response.suppliercategorybatchadjustapply.SupplierCategoryBatchAdjustApplyDetailRespDTO;
import com.weifu.srm.supplier.service.SupplierCategoryBatchAdjustApplyService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "供应商品类关系批量调整接口")
@RestController
@RequiredArgsConstructor
public class SupplierCategoryBatchAdjustApplyController implements SupplierCategoryBatchAdjustApplyApi {

    private final SupplierCategoryBatchAdjustApplyService supplierCategoryBatchAdjustApplyService;

    @Override
    public ApiResponse<String> create(CreateSupplierCategoryBatchAdjustApplyReqDTO req) {
        return ApiResponse.success(supplierCategoryBatchAdjustApplyService.create(req));
    }

    @Override
    public ApiResponse<SupplierCategoryBatchAdjustApplyDetailRespDTO> queryApplyDetail(String applyNo) {
        return ApiResponse.success(supplierCategoryBatchAdjustApplyService.queryApplyDetail(applyNo));
    }

}
