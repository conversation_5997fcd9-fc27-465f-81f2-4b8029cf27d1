package com.weifu.srm.supplier.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.api.QualificationChangeApi;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.service.QualificationChangeService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "资质变更功能接口")
@RestController
@RequiredArgsConstructor
public class QualificationChangeController implements QualificationChangeApi {

    private final QualificationChangeService qualificationChangeService;

    @Override
    public ApiResponse<QualificationChangeQueryBasicRespDTO> queryBasic(QualificationChangeQueryBasicReqDTO reqDTO) {
        QualificationChangeQueryBasicRespDTO qualificationChangeQueryBasicRespDTO = qualificationChangeService.queryBasic(reqDTO);
        return ApiResponse.success(qualificationChangeQueryBasicRespDTO);
    }

    @Override
    public ApiResponse<List<QualificationChangeQueryUserRespDTO>> queryUser(QualificationChangeQueryBasicReqDTO reqDTO) {
        List<QualificationChangeQueryUserRespDTO> qualificationChangeQueryUserRespDTOS = qualificationChangeService.queryUser(reqDTO);
        return ApiResponse.success(qualificationChangeQueryUserRespDTOS);
    }

    @Override
    public ApiResponse<List<QualificationChangeQueryFinancialRespDTO>> queryFinancial(QualificationChangeQueryBasicReqDTO reqDTO) {
        List<QualificationChangeQueryFinancialRespDTO> qualificationChangeQueryFinancialRespDTOS = qualificationChangeService.queryFinancial(reqDTO);
        return ApiResponse.success(qualificationChangeQueryFinancialRespDTOS);
    }

    @Override
    public ApiResponse<QualificationChangeQueryAssociationRespDTO> queryAssociation(QualificationChangeQueryBasicReqDTO reqDTO) {
        QualificationChangeQueryAssociationRespDTO qualificationChangeQueryAssociationRespDTO = qualificationChangeService.queryAssociation(reqDTO);
        return ApiResponse.success(qualificationChangeQueryAssociationRespDTO);
    }

    @Override
    public ApiResponse<List<QualificationChangeQueryCertificationRespDTO>> queryCertification(QualificationChangeQueryBasicReqDTO reqDTO) {
        List<QualificationChangeQueryCertificationRespDTO> qualificationChangeQueryCertificationRespDTOS = qualificationChangeService.queryCertification(reqDTO);
        return ApiResponse.success(qualificationChangeQueryCertificationRespDTOS);
    }

    @Override
    public ApiResponse<Void> updateBasic(QualificationChangeUpdateBasicReqDTO reqDTO) {
        qualificationChangeService.updateBasic(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> updateUser(QualificationChangeUpdateUserReqDTO reqDTO) {
        qualificationChangeService.updateUser(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> updateFinancial(QualificationChangeUpdateFinancialReqDTO reqDTO) {
        qualificationChangeService.updateFinancial(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> updateAssociation(QualificationChangeUpdateAssociationReqDTO reqDTO) {
        qualificationChangeService.updateAssociation(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<Void> updateCertification(QualificationChangeUpdateCertificationReqDTO reqDTO) {
        qualificationChangeService.updateCertification(reqDTO);
        return ApiResponse.success();
    }

    @Override
    public ApiResponse<PageResponse<QualificationChangeQueryApplyRespDTO>> queryApply(QualificationChangeQueryApplyReqDTO reqDTO) {
        PageResponse<QualificationChangeQueryApplyRespDTO> qualificationChangeQueryApplyRespDTOPageResponse = qualificationChangeService.queryApply(reqDTO);
        return ApiResponse.success(qualificationChangeQueryApplyRespDTOPageResponse);
    }

    @Override
    public ApiResponse<QualificationChangeQueryBasicUpdateRespDTO> queryBasicUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        QualificationChangeQueryBasicUpdateRespDTO qualificationChangeQueryBasicUpdateRespDTO = qualificationChangeService.queryBasicUpdate(reqDTO);
        return ApiResponse.success(qualificationChangeQueryBasicUpdateRespDTO);
    }

    @Override
    public ApiResponse<List<QualificationChangeQueryFinancialUpdateRespDTO>> queryFinancialUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        List<QualificationChangeQueryFinancialUpdateRespDTO> qualificationChangeQueryFinancialUpdateRespDTOS = qualificationChangeService.queryFinancialUpdate(reqDTO);
        return ApiResponse.success(qualificationChangeQueryFinancialUpdateRespDTOS);
    }

    @Override
    public ApiResponse<QualificationChangeQueryAssociationUpdateRespDTO> queryAssociationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        QualificationChangeQueryAssociationUpdateRespDTO qualificationChangeQueryAssociationUpdateRespDTO = qualificationChangeService.queryAssociationUpdate(reqDTO);
        return ApiResponse.success(qualificationChangeQueryAssociationUpdateRespDTO);
    }

    @Override
    public ApiResponse<QualificationChangeQueryCertificationUpdateRespDTO> queryCertificationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        QualificationChangeQueryCertificationUpdateRespDTO qualificationChangeQueryCertificationUpdateRespDTO = qualificationChangeService.queryCertificationUpdate(reqDTO);
        return ApiResponse.success(qualificationChangeQueryCertificationUpdateRespDTO);
    }
}
