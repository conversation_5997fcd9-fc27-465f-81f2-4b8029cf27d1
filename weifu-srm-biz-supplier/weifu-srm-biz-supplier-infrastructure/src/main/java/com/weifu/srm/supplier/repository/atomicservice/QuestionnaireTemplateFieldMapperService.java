package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldPO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldSamplePO;

import java.util.List;

public interface QuestionnaireTemplateFieldMapperService extends IService<QuestionnaireTemplateFieldPO> {

    List<QuestionnaireTemplateFieldPO> queryTemplateFieldListByCode(String templateCode);

    Boolean deleteByTemplateCode(String templateCode);

    Integer updateTemplateField(Wrapper<QuestionnaireTemplateFieldPO> wrapper);

}
