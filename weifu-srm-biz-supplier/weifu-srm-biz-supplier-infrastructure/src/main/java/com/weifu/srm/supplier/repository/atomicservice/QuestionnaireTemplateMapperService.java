package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplatePO;


public interface QuestionnaireTemplateMapperService extends IService<QuestionnaireTemplatePO> {
    Page<QuestionnaireTemplatePO> queryList(Page<QuestionnaireTemplatePO> page,
                                             QuestionnaireTemplatePO QuestionnaireTemplatePO);
    Boolean insertTemplate(QuestionnaireTemplatePO questionnaireTemplatePO);

    void updateTemplate(QuestionnaireTemplatePO oriTemplate,QuestionnaireTemplatePO newTemplate);

    QuestionnaireTemplatePO queryByTemplateCode(String templateCode);

    Integer updateTemplate(Wrapper<QuestionnaireTemplatePO> wrapper);

    Integer queryDefaultTemplateCount(String supplierType, String templateCode);
}
