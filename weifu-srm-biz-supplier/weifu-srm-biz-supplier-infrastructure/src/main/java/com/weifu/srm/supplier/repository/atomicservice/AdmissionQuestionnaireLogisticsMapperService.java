package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireLogisticsPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:55
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireLogisticsMapperService extends IService<AdmissionQuestionnaireLogisticsPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireLogisticsPO> findByAdmissionNo(String admissionNo);
}
