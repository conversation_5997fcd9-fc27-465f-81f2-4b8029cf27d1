package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireWillingnessPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 13:04
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireWillingnessMapperService extends IService<AdmissionQuestionnaireWillingnessPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireWillingnessPO> findByAdmissionNo(String admissionNo);
}
