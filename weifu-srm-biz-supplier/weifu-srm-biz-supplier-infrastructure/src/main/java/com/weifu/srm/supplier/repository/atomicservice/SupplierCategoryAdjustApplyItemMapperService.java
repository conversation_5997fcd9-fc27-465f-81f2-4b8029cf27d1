package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyItemPO;

import java.util.List;

public interface SupplierCategoryAdjustApplyItemMapperService extends IService<SupplierCategoryAdjustApplyItemPO> {

    /**
     * 查询正在调整审核中的供应商品类关系
     * @return
     */
    List<SupplierCategoryAdjustApplyItemPO> listOnApproving(List<SupplierCategoryAdjustApplyItemPO> relationships);

}
