package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyReqDTO;

import java.util.List;

public interface SupplierGreyListMapperService extends IService<SupplierGreyListPO> {

    List<SupplierGreyListPO> searchGreyListRecord(List<SupplierGreyListApplyReqDTO.GreyListDetail> list);

    void removeGreyList(List<SupplierGreyListRecordDetailPO> list);

    void removeBySupplierCode(String supplierCode);

}
