package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireQualityAssurancePO;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireSubcontractorPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 13:01
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireSubcontractorMapperService extends IService<AdmissionQuestionnaireSubcontractorPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireSubcontractorPO> findByAdmissionNo(String admissionNo);
}
