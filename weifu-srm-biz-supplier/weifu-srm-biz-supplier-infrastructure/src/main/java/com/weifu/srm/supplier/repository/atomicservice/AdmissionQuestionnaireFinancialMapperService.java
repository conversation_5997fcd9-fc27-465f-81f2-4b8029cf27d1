package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireFinancialPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:54
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireFinancialMapperService extends IService<AdmissionQuestionnaireFinancialPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireFinancialPO> findByAdmissionNo(String admissionNo);
}
