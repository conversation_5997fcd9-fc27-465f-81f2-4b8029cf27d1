package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;

public interface SupplierCategoryAdjustApplyMapperService extends IService<SupplierCategoryAdjustApplyPO> {

    SupplierCategoryAdjustApplyPO getByApplyNo(String applyNo);

    /**
     * 查询指定供应商审核中的申请
     */
    SupplierCategoryAdjustApplyPO getApproving(String sapSupplierCode);

}
