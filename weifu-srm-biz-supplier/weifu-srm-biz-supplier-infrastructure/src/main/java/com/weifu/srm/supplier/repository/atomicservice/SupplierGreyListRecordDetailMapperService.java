package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyListReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyReqDTO;
import com.weifu.srm.supplier.response.grey.SupplierGreyListApplyListRespDTO;

import java.util.List;

public interface SupplierGreyListRecordDetailMapperService extends IService<SupplierGreyListRecordDetailPO> {

    List<SupplierGreyListRecordDetailPO> searchGreyListRecordDetail(List<SupplierGreyListApplyReqDTO.GreyListDetail> list);

    IPage<SupplierGreyListApplyListRespDTO> queryListPage(Page<SupplierGreyListApplyListRespDTO> page, SupplierGreyListApplyListReqDTO req);

    List<SupplierGreyListApplyListRespDTO> queryList( SupplierGreyListApplyListReqDTO req);
}
