package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCustomerPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:51
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireCustomerMapperService extends IService<AdmissionQuestionnaireCustomerPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireCustomerPO> findByAdmissionNo(String admissionNo);
}
