package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationSearchPO;
import com.weifu.srm.supplier.repository.po.TmpSupplierLastTransactionPeriodResultPO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.Date;
import java.util.List;

public interface SupplierAdmissionInvitationInfoMapperService extends IService<SupplierAdmissionInvitationRecordPO> {

    IPage<SupplierAdmissionInvitationSearchPO> queryList(Page<SupplierAdmissionInvitationSearchPO> page, SupplierAdmissionInvitationSearchPO entity, List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    List<String> selectSupplierNameByKeyword(String keyword, Integer limit);

    TmpSupplierLastTransactionPeriodResultPO queryLastTransactionPeriod(String supplierName);

    SupplierAdmissionInvitationRecordPO getByInvitationNo(String invitationNo);

    void updateInvitationStatus(String invitationNo, String status, Long operationBy, String operationName);

}
