package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierLeaveRecordDetailPO;
import com.weifu.srm.supplier.request.leave.SupplierLeaveApplyListReqDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveApplyListRespDTO;

import java.util.List;

public interface SupplierLeaveRecordDetailMapperService extends IService<SupplierLeaveRecordDetailPO> {

    IPage<SupplierLeaveApplyListRespDTO> queryListPage(Page<SupplierLeaveApplyListReqDTO> page, SupplierLeaveApplyListReqDTO req);

    List<SupplierLeaveApplyListRespDTO> queryList( SupplierLeaveApplyListReqDTO req);
}
