package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.supplier.repository.po.QualificationChangeApplyPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.request.QualificationChangeQueryApplyReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO;

import java.util.List;

public interface QualificationChangeApplyMapperService extends IService<QualificationChangeApplyPO> {

    Page<QualificationChangeApplyPO> queryList(Page<QualificationChangeApplyPO> page, QualificationChangeQueryApplyReqDTO reqDTO, String supplierCode);

    Page<QualificationChangeApplyPO> queryListWeifu(Page<QualificationChangeApplyPO> page, QualificationChangeQueryApplyReqDTO reqDTO, List<SupplierBasicInfoPO> cpeSupplierBasicInfoPOS, List<SupplierBasicInfoPO> sqeSupplierBasicInfoPOS, DataPermissionRespDTO pcInternalPageQualifyChangeApply);

}
