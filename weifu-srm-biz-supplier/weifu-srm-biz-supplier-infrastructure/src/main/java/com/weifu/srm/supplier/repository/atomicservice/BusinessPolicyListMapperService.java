package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.repository.po.BusinessPolicyListPO;
import com.weifu.srm.supplier.request.policy.PolicyAdjustMainReqDTO;
import com.weifu.srm.supplier.request.policy.QueryPolicyListReqDTO;
import com.weifu.srm.supplier.response.policy.PolicyListRespDTO;

/**
 * <AUTHOR>
 * @description 针对表【business_policy_list(商务政策列表)】的数据库操作Service
 * @createDate 2024-09-10 14:27:57
 */
public interface BusinessPolicyListMapperService extends IService<BusinessPolicyListPO> {

    PageResponse<PolicyListRespDTO> policyList(QueryPolicyListReqDTO param);

}