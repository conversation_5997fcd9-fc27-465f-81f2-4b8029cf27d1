package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description SupplierCategoryRelationshipMapperService
 * @Version 1.0
 */
public interface SupplierCategoryRelationshipMapperService extends IService<SupplierCategoryRelationshipPO> {

    /**
     * 根据品类编码查询
     *
     * @param categoryCode 品类编码
     * @return 供应商与品类关联关系集合
     */
    List<SupplierCategoryRelationshipPO> queryByCategoryCode(String categoryCode);

    List<SupplierCategoryRelationshipPO> queryBySapSupplierCodeList(List<String> sapSupplierCodeList);

    List<SupplierCategoryRelationshipPO> listBySSCAndCC(String sapSupplierCode, List<String> categoryCodes);

    Long removeBySSCAndCC(String sapSupplierCode, List<String> categoryCodes);

    Long hardRemoveByIds(List<Long> ids);

    /**
     * 根据关联关系查询已存在的关系
     */
    List<SupplierCategoryRelationshipPO> list(List<SupplierCategoryRelationshipPO> relationships);

    List<SupplierCategoryRelationshipPO> listByCategoryCodes(List<String> categoryCodes);

}
