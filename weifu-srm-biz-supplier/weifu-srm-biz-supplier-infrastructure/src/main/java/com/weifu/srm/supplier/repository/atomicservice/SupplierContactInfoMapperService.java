package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.repository.po.SupplierContactInfoPO;
import com.weifu.srm.supplier.request.SupplierFinanceContactQueryReqDTO;
import com.weifu.srm.supplier.response.SupplierFinanceContactRespDTO;

public interface SupplierContactInfoMapperService extends IService<SupplierContactInfoPO> {
    PageResponse<SupplierFinanceContactRespDTO> pageFinancialContact(SupplierFinanceContactQueryReqDTO param);
}
