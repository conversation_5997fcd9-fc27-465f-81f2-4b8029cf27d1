package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCompanyInfoPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:48
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireCompanyInfoMapperService extends IService<AdmissionQuestionnaireCompanyInfoPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireCompanyInfoPO> findByAdmissionNo(String admissionNo);
}
