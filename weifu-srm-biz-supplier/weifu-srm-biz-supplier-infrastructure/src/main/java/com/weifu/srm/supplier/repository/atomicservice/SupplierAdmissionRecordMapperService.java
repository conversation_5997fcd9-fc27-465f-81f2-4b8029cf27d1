package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.request.AdmissionQuestionnairePageQueryReqDTO;
import com.weifu.srm.supplier.request.AdmissionQuestionnaireSaveReqDTO;
import com.weifu.srm.supplier.request.AdmissionQuestionnaireWarehousePageQueryReqDTO;
import com.weifu.srm.supplier.response.AdmissionQuestionnairePageQueryRespDTO;
import com.weifu.srm.supplier.response.SupplierAdmissionStatisticsRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 16:04
 * @Description
 * @Version 1.0
 */
public interface SupplierAdmissionRecordMapperService extends IService<SupplierAdmissionRecordPO> {
    void saveAdmissionQuestionnaire(AdmissionQuestionnaireSaveReqDTO reqDTO);

    void saveAdmissionQuestionnaireDetail(AdmissionQuestionnaireSaveReqDTO reqDTO);
    Page<AdmissionQuestionnairePageQueryRespDTO> queryPage(Page<AdmissionQuestionnairePageQueryRespDTO> page,
                                                           AdmissionQuestionnairePageQueryReqDTO reqDTO,
                                                           List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys);

    SupplierAdmissionRecordPO findByAdmissionNo(String admissionNo);

    SupplierAdmissionRecordPO findLatestBySupplierType(String supplierType, String supplierCode);

    void updateStatusByWrapper(Wrapper<SupplierAdmissionRecordPO> updateWrapper);

    Page<SupplierAdmissionRecordPO> queryWarehouse(Page<SupplierAdmissionRecordPO> page,
                                                   AdmissionQuestionnaireWarehousePageQueryReqDTO req,
                                                   DataPermissionRespDTO dataPermission);

    List<SupplierAdmissionStatisticsRespDTO> countByStatus(Long supplierId);

}
