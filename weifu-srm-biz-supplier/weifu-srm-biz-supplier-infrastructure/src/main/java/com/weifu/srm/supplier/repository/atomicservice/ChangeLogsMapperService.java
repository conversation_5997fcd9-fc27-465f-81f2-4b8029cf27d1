package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.ChangeLogsPO;

public interface ChangeLogsMapperService extends IService<ChangeLogsPO> {
    Page<ChangeLogsPO> queryByTableKeyAndColumnKey(Page<ChangeLogsPO> page, String tableKey, String columnKey);
}
