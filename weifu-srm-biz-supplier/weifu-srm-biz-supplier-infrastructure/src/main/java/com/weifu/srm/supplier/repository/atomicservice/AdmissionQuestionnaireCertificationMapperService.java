package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCertificationPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:44
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireCertificationMapperService extends IService<AdmissionQuestionnaireCertificationPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireCertificationPO> findByAdmissionNo(String admissionNo);
}
