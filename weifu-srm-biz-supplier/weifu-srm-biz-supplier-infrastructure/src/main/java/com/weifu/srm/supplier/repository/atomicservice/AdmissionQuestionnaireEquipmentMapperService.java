package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireEquipmentPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:52
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireEquipmentMapperService extends IService<AdmissionQuestionnaireEquipmentPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireEquipmentPO> findByAdmissionNo(String admissionNo);
}
