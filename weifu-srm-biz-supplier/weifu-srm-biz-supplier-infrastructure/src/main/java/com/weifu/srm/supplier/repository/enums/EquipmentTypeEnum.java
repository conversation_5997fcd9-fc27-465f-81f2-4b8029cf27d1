package com.weifu.srm.supplier.repository.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/6 0:06
 * @Description
 * @Version 1.0
 */
@Getter
public enum EquipmentTypeEnum {
    // 批产件
    MANUFACTURING_EQUIPMENT("manufacturing_equipment", "Manufacturing Equipment","制造设备"),
    INSPECTION_EQUIPMENT("inspection_equipment", "Inspection Equipment","检测设备");
    private String code;

    private String englishName;

    private String chineseName;


    private EquipmentTypeEnum(String code, String englishName, String chineseName) {
        this.code = code;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }
    private static final Map<String, EquipmentTypeEnum> lookup = new HashMap<>();

    static {
        for (EquipmentTypeEnum mode : values()) {
            lookup.put(mode.getCode(), mode);
        }
    }
    public static EquipmentTypeEnum getByCode(String code) {
        return lookup.get(code);
    }

}
