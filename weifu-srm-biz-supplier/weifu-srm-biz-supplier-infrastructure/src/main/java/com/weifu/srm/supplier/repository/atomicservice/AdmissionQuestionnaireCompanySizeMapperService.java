package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireCompanySizePO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 12:49
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireCompanySizeMapperService extends IService<AdmissionQuestionnaireCompanySizePO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireCompanySizePO> findByAdmissionNo(String admissionNo);
}
