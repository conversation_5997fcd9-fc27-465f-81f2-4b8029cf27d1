package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierTagsPO;

import java.util.List;

public interface SupplierTagsMapperService extends IService<SupplierTagsPO> {

    void removeTags(List<String> codes, List<String> tags);

    List<SupplierTagsPO> searchTags(List<String> supplierCodes);
}
