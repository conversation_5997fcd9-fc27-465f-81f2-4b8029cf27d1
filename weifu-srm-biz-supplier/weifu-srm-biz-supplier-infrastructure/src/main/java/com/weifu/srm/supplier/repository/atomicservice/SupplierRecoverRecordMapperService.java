package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierRecoverRecordPO;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyListReqDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordListRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

public interface SupplierRecoverRecordMapperService extends IService<SupplierRecoverRecordPO> {

    IPage<SupplierRecoverRecordListRespDTO> queryListPage(Page<SupplierRecoverApplyListReqDTO> page,
                                                          SupplierRecoverApplyListReqDTO req,
                                                          DataPermissionRespDTO dataPermission);

}
