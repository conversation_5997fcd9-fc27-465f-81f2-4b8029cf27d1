package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierBlackListRecordDetailPO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyListReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyListReqDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListApplyListRespDTO;
import com.weifu.srm.supplier.response.grey.SupplierGreyListApplyListRespDTO;

import java.util.List;

public interface SupplierBlackListRecordDetailMapperService extends IService<SupplierBlackListRecordDetailPO> {

    IPage<SupplierBlackListApplyListRespDTO> queryListPage(Page<SupplierBlackListApplyListReqDTO> page, SupplierBlackListApplyListReqDTO req);

    List<SupplierBlackListApplyListRespDTO> queryList(SupplierBlackListApplyListReqDTO req);

}
