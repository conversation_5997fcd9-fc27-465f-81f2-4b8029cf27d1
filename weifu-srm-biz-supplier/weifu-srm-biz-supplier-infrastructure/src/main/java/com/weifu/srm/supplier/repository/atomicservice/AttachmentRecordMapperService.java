package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.request.policy.AttachmentReqDTO;
import com.weifu.srm.supplier.request.policy.BaseUserReqDTO;
import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.SupplierBasicUpdateAttachmentRespDTO;
import com.weifu.srm.supplier.response.policy.AttachmentRespDTO;

import java.util.List;

public interface AttachmentRecordMapperService extends IService<AttachmentRecordPO> {

    Boolean removeByBizNo(String bizNo);

    void removeByBusinessTypeAndBizNo(String businessNo, List<String> businessTypes);

    /**
     * 根据业务单号 与业务类型查询附件数据
     * @param businessNo 业务单号
     * @param businessType 业务类型
     * @return {@link AttachmentMessageRespDTO}
     */
    List<AttachmentRespDTO> listAttachments(String businessNo, String businessType);

    /**
     * 根据业务单号 与业务类型查询附件数据
     * @param businessNo 业务单号
     * @param businessTypes 业务类型
     * @return {@link AttachmentMessageRespDTO}
     */
    List<AttachmentRespDTO> listAttachments(String businessNo, List<String> businessTypes);

    /**
     * 增量更新文件数据
     */
    void saveAttachments(List<AttachmentReqDTO> attachments, String businessNo, String businessType, BaseUserReqDTO user);


    /**
     * 查询最新审批通过的基本信息变更 且上传了营业执照附件的附件数据
     */
    List<SupplierBasicUpdateAttachmentRespDTO> queryBusinessLicenseAttachments(String supplierCode);
}
