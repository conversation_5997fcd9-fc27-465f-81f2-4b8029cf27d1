package com.weifu.srm.supplier.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:11
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司规模",description = "")
@TableName("admission_questionnaire_company_size")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCompanySizePO extends BaseEntity implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 厂房类型（自有租赁） */
    @ApiModelProperty(name = "厂房类型（自有租赁）",notes = "")
    private String plantType ;
    /** 厂房总面积（m2） */
    @ApiModelProperty(name = "厂房总面积（m2）",notes = "")
    private BigDecimal totalPlantArea ;
    /** 在用厂房面积 */
    @ApiModelProperty(name = "在用厂房面积",notes = "")
    private BigDecimal usedPlantArea ;
}
