package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.enums.policy.PolicyStatusEnum;
import com.weifu.srm.supplier.repository.po.BusinessPolicyApplyMainPO;
import com.weifu.srm.supplier.request.policy.PolicyAdjustMainReqDTO;
import com.weifu.srm.supplier.response.policy.PolicyAdjustMainRespDTO;

/**
 * <AUTHOR>
 * @description 针对表【business_policy_apply_main(商务政策审批申请主表)】的数据库操作Service
 * @createDate 2024-09-10 14:27:57
 */
public interface BusinessPolicyApplyMainMapperService extends IService<BusinessPolicyApplyMainPO> {
    String saveApply(PolicyAdjustMainReqDTO param, PolicyStatusEnum status);

    PolicyAdjustMainRespDTO getByApplyNo(String applyNo);

    BusinessPolicyApplyMainPO getByNo(String applyNo);
}