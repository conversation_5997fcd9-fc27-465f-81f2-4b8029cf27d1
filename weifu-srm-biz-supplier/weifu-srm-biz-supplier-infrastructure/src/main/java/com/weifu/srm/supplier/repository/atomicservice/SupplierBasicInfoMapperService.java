package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoListSearchPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.request.SupplierBasicSelectReqDTO;
import com.weifu.srm.supplier.request.SupplierForInquiryOrderQueryReqDTO;
import com.weifu.srm.supplier.request.SupplierTagsReqDTO;
import com.weifu.srm.supplier.response.SupplierBasicInfoInquiryOrderRespDTO;
import com.weifu.srm.supplier.response.SupplierBasicInfoListRespExportDTO;
import com.weifu.srm.supplier.response.SupplierBasicSimpleInfoRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

public interface SupplierBasicInfoMapperService extends IService<SupplierBasicInfoPO> {

    Page<SupplierBasicInfoPO> queryList(Page<SupplierBasicInfoPO> page,
                                        SupplierBasicInfoPO supplierBasicInfoPO);

    List<SupplierBasicInfoPO> queryCertificate(List<String> supplierCodes);

    Page<SupplierBasicInfoListSearchPO> queryPageList(Page<SupplierBasicInfoListSearchPO> page,
                                                       SupplierBasicInfoListSearchPO supplierBasicInfoListSearchPO,
                                                       DataPermissionRespDTO pcInternalPageQualifyChangeApply);

    IPage<SupplierBasicInfoListSearchPO> queryPageListForQC(Page<SupplierBasicInfoListSearchPO> page,
                                                            SupplierBasicInfoListSearchPO supplierBasicInfoListSearchPO);

    IPage<SupplierBasicInfoPO> queryByLikeNameAndCode(Page<SupplierBasicInfoPO> page, SupplierBasicSelectReqDTO reqDTO);

    List<SupplierBasicInfoPO> listEligibleBySupplierCodes(List<String> supplierCodes);

    IPage<SupplierBasicInfoInquiryOrderRespDTO> queryForInquiryOrder(Page<SupplierBasicInfoInquiryOrderRespDTO> page, SupplierForInquiryOrderQueryReqDTO reqDTO);

    List<SupplierBasicInfoInquiryOrderRespDTO> queryForSourcingBySupplierCodes(List<String> supplierCodes);

    List<SupplierBasicInfoListSearchPO> queryList(SupplierTagsReqDTO reqDTO);

    SupplierBasicInfoPO getBySapSupplierCode(String sapSupplierCode);

    List<SupplierBasicSimpleInfoRespDTO> queryEffectiveSupplier(SupplierBasicSelectReqDTO reqDTO);
    List<SupplierBasicInfoListRespExportDTO> getDetailById(List<Long> supplierIds);

    void updateSupplierStatus(Long supplierId, String status, Long operationBy, String operationName);

    List<SupplierBasicSimpleInfoRespDTO> listBySupplierCodes(List<String> supplierCodes);

}
