package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.AdmissionQuestionnaireRdCapabilityPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 13:02
 * @Description
 * @Version 1.0
 */
public interface AdmissionQuestionnaireRdCapabilityMapperService extends IService<AdmissionQuestionnaireRdCapabilityPO> {
    Boolean removeByAdmissionNo(String admissionNo);

    List<AdmissionQuestionnaireRdCapabilityPO> findByAdmissionNo(String admissionNo);
}
