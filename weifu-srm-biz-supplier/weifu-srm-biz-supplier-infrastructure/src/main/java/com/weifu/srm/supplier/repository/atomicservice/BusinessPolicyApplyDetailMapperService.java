package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.BusinessPolicyApplyDetailPO;
import com.weifu.srm.supplier.request.policy.BaseUserReqDTO;
import com.weifu.srm.supplier.request.policy.PolicyAdjustDetailReqDTO;
import com.weifu.srm.supplier.request.policy.PolicyAdjustMainReqDTO;
import com.weifu.srm.supplier.response.policy.PolicyAdjustDetailRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_policy_apply_detail(商务政策列表)】的数据库操作Service
 * @createDate 2024-09-10 14:27:57
 */
public interface BusinessPolicyApplyDetailMapperService extends IService<BusinessPolicyApplyDetailPO> {


    List<PolicyAdjustDetailRespDTO> getByApplyNo(String applyNo);

    List<BusinessPolicyApplyDetailPO> listByApplyNo(String applyNo);

    String generatePolicyNo();

    void savePolicy(PolicyAdjustMainReqDTO param);
}