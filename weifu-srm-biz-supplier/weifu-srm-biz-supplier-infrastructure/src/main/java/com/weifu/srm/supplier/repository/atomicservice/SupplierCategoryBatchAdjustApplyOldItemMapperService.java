package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyOldItemPO;

import java.util.List;

public interface SupplierCategoryBatchAdjustApplyOldItemMapperService extends IService<SupplierCategoryBatchAdjustApplyOldItemPO> {

    /**
     * 查询正在调整审核中的供应商品类关系
     * @return
     */
    List<SupplierCategoryBatchAdjustApplyOldItemPO> listOnApproving(List<SupplierCategoryBatchAdjustApplyOldItemPO> relationships);

}
