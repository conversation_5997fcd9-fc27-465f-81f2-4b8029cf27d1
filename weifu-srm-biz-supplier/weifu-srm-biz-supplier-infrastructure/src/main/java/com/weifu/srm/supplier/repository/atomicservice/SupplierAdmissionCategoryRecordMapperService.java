package com.weifu.srm.supplier.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordSearchPO;
import com.weifu.srm.supplier.request.AdmissionAndCategoryRespDTO;
import com.weifu.srm.supplier.request.QueryInvitation;

import java.util.List;

public interface SupplierAdmissionCategoryRecordMapperService extends IService<SupplierAdmissionCategoryRecordPO> {

    List<SupplierAdmissionCategoryRecordSearchPO> searchCategoryBySupplierNameAndCategories(String supplierName, List<String> categories);

    List<SupplierAdmissionCategoryRecordSearchPO> searchBySupplierName(String supplierName);

    List<SupplierAdmissionCategoryRecordPO> listByInvitationNo(String invitationNo);

    List<AdmissionAndCategoryRespDTO> getInvitation(QueryInvitation param);

}
