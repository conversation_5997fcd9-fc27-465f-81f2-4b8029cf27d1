<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierBlackListMapper">

   <delete id="removeBlackList">
       delete
       from
       supplier_black_list sb
       WHERE
       is_delete = 0
       <if test="list != null and list.size() > 0">
           and sb.supplier_code in
           <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
               #{item}
           </foreach>
       </if>

   </delete>


</mapper>