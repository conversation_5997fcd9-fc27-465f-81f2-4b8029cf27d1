<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierContactInfoMapper">
    <select id="pageFinancialContact" resultType="com.weifu.srm.supplier.response.SupplierFinanceContactRespDTO">
        select
        tb1.sap_supplier_code,tb1.supplier_name,tb1.supplier_short_name,tb1.status,
        tb2.finance_contact_name,tb2.finance_contact_email,tb2.finance_contact_phone,tb2.finance_contact_position
        from supplier_basic_info tb1
        left join supplier_contact_info tb2 on tb1.id=tb2.supplier_basic_msg_id
        where
        tb1.is_delete=0 and tb2.is_delete=0 and tb1.sap_supplier_code is not null and tb1.sap_supplier_code !=''
        <if test="param.sapSupplierCodes != null and param.sapSupplierCodes.size > 0">
            AND tb1.sap_supplier_code in
            <foreach collection="param.sapSupplierCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.supplierName != null and param.supplierName !=''">
            AND tb1.supplier_name like CONCAT('%',#{param.supplierName},'%')
        </if>
    </select>
</mapper>