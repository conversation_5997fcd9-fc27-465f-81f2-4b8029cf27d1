<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.AttachmentRecordMapper">

    <select id="queryBusinessLicenseAttachments"
            resultType="com.weifu.srm.supplier.response.SupplierBasicUpdateAttachmentRespDTO">
        select tb3.qualification_change_no, tb1.*
        from attachment_record tb1
                 left join qualification_change_basic_item tb2 on tb1.business_no = tb2.id
                 left join qualification_change_apply tb3 on tb2.qualification_change_no = tb3.qualification_change_no
        where tb1.is_delete = 0
          and tb2.is_delete = 0
          and tb3.is_delete = 0
          and tb1.business_type IN ('qualification_change_update_basic','business_license_file')
          and tb3.change_type = 'BASIC_UPDATE'
          and tb3.change_status = 'AGREE_STATUS'
          and tb3.supplier_code = #{supplierCode}
        order by tb3.approve_time desc
    </select>
</mapper>