<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierCategoryAdjustApplyMapper">

    <select id="page" resultType="com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageRespDTO">
        select scaa.apply_no, scaa.ticket_no, scaa.sap_supplier_code, scaa.apply_type, scaa.apply_status,
            scaa.apply_by, scaa.apply_name, scaa.apply_time, sbi.supplier_name, sbi.supplier_name_en
        from supplier_category_adjust_apply as scaa
        inner join supplier_basic_info as sbi on scaa.sap_supplier_code = sbi.sap_supplier_code
        where scaa.is_delete = 0
        <if test="query.applyNo != null and query.applyNo != ''">
            and scaa.apply_no like concat("%", #{query.applyNo}, "%")
        </if>
        <if test="query.ticketNo != null and query.ticketNo != ''">
            and scaa.ticket_no like concat("%", #{query.ticketNo}, "%")
        </if>
        <if test="query.sapSupplierCode != null and query.sapSupplierCode != ''">
            and sbi.sap_supplier_code like concat("%", #{query.sapSupplierCode}, "%")
        </if>
        <if test="query.supplierName != null and query.supplierName != ''">
            and (sbi.supplier_name like concat("%", #{query.supplierName}, "%")
                or sbi.supplier_name_en like concat("%", #{query.supplierName}, "%")
            )
        </if>
        <if test="query.applyTypes != null and query.applyTypes.size > 0">
            and scaa.apply_type in (
            <foreach collection="query.applyTypes" item="applyType" separator=",">
                #{applyType}
            </foreach>
            )
        </if>
        <if test="query.applyStatus != null and query.applyStatus.size > 0">
            and scaa.apply_status in (
            <foreach collection="query.applyStatus" item="status" separator=",">
                #{status}
            </foreach>
            )
        </if>
        <if test="query.applyName != null and query.applyName != ''">
            and scaa.apply_name like concat("%", #{query.applyName}, "%")
        </if>
        <if test="query.applyTimeStart != null">
            and scaa.apply_time >= #{query.applyTimeStart}
        </if>
        <if test="query.applyTimeEnd != null">
            and scaa.apply_time &lt;= #{query.applyTimeEnd}
        </if>
        and (
            1 = 0
            <foreach collection="keys" item="dataPermission">
                <if test="dataPermission.key == 'ALL'">
                    or 1 = 1
                </if>
                <if test="dataPermission.key == 'MANAGED_CATEGORY'">
                    or exists (
                        select 1 from supplier_category_adjust_apply_item as scaai
                        where scaai.apply_no = scaa.apply_no and scaai.three_level_category_code in (
                            <foreach collection="dataPermission.categoryCodes" item="categoryCode" separator=",">
                                #{categoryCode}
                            </foreach>
                        )
                    )
                </if>
                <if test="dataPermission.key == 'CREATOR'">
                    or scaa.apply_by = ${query.userId}
                </if>
            </foreach>
        )
        order by scaa.apply_time desc
    </select>

</mapper>