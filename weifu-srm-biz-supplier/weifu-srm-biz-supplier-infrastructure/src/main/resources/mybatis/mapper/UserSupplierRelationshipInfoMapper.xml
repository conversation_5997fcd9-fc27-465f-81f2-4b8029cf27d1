<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.UserSupplierRelationshipInfoMapper">

    <select id="selectSupplierRegistryList"  resultType="com.weifu.srm.supplier.response.SupplierRegistryInfoRespDTO">
        select DISTINCT
        us.supplier_basic_info_id supplierId,
        sb.sap_supplier_code supplierCode,
        us.supplier_name supplierName,
        sb.status status,
        us.create_time
        from
        user_supplier_relationship_info AS us
        join
        supplier_basic_info AS sb
        on
        us.supplier_basic_info_id =sb.id
        WHERE
        us.sys_user_id = #{userId}
        and us.is_delete = 0
        and sb.status != 'LEAVED'
        order by
        us.create_time desc
    </select>

    <select id="findAccountList"  resultType="com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO">
        SELECT
            usri.id ,
            usri.sys_user_id AS sysUserId,
            usri.role_id  AS roleId,
            usri.supplier_name AS supplierName,
            usri.is_delete AS isDelete
        FROM
            user_supplier_relationship_info usri
        WHERE
            usri.supplier_basic_info_id = #{supplierBasicInfoId}
        order by
            usri.create_time desc
    </select>

    <select id="findAccountListByUserId"  resultType="com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO">
        SELECT
        usri.id ,
        usri.sys_user_id AS sysUserId,
        usri.role_id  AS roleId,
        usri.supplier_name AS supplierName,
        usri.is_delete AS isDelete
        FROM
        user_supplier_relationship_info usri
        WHERE
        usri.supplier_basic_info_id = #{supplierBasicInfoId}
        and usri.sys_user_id = #{sysUserId}
        order by
        usri.create_time desc
    </select>

    <select id="updateAccountStatus">
        UPDATE
        user_supplier_relationship_info usri
        set
        usri.is_delete = #{status}
        WHERE
        usri.id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateSupplierName">
        UPDATE
        user_supplier_relationship_info usri
        set
        usri.supplier_name = #{supplierName}
        WHERE
        usri.supplier_basic_info_id = #{supplierBasicInfoId}
    </update>
</mapper>