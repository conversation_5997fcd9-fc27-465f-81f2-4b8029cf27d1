<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierGreyListMapper">

    <select id="searchGreyList" resultType="com.weifu.srm.supplier.repository.po.SupplierGreyListPO">
        select
        *
        from
        supplier_grey_list sg
        WHERE
        is_delete = 0
        <if test="list != null and list.size() > 0">
            and (sg.supplier_code, sg.division_id, sg.level2_category_code) in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                (#{item.supplierCode},#{item.divisionId},#{item.level2CategoryCode})
            </foreach>
        </if>
    </select>

    <delete id="removeGreyList">
        delete
        from
        supplier_grey_list sg
        WHERE
        is_delete = 0
        <if test="list != null and list.size() > 0">
            and (sg.supplier_code, sg.division_id, sg.level2_category_code) in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                (#{item.supplierCode},#{item.divisionId},#{item.level2CategoryCode})
            </foreach>
        </if>
    </delete>

    <delete id="removeBySupplierCode">
        delete
        from
        supplier_grey_list sg
        WHERE
        is_delete = 0
        and supplier_code = #{supplierCode}
    </delete>




</mapper>