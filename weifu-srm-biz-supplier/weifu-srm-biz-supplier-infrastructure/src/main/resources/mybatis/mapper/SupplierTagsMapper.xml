<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierTagsMapper">
    <delete id="removeTags">
        delete from supplier_tags where supplier_code in
        <foreach collection="supplierCodes" item="supplierCode" open="(" separator="," close=")">
            #{supplierCode}
        </foreach>
        and tag_code in
        <foreach collection="tagCodes" item="tagCode" open="(" separator="," close=")">
            #{tagCode}
        </foreach>
    </delete>
</mapper>