<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierAdmissionInvitationInfoMapper">
    <resultMap id="invitationListPO" type="com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationSearchPO">
        <id property="invitationNo" column="invitation_no"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="supplierType" column="supplier_type"/>
        <result property="admissionType" column="admission_type"/>
        <result property="startTime" column="create_time"/>
        <result property="createUser" column="create_name"/>
        <result property="supplierAdmissionInvitationStatus" column="supplier_admission_invitation_status"/>
    </resultMap>
    <select id="queryPageList" resultMap="invitationListPO">
        SELECT
        inv.invitation_no,
        inv.supplier_name,
        inv.supplier_type,
        inv.admission_type,
        inv.create_time,
        inv.create_name,
        inv.supplier_admission_invitation_status,
        inv.is_cen_purchase
        FROM
        supplier_admission_invitation_record AS inv
        WHERE inv.is_delete = 0 AND (
        1 = 0
        <foreach collection="keys" item="dataPermission">
            <if test="dataPermission.key == 'ALL'">
                OR (
                    1 = 1
                    <if test="model.excludeStatuses != null and model.excludeStatuses.size() > 0">
                        AND inv.supplier_admission_invitation_status NOT IN
                        <foreach collection="model.excludeStatuses" open="(" item="item" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>
            <if test="dataPermission.key == 'MANAGED_CATEGORY'">
                OR (
                    <if test="model.excludeStatuses != null and model.excludeStatuses.size() > 0">
                        inv.supplier_admission_invitation_status NOT IN
                        <foreach collection="model.excludeStatuses" open="(" item="item" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    AND
                    inv.invitation_no IN (
                        SELECT invitation_no FROM supplier_admission_category_record WHERE category_code IN
                        <foreach collection="dataPermission.categoryCodes" item="categoryCode" open="(" separator="," close=")">
                            #{categoryCode}
                        </foreach>
                    )
                )
            </if>
            <if test="dataPermission.key == 'CREATOR_A_MANAGED_CATEGORY'">
                OR (
                    inv.create_by = #{model.operationId}
                    OR
                    (
                        inv.create_by &lt;&gt; #{model.operationId}
                        <if test="model.excludeStatuses != null and model.excludeStatuses.size() > 0">
                            AND
                            inv.supplier_admission_invitation_status NOT IN
                            <foreach collection="model.excludeStatuses" open="(" item="item" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        AND
                        inv.invitation_no IN (
                            SELECT invitation_no FROM supplier_admission_category_record WHERE category_code IN
                            <foreach collection="dataPermission.categoryCodes" item="categoryCode" open="(" separator="," close=")">
                                #{categoryCode}
                            </foreach>
                        )
                    )
                )
            </if>
            <if test="dataPermission.key == 'CREATOR'">
               OR inv.create_by = #{model.operationId}
            </if>
            <if test="dataPermission.key == 'BELONG_DIVISION'">
                OR inv.division_code IN
                <foreach collection="dataPermission.divisionIds" item="divisionId" open="(" separator="," close=")">
                    #{divisionId}
                </foreach>
            </if>
        </foreach>
        )
        <if test="model.invitationNo != null and model.invitationNo != ''">
            AND inv.invitation_no = #{model.invitationNo}
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            AND inv.supplier_name = #{model.supplierName}
        </if>
        <if test="model.supplierType != null and model.supplierType != ''">
            AND inv.supplier_type = #{model.supplierType}
        </if>
        <if test="model.admissionType != null and model.admissionType != ''">
            AND inv.admission_type = #{model.admissionType}
        </if>
        <if test="model.supplierAdmissionInvitationStatus != null and model.supplierAdmissionInvitationStatus != ''">
            AND inv.supplier_admission_invitation_status = #{model.supplierAdmissionInvitationStatus}
        </if>
        <if test="model.startTime != null">
            <![CDATA[
                AND inv.create_time >= #{model.startTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endTime != null">
            <![CDATA[
                AND inv.create_time <= #{model.endTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.admissionCategories != null and model.admissionCategories.size() > 0">
            AND inv.invitation_no IN
            (
            SELECT invitation_no FROM supplier_admission_category_record
            WHERE category_code IN
            <foreach collection="model.admissionCategories" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="model.createUserId != null and model.createUserId != ''">
            AND inv.create_by = #{model.createUserId}
        </if>
        ORDER BY inv.create_time DESC
    </select>

    <select id="queryLastTransactionPeriod"
            resultType="com.weifu.srm.supplier.repository.po.TmpSupplierLastTransactionPeriodResultPO">
        select
        inv.admission_start_time startTime,
        inv.admission_end_time endTime
        from
        supplier_admission_invitation_record AS inv
        WHERE
        inv.is_delete = 0
        and inv.supplier_admission_invitation_status = 'APPROVED'
        and inv.supplier_name = #{supplierName}
        and inv.admission_type = 'TMP_ADMISSION_TYPE'
        order by
        inv.create_time desc
        limit 1
    </select>
    <select id="selectSupplierName" resultType="String">
        select
        DISTINCT
        sa.supplier_name
        from
        supplier_admission_invitation_record sa
        limit #{limit}
    </select>
    <select id="selectSupplierNameByKeyword" resultType="String">
        select
        DISTINCT
        sa.supplier_name
        from
        supplier_admission_invitation_record sa
        where
        sa.supplier_name like concat('%',#{keyword},'%')
        limit #{limit}
    </select>
</mapper>