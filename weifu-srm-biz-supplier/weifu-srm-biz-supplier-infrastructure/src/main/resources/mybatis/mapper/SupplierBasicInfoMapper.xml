<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierBasicInfoMapper">
    <resultMap id="basicListPO" type="com.weifu.srm.supplier.repository.po.SupplierBasicInfoListSearchPO">
        <id property="sapSupplierCode" column="sap_supplier_code" />
        <result property="supplierName" column="supplier_name" />
        <result property="status" column="status" />
        <result property="supplierClassification" column="supplier_classification" />
        <result property="tag" column="tag" />
        <result property="greyListStatus" column="grey_list_status" />
        <result property="blackListStatus" column="black_list_status" />
    </resultMap>
    <select id="queryPageList" resultMap="basicListPO">
        SELECT
            sbi.id,
            sbi.sap_supplier_code,
            sbi.supplier_name,
            sbi.supplier_short_name,
            sbi.credit_code,
            sbi.establishment_date,
            sbi.registered_address,
            sbi.status,
            skicr.create_time AS register_date,
            ptqr.update_time AS potential_to_qualified_date,
            sbi.supplier_classification,
            GROUP_CONCAT(DISTINCT st.tag_code ORDER BY st.tag_code SEPARATOR ',') AS tag,
            CASE WHEN EXISTS (
                SELECT 1
                FROM supplier_grey_list sgl
                WHERE sbi.sap_supplier_code = sgl.supplier_code and sgl.is_delete = 0
            ) THEN 1 ELSE 0 END AS grey_list_status,
            CASE WHEN EXISTS (
                SELECT 1
                FROM supplier_black_list sbl
                WHERE sbi.sap_supplier_code = sbl.supplier_code and sbl.is_delete = 0
            ) THEN 1 ELSE 0 END AS black_list_status,
            sbi.cpe_main_name,
            sbi.sqe_main_name
        FROM supplier_basic_info sbi
        LEFT JOIN supplier_tags st ON sbi.sap_supplier_code = st.supplier_code
        LEFT JOIN supplier_grey_list sgl ON sbi.sap_supplier_code = sgl.supplier_code
        LEFT JOIN supplier_black_list sbl ON sbi.sap_supplier_code = sbl.supplier_code
        left join supplier_category_relationship scr on sbi.sap_supplier_code = scr.sap_supplier_code
        LEFT JOIN (select supplier_basic_msg_id, min(create_time) as create_time from supplier_key_info_change_record where change_type = 'STATUS' AND is_delete = 0 group by supplier_basic_msg_id) skicr
            ON sbi.id = skicr.supplier_basic_msg_id
        LEFT JOIN (select sap_supplier_code, min(update_time) as update_time from potential_to_qualified_record where is_delete = 0 group by sap_supplier_code) ptqr
            ON sbi.sap_supplier_code = ptqr.sap_supplier_code
        where sbi.is_delete = 0
        <if test="model.sapSupplierCodes != null and model.sapSupplierCodes.size() > 0">
            and sbi.sap_supplier_code in
            <foreach collection="model.sapSupplierCodes" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sbi.supplier_name like CONCAT('%', #{model.supplierName}, '%')
        </if>
        <if test="model.status != null and model.status != ''">
            and sbi.status = #{model.status}
        </if>
        <if test="model.status == null">
            and sbi.status != 'DRAFT'
        </if>
        <if test="model.supplierClassification != null and model.supplierClassification != ''">
            and sbi.supplier_classification = #{model.supplierClassification}
        </if>
        <if test="model.cpeMainNames != null and model.cpeMainNames.size() > 0">
            and (sbi.cpe_main_name in
            <foreach collection="model.cpeMainNames" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="model.cpeMainNames.contains('NOTHING')">
                or sbi.cpe_main_name is NULL
            </if>
            )
        </if>
        <if test="model.sqeMainNames != null and model.sqeMainNames.size() > 0">
            and (sbi.sqe_main_name in
            <foreach collection="model.sqeMainNames" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="model.sqeMainNames.contains('NOTHING')">
                or sbi.sqe_main_name is NULL
            </if>
            )
        </if>
        <if test="model.greyListStatus != null and model.greyListStatus == 1">
            and sgl.is_delete = 0
        </if>
        <if test="model.greyListStatus != null and model.greyListStatus == 0">
            and sgl.is_delete is null
        </if>
        <if test="model.blackListStatus != null and model.blackListStatus == 1">
            and sbl.is_delete = 0
        </if>
        <if test="model.blackListStatus != null and model.blackListStatus == 0">
            and sbl.is_delete is null
        </if>
        <if test="model.tags != null and model.tags.size() > 0">
            and (st.tag_code in
            <foreach collection="model.tags" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            <foreach collection="model.tags" item="item" index="index" open="" separator="" close="">
                <if test="item == 'null'">
                    or st.tag_code is null
                </if>
            </foreach>
            )
        </if>
        <if test="model.registerDateStart != null and model.registerDateStart != ''">
            <![CDATA[
                AND skicr.create_time >= #{model.registerDateStart, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.registerDateEnd != null and model.registerDateEnd != ''">
            <![CDATA[
                AND skicr.create_time <= #{model.registerDateEnd, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.potentialToQualifiedDateStart != null and model.potentialToQualifiedDateStart != ''">
            <![CDATA[
                AND ptqr.update_time >= #{model.potentialToQualifiedDateStart, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.potentialToQualifiedDateEnd != null and model.potentialToQualifiedDateEnd != ''">
            <![CDATA[
                AND ptqr.update_time <= #{model.potentialToQualifiedDateEnd, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="keys != null and keys.size() > 0">
        and (
        1 = 0
        <foreach collection="keys" item="dataPermission">
            <if test="dataPermission.key == 'ALL'">
                or 1 = 1
            </if>
            <if test="dataPermission.key == 'MANAGED_CATEGORY'">
                or scr.category_code in
                <foreach collection="dataPermission.categoryCodes" item="categoryCode" open="(" separator="," close=")">
                    #{categoryCode}
                </foreach>
            </if>
        </foreach>
        )
        </if>
        GROUP BY sbi.id
        ORDER BY sbi.sap_supplier_code DESC
    </select>

    <select id="queryPageListForQC" resultMap="basicListPO">
        SELECT
            sbi.id,
            sbi.sap_supplier_code,
            sbi.supplier_name,
            sbi.status,
            sbi.supplier_classification,
            GROUP_CONCAT(DISTINCT st.tag_code ORDER BY st.tag_code SEPARATOR ',') AS tag,
            CASE WHEN EXISTS (
                SELECT 1
                FROM supplier_grey_list sgl
                WHERE sbi.sap_supplier_code = sgl.supplier_code and sgl.is_delete = 0
            ) THEN 1 ELSE 0 END AS grey_list_status,
            CASE WHEN EXISTS(
                SELECT 1
                FROM supplier_black_list sbl
                WHERE sbi.sap_supplier_code = sbl.supplier_code and sbl.is_delete = 0
            ) THEN 1 ELSE 0 END AS black_list_status
        FROM supplier_basic_info sbi
        LEFT JOIN supplier_tags st ON sbi.sap_supplier_code = st.supplier_code
        LEFT JOIN supplier_grey_list sgl ON sbi.sap_supplier_code = sgl.supplier_code
        LEFT JOIN supplier_black_list sbl ON sbi.sap_supplier_code = sbl.supplier_code
        where sbi.is_delete = 0
        and sbi.status in ('REGISTERED_SUPPLIER', 'POTENTIAL_SUPPLIER', 'QUALIFIED_SUPPLIER')
        and sbi.sap_supplier_code is not null
        <if test="model.sapSupplierCodes != null and model.sapSupplierCodes.size() > 0">
            and sbi.sap_supplier_code in
            <foreach collection="model.sapSupplierCodes" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sbi.supplier_name like CONCAT('%', #{model.supplierName}, '%')
        </if>
        <if test="model.supplierClassification != null and model.supplierClassification != ''">
            and sbi.supplier_classification = #{model.supplierClassification}
        </if>
        <if test="model.greyListStatus != null and model.greyListStatus == 1">
            and sgl.is_delete = 0
        </if>
        <if test="model.greyListStatus != null and model.greyListStatus == 0">
            and sgl.is_delete is null
        </if>
        <if test="model.blackListStatus != null and model.blackListStatus == 1">
            and sbl.is_delete = 0
        </if>
        <if test="model.blackListStatus != null and model.blackListStatus == 0">
            and sbl.is_delete is null
        </if>
        <if test="model.tags != null and model.tags.size() > 0">
            and st.tag_code in
            <foreach collection="model.tags" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        sbi.id
    </select>

    <select id="selectForInquiryOrder" parameterType="com.weifu.srm.supplier.request.SupplierForInquiryOrderQueryReqDTO"
            resultType="com.weifu.srm.supplier.response.SupplierBasicInfoInquiryOrderRespDTO">
        SELECT
            sbi.id AS supplierId,
            sbi.sap_supplier_code AS sapSupplierCode,
            sbi.supplier_name AS supplierName,
            sbi.supplier_name_en AS supplierNameEN,
            sbi.supplier_classification AS supplierClassification,
            scr.supplier_category_status AS supplierCategoryStatus,
            scr.admission_complete_time AS admissionCompleteTime
        FROM supplier_basic_info sbi INNER JOIN supplier_category_relationship scr ON sbi.sap_supplier_code = scr.sap_supplier_code
        WHERE NOT EXISTS (SELECT 1 FROM supplier_black_list sbl WHERE sbl.is_delete = 0 AND sbi.sap_supplier_code = sbl.supplier_code)
              AND sbi.is_delete = 0 AND scr.is_delete = 0
              AND scr.category_code = #{model.categoryCode}
              <if test="model.synSapResult != null">
                  AND sbi.syn_sap_result = #{model.synSapResult}
              </if>
              <if test="model.status != null and model.status.size() > 0">
                  AND sbi.status IN
                  <foreach collection="model.status" open="(" item="item" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
              <if test="model.excludeSupplierCategoryStatusAry != null and model.excludeSupplierCategoryStatusAry.size() > 0">
                  AND scr.supplier_category_status NOT IN
                  <foreach collection="model.excludeSupplierCategoryStatusAry" open="(" item="item" separator="," close=")">
                     #{item}
                  </foreach>
              </if>

    </select>

    <select id="selectForSourcingBySupplierCodes" parameterType="com.weifu.srm.supplier.request.SupplierForInquiryOrderQueryReqDTO"
            resultType="com.weifu.srm.supplier.response.SupplierBasicInfoInquiryOrderRespDTO">
        SELECT
        sbi.id AS supplierId,
        sbi.sap_supplier_code AS sapSupplierCode,
        sbi.supplier_name AS supplierName,
        sbi.supplier_name_en AS supplierNameEN,
        sbi.supplier_classification AS supplierClassification
        FROM supplier_basic_info sbi
        WHERE sbi.is_delete = 0
              AND sbi.sap_supplier_code IN
              <foreach collection="supplierCodes" open="(" item="item" separator="," close=")">
                  #{item}
              </foreach>
    </select>

    <select id="getDetailById" resultType="com.weifu.srm.supplier.response.SupplierBasicInfoListRespExportDTO">
        SELECT
            sbi.id,
            sbi.sap_supplier_code,
            sbi.supplier_name,
            sbi.supplier_short_name,
            sbi.credit_code,
            sbi.establishment_date,
            sbi.registered_address,
            sbi.status,
            skicr.create_time AS register_date,
            ptqr.update_time AS potential_to_qualified_date,
            sbi.supplier_classification,
            GROUP_CONCAT(DISTINCT st.tag_code ORDER BY st.tag_code SEPARATOR ',') AS tag,
            CASE WHEN EXISTS (
                SELECT 1
                FROM supplier_grey_list sgl
                WHERE sbi.sap_supplier_code = sgl.supplier_code and sgl.is_delete = 0
            ) THEN 1 ELSE 0 END AS grey_list_status,
            CASE WHEN EXISTS (
                SELECT 1
                FROM supplier_black_list sbl
                WHERE sbi.sap_supplier_code = sbl.supplier_code and sbl.is_delete = 0
            ) THEN 1 ELSE 0 END AS black_list_status,
            sbi.cpe_main_name,
            sbi.sqe_main_name
        FROM supplier_basic_info sbi
        LEFT JOIN supplier_tags st ON sbi.sap_supplier_code = st.supplier_code
        LEFT JOIN supplier_grey_list sgl ON sbi.sap_supplier_code = sgl.supplier_code
        LEFT JOIN supplier_black_list sbl ON sbi.sap_supplier_code = sbl.supplier_code
        LEFT JOIN (select supplier_basic_msg_id, min(create_time) as create_time from supplier_key_info_change_record where change_type = 'STATUS' AND is_delete = 0 group by supplier_basic_msg_id) skicr
            ON sbi.id = skicr.supplier_basic_msg_id
        LEFT JOIN (select sap_supplier_code, min(update_time) as update_time from potential_to_qualified_record where is_delete = 0 group by sap_supplier_code) ptqr
            ON sbi.sap_supplier_code = ptqr.sap_supplier_code
        where sbi.is_delete = 0
        <if test="supplierIds != null and supplierIds.size() > 0">
            and sbi.id in
            <foreach collection="supplierIds" open="(" item="supplierId" separator="," close=")">
                #{supplierId}
            </foreach>
        </if>
        GROUP BY sbi.id
    </select>

    <select id="selectEffectiveSupplier" parameterType="com.weifu.srm.supplier.request.SupplierBasicSelectReqDTO"
            resultType="com.weifu.srm.supplier.response.SupplierBasicSimpleInfoRespDTO">
        SELECT
        sbi.id,
        sbi.sap_supplier_code AS sapSupplierCode,
        sbi.supplier_name AS supplierName,
        sbi.supplier_name_en AS supplierNameEN,
        sbi.status AS supplierClassification
        FROM supplier_basic_info sbi
        WHERE NOT EXISTS (SELECT 1 FROM supplier_black_list sbl WHERE sbl.is_delete = 0 AND sbi.sap_supplier_code = sbl.supplier_code)
              AND NOT EXISTS (SELECT 1 FROM supplier_grey_list sgl WHERE sgl.is_delete = 0 AND sbi.sap_supplier_code = sgl.supplier_code)
              AND sbi.is_delete = 0
              <if test="supplierCode != null and supplierCode != ''">
                  AND (
                      sbi.sap_supplier_code LIKE CONCAT('%', #{supplierCode}, '%') OR
                      sbi.supplier_name LIKE CONCAT('%', #{supplierCode}, '%') OR
                      sbi.supplier_name_en LIKE CONCAT('%', #{supplierCode}, '%')
                  )
              </if>
              <if test="status != null and status.size() > 0">
                  AND sbi.status IN
                  <foreach collection="status" open="(" item="item" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
    </select>

    <select id="querySupplierListWithConditions" parameterType="com.weifu.srm.supplier.request.SupplierBasicSelectReqDTO"
    resultType="com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO">
        select
        sbi.*
        from
        supplier_basic_info sbi
        LEFT JOIN
        supplier_grey_list sgl ON
        sbi.sap_supplier_code = sgl.supplier_code
        LEFT JOIN
        supplier_black_list sbl ON
        sbi.sap_supplier_code = sbl.supplier_code
        where
        1 = 1
        <if test="model.supplierCodeMustExist != null and model.supplierCodeMustExist == 1">
            and
            sbi.sap_supplier_code is not null
        </if>
        <if test="model.supplierCodeAndShortName != null and model.supplierCodeAndShortName != ''">
            and
            (
            sbi.supplier_short_name like CONCAT('%', #{model.supplierCodeAndShortName}, '%')
            or
            sbi.sap_supplier_code like CONCAT('%', #{model.supplierCodeAndShortName}, '%')
            )
        </if>
        <if test="model.supplierCodeAndSupplierName != null and model.supplierCodeAndSupplierName != ''">
            and
            (
            sbi.supplier_name like CONCAT('%', #{model.supplierCodeAndSupplierName}, '%')
            or
            sbi.sap_supplier_code like CONCAT('%', #{model.supplierCodeAndSupplierName}, '%')
            )
        </if>
        <if test="model.status != null and model.status.size() > 0">
            and
            sbi.status in
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierCode != null and model.supplierCode != ''">
            and sbi.sap_supplier_code = #{model.supplierCode}
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sbi.supplier_name like CONCAT('%', #{model.supplierName}, '%')
        </if>
        <if test="model.supplierNameEn != null and model.supplierNameEn != ''">
            and sbi.supplier_name_en like CONCAT('%', #{model.supplierNameEn}, '%')
        </if>
        <if test="model.isGreyList != null and model.isGreyList == 1">
            and sgl.is_delete = 0
        </if>
        <if test="model.isGreyList != null and model.isGreyList == 0">
            and sgl.is_delete is null
        </if>
        <if test="model.isBlackList != null and model.isBlackList == 1">
            and sbl.is_delete = 0
        </if>
        <if test="model.isBlackList != null and model.isBlackList == 0">
            and sbl.is_delete is null
        </if>
        order by sbi.id desc
    </select>


    <select id="listEligibleBySupplierCodes"  resultType="com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO">
        select
        sbi.*
        from
        supplier_basic_info sbi
        LEFT JOIN
        supplier_grey_list sgl ON
        sbi.sap_supplier_code = sgl.supplier_code
        LEFT JOIN
        supplier_black_list sbl ON
        sbi.sap_supplier_code = sbl.supplier_code
        where
        sbi.sap_supplier_code is not null
        and sbi.is_delete = 0
        and sbi.status  = "QUALIFIED_SUPPLIER"
        <if test="supplierCodes != null and supplierCodes.size() > 0">
            and sbi.sap_supplier_code in
            <foreach collection="supplierCodes" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and sgl.is_delete is null
        and sbl.is_delete is null
        order by sbi.id desc
    </select>
</mapper>