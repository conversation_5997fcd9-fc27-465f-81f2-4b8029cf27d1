<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierAdmissionRecordMapper">
    <select id="countByStatus"  resultType="com.weifu.srm.supplier.response.SupplierAdmissionStatisticsRespDTO">
        SELECT supplier_basic_msg_id as supplier_id,
        case admission_status
        when 'CONDITION_APPROVING' then 'COMPLETED'
        when 'APPROVED' then 'COMPLETED'
        when 'REJECT' then 'COMPLETED'
        else admission_status
        end as admission_status,
        COUNT(*) as count
        FROM supplier_admission_record
        <where>
            <if test="supplierId != null">
                supplier_basic_msg_id = #{supplierId}
            </if>
        </where>
        GROUP BY supplier_basic_msg_id,admission_status
    </select>
    <select id="queryList" parameterType="com.baomidou.mybatisplus.core.conditions.query.QueryWrapper" resultType="com.weifu.srm.supplier.response.AdmissionQuestionnairePageQueryRespDTO">
        <!-- 定义复杂的 SQL 查询 -->
            SELECT
            sacr.admission_no,
            sacr.supplier_basic_msg_id,
            sacr.invitation_no,
            sacr.supplier_name,
            sacr.supplier_code,
            sacr.category_name as admission_category,
            sacr.admission_type,
            sacr.supplier_type,
            sacr.admission_status,
            sacr.admission_invitation_by,
            sacr.admission_invitation_name,
            sacr.admission_apply_time,
            sacr.admission_complete_time,
            sacr.supplier_category_status AS supplier_status
<!--            sar.sap_status,-->
<!--            sar.sap_desc,-->
<!--            sar.create_by,-->
<!--            sar.create_name,-->
<!--            sar.create_time,-->
<!--            sar.update_by,-->
<!--            sar.update_name,-->
<!--            sar.update_time,-->
<!--            sar.is_delete-->
            FROM
            supplier_admission_category_record sacr
            <if test="_parameter != null">
                ${ew.customSqlSegment}
            </if>
    </select>

    <select id="queryWareHousePage" parameterType="com.baomidou.mybatisplus.core.conditions.query.QueryWrapper" resultType="com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO">
        <!-- 定义复杂的 SQL 查询 -->
        SELECT distinct
        sar.admission_no,
        sar.invitation_no,
        sar.supplier_basic_msg_id,
        sar.supplier_name,
        sar.supplier_code,
        sar.admission_type,
        sar.supplier_type,
        sar.admission_status,
        sar.admission_invitation_by,
        sar.admission_invitation_name,
        sar.admission_apply_time,
        sar.admission_complete_time,
        sar.supplier_category_status AS supplier_status,
        sar.sap_status,
        sar.sap_desc,
        sar.create_by,
        sar.create_name,
        sar.create_time,
        sar.update_by,
        sar.update_name,
        sar.update_time,
        sar.is_delete
        FROM
        supplier_admission_record sar
        LEFT JOIN
        supplier_admission_category_record sacr ON sar.invitation_no = sacr.invitation_no
        <if test="_parameter != null">
            ${ew.customSqlSegment}
        </if>
    </select>
    <!-- 插入主表数据 -->
    <insert id="saveAdmissionQuestionnaire">
        INSERT INTO supplier_admission_record (
            questionnaire_template_code,
            admission_no,
            invitation_no,
            supplier_basic_msg_id,
            supplier_code,
            supplier_name,
            admission_status,
            supplier_type,
            admission_type,
            admission_invitation_by,
            admission_invitation_name,
            create_by,
            create_name,
            update_by,
            update_name
            ) VALUES (
            #{questionnaireTemplateCode},
            #{admissionNo},
            #{invitationNo},
            #{supplierBasicMsgId},
            #{sapSupplierCode},
            #{supplierName},
            #{admissionStatus},
            #{supplierType},
            #{admissionType},
            #{admissionInvitationBy},
            #{admissionInvitationName},
            #{operationBy},
            #{operationByName},
            #{operationBy},
            #{operationByName}
        );
        <!-- 插入资质认证信息 -->
<!--        <if test="certification != null">-->
<!--            <foreach collection="certification" item="item" index="index" >-->
<!--                <if test="item != null and item.admissionNo != null ">-->
<!--                    INSERT INTO admission_questionnaire_certification (-->
<!--                        admission_no,-->
<!--                        certification_body,-->
<!--                        certification_type,-->
<!--                        certification_number,-->
<!--                        certification_scope,-->
<!--                        certification_start_date,-->
<!--                        certification_expiry_date,-->
<!--                        has_quality_certification,-->
<!--                        create_by,-->
<!--                        create_name,-->
<!--                        update_by,-->
<!--                        update_name-->
<!--                        ) VALUES (-->
<!--                        #{item.admissionNo},-->
<!--                        #{item.certificationBody},-->
<!--                        #{item.certificationType},-->
<!--                        #{item.certificationNumber},-->
<!--                        #{item.certificationScope},-->
<!--                        #{item.certificationStartDate},-->
<!--                        #{item.certificationExpiryDate},-->
<!--                        #{item.hasQualityCertification},-->
<!--                        #{operationBy},-->
<!--                        #{operationByName},-->
<!--                        #{operationBy},-->
<!--                        #{operationByName}-->
<!--                    );-->
<!--                </if>-->
<!--            </foreach>-->
<!--        </if>-->

        <!-- 插入客户情况 -->
        <if test="customer != null">
            <foreach collection="customer" item="item" index="index" >
                <if test="item != null and item.admissionNo != null ">
                    INSERT INTO admission_questionnaire_customer (
                        admission_no,
                        customer_name,
                        annual_sales_amt,
                        main_supplier_products,
                        has_automotive_industry_experience
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.customerName},
                        #{item.annualSalesAmt},
                        #{item.mainSupplierProducts},
                        #{item.hasAutomotiveIndustryExperience}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入设备情况 -->
        <if test="equipment != null">
            <foreach collection="equipment" item="item" index="index" >
                <if test="item != null and item.admissionNo != null ">
                    INSERT INTO admission_questionnaire_equipment (
                        admission_no,
                        equipment_type,
                        equipment_name,
                        specifications,
                        quantity,
                        brand,
                        production_date,
                        main_technical_indicators_and_capabilities
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.equipmentType},
                        #{item.equipmentName},
                        #{item.specifications},
                        #{item.quantity},
                        #{item.brand},
                        #{item.productionDate},
                        #{item.mainTechnicalIndicatorsAndCapabilities}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入公司信息 -->
        <if test="companyInfo != null">
            <foreach collection="companyInfo" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_company_info (
                        admission_no,
                        chairman,
                        general_manager,
                        is_listed
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.chairman},
                        #{item.generalManager},
                        #{item.isListed}
                    );
                </if>
            </foreach>
        </if>

        <!-- 插入公司规模信息 -->
        <if test="companySize != null">
            <foreach collection="companySize" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_company_size (
                        admission_no,
                        plant_type,
                        total_plant_area,
                        used_plant_area
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.plantType},
                        #{item.totalPlantArea},
                        #{item.usedPlantArea}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入研发能力 -->
        <if test="rdCapability != null">
            <foreach collection="rdCapability" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_rd_capability (
                        admission_no,
                        has_lab,
                        lab_qualification_level,
                        number_of_patents_qty,
                        main_patent_names,
                        number_of_r_d_personnel_qty
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.hasLab},
                        #{item.labQualificationLevel},
                        #{item.numberOfPatentsQty},
                        #{item.mainPatentNames},
                        #{item.numberOfRDPersonnelQty}
                    );
                </if>
            </foreach>
        </if>

        <!-- 插入物流能力 -->
        <if test="logistics != null">
            <foreach collection="logistics" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_logistics (
                        admission_no,
                        has_warehouse,
                        warehouse_area
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.hasWarehouse},
                        #{item.warehouseArea}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="qualityAssurance != null">
            <foreach collection="qualityAssurance" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_quality_assurance (
                        admission_no,
                        has_incoming_inspection,
                        incoming_inspection_personnel_qty,
                        has_outgoing_inspection,
                        outgoing_inspection_personnel_qty,
                        has_in_process_inspection,
                        in_process_inspection_personnel_qty
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.hasIncomingInspection},
                        #{item.incomingInspectionPersonnelQty},
                        #{item.hasOutgoingInspection},
                        #{item.outgoingInspectionPersonnelQty},
                        #{item.hasInProcessInspection},
                        #{item.inProcessInspectionPersonnelQty}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="willingness != null">
            <foreach collection="willingness" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_willingness (
                        admission_no,
                        willing_to_sign_framework_agreement,
                        willing_to_sign_quality_agreement,
                        willing_to_sign_integrity_agreement,
                        willing_to_provide_documents,
                        willing_to_accept_acceptance_bill,
                        willing_to_accept_credit_period,
                        willing_to_accept_consignment_settlement,
                        willing_to_respond_to_national_demand
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.willingToSignFrameworkAgreement},
                        #{item.willingToSignQualityAgreement},
                        #{item.willingToSignIntegrityAgreement},
                        #{item.willingToProvideDocuments},
                        #{item.willingToAcceptAcceptanceBill},
                        #{item.willingToAcceptCreditPeriod},
                        #{item.willingToAcceptConsignmentSettlement},
                        #{item.willingToRespondToNationalDemand}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="subcontractor != null">
            <foreach collection="subcontractor" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_subcontractor (
                    admission_no,
                    subcontractor_name,
                    supply_ratio,
                    main_purchase_product,
                    annual_procurement_amt
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.subcontractorName},
                    #{item.supplyRatio},
                    #{item.mainPurchaseProduct},
                    #{item.annualProcurementAmt}
                    );
                </if>
            </foreach>
        </if>

        <!-- 插入人员情况 -->
        <if test="personnel != null">
            <foreach collection="personnel" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_personnel (
                    admission_no,
                    total_employees_qty,
                    management_personnel_qty,
                    technical_personnel_qty,
                    quality_personnel_qty,
                    production_personnel_qty,
                    local_production_personnel_ratio_ratio
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.totalEmployeesQty},
                    #{item.managementPersonnelQty},
                    #{item.technicalPersonnelQty},
                    #{item.qualityPersonnelQty},
                    #{item.productionPersonnelQty},
                    #{item.localProductionPersonnelRatioRatio}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="financial != null">
            <foreach collection="financial" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_financial (
                        admission_no,
                        report_year,
                        total_assets_amt,
                        total_liabilities_amt,
                        revenue_amt,
                        accounts_receivable_amt,
                        advance_payments_amt,
                        net_profit_margin_ratio,
                        gross_profit_margin_ratio,
                        debt_ratio
                        ) VALUES (
                        #{item.admissionNo},
                        #{item.reportYear},
                        #{item.totalAssetsAmt},
                        #{item.totalLiabilitiesAmt},
                        #{item.revenueAmt},
                        #{item.accountsReceivableAmt},
                        #{item.advancePaymentsAmt},
                        #{item.netProfitMarginRatio},
                        #{item.grossProfitMarginRatio},
                        #{item.debtRatio}
                    );
                </if>
            </foreach>
        </if>
    </insert>

    <insert id="saveAdmissionQuestionnaireDetail">
<!--        <if test="certification != null">-->
<!--            <foreach collection="certification" item="item" index="index" >-->
<!--                <if test="item != null and item.admissionNo != null ">-->
<!--                    INSERT INTO admission_questionnaire_certification (-->
<!--                    admission_no,-->
<!--                    certification_body,-->
<!--                    certification_type,-->
<!--                    certification_number,-->
<!--                    certification_scope,-->
<!--                    certification_start_date,-->
<!--                    certification_expiry_date,-->
<!--                    has_quality_certification,-->
<!--                    create_by,-->
<!--                    create_name,-->
<!--                    update_by,-->
<!--                    update_name-->
<!--                    ) VALUES (-->
<!--                    #{item.admissionNo},-->
<!--                    #{item.certificationBody},-->
<!--                    #{item.certificationType},-->
<!--                    #{item.certificationNumber},-->
<!--                    #{item.certificationScope},-->
<!--                    #{item.certificationStartDate},-->
<!--                    #{item.certificationExpiryDate},-->
<!--                    #{item.hasQualityCertification},-->
<!--                    #{operationBy},-->
<!--                    #{operationByName},-->
<!--                    #{operationBy},-->
<!--                    #{operationByName}-->
<!--                    );-->
<!--                </if>-->
<!--            </foreach>-->
<!--        </if>-->

        <!-- 插入客户情况 -->
        <if test="customer != null">
            <foreach collection="customer" item="item" index="index" >
                <if test="item != null and item.admissionNo != null ">
                    INSERT INTO admission_questionnaire_customer (
                    admission_no,
                    customer_name,
                    annual_sales_amt,
                    main_supplier_products,
                    has_automotive_industry_experience
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.customerName},
                    #{item.annualSalesAmt},
                    #{item.mainSupplierProducts},
                    #{item.hasAutomotiveIndustryExperience}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入设备情况 -->
        <if test="equipment != null">
            <foreach collection="equipment" item="item" index="index" >
                <if test="item != null and item.admissionNo != null ">
                    INSERT INTO admission_questionnaire_equipment (
                    admission_no,
                    equipment_type,
                    equipment_name,
                    specifications,
                    quantity,
                    brand,
                    production_date,
                    main_technical_indicators_and_capabilities
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.equipmentType},
                    #{item.equipmentName},
                    #{item.specifications},
                    #{item.quantity},
                    #{item.brand},
                    #{item.productionDate},
                    #{item.mainTechnicalIndicatorsAndCapabilities}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入公司信息 -->
        <if test="companyInfo != null">
            <foreach collection="companyInfo" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_company_info (
                    admission_no,
                    chairman,
                    general_manager,
                    is_listed
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.chairman},
                    #{item.generalManager},
                    #{item.isListed}
                    );
                </if>
            </foreach>
        </if>

        <!-- 插入公司规模信息 -->
        <if test="companySize != null">
            <foreach collection="companySize" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_company_size (
                    admission_no,
                    plant_type,
                    total_plant_area,
                    used_plant_area
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.plantType},
                    #{item.totalPlantArea},
                    #{item.usedPlantArea}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入研发能力 -->
        <if test="rdCapability != null">
            <foreach collection="rdCapability" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_rd_capability (
                    admission_no,
                    has_lab,
                    lab_qualification_level,
                    number_of_patents_qty,
                    main_patent_names,
                    number_of_r_d_personnel_qty
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.hasLab},
                    #{item.labQualificationLevel},
                    #{item.numberOfPatentsQty},
                    #{item.mainPatentNames},
                    #{item.numberOfRDPersonnelQty}
                    );
                </if>
            </foreach>
        </if>

        <!-- 插入物流能力 -->
        <if test="logistics != null">
            <foreach collection="logistics" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_logistics (
                    admission_no,
                    has_warehouse,
                    warehouse_area
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.hasWarehouse},
                    #{item.warehouseArea}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="qualityAssurance != null">
            <foreach collection="qualityAssurance" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_quality_assurance (
                    admission_no,
                    has_incoming_inspection,
                    incoming_inspection_personnel_qty,
                    has_outgoing_inspection,
                    outgoing_inspection_personnel_qty,
                    has_in_process_inspection,
                    in_process_inspection_personnel_qty
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.hasIncomingInspection},
                    #{item.incomingInspectionPersonnelQty},
                    #{item.hasOutgoingInspection},
                    #{item.outgoingInspectionPersonnelQty},
                    #{item.hasInProcessInspection},
                    #{item.inProcessInspectionPersonnelQty}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="willingness != null">
            <foreach collection="willingness" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_willingness (
                    admission_no,
                    willing_to_sign_framework_agreement,
                    willing_to_sign_quality_agreement,
                    willing_to_sign_integrity_agreement,
                    willing_to_provide_documents,
                    willing_to_accept_acceptance_bill,
                    willing_to_accept_credit_period,
                    willing_to_accept_consignment_settlement,
                    willing_to_respond_to_national_demand
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.willingToSignFrameworkAgreement},
                    #{item.willingToSignQualityAgreement},
                    #{item.willingToSignIntegrityAgreement},
                    #{item.willingToProvideDocuments},
                    #{item.willingToAcceptAcceptanceBill},
                    #{item.willingToAcceptCreditPeriod},
                    #{item.willingToAcceptConsignmentSettlement},
                    #{item.willingToRespondToNationalDemand}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="subcontractor != null">
            <foreach collection="subcontractor" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_subcontractor (
                    admission_no,
                    subcontractor_name,
                    supply_ratio,
                    main_purchase_product,
                    annual_procurement_amt
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.subcontractorName},
                    #{item.supplyRatio},
                    #{item.mainPurchaseProduct},
                    #{item.annualProcurementAmt}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入人员情况 -->
        <if test="personnel != null">
            <foreach collection="personnel" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_personnel (
                    admission_no,
                    total_employees_qty,
                    management_personnel_qty,
                    technical_personnel_qty,
                    quality_personnel_qty,
                    production_personnel_qty,
                    local_production_personnel_ratio_ratio
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.totalEmployeesQty},
                    #{item.managementPersonnelQty},
                    #{item.technicalPersonnelQty},
                    #{item.qualityPersonnelQty},
                    #{item.productionPersonnelQty},
                    #{item.localProductionPersonnelRatioRatio}
                    );
                </if>
            </foreach>
        </if>
        <!-- 插入质量保证能力 -->
        <if test="financial != null">
            <foreach collection="financial" item="item" index="index" >
                <if test="item != null and item.admissionNo != null">
                    INSERT INTO admission_questionnaire_financial (
                    admission_no,
                    report_year,
                    total_assets_amt,
                    total_liabilities_amt,
                    revenue_amt,
                    accounts_receivable_amt,
                    advance_payments_amt,
                    net_profit_margin_ratio,
                    gross_profit_margin_ratio,
                    debt_ratio
                    ) VALUES (
                    #{item.admissionNo},
                    #{item.reportYear},
                    #{item.totalAssetsAmt},
                    #{item.totalLiabilitiesAmt},
                    #{item.revenueAmt},
                    #{item.accountsReceivableAmt},
                    #{item.advancePaymentsAmt},
                    #{item.netProfitMarginRatio},
                    #{item.grossProfitMarginRatio},
                    #{item.debtRatio}
                    );
                </if>
            </foreach>
        </if>

    </insert>
</mapper>