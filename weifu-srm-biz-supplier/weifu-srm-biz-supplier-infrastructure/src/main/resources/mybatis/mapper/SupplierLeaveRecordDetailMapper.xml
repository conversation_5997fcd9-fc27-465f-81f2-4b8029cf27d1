<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierLeaveRecordDetailMapper">

    <select id="queryListPage" resultType="com.weifu.srm.supplier.response.leave.SupplierLeaveApplyListRespDTO">
        select
        sl.id limitId,
        sl.limit_no limitNo,
        sl.supplier_code supplierCode,
        sb.supplier_name  supplierName,
        sb.status supplierStatus,
        sb.supplier_classification supplierGrade,
        sl.wait_exit_no waitExitNo,
        sl.wait_exit_ticket_no waitExitTicketNo,
        sl.create_time createTime,
        sl.wait_exit_valid_time waitExitValidTime,
        sl.exit_valid_time exitValidTime,
        sl.exit_no exitNo,
        sl.exit_ticket_no exitTicketNo,
        sl.status
        from
        supplier_leave_record_detail sl
        left join supplier_basic_info sb on
        sl.supplier_code = sb.sap_supplier_code
        where sl.is_delete = 0
        and sb.is_delete = 0
        <if test="model.supplierCodes != null and model.supplierCodes.size() > 0">
            and sb.sap_supplier_code in
            <foreach collection="model.supplierCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sb.supplier_name like concat("%", #{model.supplierName}, "%")
        </if>
        <if test="model.supplierStatus != null and model.supplierStatus.size() > 0">
            and sb.status in
            <foreach collection="model.supplierStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.status != null and model.status.size() > 0">
            and sl.status in
            <foreach collection="model.status" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.limitNo != null and model.limitNo != ''">
            and sl.limit_no = #{model.limitNo}
        </if>
        <if test="model.waitExitTicketNo != null and model.waitExitTicketNo != ''">
            and sl.wait_exit_ticket_no like concat("%", #{model.waitExitTicketNo}, "%")
        </if>
        <if test="model.exitTicketNo != null and model.exitTicketNo != ''">
            and sl.exit_ticket_no like concat("%", #{model.exitTicketNo}, "%")
        </if>
        <if test="model.startCreateTime != null">
            <![CDATA[
                AND sl.create_time >= #{model.startCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endCreateTime != null">
            <![CDATA[
                AND sl.create_time <= #{model.endCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startWaitExitValidTime != null">
            <![CDATA[
                AND sl.wait_exit_valid_time >= #{model.startWaitExitValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endWaitExitValidTime != null">
            <![CDATA[
                AND sl.wait_exit_valid_time <= #{model.endWaitExitValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startExitValidTime != null">
            <![CDATA[
                AND sl.exit_valid_time >= #{model.startExitValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endExitValidTime != null">
            <![CDATA[
                AND sl.exit_valid_time <= #{model.endExitValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        order by sl.create_time desc, sl.id desc
    </select>


</mapper>