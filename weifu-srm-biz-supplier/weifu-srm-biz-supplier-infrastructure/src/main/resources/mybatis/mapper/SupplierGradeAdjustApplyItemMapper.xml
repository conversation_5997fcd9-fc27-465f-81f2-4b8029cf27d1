<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierGradeAdjustApplyItemMapper">
    
    <select id="queryHistoryList" resultType="com.weifu.srm.supplier.response.SupplierGradeHistoryRespDTO">
        select sa.ticket_no, sa.publish_time, sai.apply_no, sai.supplier_code, sai.supplier_name, sai.supplier_grade_before, sai.supplier_grade_after, sa.apply_remark
        from supplier_grade_adjust_apply sa
        inner join supplier_grade_adjust_apply_item sai
        on sa.apply_no = sai.apply_no
        and sa.apply_status = 'agree'
        and sa.is_delete = 0 and sai.is_delete = 0
        ${ew.customSqlSegment}
        order by sa.publish_time desc
    </select>

    <select id="queryExportHistoryList" resultType="com.weifu.srm.supplier.response.SupplierGradeHistoryRespDTO">
        select sa.ticket_no, sa.publish_time, sai.supplier_code, sai.supplier_name, sai.supplier_grade_before, sai.supplier_grade_after, sa.apply_remark
        from supplier_grade_adjust_apply sa
        inner join supplier_grade_adjust_apply_item sai
        on sa.apply_no = sai.apply_no
        and sa.apply_status = 'agree'
        and sa.is_delete = 0 and sai.is_delete = 0
        ${ew.customSqlSegment}
    </select>

    <select id="getAllHistoryGrade" resultType="com.weifu.srm.supplier.response.SupplierGetAllHistoryGradeRespDTO">
        select sai.supplier_code, sai.supplier_name, sai.supplier_grade_after, sa.publish_time
        from supplier_grade_adjust_apply sa
        inner join supplier_grade_adjust_apply_item sai
        on sa.apply_no = sai.apply_no
        and sa.apply_status = 'agree'
        and sa.is_delete = 0 and sai.is_delete = 0
        and sai.supplier_code = #{supplierCode}
        order by sa.publish_time desc
    </select>

</mapper>