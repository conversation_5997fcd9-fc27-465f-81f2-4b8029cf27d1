<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierRecoverRecordMapper">

    <select id="queryListPage" resultType="com.weifu.srm.supplier.response.recover.SupplierRecoverRecordListRespDTO">
        select
        sr.supplier_code supplierCode,
        sb.supplier_name  supplierName,
        sr.apply_no applyNo,
        sr.ticket_no ticketNo,
        sr.create_time createTime,
        sr.create_name createName,
        sr.status
        from
        supplier_recover_record sr
        left join supplier_basic_info sb on
        sr.supplier_code = sb.sap_supplier_code
        where sr.is_delete = 0
        and sb.is_delete = 0
        <if test="model.supplierCode != null and model.supplierCode != ''">
            and sb.sap_supplier_code like concat("%", #{model.supplierCode}, "%")
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sb.supplier_name like concat("%", #{model.supplierName}, "%")
        </if>
        <if test="model.ticketNo != null and model.ticketNo != ''">
            and sr.ticket_no like concat("%", #{model.ticketNo}, "%")
        </if>
        <if test="model.createId != null ">
            and sr.create_by = #{model.createId}
        </if>
        <if test="model.status != null and model.status.size() > 0">
            and sr.status in
            <foreach collection="model.status" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.startCreateTime != null">
            <![CDATA[
                AND sr.create_time >= #{model.startCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endCreateTime != null">
            <![CDATA[
                AND sr.create_time <= #{model.endCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="dataPermission != null and dataPermission.type =='SOME'">
            and sr.create_by = #{model.userId}
        </if>
        order by sr.create_time desc, sr.id desc
    </select>


</mapper>