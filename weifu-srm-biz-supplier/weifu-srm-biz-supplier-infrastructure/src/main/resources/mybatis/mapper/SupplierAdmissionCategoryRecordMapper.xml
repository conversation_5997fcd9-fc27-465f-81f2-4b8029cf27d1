<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierAdmissionCategoryRecordMapper">
    <select id="searchBySupplierNameAndCategories"
            resultType="com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordSearchPO">
        select
            re.invitation_no,
            re.category_code,
            re.category_name,
            re.expiration_date,
            inv.supplier_admission_invitation_status,
            inv.admission_type
        from supplier_admission_category_record AS re
        join supplier_admission_invitation_record AS inv on inv.invitation_no = re.invitation_no
        where inv.is_delete = 0
        and
        re.is_delete = 0
        and
        inv.supplier_name = #{supplierName}
        and
        inv.supplier_admission_invitation_status not in ('DRAFT')
        <if test="categories != null and categories.size() > 0">
            and re.category_code in
            <foreach collection="categories" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="searchBySupplierNameAndAdmissionType"
            resultType="com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordSearchPO">
        select
            re.invitation_no,
            re.admission_no,
            re.admission_status,
            re.category_code,
            re.category_name,
            re.expiration_date,
            inv.supplier_admission_invitation_status,
            inv.admission_type
        from supplier_admission_category_record AS re
        join supplier_admission_invitation_record AS inv on inv.invitation_no = re.invitation_no
        where inv.is_delete = 0  and re.is_delete = 0 and inv.supplier_name = #{supplierName}
    </select>

    <select id="getInvitation" resultType="com.weifu.srm.supplier.request.AdmissionAndCategoryRespDTO">
        select tb2.invitation_no ,tb2.supplier_admission_invitation_status,tb2.credit_code ,tb2.supplier_name ,
        tb1.admission_no,tb1.category_code
        from supplier_admission_category_record tb1
        left join supplier_admission_invitation_record tb2 on tb1.invitation_no =tb2.invitation_no
        where tb1.is_delete =0 and tb2.is_delete =0 and tb2.supplier_admission_invitation_status ='APPROVED'
        and tb2.domestic_foreign_relationship
        AND tb1.category_code IN
        <foreach collection="param.categoryCodes" item="item1" open="(" separator="," close=")">
            #{item1}
        </foreach>
        <if test="param.domesticForeignRelationship==0">
            and tb2.credit_code = #{param.creditCode}
        </if>
        <if test="param.domesticForeignRelationship==1">
            and tb2.supplier_name = #{param.supplierName}
        </if>
    </select>
</mapper>