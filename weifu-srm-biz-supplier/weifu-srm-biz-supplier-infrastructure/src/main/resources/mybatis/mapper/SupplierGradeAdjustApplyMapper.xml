<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierGradeAdjustApplyMapper">
    
    <select id="querySupplierGradeExamineCode" resultType="String">
        select sai.supplier_code
        from supplier_grade_adjust_apply sa
        inner join supplier_grade_adjust_apply_item sai
        on sa.apply_no = sai.apply_no
        where sa.apply_status = "invitation_audit"
        and sa.is_delete = 0
        and sai.is_delete = 0
    </select>
    

</mapper>