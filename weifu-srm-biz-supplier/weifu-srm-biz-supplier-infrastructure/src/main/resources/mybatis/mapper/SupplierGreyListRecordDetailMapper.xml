<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierGreyListRecordDetailMapper">

    <select id="searchGreyListRecordDetail" resultType="com.weifu.srm.supplier.repository.po.SupplierGreyListRecordDetailPO">
        select
        *
        from
        supplier_grey_list_record_detail sr
        WHERE
        is_delete = 0
        <if test="list != null and list.size() > 0">
            and (sr.supplier_code, sr.division_id, sr.level2_category_code) in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                (#{item.supplierCode},#{item.divisionId},#{item.level2CategoryCode})
            </foreach>
        </if>
        and sr.status in ('LIMIT_AUDIT','AUDITED_WAITING','LIMITED','LIMITED_CANCELING')
    </select>

    <select id="queryList" resultType="com.weifu.srm.supplier.response.grey.SupplierGreyListApplyListRespDTO">
        select
        sg.id limitId,
        sg.limit_no limitNo,
        sb.id supplierBasicInfoId,
        sb.sap_supplier_code supplierCode,
        sb.supplier_name supplierName ,
        sb.status supplierStatus,
        sg.division_id divisionId,
        sg.division_name divisionName,
        sg.level2_category_code level2CategoryCode ,
        sg.status ,
        sg.valid_no validNo,
        sg.valid_ticket_no validTicketNo,
        sg.valid_time validTime,
        sg.invalid_no invalidNo,
        sg.invalid_ticket_no invalidTicketNo,
        sg.invalid_time invalidTime,
        sg.create_time createTime
        from
        supplier_grey_list_record_detail as sg
        left join supplier_basic_info as sb on
        sg.supplier_code = sb.sap_supplier_code
        where
        sg.is_delete = 0
        and sb.is_delete = 0
        <if test="model.supplierCodes != null and model.supplierCodes.size() > 0">
            and sb.sap_supplier_code in
            <foreach collection="model.supplierCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sb.supplier_name like concat("%", #{model.supplierName}, "%")
        </if>
        <if test="model.supplierStatus != null and model.supplierStatus.size() > 0">
            and sb.status in
            <foreach collection="model.supplierStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.status != null and model.status.size() > 0">
            and sg.status in
            <foreach collection="model.status" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.limitNo != null and model.limitNo != ''">
            and sg.limit_no = #{model.limitNo}
        </if>
        <if test="model.validTicketNo != null and model.validTicketNo != ''">
            and sg.valid_ticket_no like concat("%", #{model.validTicketNo}, "%")
        </if>
        <if test="model.invalidTicketNo != null and model.invalidTicketNo != ''">
            and sg.invalid_ticket_no like concat("%", #{model.invalidTicketNo}, "%")
        </if>
        <if test="model.divisionIds != null and model.divisionIds.size() > 0">
            and sg.division_id in
            <foreach collection="model.divisionIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.level2CategoryCodes != null and model.level2CategoryCodes.size() > 0">
            and sg.level2_category_code in
            <foreach collection="model.level2CategoryCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.startCreateTime != null">
            <![CDATA[
                AND sg.create_time >= #{model.startCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endCreateTime != null">
            <![CDATA[
                AND sg.create_time <= #{model.endCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startValidTime != null">
            <![CDATA[
                AND sg.valid_time >= #{model.startValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endValidTime != null">
            <![CDATA[
                AND sg.valid_time <= #{model.endValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startInvalidTime != null">
            <![CDATA[
                AND sg.invalid_time >= #{model.startInvalidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endInvalidTime != null">
            <![CDATA[
                AND sg.invalid_time <= #{model.endInvalidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        order by sg.create_time desc, sg.id desc
    </select>


</mapper>