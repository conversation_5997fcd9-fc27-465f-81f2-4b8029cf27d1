<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierCategoryRelationshipMapper">

    <delete id="removeBySSCAndCC">
        delete from supplier_category_relationship
        where sap_supplier_code = #{sapSupplierCode}
        <if test="categoryCodes != null and categoryCodes.size() > 0">
            and category_code in (
                <foreach collection="categoryCodes" item="item" separator=",">
                    #{item}
                </foreach>
            )
        </if>
    </delete>

    <delete id="hardRemoveByIds">
        delete from supplier_category_relationship
        where id in (
            <foreach collection="ids" item="id" separator=",">${id}</foreach>
        )
    </delete>

    <select id="listSupplierWithCategory" resultType="com.weifu.srm.supplier.response.ListSupplierOnAssociatedCategoryRespDTO">
        select sbi.id as supplierId, sbi.sap_supplier_code as sapSupplierCode, sbi.supplier_name as supplierName
            , sbi.supplier_name_en as supplierNameEn
        from supplier_basic_info as sbi
        where sbi.sap_supplier_code is not null and exists (
            select 1 from supplier_category_relationship as scr
            where scr.sap_supplier_code = sbi.sap_supplier_code and scr.status in ('NORMAL', 'WAIT_NORMAL')
            and scr.category_code in (
                <foreach collection="categoryCodes" item="categoryCode" separator=",">
                    #{categoryCode}
                </foreach>
            )
        )
        <if test="searchCriteria != null and searchCriteria != ''">
            and (
                sbi.sap_supplier_code like concat('%', #{searchCriteria}, '%')
                or sbi.supplier_name like concat('%', #{searchCriteria}, '%')
                or sbi.supplier_short_name like concat('%', #{searchCriteria}, '%')
                or sbi.supplier_name_en like concat('%', #{searchCriteria}, '%')
            )
        </if>
    </select>

    <select id="page" resultType="com.weifu.srm.supplier.response.SupplierCategoryRelPageRespDTO">
        <include refid="querySupplierCategoryRelationship"></include>
    </select>

    <select id="listForExport" resultType="com.weifu.srm.supplier.response.SupplierCategoryRelPageRespDTO">
        <include refid="querySupplierCategoryRelationship"></include>
        limit #{pageSize} offset #{offset}
    </select>
    <select id="allBySupplierCode" resultType="com.weifu.srm.supplier.response.SupplierCategoryRelPageRespDTO">
        <include refid="querySupplierCategoryRelationship"></include>
    </select>

    <sql id="querySupplierCategoryRelationship">
        select sbi.sap_supplier_code, sbi.supplier_name, sbi.supplier_name_en, sbi.supplier_short_name,
            scr.category_code as threeLevelCategoryCode, scr.supplier_category_status
        from supplier_category_relationship as scr
        inner join supplier_basic_info as sbi on scr.sap_supplier_code = sbi.sap_supplier_code
        where scr.is_delete = 0 and sbi.is_delete = 0 and scr.status in ('NORMAL', 'WAIT_NORMAL')
        <if test="query.sapSupplierCode != null and query.sapSupplierCode != ''">
            and sbi.sap_supplier_code like concat("%", #{query.sapSupplierCode}, "%")
        </if>
        <if test="query.supplierName != null and query.supplierName != ''">
            and (sbi.supplier_name like concat("%", #{query.supplierName}, "%")
                or sbi.supplier_short_name like concat("%", #{query.supplierName}, "%")
            )
        </if>
        <if test="query.supplierStatus != null and query.supplierStatus.size > 0">
            and sbi.status in (
                <foreach collection="query.supplierStatus" item="status" separator=",">
                    #{status}
                </foreach>
            )
        </if>
        <if test="query.supplierCategoryStatus != null and query.supplierCategoryStatus.size > 0">
            and scr.supplier_category_status in (
                <foreach collection="query.supplierCategoryStatus" item="categoryStatus" separator=",">
                    #{categoryStatus}
                </foreach>
            )
        </if>
        <if test="query.categoryCodes != null and query.categoryCodes.size > 0">
            and scr.category_code in (
                <foreach collection="query.categoryCodes" item="categoryCode" separator=",">
                    #{categoryCode}
                </foreach>
            )
        </if>
        and (
            1 = 0
            <foreach collection="keys" item="dataPermission">
                <if test="dataPermission.key == 'ALL'">
                    or 1 = 1
                </if>
                <if test="dataPermission.key == 'MANAGED_CATEGORY'">
                    or scr.category_code in (
                        <foreach collection="dataPermission.categoryCodes" item="categoryCode" separator=",">
                            #{categoryCode}
                        </foreach>
                    )
                </if>
            </foreach>
        )
        order by scr.sap_supplier_code asc
    </sql>

    <select id="list" resultType="com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO">
        select id, sap_supplier_code, category_code, supplier_category_status, qualification_effective_time,
            validity_start_time, validity_end_time, mast_purchase_amt, annual_purchase_cnt, annual_purchase_amt
        from supplier_category_relationship
        where status in ('NORMAL', 'WAIT_NORMAL') and (sap_supplier_code, category_code) in (
            <foreach collection="relationships" item="relationship" separator=",">
                (#{relationship.sapSupplierCode}, #{relationship.categoryCode})
            </foreach>
        )
    </select>
</mapper>