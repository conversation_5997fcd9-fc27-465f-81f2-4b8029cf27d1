<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierCategoryAdjustApplyItemMapper">

    <select id="listOnApproving" resultType="com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyItemPO">
        select sap_supplier_code, three_level_category_code
        from supplier_category_adjust_apply_item as scaai
        where exists (
            select 1 from supplier_category_adjust_apply as scaa
            where scaai.apply_no = scaa.apply_no and scaa.apply_status = 'APPROVING'
        ) and (sap_supplier_code, three_level_category_code) in (
            <foreach collection="relationships" item="relationship" separator=",">
                (#{relationship.sapSupplierCode}, #{relationship.threeLevelCategoryCode})
            </foreach>
        )
    </select>

</mapper>