<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierCategoryBatchAdjustApplyOldItemMapper">

    <select id="listOnApproving" resultType="com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyOldItemPO">
        select sap_supplier_code, category_code
        from supplier_category_batch_adjust_apply_old_item as scbaaoi
        where exists (
            select 1 from supplier_category_batch_adjust_apply as scbaa
            where scbaaoi.apply_no = scbaa.apply_no and scbaa.apply_status = 'APPROVING'
        ) and (sap_supplier_code, category_code) in (
            <foreach collection="relationships" item="relationship" separator=",">
                (#{relationship.sapSupplierCode}, #{relationship.categoryCode})
            </foreach>
        )
    </select>

</mapper>