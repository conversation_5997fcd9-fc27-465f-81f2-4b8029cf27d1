<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.weifu.srm.supplier.repository.mapper.SupplierBlackListRecordDetailMapper">

    <select id="queryListPage" resultType="com.weifu.srm.supplier.response.black.SupplierBlackListApplyListRespDTO">
        select
        sl.id limitId,
        sl.limit_no limitNo,
        sl.supplier_code supplierCode ,
        sb.supplier_name supplierName ,
        sb.status supplierStatus,
        sl.status ,
        sl.valid_no validNo,
        sl.valid_ticket_no validTicketNo,
        sl.create_time createTime,
        sl.valid_time validTime,
        sl.out_time outTime,
        sl.invalid_no invalidNo,
        sl.invalid_ticket_no invalidTicketNo,
        sl.invalid_time invalidTime
        from
        supplier_black_list_record_detail as sl
        left join supplier_basic_info sb on
        sl.supplier_code = sb.sap_supplier_code
        where sl.is_delete = 0
        and sb.is_delete = 0
        <if test="model.supplierCodes != null and model.supplierCodes.size() > 0">
            and sb.sap_supplier_code in
            <foreach collection="model.supplierCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.supplierName != null and model.supplierName != ''">
            and sb.supplier_name like concat("%", #{model.supplierName}, "%")
        </if>
        <if test="model.supplierStatus != null and model.supplierStatus.size() > 0">
            and sb.status in
            <foreach collection="model.supplierStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.status != null and model.status.size() > 0">
            and sl.status in
            <foreach collection="model.status" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="model.limitNo != null and model.limitNo != ''">
            and sl.limit_no = #{model.limitNo}
        </if>
        <if test="model.validTicketNo != null and model.validTicketNo != ''">
            and sl.valid_ticket_no like concat("%", #{model.validTicketNo}, "%")
        </if>
        <if test="model.invalidTicketNo != null and model.invalidTicketNo != ''">
            and sl.invalid_ticket_no like concat("%", #{model.invalidTicketNo}, "%")
        </if>
        <if test="model.startCreateTime != null">
            <![CDATA[
                AND sl.create_time >= #{model.startCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endCreateTime != null">
            <![CDATA[
                AND sl.create_time <= #{model.endCreateTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startValidTime != null">
            <![CDATA[
                AND sl.valid_time >= #{model.startValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endValidTime != null">
            <![CDATA[
                AND sl.valid_time <= #{model.endValidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startInvalidTime != null">
            <![CDATA[
                AND sl.invalid_time >= #{model.startInvalidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endInvalidTime != null">
            <![CDATA[
                AND sl.invalid_time <= #{model.endInvalidTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.startOutTime != null">
            <![CDATA[
                AND sl.out_time >= #{model.startOutTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        <if test="model.endOutTime != null">
            <![CDATA[
                AND sl.out_time <= #{model.endOutTime, jdbcType=TIMESTAMP}
                ]]>
        </if>
        order by sl.create_time desc, sl.id desc
    </select>


</mapper>