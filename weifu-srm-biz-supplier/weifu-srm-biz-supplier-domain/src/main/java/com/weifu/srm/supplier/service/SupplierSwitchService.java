package com.weifu.srm.supplier.service;

import com.weifu.srm.supplier.request.SwithSupplierReqDTO;
import com.weifu.srm.supplier.response.SeletedSupplierRespDTO;

/**
 * 管理供应商用户当前选中的供应商
 */
public interface SupplierSwitchService {

    /**
     * 切换供应商用户当前选中的供应商
     */
    void swithSupplier(SwithSupplierReqDTO req);

    /**
     * 获取供应商用户当前选中的供应商
     */
    SeletedSupplierRespDTO getSeletedSupplier(Long userId);

}
