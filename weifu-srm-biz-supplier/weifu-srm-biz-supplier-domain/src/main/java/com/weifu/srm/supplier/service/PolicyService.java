package com.weifu.srm.supplier.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.enums.policy.ImportStatusEnum;
import com.weifu.srm.supplier.repository.po.BusinessPolicyApplyMainPO;
import com.weifu.srm.supplier.repository.po.BusinessPolicyListPO;
import com.weifu.srm.supplier.request.policy.*;
import com.weifu.srm.supplier.response.policy.PolicyAdjustDetailRespDTO;
import com.weifu.srm.supplier.response.policy.PolicyAdjustMainRespDTO;
import com.weifu.srm.supplier.response.policy.PolicyListExportRespDTO;
import com.weifu.srm.supplier.request.policy.*;
import com.weifu.srm.supplier.response.policy.PolicyListRespDTO;

import java.util.Date;
import java.util.List;

public interface PolicyService {

    /**
     *商务政策管理列表
     * 从申请明细中查询
     */
    PageResponse<PolicyListRespDTO> policyList(QueryPolicyListReqDTO param);

    /**
     *商务政策 草稿审批拒绝 数据删除
     */
    Boolean removePolicy(List<Long> ids);

    /**
     *商务政策导出数据查询
     */
    List<PolicyListExportRespDTO> policyListExport(QueryPolicyListReqDTO param);

    /**
     *关键字查询供应商或事业部或业务小类-从商务政策中查询
     */
    List<NameValueSimpleRespDTO> queryConditionByKeyword(QueryByKeywordReqDTO param);


    /**
     *根据ID查询商务政策列表
     */
    List<PolicyListRespDTO> listDetailByIds(List<Long> ids);

    /**
     *导入FSSC
     */
    Boolean importFSSC(PolicyDetailSimpleReqDTO param);

    /**
     *批量导入
     */
    List<PolicyAdjustDetailRespDTO> batchImport(List<PolicyListImportReqDTO> param);

    /**
     *导入FSSC
     */
    Boolean importPolicyToFSSC(BusinessPolicyListPO v, Date updateTime,String submitDomain);

    /**
     * 保存 新建商务政策明细
     */
    Boolean savePolicyByNew(PolicyAdjustMainReqDTO param);

    /**
     * 保存 编辑商务政策明细
     */
    Boolean savePolicyByEdit(PolicyAdjustMainReqDTO param);

    /**
     *提交商务政策
     */
    String submitPolicy(PolicyAdjustMainReqDTO param);

    PolicyAdjustMainRespDTO getApplyDetail(String applyNo);

    BusinessPolicyApplyMainPO getApply(String applyNo);

    void updateApply(BusinessPolicyApplyMainPO param);

    void updatePolicyList(List<BusinessPolicyListPO> list);

    List<BusinessPolicyListPO> listByIds(List<Long> ids);

    String getSubmitDomain(Long userId);

    void changePolicyListImportStatus(List<Long> ids, ImportStatusEnum importStatus);

    void sendImportFailMsg(BusinessPolicyListPO policy,String businessNo,Long userId,String username);

    PageResponse<PolicyAdjustDetailRespDTO> policyApplyDetailList(QueryPolicyApplyReqDTO param);
    List<PolicyAdjustDetailRespDTO> listApplyDetailByIds(PolicyDetailSimpleReqDTO param);

    void savePolicyList(List<BusinessPolicyListPO> policyList);
}
