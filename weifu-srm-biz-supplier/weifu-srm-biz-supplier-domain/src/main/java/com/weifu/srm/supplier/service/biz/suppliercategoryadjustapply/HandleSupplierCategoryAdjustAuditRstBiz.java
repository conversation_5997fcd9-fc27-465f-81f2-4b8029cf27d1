package com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.dto.SupplierCategoryRelationshipSaveDTO;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyTypeEnum;
import com.weifu.srm.supplier.manager.SupplierCategoryRelationshipManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationShipChangeTypeEnum;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationshipSourceEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理供应商品类调整申请审核结果
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HandleSupplierCategoryAdjustAuditRstBiz {

    /** 品类调整通知模版 */
    private final static String CATEGORY_ADJUST_NOTICE_TEMPLATE = "由${工单申请人}于${申请时间}发起的对${供应商名称}（供应商编码：${供应商编码} ）${调整类型}：${品类} 已经生效！";

    private final SupplierCategoryAdjustApplyMapperService supplierCategoryAdjustApplyMapperService;
    private final SupplierCategoryAdjustApplyItemMapperService supplierCategoryAdjustApplyItemMapperService;
    private final SupplierCategoryRelationshipManager supplierCategoryRelationshipManager;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final CategoryManager categoryManager;
    private final TransactionTemplate transactionTemplate;
    private final MqManager mqManager;

    public void execute(TicketStatusChangedMQ req) {
        if (!StringUtils.equalsAny(req.getTicketType(),
                TicketTypeEnum.SUPPLIER_CATEGORY_QUALIFICATION_CHANGE.getCode(),
                TicketTypeEnum.SUPPLIER_CATEGORY_DELETE.getCode())) {
            return;
        }

        String applyNo = req.getBusinessNo();
        log.info("处理供应商品类调整申请审核结果开始，applyNo={}，mq={}", applyNo, JacksonUtil.bean2Json(req));

        // 当前状态不是审核中，则不处理
        SupplierCategoryAdjustApplyPO apply = supplierCategoryAdjustApplyMapperService.getByApplyNo(applyNo);
        if (!SupplierCategoryAdjustApplyStatusEnum.APPROVING.equalsCode(apply.getApplyStatus())) {
            log.warn("放弃处理供应商品类调整申请审核结果，当前状态不是审核中，applyNo={}", applyNo);
            return;
        }

        if (TicketStatusEnum.APPROVING.equalsCode(req.getStatus())) {
            writeBackTicketNo(req); // 回写工单号
        } else if (TicketStatusEnum.APPROVED.equalsCode(req.getStatus())) {
            updateStatusToApproved(req, apply); // 更新状态为“审核通过”
        } else if (TicketStatusEnum.REJECTED.equalsCode(req.getStatus())) {
            updateStatusToRejected(req); // 更新状态为“审核拒绝”
        } else if (TicketStatusEnum.CANCELED.equalsCode(req.getStatus())) {
            updateStatusToWithdrawn(req); // 更新状态为“已撤回”
        }
        log.info("处理供应商品类调整申请审核结果完成，applyNo={}", applyNo);
    }

    /**
     * 回写工单号
     */
    private void writeBackTicketNo(TicketStatusChangedMQ req) {
        String applyNo = req.getBusinessNo();
        SupplierCategoryAdjustApplyPO apply = supplierCategoryAdjustApplyMapperService.getByApplyNo(applyNo);
        if (StringUtils.isNotEmpty(apply.getTicketNo())) {
            return;
        }

        apply.setTicketNo(req.getTicketNo());
        BaseEntityUtil.setCommonForU(apply, req.getOperateBy(), req.getOperateName(), new Date());
        supplierCategoryAdjustApplyMapperService.updateById(apply);
        log.info("供应商品类调整申请回写工单号完成，applyNo={}，ticketNo={}", applyNo, req.getTicketNo());
    }

    /**
     * 更新状态为“审核通过”
     */
    private void updateStatusToApproved(TicketStatusChangedMQ mq, SupplierCategoryAdjustApplyPO apply) {
        List<SupplierCategoryAdjustApplyItemPO> applyItems = supplierCategoryAdjustApplyItemMapperService.lambdaQuery()
                .eq(SupplierCategoryAdjustApplyItemPO::getApplyNo, mq.getBusinessNo()).list();

        // 构建站内信
        List<CreateSiteMessageMQ> siteMessages = buildSiteMessage(apply, applyItems);

        Date current = new Date();
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                // 前置状态为审核中
                supplierCategoryAdjustApplyMapperService.lambdaUpdate()
                        .set(SupplierCategoryAdjustApplyPO::getApplyStatus, SupplierCategoryAdjustApplyStatusEnum.APPROVED.getCode())
                        .set(SupplierCategoryAdjustApplyPO::getUpdateBy, mq.getOperateBy())
                        .set(SupplierCategoryAdjustApplyPO::getUpdateName, mq.getOperateName())
                        .set(SupplierCategoryAdjustApplyPO::getUpdateTime, current)
                        .eq(SupplierCategoryAdjustApplyPO::getId, apply.getId())
                        .update();

                SupplierCategoryRelationshipSourceEnum sourceEm = toSource(apply.getApplyType());

                // 通过三级品类扩充出来的关系，都默认给“合格供应商”的品类资质
                SupplierCategoryRelationshipSaveDTO req = new SupplierCategoryRelationshipSaveDTO();
                req.setChangeType(calcChangeType(apply));
                req.setSapSupplierCode(apply.getSapSupplierCode());
                req.setSupplierCategoryStatus(SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode());
                req.setBusinessNo(apply.getApplyNo());
                req.setCategoryCodes(applyItems.stream().map(SupplierCategoryAdjustApplyItemPO::getThreeLevelCategoryCode).collect(Collectors.toList()));
                req.setOperationUserId(apply.getApplyBy());
                req.setOperationName(apply.getApplyName());
                req.setSource(sourceEm.getCode());
                req.setRemark(sourceEm.getChineseName());
                supplierCategoryRelationshipManager.save(req);

                // 发送站内信
                if (CollectionUtils.isNotEmpty(siteMessages)) {
                    mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(siteMessages));
                }
            }
        });
    }

    /**
     * 申请类型转关系来源枚举
     */
    private SupplierCategoryRelationshipSourceEnum toSource(String applyType) {
        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_ADD.equalsCode(applyType)) {
            return SupplierCategoryRelationshipSourceEnum.SCA_THREE_LEVEL_CATEGORY_ADD;
        } else if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_DEL.equalsCode(applyType)) {
            return SupplierCategoryRelationshipSourceEnum.SCA_THREE_LEVEL_CATEGORY_DEL;
        } else if (SupplierCategoryAdjustApplyTypeEnum.HIGH_LEVEL_CATEGORY_DEL.equalsCode(applyType)) {
            return SupplierCategoryRelationshipSourceEnum.SCA_HIGH_LEVEL_CATEGORY_DEL;
        }
        throw new BizFailException("不支持的申请类型");
    }

    /**
     * 构建站内信消息
     */
    private List<CreateSiteMessageMQ> buildSiteMessage(SupplierCategoryAdjustApplyPO apply, List<SupplierCategoryAdjustApplyItemPO> applyItems) {
        List<String> categoryCodes = applyItems.stream().map(SupplierCategoryAdjustApplyItemPO::getThreeLevelCategoryCode).collect(Collectors.toList());
        List<CategoryEngineerResultDTO> engineers = categoryManager.queryCategoryEngineerByCategoryCodes(categoryCodes);
        SupplierBasicInfoPO supplier = supplierBasicInfoMapperService.getBySapSupplierCode(apply.getSapSupplierCode());

        String content = CATEGORY_ADJUST_NOTICE_TEMPLATE;
        content = content.replace("${工单申请人}", apply.getApplyName());
        content = content.replace("${申请时间}", DateUtil.format(apply.getApplyTime(), DatePattern.NORM_DATETIME_PATTERN));
        content = content.replace("${供应商名称}", supplier.getSupplierName());
        content = content.replace("${供应商编码}", supplier.getSapSupplierCode());
        content = content.replace("${调整类型}", SupplierCategoryAdjustApplyTypeEnum.getName(apply.getApplyType(), null));
        content = content.replace("${品类}", getAdjustCategoryName(applyItems));

        String businessType = null;
        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_ADD.equalsCode(apply.getApplyType())
                || SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_DEL.equalsCode(apply.getApplyType())) {
            businessType = MessageClsEnum.SUPPLIER_TERTIARY_CATEGORY_ADJUST_EFFECTIVE.getCode();
        } else {
            businessType = MessageClsEnum.SUPPLIER_CATEGORY_QUALIFICATION_DELETE_EFFECTIVE.getCode();
        }

        Set<Long> existUsers = new HashSet<>();
        List<CreateSiteMessageMQ> messages = new ArrayList<>();
        for (CategoryEngineerResultDTO engineer:engineers) {
            // 判断是否已在收信人列表里
            if (existUsers.contains(engineer.getUserId())) {
                continue;
            }

            if (StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.CPE.getCode())
                    || StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.CPE_MASTER.getCode())
                    || StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.SQE.getCode())
                    || StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.SQE_MASTER.getCode())
                    || StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode())
                    || StringUtils.equals(engineer.getRoleId(), CategoryRoleEnum.AUTHORIZED_PEM_DIRECTOR.getCode())) {
                CreateSiteMessageMQ message = new CreateSiteMessageMQ();
                message.setMessageType(MessageTypeEnum.PRIVATE.getCode());
                message.setIconType(IconTypeEnum.GENERAL.getCode());
                message.setBusinessType(businessType);
                message.setBusinessNo(apply.getApplyNo());
                message.setTitle(MessageClsEnum.SUPPLIER_TERTIARY_CATEGORY_ADJUST_EFFECTIVE.getDesc());
                message.setContent(content);
                message.setUserId(engineer.getUserId());
                message.setUserName(engineer.getUserName());
                messages.add(message);

                existUsers.add(engineer.getUserId());
            }
        }
        return messages;
    }

    /**
     * 生成被调整品类的名称
     */
    private String getAdjustCategoryName(List<SupplierCategoryAdjustApplyItemPO> applyItems) {
        List<String> categoryNames = new ArrayList<>();
        for (SupplierCategoryAdjustApplyItemPO applyItem:applyItems) {
            categoryNames.add(applyItem.getThreeLevelCategoryName());
        }
        return String.join("，", categoryNames);
    }

    private String calcChangeType(SupplierCategoryAdjustApplyPO apply) {
        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_ADD.equalsCode(apply.getApplyType())) {
            return SupplierCategoryRelationShipChangeTypeEnum.ADD.getCode();
        }
        return SupplierCategoryRelationShipChangeTypeEnum.REMOVE.getCode();
    }

    /**
     * 更新状态为“审核拒绝”
     */
    private void updateStatusToRejected(TicketStatusChangedMQ req) {
        // 前置状态为审核中
        supplierCategoryAdjustApplyMapperService.lambdaUpdate()
                .set(SupplierCategoryAdjustApplyPO::getApplyStatus, SupplierCategoryAdjustApplyStatusEnum.REJECTED.getCode())
                .set(SupplierCategoryAdjustApplyPO::getUpdateBy, req.getOperateBy())
                .set(SupplierCategoryAdjustApplyPO::getUpdateName, req.getOperateName())
                .set(SupplierCategoryAdjustApplyPO::getUpdateTime, new Date())
                .eq(SupplierCategoryAdjustApplyPO::getApplyNo, req.getBusinessNo())
                .eq(SupplierCategoryAdjustApplyPO::getApplyStatus, SupplierCategoryAdjustApplyStatusEnum.APPROVING.getCode())
                .update();
    }

    /**
     * 更新状态为“已撤回”
     */
    private void updateStatusToWithdrawn(TicketStatusChangedMQ req) {
        // 前置状态为审核中
        supplierCategoryAdjustApplyMapperService.lambdaUpdate()
                .set(SupplierCategoryAdjustApplyPO::getApplyStatus, SupplierCategoryAdjustApplyStatusEnum.WITHDRAWN.getCode())
                .set(SupplierCategoryAdjustApplyPO::getUpdateBy, req.getOperateBy())
                .set(SupplierCategoryAdjustApplyPO::getUpdateName, req.getOperateName())
                .set(SupplierCategoryAdjustApplyPO::getUpdateTime, new Date())
                .eq(SupplierCategoryAdjustApplyPO::getApplyNo, req.getBusinessNo())
                .eq(SupplierCategoryAdjustApplyPO::getApplyStatus, SupplierCategoryAdjustApplyStatusEnum.APPROVING.getCode())
                .update();
    }

}
