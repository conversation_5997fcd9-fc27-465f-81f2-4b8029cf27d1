package com.weifu.srm.supplier.service.biz;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.integration.api.CMSApi;
import com.weifu.srm.integration.enums.ContractCreateForEnum;
import com.weifu.srm.integration.request.cms.QueryCmsContractReqDTO;
import com.weifu.srm.integration.request.cms.SupplierItemReqDTO;
import com.weifu.srm.integration.response.cms.CmsContractInfoRespDTO;
import com.weifu.srm.integration.response.cms.ContractItemRespDTO;
import com.weifu.srm.supplier.manager.remote.intergration.CMSManager;
import com.weifu.srm.supplier.request.SupplierGetAllHistoryGradeReqDTO;
import com.weifu.srm.supplier.response.SupplierGetContractDetailRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGetContractDetailBiz {

    private final CMSManager cmsManager;
    private final CMSApi cmsApi;

    private static final String HONEST = "7";
    private static final String SECURITY = "6";
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    public SupplierGetContractDetailRespDTO getContractDetail(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        // 判断供应商是否存在签署中的协议数据 (保密和廉洁)
        QueryCmsContractReqDTO reqSecurityDTO = new QueryCmsContractReqDTO();
        QueryCmsContractReqDTO reqHonestDTO = new QueryCmsContractReqDTO();
        SupplierItemReqDTO supplierItemReqDTO = new SupplierItemReqDTO();
        supplierItemReqDTO.setSupplierCode(reqDTO.getSupplierCode());
        reqSecurityDTO.setContractCreateFor(SECURITY);
        reqSecurityDTO.setSupplierItems(List.of(supplierItemReqDTO));
        reqHonestDTO.setContractCreateFor(HONEST);
        reqHonestDTO.setSupplierItems(List.of(supplierItemReqDTO));
        List<CmsContractInfoRespDTO> securityResp = cmsManager.getSupplierContractInfo(reqSecurityDTO);
        List<CmsContractInfoRespDTO> honestResp = cmsManager.getSupplierContractInfo(reqHonestDTO);
        SupplierGetContractDetailRespDTO result = new SupplierGetContractDetailRespDTO();
        boolean existHonest = CollectionUtils.isNotEmpty(honestResp) && CollectionUtils.isNotEmpty(honestResp.get(0).getContractItems());
        if (existHonest) {
            result.setHonestContractSign(YesOrNoEnum.YES.getCode());
            result.setHonestStartDate(DateUtil.dateFormat(honestResp.get(0).getContractItems().get(0).getValidityPeriodStart(), DATE_FORMAT));
            result.setHonestEndDate(DateUtil.dateFormat(honestResp.get(0).getContractItems().get(0).getValidityPeriodEnd(), DATE_FORMAT));
        } else {
            result.setHonestContractSign(YesOrNoEnum.NO.getCode());
        }
        boolean existSecrecy = CollectionUtils.isNotEmpty(securityResp) && CollectionUtils.isNotEmpty(securityResp.get(0).getContractItems());
        if (existSecrecy) {
            result.setSecrecyContractSign(YesOrNoEnum.YES.getCode());
        } else {
            result.setSecrecyContractSign(YesOrNoEnum.NO.getCode());
        }
        // 查询框架协议
        QueryCmsContractReqDTO paramDTO = new QueryCmsContractReqDTO();
        List<SupplierItemReqDTO> supplierItemReqDTOs = new ArrayList<>();
        supplierItemReqDTOs.add(supplierItemReqDTO);
        paramDTO.setSupplierItems(supplierItemReqDTOs);
        paramDTO.setContractCreateFor(ContractCreateForEnum.FRAMEWORK_AGREEMENT.getCode());
        ApiResponse<List<CmsContractInfoRespDTO>> response = cmsApi.queryCmsContractInfo(paramDTO);
        boolean frameworkExist = CollectionUtils.isNotEmpty(response.getData()) && CollectionUtils.isNotEmpty(response.getData().get(0).getContractItems());
        ArrayList<SupplierGetContractDetailRespDTO.FrameworkContractDetail> frameworkContractDetails = new ArrayList<>();
        if (frameworkExist) {
            for (ContractItemRespDTO item : response.getData().get(0).getContractItems()) {
                SupplierGetContractDetailRespDTO.FrameworkContractDetail detail = new SupplierGetContractDetailRespDTO.FrameworkContractDetail();
                detail.setContractNo(item.getContractNo());
                detail.setContractName(item.getContractName());
                detail.setStartDate(DateUtil.dateFormat(item.getValidityPeriodStart(), DATE_FORMAT));
                detail.setEndDate(DateUtil.dateFormat(item.getValidityPeriodEnd(), DATE_FORMAT));
                frameworkContractDetails.add(detail);
            }
        }
        result.setFrameworkContractDetailList(frameworkContractDetails);
        return result;
    }

}
