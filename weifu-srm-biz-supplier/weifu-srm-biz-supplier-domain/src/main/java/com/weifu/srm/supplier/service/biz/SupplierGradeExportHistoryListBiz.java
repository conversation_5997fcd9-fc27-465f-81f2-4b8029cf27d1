package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierGradeTypeEnum;
import com.weifu.srm.supplier.request.SupplierGradeHistoryReqDTO;
import com.weifu.srm.supplier.response.SupplierGradeHistoryRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGradeExportHistoryListBiz {

    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;

    public void exportHistoryList(HttpServletResponse response, SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO) {
        List<SupplierGradeHistoryRespDTO> records = supplierGradeAdjustApplyItemMapperService.queryExportHistoryList(supplierGradeHistoryReqDTO);
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        try {
            XSSFSheet sheet = workbook.createSheet("供应商分级历史记录");
            // 写入表头
            XSSFRow indexRow = sheet.createRow(0);
            indexRow.createCell(0).setCellValue("序号");
            indexRow.createCell(1).setCellValue("供应商编码");
            indexRow.createCell(2).setCellValue("供应商名称");
            indexRow.createCell(3).setCellValue("修改前分级");
            indexRow.createCell(4).setCellValue("供应商分级");
            indexRow.createCell(5).setCellValue("工单编号");
            indexRow.createCell(6).setCellValue("发布时间");
            indexRow.createCell(7).setCellValue("备注");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (CollectionUtils.isNotEmpty(records)) {
                int indexNum = 1;
                for (SupplierGradeHistoryRespDTO supplierGradeHistoryRespDTO: records) {
                    XSSFRow row = sheet.createRow(indexNum);
                    row.createCell(0).setCellValue(String.valueOf(indexNum));
                    row.createCell(1).setCellValue(supplierGradeHistoryRespDTO.getSupplierCode());
                    row.createCell(2).setCellValue(supplierGradeHistoryRespDTO.getSupplierName());
                    String supplierGradeBefore = SupplierGradeTypeEnum.getByCode(supplierGradeHistoryRespDTO.getSupplierGradeBefore()).getChineseName();
                    row.createCell(3).setCellValue(supplierGradeBefore);
                    String supplierGradeAfter = SupplierGradeTypeEnum.getByCode(supplierGradeHistoryRespDTO.getSupplierGradeAfter()).getChineseName();
                    row.createCell(4).setCellValue(supplierGradeAfter);
                    row.createCell(5).setCellValue(supplierGradeHistoryRespDTO.getTicketNo());
                    row.createCell(6).setCellValue(DateUtil.format(supplierGradeHistoryRespDTO.getPublishTime(), formatter));
                    row.createCell(7).setCellValue(supplierGradeHistoryRespDTO.getApplyRemark());
                    indexNum++;
                }
            }
            String downloadFileName = "SUPPLIER_GRADE_HISTORY_" + DateUtil.formatDate(new Date()) + ".xlsx";
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + downloadFileName);
            // 将Excel文件写入响应输出流
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            // 关闭资源
            IoUtil.closeIfPosible(workbook);
            IoUtil.closeIfPosible(outputStream);
        }
    }

}
