package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.exception.ParamErrorException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.constants.BizErrorCode;
import com.weifu.srm.supplier.convert.QuestionnaireTemplateConvert;
import com.weifu.srm.supplier.repository.atomicservice.ChangeLogsMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.SupplierBizEnum;
import com.weifu.srm.supplier.repository.enums.SupplierTypeEnum;
import com.weifu.srm.supplier.repository.po.ChangeLogsPO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldPO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplatePO;
import com.weifu.srm.supplier.request.QuestionnaireTemplateFieldDTO;
import com.weifu.srm.supplier.request.QuestionnaireTemplateSaveReqDTO;
import com.weifu.srm.supplier.util.ObjectCompareUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/08 14:38
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class QuestionnaireTemplateSaveBiz {

    private final QuestionnaireTemplateMapperService questionnaireTemplateMapperService;
    private final QuestionnaireTemplateFieldMapperService questionnaireTemplateFieldMapperService;
    private final QuestionnaireTemplateConvert questionnaireTemplateConvert;
    private final TransactionTemplate transactionTemplate;
    private final RedisUtil redisUtil;
    private final ChangeLogsMapperService changeLogsMapperService;
    private final IdService idService;
    public static final String TABLE_NAME = "questionnaire_template_field";

    private final LocaleMessage localeMessage;

    public String saveTemplate(QuestionnaireTemplateSaveReqDTO saveReqDTO){
         String templateCode = saveReqDTO.getTemplateCode();
         List<QuestionnaireTemplateFieldDTO> templateFields = saveReqDTO.getTemplateFields();
        //根据模板code查询数据库 若有值则更新数据，无则新增
        SupplierTypeEnum supplierTypeEnum = SupplierTypeEnum.getByCode(saveReqDTO.getSupplierType());
        if (supplierTypeEnum == null) {
            throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        Integer defaultTemplateCount = questionnaireTemplateMapperService.queryDefaultTemplateCount(saveReqDTO.getSupplierType(), templateCode);
        if (!YesOrNoEnum.NO.getCode().equals(defaultTemplateCount) && YesOrNoEnum.YES.getCode().equals(saveReqDTO.getIsDefault())) {
            throw new BizFailException(BizErrorCode.SAME_TYPE_DEFAULT_TEMPLATE_CAN_NOT_BE_MORE_THAN_ONE.getMsg());
        }
         QuestionnaireTemplatePO oriTemplatePO = questionnaireTemplateMapperService.lambdaQuery()
                .eq(QuestionnaireTemplatePO::getTemplateCode,templateCode)
                .eq(QuestionnaireTemplatePO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
         //查询该模板下的明细字段
         List<QuestionnaireTemplateFieldPO> oriTemplateFieldsPO = questionnaireTemplateFieldMapperService
                 .queryTemplateFieldListByCode(templateCode);
         //
        QuestionnaireTemplatePO saveTemplatePo = questionnaireTemplateConvert.toSavePo(saveReqDTO);
        String newTemplateCode = generateBusinessNo(idService,SupplierBizEnum.QUESTIONNAIRE_TEMPLATE_CODE.getBizTypeCodee());
        saveTemplatePo.setTemplateCode(newTemplateCode);
        saveTemplatePo.setUpdateBy(saveReqDTO.getOperationBy());
        saveTemplatePo.setCreateBy(saveReqDTO.getOperationBy());
        saveTemplatePo.setUpdateName(saveReqDTO.getOperationByName());
        saveTemplatePo.setCreateName(saveReqDTO.getOperationByName());
        List<QuestionnaireTemplateFieldPO> newFields = questionnaireTemplateConvert.toSaveFieldsPo(templateFields);
        List<ObjectCompareUtil.FieldChange> totalChangeList = getFieldChanges(oriTemplatePO,
                oriTemplateFieldsPO, newFields, saveTemplatePo);
        List<ChangeLogsPO> changeLogsPOS = getChangeLogs(totalChangeList, oriTemplatePO,saveTemplatePo);

        transactionTemplate.execute(transactionStatus -> {
             if (!ObjectUtil.isNull(oriTemplatePO)) {
                 newFields.forEach(fieldPO -> fieldPO.setUpdateTime(DateUtil.date()));
                 questionnaireTemplateMapperService.updateTemplate(oriTemplatePO,saveTemplatePo);
                 questionnaireTemplateFieldMapperService.deleteByTemplateCode(templateCode);
                 questionnaireTemplateFieldMapperService.saveBatch(newFields);
                 changeLogsMapperService.saveBatch(changeLogsPOS);
             } else {
                 List<QuestionnaireTemplateFieldPO> fieldPOS = questionnaireTemplateConvert
                         .toSaveFieldsPo(templateFields);
                 //依次给fieldPOS里面的每一个数据的templateCode赋值
                 fieldPOS.forEach(fieldPO -> {
                     fieldPO.setTemplateCode(saveTemplatePo.getTemplateCode());
                     fieldPO.setId(null);
                 } );
                 questionnaireTemplateMapperService.insertTemplate(saveTemplatePo);
                 questionnaireTemplateFieldMapperService.saveBatch(fieldPOS);
             }
             return null;
        });
        return saveTemplatePo.getTemplateCode();
     }

    private String generateBusinessNo(IdService idService, String bizType) {
        Long num = idService.nextId(bizType, bizType);
        String suffix = String.format("%04d",num);
        return bizType + suffix;
    }

    /**
     *
     * @param oriTemplatePO
     * @param oriTemplateFieldsPO
     * @param newFields
     * @param saveTemplatePo
     * @return 变更的字段列表
     */
    private List<ObjectCompareUtil.FieldChange> getFieldChanges(QuestionnaireTemplatePO oriTemplatePO,
                                                                List<QuestionnaireTemplateFieldPO> oriTemplateFieldsPO,
                                                                List<QuestionnaireTemplateFieldPO> newFields,
                                                                QuestionnaireTemplatePO saveTemplatePo) {
        List<ObjectCompareUtil.FieldChange> totalChangeList = new ArrayList<>();
        if (ObjectUtil.isNull(oriTemplatePO)) {
            return totalChangeList;
        }
        newFields.forEach(fieldPO -> fieldPO.setTemplateCode(oriTemplatePO.getTemplateCode()));
        totalChangeList = getFieldChanges(oriTemplateFieldsPO, newFields);
        saveTemplatePo.setId(oriTemplatePO.getId());
        saveTemplatePo.setTemplateCode(oriTemplatePO.getTemplateCode());
        saveTemplatePo.setUpdateTime(DateUtil.date());
        totalChangeList.addAll(ObjectCompareUtil.compareObjects(oriTemplatePO, saveTemplatePo));

        return totalChangeList;
    }

    private List<ChangeLogsPO> getChangeLogs(List<ObjectCompareUtil.FieldChange> totalChangeList,
                                             QuestionnaireTemplatePO oriTemplatePO,
                                             QuestionnaireTemplatePO saveTemplatePo) {
        List<ChangeLogsPO> changeLogsPOS = new ArrayList<>();
        totalChangeList.forEach(r->{
            ChangeLogsPO changeLogsPO = new ChangeLogsPO();
            changeLogsPO.setChangeTable(TABLE_NAME);
            changeLogsPO.setBusinessNo(oriTemplatePO.getTemplateCode());
            changeLogsPO.setChangeField(r.getFieldName());
            changeLogsPO.setOriValue(String.valueOf(r.getOldValue()));
            changeLogsPO.setNewValue(String.valueOf(r.getNewValue()));
            changeLogsPO.setContent(r.getContent());
            changeLogsPO.setCreateBy(saveTemplatePo.getCreateBy());
            changeLogsPO.setUpdateBy(saveTemplatePo.getCreateBy());
            changeLogsPO.setUpdateName(saveTemplatePo.getUpdateName());
            changeLogsPO.setCreateName(saveTemplatePo.getCreateName());
            changeLogsPOS.add(changeLogsPO);
        });
        return changeLogsPOS;
    }

    private static List<ObjectCompareUtil.FieldChange> getFieldChanges(List<QuestionnaireTemplateFieldPO> oriTemplateFieldsPO,
                                                                       List<QuestionnaireTemplateFieldPO> newFields) {
        List<ObjectCompareUtil.FieldChange> totalChangeList = new ArrayList<>();
        Map<Tuple, QuestionnaireTemplateFieldPO> mapList1 = oriTemplateFieldsPO.stream()
                .collect(Collectors.toMap(
                        po -> new Tuple(po.getTemplateCode(), po.getCategorize(),po.getFieldName()), // key
                        po -> po));
        newFields.forEach(newPO -> {
            QuestionnaireTemplateFieldPO oriPo = mapList1.get(new Tuple(
                    newPO.getTemplateCode(),
                    newPO.getCategorize(),
                    newPO.getFieldName()
            ));
            List<ObjectCompareUtil.FieldChange> changeList = ObjectCompareUtil.compareObjects(oriPo, newPO);
            totalChangeList.addAll(changeList);
        });
        return totalChangeList;
    }

    // 辅助类用于存储 id 和 type 的组合，作为 Map 的键
    static class Tuple {
        private final String code;
        private final String type;
        private final String name;

        public Tuple(String code, String type, String name) {
            this.code = code;
            this.type = type;
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Tuple tuple = (Tuple) o;
            return Objects.equals(code, tuple.code) &&
                    Objects.equals(type, tuple.type)
                    &&
                    Objects.equals(name, tuple.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(code, type, name);
        }
    }
}
