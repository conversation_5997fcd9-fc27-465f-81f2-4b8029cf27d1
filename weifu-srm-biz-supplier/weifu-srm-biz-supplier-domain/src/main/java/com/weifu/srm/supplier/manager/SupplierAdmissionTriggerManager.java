package com.weifu.srm.supplier.manager;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.request.todolist.CreateTodoListReqDTO;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.user.SysDivisionManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.CreateTicketConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import com.weifu.srm.user.response.division.FindPurchaseManagerRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAdmissionTriggerManager {

    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final IdService idService;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final TransactionTemplate transactionTemplate;
    private final SysUserManager sysUserManager;
    private final MQServiceManager mqService;
    private final SysDivisionManager sysDivisionManager;
    private final CategoryManager categoryManager;

    private static final String REPLACE_SUPPLIER = "${供应商名称}";
    private static final String CATEGORY_NAME = "${品类名称}";
    private static final String ADMISSION_TYPE = "${准入类型}";
    private static final String REPLACE_TICKET_TYPE = "${工单类型}";
    @Value("${environment.params.admissionUrl}")
    private String admissionUrl;

    /**
     * 准入邀请完成后，发起准入
     *
     * @param invitationNo
     * @param supplierId
     */
    public void completedAdmissionInvitation(String invitationNo, Long supplierId) {
        if (StringUtils.isBlank(invitationNo) && ObjectUtils.isEmpty(supplierId)) {
            log.error("process admission invitation complete params error the invitationNo and supplierId can not be all null ");
            return;
        }
        if (StringUtils.isNotBlank(invitationNo)) {
            SupplierAdmissionInvitationRecordPO invitationRecordPO = invitationInfoMapperService.lambdaQuery()
                    .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo)
                    .one();
            BaseSysUserRespDTO registerUser = userSupplierRelationshipManager.getRegisterUserBySupplierId(invitationRecordPO.getSupplierBasicMsgId());
            SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                    .eq(SupplierBasicInfoPO::getId, invitationRecordPO.getSupplierBasicMsgId())
                    .one();
            sendTodo(invitationRecordPO, basicInfoPO, registerUser);
            return;
        }
        // 查询准入邀请信息
        List<SupplierAdmissionInvitationRecordPO> list = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierBasicMsgId, supplierId)
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.APPROVED_STATUS.getCode())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BaseSysUserRespDTO registerUser = userSupplierRelationshipManager.getRegisterUserBySupplierId(list.get(0).getSupplierBasicMsgId());
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, list.get(0).getSupplierBasicMsgId())
                .one();
        for (SupplierAdmissionInvitationRecordPO invitationRecordPO : list) {
            sendTodo(invitationRecordPO, basicInfoPO, registerUser);
        }
    }

    private void sendTodo(SupplierAdmissionInvitationRecordPO invitationRecord, SupplierBasicInfoPO basicInfoPO, BaseSysUserRespDTO registerUser) {
        // 查询准入邀请品类信息
        List<SupplierAdmissionCategoryRecordPO> categoryRecordPOS = categoryRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, invitationRecord.getInvitationNo())
                .list();
        List<String> categoriesNames = categoryRecordPOS.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryName).collect(Collectors.toList());
        //  修改准入邀请状态
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            // 非批件产不用发起代办与邮件，同时准入邀请状态也保持为审核通过
            if (!AdmissionInvitationTypeEnum.BATCH_PRODUCTION_ADMISSION_TYPE.getCode().equals(invitationRecord.getAdmissionType())) {
                SupplierAdmissionRecordPO admissionRecordPO = boxAdmissionRecordPO(invitationRecord, basicInfoPO, registerUser);
                supplierAdmissionRecordMapperService.save(admissionRecordPO);
                // 冗余信息至准入品类关系表
                redundantDataToAdmissionCategoryRelationship(admissionRecordPO);
                CreateTicketMQ createTicketMQ = buildCreateTicketMQMSg(admissionRecordPO.getAdmissionNo(), invitationRecord);
                mqService.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
                return;
            }
            invitationInfoMapperService.lambdaUpdate()
                    .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationRecord.getInvitationNo())
                    .set(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.INVITATION_STATUS.getCode())
                    .update();
            // 批产发起待办+邮件
            mqService.sendMQ(CommunicationTopicConstants.CREATE_TODO, JacksonUtil.bean2Json(boxAdmissionTodoDTO(invitationRecord, categoriesNames, registerUser)));
            // 发送邮件
            mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(boxInvitationSuccessEmail(invitationRecord, categoriesNames)));
        });

    }

    private CreateTodoListReqDTO boxAdmissionTodoDTO(SupplierAdmissionInvitationRecordPO invitationInfo, List<String> categoriesNames, BaseSysUserRespDTO registerUser) {
        String content = TodoTemplateEnum.ADMISSION_INVITATION_TODO.getContent();
        content = content
                .replace(REPLACE_SUPPLIER, invitationInfo.getSupplierName())
                .replace(CATEGORY_NAME, JacksonUtil.bean2Json(categoriesNames))
                .replace(ADMISSION_TYPE, AdmissionInvitationTypeEnum.getByCode(invitationInfo.getAdmissionType()).getChineseName());
        CreateTodoListReqDTO todo = new CreateTodoListReqDTO();
        todo.setBusinessNo(invitationInfo.getInvitationNo());
        todo.setBusinessType(TodoClsEnum.CATEGORY_ADMISSION.getCode());
        todo.setUserId(registerUser.getId());
        todo.setIconType(IconTypeEnum.GENERAL.getCode());
        todo.setContent(content);
        todo.setSupplierId(invitationInfo.getSupplierBasicMsgId());
        return todo;
    }

    private CreateSendEmailTaskMQ boxInvitationSuccessEmail(SupplierAdmissionInvitationRecordPO invitationRecord, List<String> categoriesNames) {
        BaseSysUserRespDTO sponsor = sysUserManager.getUserDetailById(invitationRecord.getCreateBy());
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.ADMISSION_INVITATION_RESULT_CONTENT.getTitle()
                .replace("${0}", String.join(" ", categoriesNames)));
        emailReq.setContent(EmailTemplateEnum.ADMISSION_INVITATION_RESULT_CONTENT.getContent()
                .replace("${0}", String.join(" ", categoriesNames))
                .replace("${1}", invitationRecord.getSupplierName())
                .replace("${2}", admissionUrl)
                .replace("${3}", sponsor.getRealName())
                .replace("${4}", sponsor.getEmail()));
        emailReq.setRecipients(invitationRecord.getSupplierRegistrationContactEmail());
        emailReq.setBusinessNo(invitationRecord.getInvitationNo());
        emailReq.setBusinessType("准入邀请");
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }


    private CreateTicketMQ buildCreateTicketMQMSg(String admissionNo, SupplierAdmissionInvitationRecordPO invitationRecordPO) {
        String desc = "";
        AdmissionInvitationTypeEnum supplierTypeEnum = AdmissionInvitationTypeEnum.getByCode(invitationRecordPO.getAdmissionType());
        if (ObjectUtil.isNull(supplierTypeEnum)) {
            throw new BizFailException(localeMessage.getMessage("supplier.admission.type.error"));
        }
        CreateTicketMQ.ApprovalProcessVar isCenPurchaseVar = new CreateTicketMQ.ApprovalProcessVar();
        isCenPurchaseVar.setKey("IS_JC");
        isCenPurchaseVar.setV(YesOrNoEnum.NO.equalsCode(invitationRecordPO.getIsCenPurchase()) ? Boolean.FALSE.toString() : Boolean.TRUE.toString());

        //通过准入邀请号查询 准入品类
        List<SupplierAdmissionCategoryRecordPO> categoryCodePOs = categoryRecordMapperService
                .lambdaQuery().eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, invitationRecordPO.getInvitationNo())
                .eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, YesOrNoEnum.NO.getCode())
                .list();
        List<String> categoryCodes = Optional.ofNullable(categoryCodePOs)
                .map(k -> k.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        // 获取品类挂钩的工程师及其上级主管
        List<CategoryEngineerResultDTO> resultDTOList = categoryManager.queryCategoryEngineerByCategoryCodes(categoryCodes);
        CreateTicketMQ msg = new CreateTicketMQ();
        msg.setBusinessNo(admissionNo);
        switch (supplierTypeEnum) {
            case SAMPLE_ADMISSION_TYPE:
                List<CreateTicketMQ.ApprovalProcessVar> processVarList = getSampleProcessVars(resultDTOList, invitationRecordPO);
                processVarList.add(isCenPurchaseVar);
                msg.setTicketType(TicketTypeEnum.SAMPLE_SUPPLIER_ADMISSION.getCode());
                msg.setProcessVars(processVarList);
                desc = CreateTicketConstants.TITLE_NAME
                        .replace(REPLACE_SUPPLIER, invitationRecordPO.getSupplierName())
                        .replace(REPLACE_TICKET_TYPE, TicketTypeEnum.SAMPLE_SUPPLIER_ADMISSION.getDesc());
                break;
            case TMP_ADMISSION_TYPE:
                msg.setTicketType(TicketTypeEnum.TEMPORARY_SUPPLIER_ADMISSION.getCode());
                List<CreateTicketMQ.ApprovalProcessVar> tempProcessVars = getTempProcessVars(resultDTOList, invitationRecordPO);
                tempProcessVars.add(isCenPurchaseVar);
                msg.setProcessVars(tempProcessVars);
                desc = CreateTicketConstants.TITLE_NAME
                        .replace(REPLACE_SUPPLIER, invitationRecordPO.getSupplierName())
                        .replace(REPLACE_TICKET_TYPE, TicketTypeEnum.TEMPORARY_SUPPLIER_ADMISSION.getDesc());
                break;
            default:
                log.error("not support admissionType ={}", supplierTypeEnum);
        }
        msg.setSubmitDesc(desc);
        msg.setSubmitBy(invitationRecordPO.getCreateBy());
        msg.setSubmitName(invitationRecordPO.getCreateName());
        msg.setSubmitRemark(desc);
        return msg;
    }

    private List<CreateTicketMQ.ApprovalProcessVar> getSampleProcessVars(List<CategoryEngineerResultDTO> resultDTOList,
                                                                         SupplierAdmissionInvitationRecordPO invitationRecordPO) {
        List<CreateTicketMQ.ApprovalProcessVar> processVarList = new ArrayList<>();
        List<String> domainNames = resultDTOList.stream().filter(r -> CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.equalsCode(r.getRoleId()))
                .map(CategoryEngineerResultDTO::getUserDomain).distinct()
                .collect(Collectors.toList());
        if (YesOrNoEnum.NO.equalsCode(invitationRecordPO.getIsCenPurchase())) {
            // 非集采在此基础上，新增事业部采购经理
            FindPurchaseManagerRespDTO sysDivision = sysDivisionManager.getSysDivision(invitationRecordPO.getInvitationBy());
            if (ObjectUtil.isNull(sysDivision) || StringUtils.isBlank(sysDivision.getDomain()) || StringUtils.isBlank(sysDivision.getSeniorPurchaseManagerUserDomain())) {
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.INVITATION_DIVISION_NOT_COMPLETE));
            }
            domainNames.add(sysDivision.getSeniorPurchaseManagerUserDomain());
        }
        log.info("{} domainNames:{}", CreateTicketConstants.CGCZ_APPROVE_USER_ACCOUNT, domainNames);
        CreateTicketMQ.ApprovalProcessVar processVar2 = new CreateTicketMQ.ApprovalProcessVar();
        processVar2.setKey(CreateTicketConstants.CGCZ_APPROVE_USER_ACCOUNT);
        processVar2.setV(domainNames.stream().distinct().collect(Collectors.joining(" ")));
        processVarList.add(processVar2);
        return processVarList;
    }

    private List<CreateTicketMQ.ApprovalProcessVar> getTempProcessVars(List<CategoryEngineerResultDTO> resultDTOList,
                                                                       SupplierAdmissionInvitationRecordPO invitationRecordPO) {
        List<CreateTicketMQ.ApprovalProcessVar> processVarList = new ArrayList<>();
        CreateTicketMQ.ApprovalProcessVar processVar0 = new CreateTicketMQ.ApprovalProcessVar();
        processVar0.setKey(CreateTicketConstants.COMPANY_CODE);
        processVar0.setV(invitationRecordPO.getTmpPurchaseOrg());
        processVarList.add(processVar0);
        List<String> domainNames = resultDTOList.stream().filter(r -> CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.equalsCode(r.getRoleId()))
                .map(CategoryEngineerResultDTO::getUserDomain)
                .distinct()
                .collect(Collectors.toList());
        if (YesOrNoEnum.NO.equalsCode(invitationRecordPO.getIsCenPurchase())) {
            // 非集采在此基础上，新增事业部采购经理
            FindPurchaseManagerRespDTO sysDivision = sysDivisionManager.getSysDivision(invitationRecordPO.getInvitationBy());
            if (ObjectUtil.isNull(sysDivision) || StringUtils.isBlank(sysDivision.getDomain()) || StringUtils.isBlank(sysDivision.getSeniorPurchaseManagerUserDomain())) {
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.INVITATION_DIVISION_NOT_COMPLETE));
            }
            domainNames.add(sysDivision.getSeniorPurchaseManagerUserDomain());
        }
        log.info(CreateTicketConstants.CG_APPROVE_USER_ACCOUNT + "domainNames:{}", domainNames);
        CreateTicketMQ.ApprovalProcessVar processVar = new CreateTicketMQ.ApprovalProcessVar();
        processVar.setKey(CreateTicketConstants.CG_APPROVE_USER_ACCOUNT);
        processVar.setV(String.join(" ", domainNames));
        processVarList.add(processVar);
        return processVarList;
    }

    private SupplierAdmissionRecordPO boxAdmissionRecordPO(SupplierAdmissionInvitationRecordPO invitationRecord, SupplierBasicInfoPO basicInfoPO, BaseSysUserRespDTO registerUser) {
        String admissionNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.ADMISSION_NO.getBizTypeCodee(), idService);
        SupplierAdmissionRecordPO admissionPO = new SupplierAdmissionRecordPO();
        admissionPO.setAdmissionNo(admissionNo);
        admissionPO.setInvitationNo(invitationRecord.getInvitationNo());
        admissionPO.setSupplierBasicMsgId(invitationRecord.getSupplierBasicMsgId());
        admissionPO.setSupplierName(invitationRecord.getSupplierName());
        admissionPO.setSupplierType(invitationRecord.getSupplierType());
        admissionPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        admissionPO.setAdmissionInvitationBy(invitationRecord.getCreateBy() + "");
        admissionPO.setAdmissionStatus(AdmissionStatusEnum.APPROVING.getCode());
        admissionPO.setAdmissionInvitationName(invitationRecord.getCreateName());
        admissionPO.setAdmissionType(invitationRecord.getAdmissionType());
        admissionPO.setAdmissionApplyTime(new Date());
        admissionPO.setAdmissionInvitationDivisionCode(invitationRecord.getDivisionCode());
        admissionPO.setIsCenPurchase(invitationRecord.getIsCenPurchase());
        BaseEntityUtil.setCommon(admissionPO, registerUser.getId(), registerUser.getRealName(), null);
        return admissionPO;
    }

    private void redundantDataToAdmissionCategoryRelationship(SupplierAdmissionRecordPO supplierAdmissionRecordPO) {
        categoryRecordMapperService.lambdaUpdate()
                .eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, supplierAdmissionRecordPO.getInvitationNo())
                .set(SupplierAdmissionCategoryRecordPO::getAdmissionNo, supplierAdmissionRecordPO.getAdmissionNo())
                .set(SupplierAdmissionCategoryRecordPO::getSupplierCode, supplierAdmissionRecordPO.getSupplierCode())
                .set(SupplierAdmissionCategoryRecordPO::getAdmissionStatus, supplierAdmissionRecordPO.getAdmissionStatus())
                .set(SupplierAdmissionCategoryRecordPO::getAdmissionApplyTime, new Date())
                .set(SupplierAdmissionCategoryRecordPO::getSupplierBasicMsgId, supplierAdmissionRecordPO.getSupplierBasicMsgId())
                .update();
    }

}
