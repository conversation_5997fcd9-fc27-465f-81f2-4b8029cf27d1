package com.weifu.srm.supplier.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.api.SiteMessageApi;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.dto.SupplierCategoryRelationshipSaveDTO;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.manager.EmailServiceManager;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.SupplierCategoryRelationshipManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierPotentialToQualifiedHandleAuditBiz {

    private final SupplierPotentialToQualifiedRecordMapperService supplierPotentialToQualifiedRecordMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SysUserManager sysUserManager;
    private final MQServiceManager mqService;
    private final CategoryApi categoryApi;
    private final MqManager mqManager;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierCategoryRelationshipManager supplierCategoryRelationshipManager;
    private final SupplierKeyInfoChangeRecordMapperService supplierKeyInfoChangeRecordMapperService;
    private final SupplierAdmissionCategoryRecordMapperService admissionCategoryRecordMapperService;

    /**
     * 处理潜在转合格审批结果
     */
    public void handleAudit(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        // 查询申请单相关信息
        LambdaQueryWrapper<SupplierPotentialToQualifiedRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, ticketInfo.getBusinessNo())
                .eq(SupplierPotentialToQualifiedRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO = supplierPotentialToQualifiedRecordMapperService.getOne(queryWrapper);
        // 审批通过
        if (TicketStatusEnum.APPROVED.equalsCode(ticketInfo.getStatus())) {
            // 更新申请单审核状态
            LambdaUpdateWrapper<SupplierPotentialToQualifiedRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, ticketInfo.getBusinessNo())
                    .set(SupplierPotentialToQualifiedRecordPO::getApplyStatus, CommonStatusEnum.AGREE_STATUS.getCode())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateTime, new Date());
            // 更新准入表数据状态
            LambdaUpdateWrapper<SupplierAdmissionRecordPO> supplierAdmissionRecordPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            supplierAdmissionRecordPOLambdaUpdateWrapper
                    .eq(SupplierAdmissionRecordPO::getAdmissionNo, supplierPotentialToQualifiedRecordPO.getAdmissionNo())
                    .set(SupplierAdmissionRecordPO::getSupplierCategoryStatus, SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode())
                    .set(SupplierAdmissionRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierAdmissionRecordPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierAdmissionRecordPO::getUpdateTime, new Date());
            // 查询准入表信息
            LambdaQueryWrapper<SupplierBasicInfoPO> basicInfoPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            basicInfoPOLambdaQueryWrapper
                            .eq(SupplierBasicInfoPO::getSapSupplierCode, supplierPotentialToQualifiedRecordPO.getSapSupplierCode())
                            .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
            SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.getOne(basicInfoPOLambdaQueryWrapper);
            transactionTemplate.execute(transactionStatus -> {
                supplierPotentialToQualifiedRecordMapperService.update(updateWrapper);
                // 修改准入表
                supplierAdmissionRecordMapperService.update(supplierAdmissionRecordPOLambdaUpdateWrapper);
                // 修改准入列表(冗余数据)
                admissionCategoryRecordMapperService.lambdaUpdate()
                        .eq(SupplierAdmissionCategoryRecordPO::getAdmissionNo, supplierPotentialToQualifiedRecordPO.getAdmissionNo())
                        .set(SupplierAdmissionCategoryRecordPO::getSupplierCategoryStatus, SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode())
                        .update();
                // 如果供应商对应的状态是潜在供应商，就修改为合格供应商
                if (basicInfoPO.getStatus().equals(SupplierBasicStatusEnum.POTENTIAL_SUPPLIER.getCode())) {
                    LambdaUpdateWrapper<SupplierBasicInfoPO> basicInfoPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    basicInfoPOLambdaUpdateWrapper
                            .eq(SupplierBasicInfoPO::getSapSupplierCode, supplierPotentialToQualifiedRecordPO.getSapSupplierCode())
                            .set(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode());
                    // 如果对应供应商的分级是无分级，将分级修改为基本供应商
                    if (basicInfoPO.getSupplierClassification().equals(SupplierGradeTypeEnum.NON_GRADE.getCode())) {
                        basicInfoPOLambdaUpdateWrapper.set(SupplierBasicInfoPO::getSupplierClassification, SupplierGradeTypeEnum.BASE_SUPPLIER.getCode());
                    }
                    supplierBasicInfoMapperService.update(basicInfoPOLambdaUpdateWrapper);
                    // 记录时间节点
                    SupplierKeyInfoChangeRecordPO supplierKeyInfoChangeRecordPO = new SupplierKeyInfoChangeRecordPO();
                    supplierKeyInfoChangeRecordPO.setBusinessNo(supplierPotentialToQualifiedRecordPO.getBusinessNo());
                    supplierKeyInfoChangeRecordPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
                    supplierKeyInfoChangeRecordPO.setSupplierBasicMsgId(basicInfoPO.getId());
                    supplierKeyInfoChangeRecordPO.setChangeType(SupplierLifeCycleTypeEnum.POTENTIAL_TO_QUALIFIED.getCode());
                    supplierKeyInfoChangeRecordPO.setContent(SupplierLifeCycleTypeEnum.POTENTIAL_TO_QUALIFIED.getType());
                    supplierKeyInfoChangeRecordPO.setRemark(SupplierLifeCycleTypeEnum.POTENTIAL_TO_QUALIFIED.getType());
                    BaseEntityUtil.setCommon(supplierKeyInfoChangeRecordPO, ticketInfo.getOperateBy(), ticketInfo.getOperateName(),new Date());
                    supplierKeyInfoChangeRecordPO.setIsDelete(YesOrNoEnum.NO.getCode());
                    supplierKeyInfoChangeRecordMapperService.save(supplierKeyInfoChangeRecordPO);
                }
                // 对品类资质变化进行记录
                SupplierCategoryRelationshipSaveDTO req = new SupplierCategoryRelationshipSaveDTO();
                req.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.ADD.getCode());
                req.setSapSupplierCode(supplierPotentialToQualifiedRecordPO.getSapSupplierCode());
                req.setSupplierCategoryStatus(SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode());
                req.setBusinessNo(supplierPotentialToQualifiedRecordPO.getBusinessNo());
                req.setCategoryCodes(List.of(supplierPotentialToQualifiedRecordPO.getAdmissionCategoryCode().split("、")));
                req.setOperationUserId(supplierPotentialToQualifiedRecordPO.getCreateBy());
                req.setOperationName(supplierPotentialToQualifiedRecordPO.getCreateName());
                req.setSource(SupplierCategoryRelationshipSourceEnum.POTENTIAL_TO_QUALIFIED.getCode());
                req.setRemark(SupplierCategoryRelationshipSourceEnum.POTENTIAL_TO_QUALIFIED.getChineseName());
                supplierCategoryRelationshipManager.save(req);
                mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(supplierPotentialToQualifiedRecordPO,ticketInfo)));
                sendSupplierRegisterTodoAndEmail(supplierPotentialToQualifiedRecordPO);
                return null;
            });
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus())) {
            // 更新申请单审核状态
            LambdaUpdateWrapper<SupplierPotentialToQualifiedRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, ticketInfo.getBusinessNo())
                    .set(SupplierPotentialToQualifiedRecordPO::getApplyStatus, CommonStatusEnum.REJECT_STATUS.getCode())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateTime, new Date());
            supplierPotentialToQualifiedRecordMapperService.update(updateWrapper);
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            // 更新申请单审核状态
            LambdaUpdateWrapper<SupplierPotentialToQualifiedRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, ticketInfo.getBusinessNo())
                    .set(SupplierPotentialToQualifiedRecordPO::getApplyStatus, CommonStatusEnum.DRAFT.getCode())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierPotentialToQualifiedRecordPO::getUpdateTime, new Date());
            supplierPotentialToQualifiedRecordMapperService.update(updateWrapper);
        }
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO, TicketStatusChangedMQ ticketInfo) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        // 品类工程师
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setBusinessNo(ticketInfo.getBusinessNo());
        notice.setBusinessType(MessageClsEnum.SUPPLIER_UPGRADE.getCode());
        notice.setUserId(supplierPotentialToQualifiedRecordPO.getCreateBy());
        notice.setUserName(supplierPotentialToQualifiedRecordPO.getCreateName());
        notice.setIconType(IconTypeEnum.GENERAL.getCode());
        notice.setTitle(NoticeTemplateEnum.UP_QUALIFIED_SUPPLIER_CONTENT.getTitle());
        String content = NoticeTemplateEnum.UP_QUALIFIED_SUPPLIER_CONTENT.getContent();
        content = content
                .replace("${供应商名称}", supplierPotentialToQualifiedRecordPO.getSupplierName())
                .replace("${品类名称}", supplierPotentialToQualifiedRecordPO.getAdmissionCategory())
                .replace("${供应商状态}", "合格供应商");
        notice.setContent(content);
        // 供应商
        SupplierAdmissionRecordPO admissionRecordPO = supplierAdmissionRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionRecordPO::getAdmissionNo, supplierPotentialToQualifiedRecordPO.getAdmissionNo())
                .eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        CreateSiteMessageMQ notice2 = new CreateSiteMessageMQ();
        notice2.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice2.setBusinessNo(ticketInfo.getBusinessNo());
        notice2.setBusinessType(MessageClsEnum.SUPPLIER_UPGRADE.getCode());
        notice2.setUserId(admissionRecordPO.getCreateBy());
        notice2.setUserName(admissionRecordPO.getCreateName());
        notice2.setIconType(IconTypeEnum.GENERAL.getCode());
        notice2.setTitle(NoticeTemplateEnum.UP_QUALIFIED_SUPPLIER_CONTENT.getTitle());
        notice2.setContent(content);
        result.add(notice);
        result.add(notice2);
        return result;
    }

    private void sendSupplierRegisterTodoAndEmail(SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO) {
        SupplierAdmissionRecordPO admissionRecordPO = supplierAdmissionRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionRecordPO::getAdmissionNo, supplierPotentialToQualifiedRecordPO.getAdmissionNo())
                .eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        List<Long> ids = new ArrayList<>();
        ids.add(admissionRecordPO.getCreateBy());
        ids.add(supplierPotentialToQualifiedRecordPO.getCreateBy());
        String[] split = supplierPotentialToQualifiedRecordPO.getAdmissionCategoryCode().split("、");
        ApiResponse<List<CategoryEngineerResultDTO>> listApiResponse = categoryApi.queryCategoryEngineerByCategoryCodes(List.of(split));
        listApiResponse.getData().forEach(categoryEngineerResultDTO -> {
            if (categoryEngineerResultDTO.getRoleId().equals("CPE_MASTER")) {
                ids.add(categoryEngineerResultDTO.getUserId());
            }
        });
        List<BaseSysUserRespDTO> users = sysUserManager.findByIds(ids);
        String emailAll = users.stream().map(BaseSysUserRespDTO::getEmail).collect(Collectors.joining(";"));
        CreateSendEmailTaskMQ req = new CreateSendEmailTaskMQ();
        req.setTitle(EmailTemplateEnum.SUPPLIER_POTENTIAL_TO_QUALIFIED_CONTENT.getTitle()
                .replace("${0}", supplierPotentialToQualifiedRecordPO.getSupplierName())
                .replace("${1}", supplierPotentialToQualifiedRecordPO.getAdmissionCategory()));
        req.setContent(EmailTemplateEnum.SUPPLIER_POTENTIAL_TO_QUALIFIED_CONTENT.getContent()
                .replace("${0}", supplierPotentialToQualifiedRecordPO.getSupplierName())
                .replace("${1}", supplierPotentialToQualifiedRecordPO.getAdmissionCategory()));
        req.setRecipients(emailAll);
        req.setCreateBy(supplierPotentialToQualifiedRecordPO.getCreateBy());
        req.setCreateName(supplierPotentialToQualifiedRecordPO.getCreateName());
        // 发送供应商注册通知e-mail
        mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(req));
    }

}
