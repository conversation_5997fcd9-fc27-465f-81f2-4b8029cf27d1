package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO;
import com.weifu.srm.supplier.response.suppliercategorybatchadjustapply.SupplierCategoryBatchAdjustApplyDetailRespDTO;

/**
 * 供应商品类关系批量调整服务
 */
public interface SupplierCategoryBatchAdjustApplyService {

    /**
     * 创建供应商品类关系批量调整申请
     */
    String create(CreateSupplierCategoryBatchAdjustApplyReqDTO req);

    /**
     * 查询申请详情
     */
    SupplierCategoryBatchAdjustApplyDetailRespDTO queryApplyDetail(String applyNo);

    /**
     * 处理供应商品类关系批量调整工单审核结果
     */
    void handleSupplierCategoryAdjustApplyRst(TicketStatusChangedMQ mq);

}
