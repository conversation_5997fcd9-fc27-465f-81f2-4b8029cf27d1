package com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply;

import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.exception.ParamErrorException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;
import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyUploadFileReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;

/**
 * 上传供应商品类关系调整申请会议决议文件
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UploadMeetingResolutionFileBiz {

    private final SupplierCategoryAdjustApplyMapperService supplierCategoryAdjustApplyMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;

    public void execute(SupplierCategoryAdjustApplyUploadFileReqDTO req) {
        log.info("上传供应商品类关系调整申请会议决议文件开始，applyNo={}，req={}", req.getApplyNo(), JacksonUtil.bean2Json(req));
        if (req.getFile() == null) {
            throw new ParamErrorException("文件不能为空");
        }

        AttachmentRecordPO attachmentRecord = attachmentRecordMapperService.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessType, Arrays.asList(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_MEETING_RESOLUTION))
                .eq(AttachmentRecordPO::getBusinessNo, req.getApplyNo())
                .one();

        Date current = new Date();
        AttachmentMessageReqDTO file = req.getFile();
        if (attachmentRecord != null) {
            attachmentRecord.setFileName(file.getFileName());
            attachmentRecord.setFileUrl(file.getFileUrl());
            attachmentRecord.setFileOriginalName(file.getFileOriginalName());
            BaseEntityUtil.setCommonForU(attachmentRecord, req.getOperatorBy(), req.getOperatorName(), current);
            attachmentRecordMapperService.updateById(attachmentRecord);
        } else {
            attachmentRecord = new AttachmentRecordPO();
            attachmentRecord.setBusinessType(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_MEETING_RESOLUTION);
            attachmentRecord.setBusinessNo(req.getApplyNo());
            attachmentRecord.setFileName(file.getFileName());
            attachmentRecord.setFileUrl(file.getFileUrl());
            attachmentRecord.setFileOriginalName(file.getFileOriginalName());
            BaseEntityUtil.setCommon(attachmentRecord, req.getOperatorBy(), req.getOperatorName(), current);
            attachmentRecordMapperService.save(attachmentRecord);
        }
        log.info("上传供应商品类关系调整申请会议决议文件完成，applyNo={}", req.getApplyNo());
    }

    private void check(SupplierCategoryAdjustApplyUploadFileReqDTO req) {
        if (req.getFile() == null) {
            throw new ParamErrorException("文件不能为空");
        }
        SupplierCategoryAdjustApplyPO apply = supplierCategoryAdjustApplyMapperService.lambdaQuery()
                .eq(SupplierCategoryAdjustApplyPO::getApplyNo, req.getApplyNo()).one();
        if (!SupplierCategoryAdjustApplyStatusEnum.APPROVING.equalsCode(apply.getApplyStatus())) {
            throw new BizFailException("只有审核中的申请才能上传会议决议文件");
        }
    }

}
