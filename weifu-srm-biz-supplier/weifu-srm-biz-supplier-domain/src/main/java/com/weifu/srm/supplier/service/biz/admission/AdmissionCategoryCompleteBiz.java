package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.masterdata.response.CategoryResultDTO;
import com.weifu.srm.supplier.convert.CategoryConvert;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.manager.SupplierAdmissionCategoryManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationTypeEnum;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationshipStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordSearchPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.response.AdmissionInvitationCategoryRespDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdmissionCategoryCompleteBiz {
    private final SupplierAdmissionCategoryManager supplierAdmissionCategoryManager;
    private final SupplierAdmissionCategoryRecordMapperService admissionCategoryRecordMapperService;
    private final CategoryManager categoryManager;
    private final CategoryConvert categoryConvert;

    public List<AdmissionInvitationCategoryRespDTO> searchCompleteAdmissionCategory(Long userId, String supplierName,
                                                                                    String admissionType,Integer isCenPurchase) {
        // 获取 品类树
        List<CategoryResultDTO> categoryResultDTOS = getCategoryTree(userId, isCenPurchase);
        // 获取供应商品类资质数据--已完成的
        List<SupplierCategoryRelationshipPO> list = supplierAdmissionCategoryManager.getSupplierCategoryRelationshipPOs(supplierName);
        list = list.stream().filter(ship ->SupplierCategoryRelationshipStatusEnum.NORMAL.getCode().equals(ship.getStatus())).collect(Collectors.toList());
        // 在途的供应商品类数据
        List<SupplierAdmissionCategoryRecordSearchPO> allRecords = admissionCategoryRecordMapperService.searchBySupplierName(supplierName);
        // 批产--在途数据
        List<SupplierAdmissionCategoryRecordSearchPO> batchPos = allRecords.stream().filter(recordPO -> AdmissionInvitationTypeEnum.BATCH_PRODUCTION_ADMISSION_TYPE.getCode().equals(recordPO.getAdmissionType())).collect(Collectors.toList());
        batchPos = supplierAdmissionCategoryManager.getBatchCodes(batchPos);
        // 临时--在途数据
        List<SupplierAdmissionCategoryRecordSearchPO> tmpPos = allRecords.stream().filter(recordPO -> AdmissionInvitationTypeEnum.TMP_ADMISSION_TYPE.getCode().equals(recordPO.getAdmissionType())).collect(Collectors.toList());
        tmpPos = supplierAdmissionCategoryManager.getTmpCodes(tmpPos);
        // 样件-- 在途数据
        List<SupplierAdmissionCategoryRecordSearchPO> samplePos = allRecords.stream().filter(recordPO -> AdmissionInvitationTypeEnum.SAMPLE_ADMISSION_TYPE.getCode().equals(recordPO.getAdmissionType())).collect(Collectors.toList());
        samplePos = supplierAdmissionCategoryManager.getSampleCodes(samplePos);
        List<SupplierAdmissionCategoryRecordSearchPO> allProcess = new ArrayList<>();
        allProcess.addAll(batchPos);
        allProcess.addAll(tmpPos);
        allProcess.addAll(samplePos);
        if (CollectionUtils.isNotEmpty(allProcess)){
            List<String> processCodes = allProcess.stream().map(SupplierAdmissionCategoryRecordSearchPO::getCategoryCode).collect(Collectors.toList());
            // 过滤所有在途数据
            filterCategory(categoryResultDTOS, processCodes);
        }
        // 批产生效
        if (AdmissionInvitationTypeEnum.BATCH_PRODUCTION_ADMISSION_TYPE.getCode().equals(admissionType)) {
            List<String> validBatchCodes = list.stream().filter(ship -> SupplierCategoryStatusEnum.QUALIFIED_STATUS.equalsCode(ship.getSupplierCategoryStatus())
                    || SupplierCategoryStatusEnum.POTENTIAL_STATUS.equalsCode(ship.getSupplierCategoryStatus()))
                    .map(SupplierCategoryRelationshipPO::getCategoryCode)
                    .collect(Collectors.toList());
            filterCategory(categoryResultDTOS, validBatchCodes);
            return categoryConvert.toListAdmissionInvitationCategoryRespDTO(categoryResultDTOS);
        }
        // 临时+样件互斥
        List<String> validCategoryCodes = list.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toList());
        filterCategory(categoryResultDTOS,validCategoryCodes);
        return categoryConvert.toListAdmissionInvitationCategoryRespDTO(categoryResultDTOS);
    }

    private void filterCategory(List<CategoryResultDTO> categoryResultDTOS, List<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)){
            return;
        }
        categoryResultDTOS.removeIf(categoryResultDTO -> categoryIds.contains(categoryResultDTO.getCategoryCode()));
        for (CategoryResultDTO categoryResultDTO : categoryResultDTOS) {
            if (CollectionUtils.isNotEmpty(categoryResultDTO.getChildren())) {
                filterCategory(categoryResultDTO.getChildren(), categoryIds);
            }
        }
    }

    private List<CategoryResultDTO> getCategoryTree(Long userId, Integer isCenPurchase) {
        // 调用category 获取 品类树
        List<CategoryResultDTO> categoryResultDTOS = categoryManager.queryCategoryTree();
        if (YesOrNoEnum.NO.equalsCode(isCenPurchase)){
            return categoryResultDTOS;
        }
        // 获取当前工程师能操作的品类
        List<CategoryEngineerResultDTO> categoryEngineerResultDTOS = categoryManager.queryCategoryEngineerByUserId(userId);
        categoryEngineerResultDTOS = categoryEngineerResultDTOS.stream().filter(category -> CategoryRoleEnum.CPE.equalsCode(category.getRoleId()) || CategoryRoleEnum.CPE_MASTER.equalsCode(category.getRoleId())).collect(Collectors.toList());
        List<String> categoryCodes = categoryEngineerResultDTOS.stream().map(CategoryEngineerResultDTO::getCategoryCode).collect(Collectors.toList());
        // 过滤掉不属于当前品类工程操作的3级品类
        filterCategoryTreeWithCategoryCodes(categoryResultDTOS, categoryCodes);
        // 过滤掉没有子类的1级 2级品类
        filterNullChildrenTree(categoryResultDTOS, "2");
        filterNullChildrenTree(categoryResultDTOS, "1");
        // 对获取对品类树进行过滤
        return categoryResultDTOS;
    }

    private void filterCategoryTreeWithCategoryCodes(List<CategoryResultDTO> categoryResultDTOS, List<String> categoryCodes) {
        // 过滤掉所有不属于当前品类工程师操作的3级品类
        categoryResultDTOS.removeIf(categoryResultDTO -> "3".equals(categoryResultDTO.getCategoryLevel()) && !categoryCodes.contains(categoryResultDTO.getCategoryCode()));
        for (CategoryResultDTO categoryResultDTO : categoryResultDTOS) {
            if (CollectionUtils.isNotEmpty(categoryResultDTO.getChildren())) {
                filterCategoryTreeWithCategoryCodes(categoryResultDTO.getChildren(), categoryCodes);
            }
        }
    }

    private void filterNullChildrenTree(List<CategoryResultDTO> categoryResultDTOS, String categoryLevel) {
        categoryResultDTOS.removeIf(categoryResultDTO -> categoryLevel.equals(categoryResultDTO.getCategoryLevel()) && CollectionUtils.isEmpty(categoryResultDTO.getChildren()));
        for (CategoryResultDTO categoryResultDTO : categoryResultDTOS) {
            if (CollectionUtils.isNotEmpty(categoryResultDTO.getChildren())) {
                filterNullChildrenTree(categoryResultDTO.getChildren(), categoryLevel);
            }
        }
    }

}
