package com.weifu.srm.supplier.manager;

import cn.hutool.core.bean.BeanUtil;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.dto.SupplierCategoryRelationshipSaveDTO;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryChangeRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationShipChangeTypeEnum;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationshipSourceEnum;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationshipStatusEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCategoryRelationshipManager {

    /**
     * 供应商品类资质优先级
     */
    private static final Map<String, Integer> SCS_PRIORITY = Map.of(
            SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode(), 4,
            SupplierCategoryStatusEnum.POTENTIAL_STATUS.getCode(), 3,
            SupplierCategoryStatusEnum.TMP_STATUS.getCode(), 2,
            SupplierCategoryStatusEnum.SAMPLE_STATUS.getCode(), 1
    );

    private final SupplierCategoryChangeRecordMapperService supplierCategoryChangeRecordMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final LocaleMessage localeMessage;

    @Transactional
    public void save(SupplierCategoryRelationshipSaveDTO req) {
        // 判断业务单是否已处理过
        SupplierCategoryChangeRecordPO existFlag = supplierCategoryChangeRecordMapperService.lambdaQuery()
                .eq(SupplierCategoryChangeRecordPO::getSource, req.getSource())
                .eq(SupplierCategoryChangeRecordPO::getBusinessNo, req.getBusinessNo())
                .last(" limit 1 ").one();
        if (existFlag != null) {
            return;
        }

        String sapSupplierCode = req.getSapSupplierCode();
        List<String> categoryCodes = req.getCategoryCodes();
        List<SupplierCategoryRelationshipPO> existRelationships = supplierCategoryRelationshipMapperService.listBySSCAndCC(sapSupplierCode, categoryCodes);

        Date current = new Date();
        List<SupplierCategoryChangeRecordPO> changeRecords = new ArrayList<>();
        if (SupplierCategoryRelationShipChangeTypeEnum.ADD.equalsCode(req.getChangeType())) {
            String relationshipStatus = getSupplierCategoryStatus(SupplierCategoryStatusEnum.getByCode(req.getSupplierCategoryStatus()), req.getValidityStartTime(), req.getValidityEndTime());
            Map<String, SupplierCategoryRelationshipPO> existRelMap = existRelationships.stream().collect(
                    Collectors.toMap(SupplierCategoryRelationshipPO::getCategoryCode, Function.identity(), (v1, v2) -> v1));

            List<SupplierCategoryRelationshipPO> newRelationships = new ArrayList<>();
            List<Long> upRelationships = new ArrayList<>();
            // 记录变动
            boxUpdateAndNewCategoryRecord(req, existRelMap, newRelationships, upRelationships, changeRecords, current, relationshipStatus);

            if (CollectionUtils.isNotEmpty(newRelationships)) {
                supplierCategoryRelationshipMapperService.saveBatch(newRelationships);
            }
            if (CollectionUtils.isNotEmpty(upRelationships)) {
                supplierCategoryRelationshipMapperService.lambdaUpdate()
                        .set(SupplierCategoryRelationshipPO::getSupplierCategoryStatus, req.getSupplierCategoryStatus())
                        .set(SupplierCategoryRelationshipPO::getQualificationEffectiveTime, current)
                        .set(SupplierCategoryRelationshipPO::getValidityStartTime, req.getValidityStartTime())
                        .set(SupplierCategoryRelationshipPO::getValidityEndTime, req.getValidityEndTime())
                        .set(SupplierCategoryRelationshipPO::getMastPurchaseAmt, req.getMastPurchaseAmt())
                        .set(SupplierCategoryRelationshipPO::getAnnualPurchaseCnt, req.getAnnualPurchaseCnt())
                        .set(SupplierCategoryRelationshipPO::getAnnualPurchaseAmt, req.getAnnualPurchaseAmt())
                        .set(SupplierCategoryRelationshipPO::getStatus, relationshipStatus)
                        .set(SupplierCategoryRelationshipPO::getSource, req.getSource())
                        .set(SupplierCategoryRelationshipPO::getUpdateBy, req.getOperationUserId())
                        .set(SupplierCategoryRelationshipPO::getUpdateName, req.getOperationName())
                        .set(SupplierCategoryRelationshipPO::getUpdateTime, current)
                        .in(SupplierCategoryRelationshipPO::getId, upRelationships)
                        .update();
            }
        } else if (SupplierCategoryRelationShipChangeTypeEnum.REMOVE.equalsCode(req.getChangeType())) {
            // 从数据库删除存在的品类关系，并增加变更记录
            changeRecords.addAll(remove(current, req.getSource(), req.getBusinessNo(), req.getOperationUserId(),
                    req.getOperationName(), req.getRemark(), existRelationships));
        }

        // 保存变更记录
        if (CollectionUtils.isNotEmpty(changeRecords)) {
            supplierCategoryChangeRecordMapperService.saveBatch(changeRecords);
        }
    }

    private void boxUpdateAndNewCategoryRecord(SupplierCategoryRelationshipSaveDTO req,
                                               Map<String, SupplierCategoryRelationshipPO> existRelMap,
                                               List<SupplierCategoryRelationshipPO> newRelationships,
                                               List<Long> upRelationships,
                                               List<SupplierCategoryChangeRecordPO> changeRecords,
                                               Date current, String relationshipStatus) {
        List<String> categoryCodes = req.getCategoryCodes();
        for (String categoryCode : categoryCodes) {
            SupplierCategoryChangeRecordPO changeRecord = BeanUtil.toBean(req, SupplierCategoryChangeRecordPO.class);
            changeRecord.setCategoryCode(categoryCode);
            changeRecord.setQualificationEffectiveTime(current);
            BaseEntityUtil.setCommon(changeRecord, req.getOperationUserId(), req.getOperationName(), current);

            // 如果关系存在，则根据供应商品类资质优先级判断是否更新
            if (existRelMap.containsKey(categoryCode)) {
                // 比较供应商品类资质优先级
                SupplierCategoryRelationshipPO existRel = existRelMap.get(categoryCode);
                if (isHighgerPriority(existRel.getSupplierCategoryStatus(), req.getSupplierCategoryStatus())) {
                    changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.UPDATE.getCode());
                    changeRecords.add(changeRecord);

                    upRelationships.add(existRel.getId());
                }
            } else {
                changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.ADD.getCode());
                changeRecords.add(changeRecord);

                SupplierCategoryRelationshipPO newRel = BeanUtil.toBean(req, SupplierCategoryRelationshipPO.class);
                newRel.setStatus(relationshipStatus);
                newRel.setCategoryCode(categoryCode);
                newRel.setQualificationEffectiveTime(current);
                BaseEntityUtil.setCommon(newRel, req.getOperationUserId(), req.getOperationName(), current);
                newRelationships.add(newRel);
            }
        }
    }

    /**
     * 删除、新增供应商品类关系
     * addItems只会将供应商品类关系的品类资质新增为“合格供应商”
     */
    public void save(SupplierCategoryRelationshipSourceEnum source, String businessNo,
                     Long operateBy, String operateName, String remark,
                     List<SupplierCategoryBatchAdjustApplyOldItemPO> delItems,
                     List<SupplierCategoryBatchAdjustApplyNewItemPO> addItems, Date current) {
        List<SupplierCategoryChangeRecordPO> changeRecords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addItems)) {
            List<SupplierCategoryRelationshipPO> relationshipQrys = addItems.stream().map(addItem -> {
                SupplierCategoryRelationshipPO relationship = new SupplierCategoryRelationshipPO();
                relationship.setSapSupplierCode(addItem.getSapSupplierCode());
                relationship.setCategoryCode(addItem.getCategoryCode());
                return relationship;
            }).collect(Collectors.toList());

            List<SupplierCategoryRelationshipPO> existRelationships = supplierCategoryRelationshipMapperService.list(relationshipQrys);
            Map<String, SupplierCategoryRelationshipPO> existRelMap = existRelationships.stream().collect(
                    Collectors.toMap(SupplierCategoryRelationshipPO::getCategoryCode, Function.identity(), (v1, v2) -> v1));

            List<SupplierCategoryRelationshipPO> newRelationships = new ArrayList<>();
            List<Long> upRelationships = new ArrayList<>();
            boxSaveNewAndUpdateRecord(source, addItems, existRelMap, newRelationships, changeRecords, upRelationships,
                    businessNo, operateBy, operateName, remark, current);

            if (CollectionUtils.isNotEmpty(newRelationships)) {
                supplierCategoryRelationshipMapperService.saveBatch(newRelationships);
            }
            if (CollectionUtils.isNotEmpty(upRelationships)) {
                supplierCategoryRelationshipMapperService.lambdaUpdate()
                        .set(SupplierCategoryRelationshipPO::getSupplierCategoryStatus, SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode())
                        .set(SupplierCategoryRelationshipPO::getQualificationEffectiveTime, current)
                        .set(SupplierCategoryRelationshipPO::getValidityStartTime, null)
                        .set(SupplierCategoryRelationshipPO::getValidityEndTime, null)
                        .set(SupplierCategoryRelationshipPO::getMastPurchaseAmt, null)
                        .set(SupplierCategoryRelationshipPO::getAnnualPurchaseCnt, null)
                        .set(SupplierCategoryRelationshipPO::getAnnualPurchaseAmt, null)
                        .set(SupplierCategoryRelationshipPO::getStatus, SupplierCategoryRelationshipStatusEnum.NORMAL.getCode())
                        .set(SupplierCategoryRelationshipPO::getSource, source.getCode())
                        .set(SupplierCategoryRelationshipPO::getUpdateBy, operateBy)
                        .set(SupplierCategoryRelationshipPO::getUpdateName, operateName)
                        .set(SupplierCategoryRelationshipPO::getUpdateTime, current)
                        .in(SupplierCategoryRelationshipPO::getId, upRelationships)
                        .update();
            }
        }

        if (CollectionUtils.isNotEmpty(delItems)) {
            List<SupplierCategoryRelationshipPO> relationshipQrys = delItems.stream().map(oldItem -> {
                SupplierCategoryRelationshipPO relationship = new SupplierCategoryRelationshipPO();
                relationship.setSapSupplierCode(oldItem.getSapSupplierCode());
                relationship.setCategoryCode(oldItem.getCategoryCode());
                return relationship;
            }).collect(Collectors.toList());

            // 从数据库删除存在的品类关系，并增加变更记录
            List<SupplierCategoryRelationshipPO> existRelationships = supplierCategoryRelationshipMapperService.list(relationshipQrys);
            changeRecords.addAll(remove(current, source.getCode(), businessNo, operateBy, operateName, remark, existRelationships));
        }

        // 保存变更记录
        if (CollectionUtils.isNotEmpty(changeRecords)) {
            supplierCategoryChangeRecordMapperService.saveBatch(changeRecords);
        }
    }

    private void boxSaveNewAndUpdateRecord(SupplierCategoryRelationshipSourceEnum source,
                                           List<SupplierCategoryBatchAdjustApplyNewItemPO> addItems,
                                           Map<String, SupplierCategoryRelationshipPO> existRelMap,
                                           List<SupplierCategoryRelationshipPO> newRelationships,
                                           List<SupplierCategoryChangeRecordPO> changeRecords,
                                           List<Long> upRelationships,
                                           String businessNo,
                                           Long operateBy, String operateName, String remark, Date current
    ) {
        for (SupplierCategoryBatchAdjustApplyNewItemPO newItem : addItems) {
            String categoryCode = newItem.getCategoryCode();
            SupplierCategoryChangeRecordPO changeRecord = BeanUtil.toBean(newItem, SupplierCategoryChangeRecordPO.class);
            changeRecord.setBusinessNo(businessNo);
            changeRecord.setSource(source.getCode());
            changeRecord.setCategoryCode(categoryCode);
            changeRecord.setQualificationEffectiveTime(current);
            changeRecord.setRemark(remark);
            BaseEntityUtil.setCommon(changeRecord, operateBy, operateName, current);

            // 如果关系存在，则根据供应商品类资质优先级判断是否更新
            if (existRelMap.containsKey(categoryCode)) {
                // 比较供应商品类资质优先级
                SupplierCategoryRelationshipPO existRel = existRelMap.get(categoryCode);
                if (isHighgerPriority(existRel.getSupplierCategoryStatus(), newItem.getSupplierCategoryStatus())) {
                    changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.UPDATE.getCode());
                    changeRecords.add(changeRecord);

                    upRelationships.add(existRel.getId());
                }
            } else {
                changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.ADD.getCode());
                changeRecords.add(changeRecord);

                SupplierCategoryRelationshipPO newRel = BeanUtil.toBean(newItem, SupplierCategoryRelationshipPO.class);
                newRel.setSource(source.getCode());
                newRel.setStatus(SupplierCategoryRelationshipStatusEnum.NORMAL.getCode());
                newRel.setCategoryCode(categoryCode);
                newRel.setQualificationEffectiveTime(current);
                BaseEntityUtil.setCommon(newRel, operateBy, operateName, current);
                newRelationships.add(newRel);
            }
        }
    }

    /**
     * 从数据库删除存在的品类关系，返回变更记录
     *
     * @return
     */
    private List<SupplierCategoryChangeRecordPO> remove(Date current, String source, String businessNo,
                                                        Long operateBy, String operateName, String remark,
                                                        List<SupplierCategoryRelationshipPO> existRelationships) {
        if (CollectionUtils.isEmpty(existRelationships)) {
            return new ArrayList<>();
        }

        List<Long> removeIds = existRelationships.stream().map(SupplierCategoryRelationshipPO::getId)
                .collect(Collectors.toList());
        supplierCategoryRelationshipMapperService.hardRemoveByIds(removeIds);

        List<SupplierCategoryChangeRecordPO> changeRecords = new ArrayList<>();
        for (SupplierCategoryRelationshipPO relationship : existRelationships) {
            SupplierCategoryChangeRecordPO changeRecord = BeanUtil.toBean(relationship, SupplierCategoryChangeRecordPO.class);
            changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.REMOVE.getCode());
            changeRecord.setSource(source);
            changeRecord.setBusinessNo(businessNo);
            changeRecord.setSupplierCategoryStatus(relationship.getSupplierCategoryStatus());
            changeRecord.setQualificationEffectiveTime(relationship.getQualificationEffectiveTime());
            changeRecord.setValidityStartTime(relationship.getValidityStartTime());
            changeRecord.setValidityEndTime(relationship.getValidityEndTime());
            changeRecord.setMastPurchaseAmt(relationship.getMastPurchaseAmt());
            changeRecord.setAnnualPurchaseCnt(relationship.getAnnualPurchaseCnt());
            changeRecord.setAnnualPurchaseAmt(relationship.getAnnualPurchaseAmt());
            changeRecord.setRemark(remark);
            BaseEntityUtil.setCommon(changeRecord, operateBy, operateName, current);
            changeRecords.add(changeRecord);
        }
        return changeRecords;
    }

    /**
     * 是否是更高优先级的品类资质
     */
    private boolean isHighgerPriority(String oldSupplierCategoryStatus, String newSupplierCategoryStatus) {
        return SCS_PRIORITY.get(newSupplierCategoryStatus) > SCS_PRIORITY.get(oldSupplierCategoryStatus)
                || (StringUtils.equals(newSupplierCategoryStatus, SupplierCategoryStatusEnum.TMP_STATUS.getCode())
                && StringUtils.equals(oldSupplierCategoryStatus, SupplierCategoryStatusEnum.TMP_STATUS.getCode()));
    }

    public void updateAllSupplierCategoryStatus() {
        List<SupplierCategoryRelationshipPO> list = supplierCategoryRelationshipMapperService.lambdaQuery()
                .eq(SupplierCategoryRelationshipPO::getSupplierCategoryStatus, SupplierCategoryStatusEnum.TMP_STATUS.getCode())
                .in(SupplierCategoryRelationshipPO::getStatus, SupplierCategoryRelationshipStatusEnum.WAIT_NORMAL.getCode(),
                        SupplierCategoryRelationshipStatusEnum.NORMAL.getCode())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierCategoryRelationshipPO> updateList = new ArrayList<>();
        for (SupplierCategoryRelationshipPO supplierCategoryRelationshipPO : list) {
            if (currentDate.after(supplierCategoryRelationshipPO.getValidityEndTime())) {
                supplierCategoryRelationshipPO.setStatus(SupplierCategoryRelationshipStatusEnum.INVALID.getCode());
                updateList.add(supplierCategoryRelationshipPO);
                BaseEntityUtil.setCommonForU(supplierCategoryRelationshipPO, -1L, "schedule", null);
                continue;
            }
            if (!currentDate.before(supplierCategoryRelationshipPO.getValidityStartTime())) {
                supplierCategoryRelationshipPO.setStatus(SupplierCategoryRelationshipStatusEnum.NORMAL.getCode());
                updateList.add(supplierCategoryRelationshipPO);
                BaseEntityUtil.setCommonForU(supplierCategoryRelationshipPO, -1L, "schedule", null);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            supplierCategoryRelationshipMapperService.updateBatchById(updateList);
        }
    }

    private String getSupplierCategoryStatus(SupplierCategoryStatusEnum supplierCategoryStatus, Date validStartDate, Date validEndDate) {
        if (ObjectUtils.isEmpty(supplierCategoryStatus)) {
            log.error("not support supplierCategoryStatus={}", supplierCategoryStatus);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_CATEGORY_STATUS_NOT_SUPPORT));
        }
        if (!supplierCategoryStatus.equalsCode(SupplierCategoryStatusEnum.TMP_STATUS.getCode())) {
            return SupplierCategoryRelationshipStatusEnum.NORMAL.getCode();
        }
        if (ObjectUtils.isEmpty(validEndDate) && ObjectUtils.isEmpty(validStartDate)) {
            log.error("supplierCategoryStatus={} the validStartTime and validEndTime can not be null", supplierCategoryStatus);
        }
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        if (!currentDate.before(validStartDate)) {
            return SupplierCategoryRelationshipStatusEnum.NORMAL.getCode();
        }
        return SupplierCategoryRelationshipStatusEnum.WAIT_NORMAL.getCode();
    }

    @Transactional
    public void exitSupplier(SupplierLeaveRecordDetailPO detailPO, TicketStatusChangedMQ ticketInfo) {
        List<SupplierCategoryChangeRecordPO> changeRecords = supplierCategoryChangeRecordMapperService.lambdaQuery()
                .eq(SupplierCategoryChangeRecordPO::getBusinessNo, ticketInfo.getBusinessNo())
                .eq(SupplierCategoryChangeRecordPO::getChangeType, SupplierCategoryRelationShipChangeTypeEnum.REMOVE.getCode())
                .eq(SupplierCategoryChangeRecordPO::getSapSupplierCode, detailPO.getSupplierCode())
                .list();
        if (CollectionUtils.isNotEmpty(changeRecords)) {
            return;
        }
        List<SupplierCategoryRelationshipPO> categoryRelationshipPOS = supplierCategoryRelationshipMapperService.lambdaQuery().eq(SupplierCategoryRelationshipPO::getSapSupplierCode, detailPO.getSupplierCode()).list();

        // 删除供应商品类关系
        supplierCategoryRelationshipMapperService.removeBySSCAndCC(detailPO.getSupplierCode(), null);
        List<SupplierCategoryChangeRecordPO> newRecords = categoryRelationshipPOS.stream().map(categoryRelationshipPO -> {
            // 保存供应商品类关系变懂记录
            SupplierCategoryChangeRecordPO changeRecord = new SupplierCategoryChangeRecordPO();
            BaseEntityUtil.setCommon(changeRecord, ticketInfo.getOperateBy(), ticketInfo.getOperateName(), null);
            changeRecord.setCategoryCode(categoryRelationshipPO.getCategoryCode());
            changeRecord.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.REMOVE.getCode());
            changeRecord.setSapSupplierCode(detailPO.getSupplierCode());
            changeRecord.setBusinessNo(detailPO.getExitNo());
            changeRecord.setRemark("供应商退出，清空供应商-品类关系");
            changeRecord.setSource(SupplierCategoryRelationshipSourceEnum.SUPPLIER_EXIT.getCode());
            return changeRecord;
        }).collect(Collectors.toList());

        supplierCategoryChangeRecordMapperService.saveBatch(newRecords);
    }

}
