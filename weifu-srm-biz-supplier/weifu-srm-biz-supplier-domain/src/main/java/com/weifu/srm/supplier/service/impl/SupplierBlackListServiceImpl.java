package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.manager.remote.intergration.SAPManager;
import com.weifu.srm.supplier.mq.SupplierBlackListMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListRecordDetailMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierBlackListStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBlackListPO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyCancelReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyListReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListSearchReqDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListApplyDetailRespDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListApplyListRespDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListItemRespDTO;
import com.weifu.srm.supplier.service.SupplierBlackListService;
import com.weifu.srm.supplier.service.biz.black.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierBlackListServiceImpl implements SupplierBlackListService {

    private final SupplierBlackListApplyBiz supplierBlackListApplyBiz;
    private final SupplierBlackListCancelCollectionBiz supplierBlackListCancelCollectionBiz;
    private final SupplierBlackListCancelBiz supplierBlackListCancelBiz;
    private final BlackListDetailBiz blackListDetailBiz;
    private final SupplierBlackListRecordDetailMapperService blackListRecordDetailMapperService;
    private final BlackListApplyHandleBiz blackListApplyHandleBiz;
    private final BlackListApplyCancelHandleBiz blackListApplyCancelHandleBiz;
    private final ScheduleUpdateBlackListValidBiz scheduleUpdateBlackListValidBiz;
    private final BlackListSuggestOutTimeNoticeBiz blackListSuggestOutTimeNoticeBiz;
    private final SupplierBlackListMapperService blackListMapperService;
    private final SAPManager sapManager;

    @Override
    public PageResponse<SupplierBlackListApplyListRespDTO> queryListPage(SupplierBlackListApplyListReqDTO req) {
        Page<SupplierBlackListApplyListReqDTO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<SupplierBlackListApplyListRespDTO> pageResult = blackListRecordDetailMapperService.queryListPage(page, req);
        if(pageResult.getTotal() == 0 ){
            return PageResponse.toResult(req.getPageNum(),req.getPageSize(),0L, null);
        }
        List<SupplierBlackListApplyListRespDTO> records = pageResult.getRecords();
        return PageResponse.toResult(req.getPageNum(),req.getPageSize(),pageResult.getTotal(), records);
    }

    @Override
    public String apply(SupplierBlackListApplyReqDTO req) {
        return supplierBlackListApplyBiz.apply(req);
    }

    @Override
    public String applyCancel(SupplierBlackListApplyCancelReqDTO req) {
       return supplierBlackListCancelBiz.applyCancel(req);
    }

    @Override
    public SupplierBlackListApplyDetailRespDTO queryDetail(String applyNo) {
        return blackListDetailBiz.searchBlackListApplyDetailByApplyNo(applyNo);
    }

    @Override
    public List<SupplierBlackListItemRespDTO> cancelBlackListCollection(List<Integer> limitIds) {
        return supplierBlackListCancelCollectionBiz.cancelBlackListCollection(limitIds);
    }

    @Override
    public List<SupplierBlackListApplyListRespDTO> exportBlackListApplyList(SupplierBlackListApplyListReqDTO req) {
        List<SupplierBlackListApplyListRespDTO> recordList = blackListRecordDetailMapperService.queryList(req);
        if (CollectionUtils.isEmpty(recordList)){
            return recordList;
        }
        for (SupplierBlackListApplyListRespDTO blackListRecord : recordList) {
            blackListRecord.setSupplierStatus(SupplierBasicStatusEnum.getByCode(blackListRecord.getSupplierStatus()).getChineseName());
            blackListRecord.setStatus(SupplierBlackListStatusEnum.getByCode(blackListRecord.getStatus()).getChineseName());
        }
        return recordList;
    }

    @Override
    public void handleBlackListApply(TicketStatusChangedMQ ticketInfo) {
        blackListApplyHandleBiz.handleBlackListApply(ticketInfo);
    }

    @Override
    public void handleBlackListApplyCancel(TicketStatusChangedMQ ticketInfo) {
        blackListApplyCancelHandleBiz.handleBlackListApplyCancel(ticketInfo);
    }
    @Override
    public void scheduleCheckBlackList() {
        scheduleUpdateBlackListValidBiz.processWaitValidData();
    }

    @Override
    public void scheduleCheckBlackListSuggestOutTime() {
        blackListSuggestOutTimeNoticeBiz.blackListSuggestOutTimeNotice();
    }

    @Override
    public void handleSupplierBlackListForSap(SupplierBlackListMQ mq) {
        sapManager.syncSupplierBlackListToSAP(mq.getSupplierCode(), mq.getAddOrRemove().toString());
    }

    @Override
    public List<String> queryBlackListBySupplierCodes(SupplierBlackListSearchReqDTO req) {
        if (CollectionUtils.isEmpty(req.getSupplierCodes())){
            return Collections.emptyList();
        }
        List<SupplierBlackListPO> list = blackListMapperService.lambdaQuery()
                .in(SupplierBlackListPO::getSupplierCode, req.getSupplierCodes())
                .list();
        if (CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream().map(SupplierBlackListPO::getSupplierCode).collect(Collectors.toList());
    }
}
