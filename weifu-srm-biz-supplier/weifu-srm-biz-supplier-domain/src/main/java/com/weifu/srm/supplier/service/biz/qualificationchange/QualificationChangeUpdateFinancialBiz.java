package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeFinancialItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.repository.utils.BizNoGenerateUtil;
import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import com.weifu.srm.supplier.request.QualificationChangeUpdateBasicReqDTO;
import com.weifu.srm.supplier.request.QualificationChangeUpdateCertificationReqDTO;
import com.weifu.srm.supplier.request.QualificationChangeUpdateFinancialReqDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.response.SysUserWithPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeUpdateFinancialBiz {

    private final RedisUtil redisUtil;
    private final IdService idService;
    private final LocaleMessage localeMessage;
    private final KafkaTemplate<String,String> kafkaTemplate;
    private final TransactionTemplate transactionTemplate;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeFinancialItemMapperService qualificationChangeFinancialItemMapperService;
    private final MQServiceManager mqService;
    private final SysUserApi sysUserApi;

    public void updateFinancial(QualificationChangeUpdateFinancialReqDTO reqDTO, String submitSource) {
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getChangeType, QualificationChangeTypeEnum.FINANCIAL_UPDATE.getCode())
                .eq(QualificationChangeApplyPO::getSupplierCode, basicInfoPO.getSapSupplierCode())
                .eq(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.SUBMIT.getCode())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNotEmpty(changeApplyPO)) {
            throw new BizFailException(QualificationChangeCheckoutFinancialEnum.FINANCIAL_ALREADY_EXIST.getContent());
        }
        List<String> bankAccountTypeList = reqDTO.getQualificationChangeUpdateFinancialItemReqDTOS().stream().map(QualificationChangeUpdateFinancialReqDTO.QualificationChangeUpdateFinancialItemReqDTO::getBankAccountType).collect(Collectors.toList());
        if (bankAccountTypeList.contains("49-2") && bankAccountTypeList.contains("99")) {
            throw new BizFailException(QualificationChangeCheckoutFinancialEnum.REPETITION.getContent());
        }
        if (bankAccountTypeList.contains("34") && bankAccountTypeList.contains("99")) {
            throw new BizFailException(QualificationChangeCheckoutFinancialEnum.REPETITION.getContent());
        }
        HashSet<String> bankAccountTypeSet = new HashSet<>(bankAccountTypeList);
        if (bankAccountTypeList.size() != bankAccountTypeSet.size()) {
            throw new BizFailException(QualificationChangeCheckoutFinancialEnum.REPETITION.getContent());
        }
        String qualificationChangeNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.QUALIFICATION_CHANGE.getBizTypeCodee(),idService);
        // 需要存入资质变更申请表
        QualificationChangeApplyPO qualificationChangeApplyPO = new QualificationChangeApplyPO();
        qualificationChangeApplyPO.setQualificationChangeNo(qualificationChangeNo);
        qualificationChangeApplyPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        qualificationChangeApplyPO.setSupplierName(basicInfoPO.getSupplierName());
        qualificationChangeApplyPO.setChangeType(QualificationChangeTypeEnum.FINANCIAL_UPDATE.getCode());
        qualificationChangeApplyPO.setChangeStatus(QualificationChangeStatusEnum.SUBMIT.getCode());
        qualificationChangeApplyPO.setSubmitSource(submitSource);
        qualificationChangeApplyPO.setCreateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setCreateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setCreateTime(new Date());
        qualificationChangeApplyPO.setUpdateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setUpdateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setUpdateTime(new Date());
        qualificationChangeApplyPO.setIsDelete(YesOrNoEnum.NO.getCode());
        List<QualificationChangeFinancialItemPO> qualificationChangeFinancialItemPOS = new ArrayList<>();
        reqDTO.getQualificationChangeUpdateFinancialItemReqDTOS().forEach(qualificationChangeUpdateFinancialItemReqDTO -> {
            QualificationChangeFinancialItemPO updateFinancialPO = qualificationChangeConvert.toUpdateFinancialPO(qualificationChangeUpdateFinancialItemReqDTO);
            updateFinancialPO.setQualificationChangeNo(qualificationChangeNo);
            updateFinancialPO.setIsDelete(YesOrNoEnum.NO.getCode());
            BaseEntityUtil.setCommon(updateFinancialPO,reqDTO.getOperationUserId(),reqDTO.getOperationUser(),new Date());
            qualificationChangeFinancialItemPOS.add(updateFinancialPO);
        });
        transactionTemplate.execute(transactionStatus -> {
            qualificationChangeFinancialItemMapperService.saveBatch(qualificationChangeFinancialItemPOS);
            Map<String,Long> bankCodeMapId = new HashMap<>();
            for (QualificationChangeFinancialItemPO financialItemPO : qualificationChangeFinancialItemPOS) {
                bankCodeMapId.put(financialItemPO.getBankAccount(), financialItemPO.getId());
            }
            // 财务信息附件
            List<AttachmentRecordPO> attachmentRecords = new ArrayList<>();
            reqDTO.getQualificationChangeUpdateFinancialItemReqDTOS().forEach(qualificationChangeUpdateFinancialItemReqDTO -> {
                if (ObjectUtil.isNotEmpty(qualificationChangeUpdateFinancialItemReqDTO.getFinancialAttachmentList())) {
                    AttachmentMessageReqDTO attachmentMessageReqDTO = qualificationChangeUpdateFinancialItemReqDTO.getFinancialAttachmentList().get(0);
                    AttachmentRecordPO po = attachmentMessageConvert.toPO(attachmentMessageReqDTO);
                    po.setBusinessNo(bankCodeMapId.get(qualificationChangeUpdateFinancialItemReqDTO.getBankAccount())+"");
                    po.setBusinessType(AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_FINANCIAL);
                    po.setIsDelete(YesOrNoEnum.NO.getCode());
                    BaseEntityUtil.setCommon(po,reqDTO.getOperationUserId(),reqDTO.getOperationUser(),new Date());
                    attachmentRecords.add(po);
                }
            });
            attachmentRecordMapperService.saveBatch(attachmentRecords);
            qualificationChangeApplyMapperService.save(qualificationChangeApplyPO);
            // 发送MQ给工单系统生成准入邀请审批工单
            CreateTicketMQ createTicketMQ = boxCreateTicketMQ(reqDTO, qualificationChangeNo);
            log.info("start send MQ to ticket service create ticket ={}",createTicketMQ);
            kafkaTemplate.send(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
            log.info("end send MQ to ticket service create ticket ={}",createTicketMQ);
            // 通知对应审批的人
            mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(qualificationChangeApplyPO)));
            return null;
        });
    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(QualificationChangeUpdateFinancialReqDTO reqDTO, String qualificationChangeNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(qualificationChangeNo);
        mq.setTicketType(TicketTypeEnum.FINANCIAL_INFO_ADJUSTMENT.getCode());
        mq.setSupplierId(reqDTO.getSupplierId());
        mq.setSubmitBy(reqDTO.getOperationUserId());
        mq.setSubmitName(reqDTO.getOperationUser());
        return mq;
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(QualificationChangeApplyPO qualificationChangeApplyPO) {
        ApiResponse<SysUserWithPermissionRespDTO> userByUserName = sysUserApi.findUserByUserName("wei.gao");
        if (ObjectUtil.isNull(userByUserName.getData())) {
            return null;
        }
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setBusinessNo(qualificationChangeApplyPO.getQualificationChangeNo());
        notice.setBusinessType(MessageClsEnum.SUPPLIER_FINANCIAL_ADJUST_APPLY.getCode());
        notice.setUserId(userByUserName.getData().getId());
        notice.setUserName("wei.gao");
        notice.setIconType(IconTypeEnum.GENERAL.getCode());
        notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_FINANCIAL.getTitle());
        String content = NoticeTemplateEnum.QUALIFICATION_CHANGE_FINANCIAL.getContent();
        content = content
                .replace("${供应商名称}", qualificationChangeApplyPO.getSupplierName())
                .replace("${供应商编码}", qualificationChangeApplyPO.getSupplierCode());
        notice.setContent(content);
        result.add(notice);
        return result;
    }

}
