package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.integration.response.bpm.SapImportRespDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.manager.SupplierAdmissionAuditRecordManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionCategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.service.SupplierAdmissionService;
import com.weifu.srm.supplier.service.biz.*;
import com.weifu.srm.supplier.service.biz.admission.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/16 15:05
 * @Description 供应商准入接口实现类
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class SupplierAdmissionServiceImpl implements SupplierAdmissionService {

    private final SupplierAdmissionSaveBiz supplierAdmissionSaveBiz;
    private final SupplierAdmissionSubmitBiz supplierAdmissionSubmitBiz;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierAdmissionQueryByCodeBiz supplierAdmissionQueryByCodeBiz;
    private final SupplierAdmissionQueryBySupplierTypeBiz supplierAdmissionQueryBySupplierTypeBiz;
    private final SupplierAdmissionAuditRecordManager supplierAdmissionAuditRecordManager;
    private final LocaleMessage localeMessage;
    private final SupplierAdmissionInvitationInfoMapperService admissionInvitationService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final AdmissionHandleAuditBiz admissionHandleAuditBiz;
    private final SupplierExportToSapBiz supplierExportToSapBiz;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final SupplierAdmissionPageQueryBiz supplierAdmissionPageQueryBiz;
    private final AdmissionUpdateLimitBiz admissionUpdateLimitBiz;
    private final SupplierAdmissionQueryByInvitationNoBiz supplierAdmissionQueryByInvitationNoBiz;
    private final AdmissionQuestionnaireWarehouseQueryBiz admissionQuestionnaireWarehouseQueryBiz;
    private final SupplierAdmissionCategoryManager supplierAdmissionCategoryManager;


    @Override
    public String saveQuestionnaire(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        //查询供应商
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.getById(reqDTO.getSupplierBasicMsgId());
        checkPara(reqDTO,supplierBasicInfoPO);
        return supplierAdmissionSaveBiz.saveQuestionnaire(reqDTO);
    }

    private SupplierAdmissionInvitationRecordPO checkPara(AdmissionQuestionnaireSaveReqDTO reqDTO, SupplierBasicInfoPO supplierBasicInfoPO) {
        if (YesOrNoEnum.NO.equalsCode(reqDTO.getIsAgree())) {
            return null;
        }
        String admissionNo = reqDTO.getAdmissionNo();
        SupplierAdmissionRecordPO po = supplierAdmissionRecordMapperService.findByAdmissionNo(admissionNo);
        String invitationNo = StringUtil.isNullOrEmpty(reqDTO.getInvitationNo()) ? po.getInvitationNo() : reqDTO.getInvitationNo();
        // 查询邀请基础表
        SupplierAdmissionInvitationRecordPO invitationRecordPO = getInvitationRecordPO(invitationNo, admissionNo);
        // 通过邀请单号查询 准入信息 如果准入记录存在pending的数据，则覆盖，如果存在其他状态的数据则报错
        LambdaQueryWrapper<SupplierAdmissionRecordPO> admissionWrapper = new LambdaQueryWrapper<>();
        admissionWrapper.eq(SupplierAdmissionRecordPO::getInvitationNo, invitationNo);
        admissionWrapper.eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierAdmissionRecordPO existsPo = supplierAdmissionRecordMapperService.list(admissionWrapper).stream().findFirst().orElse(null);
        Optional.ofNullable(existsPo).ifPresent(k -> {
            if (!AdmissionStatusEnum.PENDING.getCode().equals(k.getAdmissionStatus())
                    && !AdmissionStatusEnum.REJECT.getCode().equals(k.getAdmissionStatus())) {
                throw new BizFailException(localeMessage.getMessage("supplier.admission.status.error"));
            }
            reqDTO.setAdmissionNo(k.getAdmissionNo());
        });
        if (!StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(po)) {
            throw new BizFailException(localeMessage.getMessage("supplier.admission.no.error"));
        }
        // 入参数校验
        checkNecessaryParams(admissionNo, reqDTO);
        if (StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(supplierBasicInfoPO)) {
            throw new BizFailException(localeMessage.getMessage("supplier.not.exist.error"));
        }
        // 财务信息验证
        checkFinancial(reqDTO);
        // 资质证书验证
        checkCertification(reqDTO);
        // 人员信息验证
        checkPersonal(reqDTO);
        // 公司规模验证
        checkFactoryArea(reqDTO);
       return invitationRecordPO;
    }

    private void checkFactoryArea(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        // 在用厂房面积不能大于 厂房总面积
        List<AdmissionQuestionnaireCompanySizeSaveReqDTO> companySize = reqDTO.getCompanySize();
        if (CollectionUtils.isEmpty(companySize)){
            return;
        }
        for (AdmissionQuestionnaireCompanySizeSaveReqDTO factoryArea : companySize) {
            if (ObjectUtil.isNull(factoryArea.getUsedPlantArea()) || ObjectUtil.isNull(factoryArea.getTotalPlantArea())){
                continue;
            }
            if (factoryArea.getUsedPlantArea().compareTo(factoryArea.getTotalPlantArea()) > 0) {
                throw new BizFailException(localeMessage.getMessage("supplier.admission.factory.area.error"));
            }
        }
    }

    private void checkPersonal(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getPersonnel())) {
            return;
        }
        reqDTO.getPersonnel().forEach(r -> {
            Integer a = r.getManagementPersonnelQty() == null ? 0 : r.getManagementPersonnelQty();
            Integer b = r.getQualityPersonnelQty() == null ? 0 : r.getQualityPersonnelQty();
            Integer c = r.getTechnicalPersonnelQty() == null ? 0 : r.getTechnicalPersonnelQty();
            Integer d = r.getProductionPersonnelQty() == null ? 0 : r.getProductionPersonnelQty();
            Integer e = r.getTotalEmployeesQty() == null ? 0 : r.getTotalEmployeesQty();
            Integer sum = Arrays.asList(a, b, c, d).stream()
                    .mapToInt(Integer::intValue)
                    .sum();
            if (e < sum) {
                throw new BizFailException(localeMessage.getMessage("supplier.admission.personnel.qty.error"));
            }
        });
    }

    private void checkCertification(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        if (!CollUtil.isEmpty(reqDTO.getCertification())) {
            reqDTO.getCertification().forEach(r -> {
                if (r.getCertificationStartDate() != null
                        && r.getCertificationExpiryDate() != null
                        && r.getCertificationStartDate().after(r.getCertificationExpiryDate())) {
                    throw new BizFailException(localeMessage.getMessage("supplier.admission.start.expire.compare.error"));
                }
            });
        }
    }

    private void checkFinancial(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        if (!CollUtil.isEmpty(reqDTO.getFinancial())) {
            reqDTO.getFinancial().forEach(r -> {
                if (r.getNetProfitMarginRatio() != null
                        && r.getGrossProfitMarginRatio() != null
                        && r.getNetProfitMarginRatio().compareTo(r.getGrossProfitMarginRatio()) > 0) {
                    throw new BizFailException(localeMessage.getMessage("supplier.admission.net.gross.compare.error"));
                }
            });
        }
    }

    private void checkNecessaryParams(String admissionNo, AdmissionQuestionnaireSaveReqDTO reqDTO) {
        if (StringUtil.isNullOrEmpty(admissionNo) && StringUtil.isNullOrEmpty(reqDTO.getInvitationNo())) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.no.empty.error"));
        }
        if (StringUtil.isNullOrEmpty(admissionNo) && StringUtil.isNullOrEmpty(reqDTO.getSupplierName())) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.name.empty.error"));
        }
        if (StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(reqDTO.getSupplierBasicMsgId())) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.id.empty.error"));
        }
        if (StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(reqDTO.getAdmissionInvitationBy())) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.by.empty.error"));
        }
        if (StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(reqDTO.getAdmissionType())) {
            throw new BizFailException(localeMessage.getMessage("supplier.admission.type.empty.error"));
        }
        SupplierTypeEnum supplierTypeEnum = SupplierTypeEnum.getByCode(reqDTO.getSupplierType());
        if (ObjectUtil.isNull(supplierTypeEnum)) {
            throw new BizFailException(localeMessage.getMessage("supplier.type.not.exists.error"));
        }
        AdmissionStatusEnum statusEnum = AdmissionStatusEnum.getByCode(reqDTO.getAdmissionStatus());
        if (ObjectUtil.isNull(statusEnum)) {
            throw new BizFailException(localeMessage.getMessage("supplier.admission.status.not.exists.error"));
        }
        AdmissionInvitationTypeEnum admissionInvitationTypeEnum = AdmissionInvitationTypeEnum.getByCode(reqDTO.getAdmissionType());
        if (ObjectUtil.isNull(admissionInvitationTypeEnum)) {
            throw new BizFailException(localeMessage.getMessage("supplier.admission.type.not.exists.error"));
        }
    }


    private SupplierAdmissionInvitationRecordPO getInvitationRecordPO(String invitationNo, String admissionNo) {
        // 查询邀请基础表
        LambdaQueryWrapper<SupplierAdmissionInvitationRecordPO> baseWrapper = new LambdaQueryWrapper<>();
        baseWrapper.eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo);
        baseWrapper.eq(SupplierAdmissionInvitationRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierAdmissionInvitationRecordPO invitationRecordPO = admissionInvitationService.getOne(baseWrapper);
        if (StringUtil.isNullOrEmpty(admissionNo) && ObjectUtil.isNull(invitationRecordPO)) {
            throw new BizFailException(localeMessage.getMessage("supplier.invitation.not.exist"));
        }
        Optional.ofNullable(invitationRecordPO).ifPresent(r -> {
            AdmissionInvitationStatusEnum invitationStatusEnum = AdmissionInvitationStatusEnum.getByCode(invitationRecordPO.getSupplierAdmissionInvitationStatus());
            if (!AdmissionInvitationStatusEnum.INVITATION_STATUS.equals(invitationStatusEnum)
                    && !AdmissionInvitationStatusEnum.AGREE_STATUS.equals(invitationStatusEnum)) {
                throw new BizFailException(localeMessage.getMessage("supplier.invitation.not.exist"));
            }
        });
        return invitationRecordPO;
    }

    @Override
    public String submit(AdmissionQuestionnaireSaveReqDTO reqDTO) {
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.getById(reqDTO.getSupplierBasicMsgId());
        //参数校验
        SupplierAdmissionInvitationRecordPO invitationRecordPO = checkPara(reqDTO,supplierBasicInfoPO);
        //先做保存
        if (YesOrNoEnum.YES.equalsCode(reqDTO.getIsAgree())) {
            checkCategory(reqDTO,supplierBasicInfoPO);
            supplierAdmissionSaveBiz.saveQuestionnaire(reqDTO);
        }
        //再提交意见
        return supplierAdmissionSubmitBiz.submit(reqDTO, invitationRecordPO);
    }

    private void checkCategory(AdmissionQuestionnaireSaveReqDTO reqDTO,SupplierBasicInfoPO supplierBasicInfoPO) {
        // 准入品类限制
        List<SupplierAdmissionCategoryRecordPO> categoryRecords = supplierAdmissionCategoryManager.getSupplierAdmissionCategoryRecordPOs(reqDTO.getInvitationNo());
        List<String> codes = categoryRecords.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList());
        supplierAdmissionCategoryManager.checkCategoryCanOrNotAdmission(reqDTO.getInvitationNo(),codes,supplierBasicInfoPO.getSupplierName(), supplierBasicInfoPO.getId(), reqDTO.getAdmissionType());

    }


    @Override
    public PageResponse<AdmissionQuestionnairePageQueryRespDTO> queryList(AdmissionQuestionnairePageQueryReqDTO req) {
        return supplierAdmissionPageQueryBiz.queryPage(req);

    }

    @Override
    public AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByCode(AdmissionQuestionnaireQueryByCodeReqDTO reqDTO) {
        AdmissionQuestionnaireQueryByCodeRespDTO req = supplierAdmissionQueryByCodeBiz.queryAdmissionByCode(reqDTO.getAdmissionNo());
        setEnum(req);
        return req;
    }

    private static void setEnum(AdmissionQuestionnaireQueryByCodeRespDTO req) {
        Optional<SupplierTypeEnum> supplierTypeEnum = Optional.ofNullable(SupplierTypeEnum.getByCode(req.getSupplierType()));
        supplierTypeEnum.ifPresent(l -> req.setSupplierTypeDesc(l.getChineseName()));
        Optional<AdmissionStatusEnum> admissionStatusEnum = Optional.ofNullable(AdmissionStatusEnum.getByCode(req.getAdmissionStatus()));
        admissionStatusEnum.ifPresent(k -> req.setAdmissionStatusDesc(k.getChineseName()));
        Optional<AdmissionInvitationTypeEnum> admissionTypeEnum = Optional.ofNullable(AdmissionInvitationTypeEnum.getByCode(req.getAdmissionType()));
        admissionTypeEnum.ifPresent(p -> req.setAdmissionTypeDesc(p.getChineseName()));
    }

    @Override
    public AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByInvitationNo(String invitationNo) {
        AdmissionQuestionnaireQueryByCodeRespDTO req = supplierAdmissionQueryByInvitationNoBiz.queryAdmissionByInvitationNo(invitationNo);
        setEnum(req);
        return req;
    }

    @Override
    public AdmissionQuestionnaireFindLatestRespDTO queryAdmissionBySupplierType(AdmissionQuestionnaireQueryBySupplierTypeReqDTO reqDTO) {
        return supplierAdmissionQueryBySupplierTypeBiz.findLatestBySupplierType(reqDTO.getSupplerType(), reqDTO.getSupplerCode());
    }

    @Override
    public SapImportRespDTO exportSupplierToSap(AdmissionQuestionnaireQueryByCodeReqDTO req) {
        return supplierExportToSapBiz.supplierExportToSapBiz(req.getAdmissionNo());
    }


    @Override
    public void saveAppraisalResult(AdmissionAppraisalResultReqDTO reqDTO) {
        List<AttachmentRecordPO> listFile = reqDTO.getListFiles().stream().map(s -> {
            AttachmentRecordPO po = attachmentMessageConvert.toPO(s);
            po.setBusinessNo(reqDTO.getTicketNo());
            po.setBusinessType(AttachmentBizTypeConstants.APPRAISE_RESULT_FILE);
            BaseEntityUtil.setCommon(po, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), null);
            return po;
        }).collect(Collectors.toList());
        attachmentRecordMapperService.saveBatch(listFile);
    }

    @Override
    public void saveGBMResult(AdmissionGBMResultReqDTO reqDTO) {
        supplierAdmissionAuditRecordManager.saveGBMResult(reqDTO);
    }

    @Override
    public AdmissionGBMResultRespDTO getGBMResultByAdmissionNo(String admissionNo, String ticketNo) {
        return supplierAdmissionAuditRecordManager.queryGBMResultByAdmissionNo(admissionNo, ticketNo);
    }


    @Override
    public void admissionHandleAudit(TicketStatusChangedMQ mq) {
        admissionHandleAuditBiz.handleAudit(mq);
    }

    @Override
    public PageResponse<AdmissionQuestionnaireWarehousePageRespDTO> queryWarehouse(AdmissionQuestionnaireWarehousePageQueryReqDTO req) {
        return admissionQuestionnaireWarehouseQueryBiz.queryWarehouse(req);
    }


    @Override
    public List<AttachmentMessageRespDTO> searchAttachmentForAppraisal(String ticketNo) {
        List<AttachmentRecordPO> list = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, ticketNo)
                .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.APPRAISE_RESULT_FILE)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return attachmentMessageConvert.toList(list);
    }

    @Override
    public void updateLimit(AdmissionLimitUpdateReqDTO reqDTO) {
        admissionUpdateLimitBiz.updateLimit(reqDTO);
    }

    @Override
    public List<SupplierAdmissionStatisticsRespDTO> statistics(Long supplierId) {
        return supplierAdmissionRecordMapperService.countByStatus(supplierId);
    }


}
