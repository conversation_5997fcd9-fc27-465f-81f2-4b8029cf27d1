package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.integration.response.bpm.SapImportRespDTO;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 15:02
 * @Description
 * @Version 1.0
 */
public interface SupplierAdmissionService {
    String saveQuestionnaire(AdmissionQuestionnaireSaveReqDTO reqDTO);

    String submit(AdmissionQuestionnaireSaveReqDTO reqDTO);

    PageResponse<AdmissionQuestionnairePageQueryRespDTO> queryList(AdmissionQuestionnairePageQueryReqDTO req);

    AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByCode(AdmissionQuestionnaireQueryByCodeReqDTO reqDTO);

    AdmissionQuestionnaireFindLatestRespDTO queryAdmissionBySupplierType(AdmissionQuestionnaireQueryBySupplierTypeReqDTO reqDTO);

    SapImportRespDTO exportSupplierToSap(AdmissionQuestionnaireQueryByCodeReqDTO req);

    void saveAppraisalResult(AdmissionAppraisalResultReqDTO reqDTO);

    void saveGBMResult(AdmissionGBMResultReqDTO reqDTO);

    AdmissionGBMResultRespDTO getGBMResultByAdmissionNo(String admissionNo, String ticketNo);

    void admissionHandleAudit(TicketStatusChangedMQ mq);

    PageResponse<AdmissionQuestionnaireWarehousePageRespDTO> queryWarehouse(AdmissionQuestionnaireWarehousePageQueryReqDTO req);

    List<AttachmentMessageRespDTO> searchAttachmentForAppraisal(String businessNo);

    void updateLimit(AdmissionLimitUpdateReqDTO reqDTO);

    List<SupplierAdmissionStatisticsRespDTO> statistics(Long supplierId);

    AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByInvitationNo(String invitationNo);
}
