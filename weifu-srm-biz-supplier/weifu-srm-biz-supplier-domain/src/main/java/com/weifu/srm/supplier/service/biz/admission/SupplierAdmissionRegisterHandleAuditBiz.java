package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.supplier.manager.SupplierAdmissionTriggerManager;
import com.weifu.srm.supplier.mq.SupplierAdmissionRegisterCompletedMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAdmissionRegisterHandleAuditBiz {

    private final SupplierAdmissionTriggerManager admissionTriggerManager;
    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;

    public void handleAudit(SupplierAdmissionRegisterCompletedMQ mq){
        List<SupplierAdmissionInvitationRecordPO> list = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierBasicMsgId, mq.getSupplierId())
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.APPROVED_STATUS.getCode())
                .list();
        if (CollectionUtils.isEmpty(list)){
            log.info("非准入列表邀请供应商用户注册数据 不进行处理 = {}",mq);
            return;
        }
        // 触发准入动作
        admissionTriggerManager.completedAdmissionInvitation(null, mq.getSupplierId());
    }
}
