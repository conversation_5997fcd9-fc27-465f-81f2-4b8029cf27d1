package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.supplier.repository.atomicservice.ThresholdChangeRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.ThresholdInfoMapperService;
import com.weifu.srm.supplier.repository.po.ThresholdChangeRecordPO;
import com.weifu.srm.supplier.repository.po.ThresholdInfoPO;
import com.weifu.srm.supplier.response.SupplierAdmissionThresholdInfoResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAdmissionThresholdInfoSearchBiz {

    private final ThresholdChangeRecordMapperService thresholdChangeRecordMapperService;
    private final ThresholdInfoMapperService thresholdInfoMapperService;

    public SupplierAdmissionThresholdInfoResponseDTO select() {
        // 获取当前阈值信息
        ThresholdInfoPO thresholdInfoPO = thresholdInfoMapperService.getOne(null);
        if (thresholdInfoPO == null || thresholdInfoPO.getId() == null) {
            return null;
        }
        SupplierAdmissionThresholdInfoResponseDTO result = new SupplierAdmissionThresholdInfoResponseDTO();
        result.setAnnualPurchaseAmt(thresholdInfoPO.getAnnualPurchaseAmt());
        result.setTransactionPeriod(thresholdInfoPO.getTransactionPeriod());
        result.setAnnualPurchaseCnt(thresholdInfoPO.getAnnualPurchaseCnt());
        result.setMastPurchaseAmt(thresholdInfoPO.getMastPurchaseAmt());
        // 获取阈值变动记录
        List<ThresholdChangeRecordPO> records = thresholdChangeRecordMapperService.list();
        if (CollectionUtils.isNotEmpty(records)) {
            boxChangeList(result, records);
        }
        return result;
    }

    private void boxChangeList(SupplierAdmissionThresholdInfoResponseDTO result, List<ThresholdChangeRecordPO> records) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        records.sort(Comparator.comparingLong(ThresholdChangeRecordPO::getId));
        List<SupplierAdmissionThresholdInfoResponseDTO.ChangeDetail> changeList = records.stream().map(s -> {
            SupplierAdmissionThresholdInfoResponseDTO.ChangeDetail changeDetail = new SupplierAdmissionThresholdInfoResponseDTO.ChangeDetail();
            changeDetail.setModifyTime(sdf.format(s.getCreateTime()));
            changeDetail.setModifyByName(s.getCreateName());
            StringBuilder builder = new StringBuilder();
            boxRangeChangeContent(builder, "更新：样件供应商年度采购次数（笔） ", s.getAnnualPurchaseOldCnt(), s.getAnnualPurchaseNewCnt());
            boxDecimalChangeContent(builder, "更新：样件供应商年度采购金额（元）  ", s.getAnnualPurchaseOldAmt(),s.getAnnualPurchaseNewAmt());
            boxDecimalChangeContent(builder, "更新：样件供应商最大单笔采购金额（元）  ", s.getMastPurchaseOldAmt(),s.getMastPurchaseNewAmt());
            boxRangeChangeContent(builder, "更新：临时供应商交易有效期（月）  ", s.getTransactionOldPeriod(),s.getTransactionNewPeriod());
            changeDetail.setModifyContent(builder.toString());
            return changeDetail;
        }).collect(Collectors.toList());
        result.setChangeList(changeList);
    }

    private StringBuilder boxDecimalChangeContent(StringBuilder builder, String content, BigDecimal oldAmt, BigDecimal newAMt) {
        if (oldAmt == null || newAMt.compareTo(oldAmt) != 0) {
            builder.append(content)
                    .append(oldAmt == null ? "空" : oldAmt)
                    .append("->")
                    .append(newAMt)
                    .append("\r\n");
        }
        return builder;
    }

    private StringBuilder boxRangeChangeContent(StringBuilder builder, String content, Long oldRange, Long newRange) {
        if (oldRange == null || newRange.compareTo(oldRange) != 0) {
            builder.append(content)
                    .append(oldRange == null ? "空" : oldRange)
                    .append("->")
                    .append(newRange)
                    .append("\r\n");
        }
        return builder;
    }
}
