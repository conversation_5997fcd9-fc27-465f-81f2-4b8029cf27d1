package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.request.certification.SupplierCerForPerResDTO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.response.certification.SupplierCerForPerReqDTO;

import java.util.List;

public interface QualificationChangeService {

    QualificationChangeQueryBasicRespDTO queryBasic(QualificationChangeQueryBasicReqDTO reqDTO);

    List<QualificationChangeQueryUserRespDTO> queryUser(QualificationChangeQueryBasicReqDTO reqDTO);

    List<QualificationChangeQueryFinancialRespDTO> queryFinancial(QualificationChangeQueryBasicReqDTO reqDTO);

    QualificationChangeQueryAssociationRespDTO queryAssociation(QualificationChangeQueryBasicReqDTO reqDTO);

    List<QualificationChangeQueryCertificationRespDTO> queryCertification(QualificationChangeQueryBasicReqDTO reqDTO);

    void updateBasic(QualificationChangeUpdateBasicReqDTO reqDTO);

    void updateUser(QualificationChangeUpdateUserReqDTO reqDTO);

    void updateFinancial(QualificationChangeUpdateFinancialReqDTO reqDTO);

    void updateAssociation(QualificationChangeUpdateAssociationReqDTO reqDTO);

    void updateCertification(QualificationChangeUpdateCertificationReqDTO reqDTO);

    PageResponse<QualificationChangeQueryApplyRespDTO> queryApply(QualificationChangeQueryApplyReqDTO reqDTO);

    QualificationChangeQueryBasicUpdateRespDTO queryBasicUpdate(QualificationChangeQueryUpdateReqDTO reqDTO);

    List<QualificationChangeQueryFinancialUpdateRespDTO> queryFinancialUpdate(QualificationChangeQueryUpdateReqDTO reqDTO);

    QualificationChangeQueryAssociationUpdateRespDTO queryAssociationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO);

    QualificationChangeQueryCertificationUpdateRespDTO queryCertificationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO);

    void handleQualificationChangeBasicRst(TicketStatusChangedMQ mq);

    void handleQualificationChangeFinancialRst(TicketStatusChangedMQ mq);

    void handleQualificationChangeAssociationRst(TicketStatusChangedMQ mq);

    void handleQualificationChangeCertificateRst(TicketStatusChangedMQ mq);

    List<SupplierCerForPerReqDTO> findQualityCertificationListBySupplierNotExpiry(SupplierCerForPerResDTO req);
}
