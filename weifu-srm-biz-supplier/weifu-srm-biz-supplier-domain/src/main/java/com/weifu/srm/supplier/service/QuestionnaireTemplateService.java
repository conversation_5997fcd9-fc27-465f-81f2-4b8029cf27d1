package com.weifu.srm.supplier.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.QuestionnaireTemplateReqDTO;
import com.weifu.srm.supplier.request.QuestionnaireTemplateSaveReqDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateDetailRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateFieldsRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
public interface QuestionnaireTemplateService {

    PageResponse<QuestionnaireTemplateRespDTO> queryList(QuestionnaireTemplateReqDTO questionnaireTemplateReqDTO);

    List<QuestionnaireTemplateFieldsRespDTO> tempLateSampleFields();

    String saveTemplate(QuestionnaireTemplateSaveReqDTO saveReqDTO);

    QuestionnaireTemplateDetailRespDTO queryTemplateFieldsByCode(String templateCode);

    Boolean deleteTemplateByCode(List<String> templateCode);

    String  enableTemplate(String templateCode,Integer isEnable);
    String  makeDefaultTemplate(String templateCode,Integer isEnable);



}
