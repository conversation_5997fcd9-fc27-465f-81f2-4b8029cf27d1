package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.mq.SupplierLeavedMQ;
import com.weifu.srm.supplier.request.leave.SupplierLeaveApplyListReqDTO;
import com.weifu.srm.supplier.request.leave.SupplierLeaveExistReqDTO;
import com.weifu.srm.supplier.request.leave.SupplierLeaveWaitExitReqDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveApplyListRespDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveDetailRespDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveItemRespDTO;

import java.util.List;

public interface SupplierLeaveService {


    PageResponse<SupplierLeaveApplyListRespDTO> queryListPage( SupplierLeaveApplyListReqDTO req);


    String applyWaiting(SupplierLeaveWaitExitReqDTO req);

    String applyLeave( SupplierLeaveExistReqDTO req);

    SupplierLeaveDetailRespDTO queryDetail(String applyNo);

    List<SupplierLeaveItemRespDTO> leaveCollection(List<Integer> limitIds);

    List<SupplierLeaveApplyListRespDTO> exportLeaveApplyList(SupplierLeaveApplyListReqDTO req);

    void handWaitLeLeaveApply(TicketStatusChangedMQ ticketInfo);

    void handleLeaveApply(TicketStatusChangedMQ ticketInfo);

    void scheduleCheckWaitLeave();

    void handleLeaved(SupplierLeavedMQ mq);

    void handleSupplierLeaveForSap(SupplierLeavedMQ mq);

}
