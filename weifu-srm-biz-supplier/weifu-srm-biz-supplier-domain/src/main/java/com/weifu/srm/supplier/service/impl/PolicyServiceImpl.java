package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.audit.response.TicketInfoRespDTO;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.masterdata.response.CurrencyRespDTO;
import com.weifu.srm.masterdata.response.DictRespDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.supplier.constants.LanguageConstant;
import com.weifu.srm.supplier.constants.StringConstant;
import com.weifu.srm.supplier.enums.AttachmentTypeEnum;
import com.weifu.srm.supplier.enums.policy.*;
import com.weifu.srm.supplier.manager.*;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.manager.remote.user.SysDivisionManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.po.BusinessPolicyApplyDetailPO;
import com.weifu.srm.supplier.repository.po.BusinessPolicyApplyMainPO;
import com.weifu.srm.supplier.repository.po.BusinessPolicyListPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.utils.BaseUserUtils;
import com.weifu.srm.supplier.service.biz.PolicyBiz;
import com.weifu.srm.supplier.utils.CommonUtils;
import com.weifu.srm.supplier.utils.ValidationUtils;
import com.weifu.srm.supplier.request.policy.*;
import com.weifu.srm.supplier.request.policy.validation.DefaultGroup;
import com.weifu.srm.supplier.response.policy.PolicyAdjustDetailRespDTO;
import com.weifu.srm.supplier.response.policy.PolicyAdjustMainRespDTO;
import com.weifu.srm.supplier.response.policy.PolicyListExportRespDTO;
import com.weifu.srm.supplier.response.policy.PolicyListRespDTO;
import com.weifu.srm.supplier.service.PolicyService;
import com.weifu.srm.supplier.response.SupplierBasicSimpleInfoRespDTO;
import com.weifu.srm.user.enums.DataPermissionKeyEnum;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.division.SysDivisionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PolicyServiceImpl implements PolicyService {

    /**
     *商务政策列表
     */
    private static final String PC_INTERNAL_PAGE_BUSINESS_POLICY_MANAGE = "PC_INTERNAL_PAGE_BUSINESS_POLICY_MANAGE";

    /**
     *商务政策申请列表
     */
    private static final String PC_INTERNAL_PAGE_BUSINESS_POLICY_APPLY_LIST = "PC_INTERNAL_PAGE_BUSINESS_POLICY_APPLY_LIST";

    private final BusinessPolicyApplyDetailMapperService detailService;
    private final MasterDataManager masterDataManager;
    private final FSSCManager fsscManager;
    private final BusinessPolicyListMapperService policyListService;
    private final SysDivisionManager divisionManager;
    private final BusinessPolicyApplyMainMapperService applyMainService;
    private final AttachmentRecordMapperService attachmentService;
    private final MqManager mqManager;
    private final UserManager userManager;
    private final LocaleMessage localeMessage;
    private final BaseManager baseManager;
    private final SupplierBasicInfoMapperService supplierBasicService;
    private final DataPermissionManager dataPermissionManager;
    private final PolicyBiz policyBiz;



    @Override
    public PageResponse<PolicyListRespDTO> policyList(QueryPolicyListReqDTO param) {
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(param.getUserId(), PC_INTERNAL_PAGE_BUSINESS_POLICY_MANAGE);
        if(BooleanUtil.isTrue(dataPermission.isNo()))
            return PageResponse.toResult(param.getPageNum(),param.getPageSize(),0L,Collections.emptyList());
        if(!BooleanUtil.isTrue(dataPermission.isAll())){
            for (DataPermissionRespDTO.DataPermissionKeyRespDTO v : dataPermission.getKeys()) {
                if(CharSequenceUtil.equalsIgnoreCase(v.getKey(), DataPermissionKeyEnum.MANAGED_CATEGORY.getKey()))
                    param.setDataPermissionsSupplierCodes(policyBiz.getCategoryRelationSupplier(param.getUserId()));
                if(CharSequenceUtil.equalsIgnoreCase(v.getKey(), DataPermissionKeyEnum.BELONG_DIVISION.getKey()))
                    param.setDataPermissionDivisionCodes(CollUtil.isNotEmpty(v.getDivisionIds()) ? v.getDivisionIds() : StringConstant.DEFAULT_LIST);
            }
        }
        PageResponse<PolicyListRespDTO> result = policyListService.policyList(param);
        //处理其它数据
        this.handleOtherData(result.getList());

        return result;
    }

    @Override
    public Boolean removePolicy(List<Long> ids) {
        List<BusinessPolicyApplyDetailPO> list = detailService.listByIds(ids);
        if (!CommonUtils.equalsSize(ids, list))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DATA_NOT_EXISTS));

        Boolean statusOk = list.stream()
                .allMatch(v -> PolicyStatusEnum.DRAFT.getCode().equals(v.getStatus()));
        if (!statusOk)
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_REMOVE_STATUS));

        return detailService.removeByIds(ids);
    }

    @Override
    public List<PolicyListExportRespDTO> policyListExport(QueryPolicyListReqDTO param) {
        PageResponse<PolicyListRespDTO> result = this.policyList(param);

        List<PolicyListRespDTO> list = result.getList();
        if (CollUtil.isEmpty(list)) return Collections.emptyList();

        List<PolicyListExportRespDTO> exportList = BeanUtil.copyToList(list, PolicyListExportRespDTO.class);
        //付款基准日期类型
        List<String> paymentBaseDateTypeList = exportList.stream().filter(v -> CharSequenceUtil.isNotEmpty(v.getPaymentBaseDateType()))
                .map(PolicyListExportRespDTO::getPaymentBaseDateType)
                .distinct()
                .collect(Collectors.toList());
        Map<String, DictRespDTO> dictMap = masterDataManager.listDictMap(StringConstant.DICT_PAYMENT_BASE_DATE_TYPE, paymentBaseDateTypeList);
        //币种
        List<String> currencyCodes = exportList.stream().filter(v -> CharSequenceUtil.isNotBlank(v.getCurrency())).map(v -> v.getCurrency()).distinct().collect(Collectors.toList());
        Map<String, CurrencyRespDTO> currencyMap = masterDataManager.listCurrencyMap(currencyCodes);
        exportList.forEach(v -> {
            v.setCurrencyName(v.getCurrency());

            DictRespDTO dictValue = CommonUtils.getMapValue(dictMap, v.getPaymentBaseDateType());
            if (Objects.nonNull(dictValue)) v.setPaymentBaseDateTypeName(dictValue.getDataName());

            CurrencyRespDTO currency = CommonUtils.getMapValue(currencyMap,v.getCurrency());
            if(Objects.nonNull(currency)) v.setCurrencyName(currency.getCurrencyName());
        });

        return exportList;
    }

    @Override
    public List<NameValueSimpleRespDTO> queryConditionByKeyword(QueryByKeywordReqDTO param) {
        if (Objects.isNull(param.getType())) return Collections.emptyList();
        LambdaQueryWrapper<BusinessPolicyListPO> query = new LambdaQueryWrapper<>();

        //查供应商
        if (Objects.equals(1, param.getType())) {
            query.select(BusinessPolicyListPO::getSapSupplierCode, BusinessPolicyListPO::getSupplierName);
            query.and(CharSequenceUtil.isNotEmpty(param.getKeyword()),
                    wp -> wp.like(BusinessPolicyListPO::getSapSupplierCode, param.getKeyword())
                            .or().like(BusinessPolicyListPO::getSupplierName, param.getKeyword()));

            List<BusinessPolicyListPO> list = policyListService.list(query);
            if (CollUtil.isEmpty(list)) return Collections.emptyList();
            return list.stream()
                    .map(v -> new NameValueSimpleRespDTO(v.getSapSupplierCode(), v.getSupplierName()))
                    .distinct().collect(Collectors.toList());
        } else if (Objects.equals(2, param.getType())) {
            //查事业部
            query.select(BusinessPolicyListPO::getDivisionCode, BusinessPolicyListPO::getDivisionName);
            query.and(CharSequenceUtil.isNotEmpty(param.getKeyword()),
                    wp -> wp.like(BusinessPolicyListPO::getDivisionCode, param.getKeyword())
                            .or().like(BusinessPolicyListPO::getDivisionName, param.getKeyword()));

            List<BusinessPolicyListPO> list = policyListService.list(query);
            if (CollUtil.isEmpty(list)) return Collections.emptyList();
            return list.stream()
                    .map(v -> new NameValueSimpleRespDTO(v.getDivisionCode(), v.getDivisionName()))
                    .distinct().collect(Collectors.toList());
        } else if (Objects.equals(3, param.getType())) {
            //查业务小类
            query.select(BusinessPolicyListPO::getBusinessSubclassCode, BusinessPolicyListPO::getBusinessSubclassName);
            query.and(CharSequenceUtil.isNotEmpty(param.getKeyword()),
                    wp -> wp.like(BusinessPolicyListPO::getBusinessSubclassCode, param.getKeyword())
                            .or().like(BusinessPolicyListPO::getBusinessSubclassName, param.getKeyword()));

            List<BusinessPolicyListPO> list = policyListService.list(query);
            if (CollUtil.isEmpty(list)) return Collections.emptyList();
            return list.stream()
                    .map(v -> new NameValueSimpleRespDTO(v.getBusinessSubclassCode(), v.getBusinessSubclassName()))
                    .distinct().collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<PolicyListRespDTO> listDetailByIds(List<Long> ids) {
        List<BusinessPolicyListPO> list = policyListService.listByIds(ids);
        return BeanUtil.copyToList(list, PolicyListRespDTO.class);
    }

    @Override
    public Boolean importFSSC(PolicyDetailSimpleReqDTO param) {
        List<BusinessPolicyListPO> list = policyListService.listByIds(param.getIds());
        if (!CommonUtils.equalsSize(list, param.getIds()))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DATA_NOT_EXISTS));

        Boolean validStatus = list.stream()
                .allMatch(v -> CharSequenceUtil.equals(ImportStatusEnum.IMPORT_FAIL.getCode(), v.getImportStatus())
                );
        if (!validStatus)
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_IMPORT_FSSC_STATUS));

        Date now = new Date();
        //设置提交人域账号
        String submitDomain = this.getSubmitDomain(param.getUserId());

        for (BusinessPolicyListPO v : list) {
            v.setUpdateTime(now);

            if (Objects.nonNull(param.getUserId())) {
                v.setUpdateBy(param.getUserId());
                v.setUpdateName(param.getUserRealName());
            }

            this.importPolicyToFSSC(v, now, submitDomain);
        }
        //更新导入结果数据
        policyListService.updateBatchById(list);

        return true;
    }

    @Override
    public List<PolicyAdjustDetailRespDTO> batchImport(List<PolicyListImportReqDTO> param) {
        if (CollUtil.isEmpty(param))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_NOT_DATA));

        List<PolicyAdjustDetailReqDTO> details = BeanUtil.copyToList(param, PolicyAdjustDetailReqDTO.class);

        Map<String, SupplierBasicSimpleInfoRespDTO> supplierMap = this.getSupplierSimpleMap(
                CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getSapSupplierCode, details));
        Map<String, SysDivisionRespDTO> divisionMap = divisionManager.getMapByDivisionCode(
                CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getDivisionCode, details));
        Map<String, DictRespDTO> businessSubClassMap = masterDataManager.listDictMap(StringConstant.DICT_FINANCIAL_BUSINESS_TYPE);
        List<DictRespDTO> paymentBaseDateTypeList = masterDataManager.listDict(StringConstant.DICT_PAYMENT_BASE_DATE_TYPE);
        Map<String, DictRespDTO> paymentBaseDateTypeMap = CommonUtils.getMapByList(DictRespDTO::getDataName, paymentBaseDateTypeList);
        Map<String, CurrencyRespDTO> currencyMap = masterDataManager.listCurrencyMap(
                CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getCurrency, details)
        );

        for (PolicyAdjustDetailReqDTO v : details) {
            //设置供应商 事业部 业务大小类相关数据
            handlePolicyDetail(v, supplierMap, divisionMap, businessSubClassMap, paymentBaseDateTypeMap, currencyMap);
            //设置默认值
            v.initDefault();
        }

        //校验数据
        this.verifyDetails(details);

        return BeanUtil.copyToList(details, PolicyAdjustDetailRespDTO.class);
    }

    private void handlePolicyDetail(PolicyAdjustDetailReqDTO v,
                                    Map<String, SupplierBasicSimpleInfoRespDTO> supplierMap,
                                    Map<String, SysDivisionRespDTO> divisionMap,
                                    Map<String, DictRespDTO> businessSubClassMap,
                                    Map<String, DictRespDTO> paymentBaseDateTypeMap,
                                    Map<String, CurrencyRespDTO> currencyMap) {
        SupplierBasicSimpleInfoRespDTO supplier = CommonUtils.getMapValue(supplierMap, v.getSapSupplierCode());
        if (CharSequenceUtil.isNotBlank(v.getSapSupplierCode()) && Objects.isNull(supplier))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_SUPPLIER_NOT_EXISTS, new String[]{v.getSapSupplierCode()}));
        if (Objects.nonNull(supplier))
            v.setSupplierName(supplier.getSupplierName());

        SysDivisionRespDTO division = CommonUtils.getMapValue(divisionMap, v.getDivisionCode());
        if (CharSequenceUtil.isNotBlank(v.getDivisionCode()) && Objects.isNull(division))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DIVISION_NOT_EXISTS, new String[]{v.getDivisionCode()}));
        if (Objects.nonNull(division))
            v.setDivisionName(division.getDivisionName());

        DictRespDTO businessSubClass = CommonUtils.getMapValue(businessSubClassMap, v.getBusinessSubclassCode());
        if (CharSequenceUtil.isNotBlank(v.getBusinessSubclassCode()) && Objects.isNull(businessSubClass))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_SUBCLASS_NOT_EXISTS, new String[]{v.getBusinessSubclassCode()}));
        if (Objects.nonNull(businessSubClass)) {
            v.setBusinessSubclassName(businessSubClass.getDataName());
            DictRespDTO parent = businessSubClass.getParent();
            if (Objects.nonNull(parent)) {
                v.setBusinessCategoryCode(parent.getDataValue());
                v.setBusinessCategoryName(parent.getDataName());
            }
        }

        handlePolicyDetailOther(v, paymentBaseDateTypeMap, currencyMap);
    }

    private void handlePolicyDetailOther(PolicyAdjustDetailReqDTO v, Map<String, DictRespDTO> paymentBaseDateTypeMap, Map<String, CurrencyRespDTO> currencyMap) {
        DictRespDTO paymentBaseDateType = CommonUtils.getMapValue(paymentBaseDateTypeMap, v.getPaymentBaseDateTypeName());
        if (CharSequenceUtil.isNotBlank(v.getPaymentBaseDateTypeName()) && Objects.isNull(paymentBaseDateType))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_PAYMENTBASEDATETYPE_NOT_EXISTS, new String[]{v.getPaymentBaseDateTypeName()}));
        if (Objects.nonNull(paymentBaseDateType)) {
            v.setPaymentBaseDateType(paymentBaseDateType.getDataValue());
        }

        CurrencyRespDTO currency = CommonUtils.getMapValue(currencyMap, v.getCurrency());
        if (CharSequenceUtil.isNotBlank(v.getCurrency()) && Objects.isNull(currency))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_CURRENCY_NOT_EXISTS, new String[]{v.getCurrency()}));
    }

    @Override
    public Boolean importPolicyToFSSC(BusinessPolicyListPO v, Date updateTime, String submitDomain) {
        ImportStatusEnum importStatusEnum = ImportStatusEnum.IMPORT_SUCCESS;
        String importFeedback = null;
        Boolean isImportSuccess = false;
        try {
            if (!BooleanUtil.isTrue(fsscManager.importFSSC(v, submitDomain))) {
                importStatusEnum = ImportStatusEnum.IMPORT_FAIL;
                importFeedback = "Import Fail!";
            }else{
                isImportSuccess = true;
            }
        } catch (Exception e) {
            importStatusEnum = ImportStatusEnum.IMPORT_FAIL;
            importFeedback = e.getMessage();
        } finally {
            v.setImportStatus(importStatusEnum.getCode());
            v.setImportFeedback(importFeedback);
            v.setIsImportSuccess(isImportSuccess);
        }

        return isImportSuccess;
    }

    @Override
    public Boolean savePolicyByNew(PolicyAdjustMainReqDTO param) {
        //校验数据
        this.verifyDetails(param.getDetails());

        //处理数据
        this.dealDetails(param.getDetails());

        List<BusinessPolicyApplyDetailPO> list = BeanUtil.copyToList(param.getDetails(), BusinessPolicyApplyDetailPO.class);
        list.forEach(v -> {
            v.setStatus(PolicyStatusEnum.DRAFT.getCode());
            v.setType(PolicyChangeTypeEnum.NEW_CREATE.getCode());
            v.setPolicyNo(detailService.generatePolicyNo());
        });
        BaseUserUtils.setCreateUserInfo(list, param);

        if (!detailService.saveBatch(list))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_SAVE_FAIL));

        return true;
    }

    @Override
    public Boolean savePolicyByEdit(PolicyAdjustMainReqDTO param) {
        //校验数据
        this.verifyDetails(param.getDetails());

        //处理数据
        this.dealDetails(param.getDetails());

        List<Long> ids = param.getDetails().stream().filter(v->Objects.nonNull(v.getId())).map(v->v.getId()).collect(Collectors.toList());
        if (!CommonUtils.equalsSize(ids, param.getDetails()))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_ID_NOT_NULL));

        List<BusinessPolicyApplyDetailPO> list = detailService.listByIds(ids);
        if (!CommonUtils.equalsSize(list, param.getDetails()))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DATA_NOT_EXISTS));
        //判断状态
        Boolean verifyStatus = list.stream()
                .allMatch(v -> CharSequenceUtil.equals(v.getStatus(), PolicyStatusEnum.DRAFT.getCode()));
        if (!verifyStatus)
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_REMOVE_STATUS));

        Map<Long, PolicyAdjustDetailReqDTO> detailMap = CommonUtils.getMapByList(PolicyAdjustDetailReqDTO::getId, param.getDetails());
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(false);
        copyOptions.setIgnoreProperties(BusinessPolicyApplyDetailPO::getId, BusinessPolicyApplyDetailPO::getApplyNo,BusinessPolicyApplyDetailPO::getPolicyNo,
                BusinessPolicyApplyDetailPO::getType, BusinessPolicyApplyDetailPO::getCreateBy, BusinessPolicyApplyDetailPO::getCreateName,
                BusinessPolicyApplyDetailPO::getCreateTime);
        list.forEach(v -> {
            v.setStatus(PolicyStatusEnum.DRAFT.getCode());
            v.setType(PolicyChangeTypeEnum.NEW_CREATE.getCode());
            BeanUtil.copyProperties(CommonUtils.getMapValue(detailMap, v.getId()), v, copyOptions);
        });
        BaseUserUtils.setUpdateUserInfo(list, param);

        return detailService.updateBatchById(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitPolicy(PolicyAdjustMainReqDTO param) {
        //数据校验
        this.verifySubmitPolicy(param);
        //处理数据
        this.dealDetails(param.getDetails());
        //保存主信息
        String applyNo = applyMainService.saveApply(param,PolicyStatusEnum.APPROVING);
        //保存附件数据
        attachmentService.saveAttachments(param.getAttachments(), applyNo, AttachmentTypeEnum.BUSINESS_POLICY.getCode(), param);
        //保存申请明细数据
        detailService.savePolicy(param);
        //发送工单
        this.sendTicket(param);

        return applyNo;
    }

    @Override
    public PolicyAdjustMainRespDTO getApplyDetail(String applyNo) {
        PolicyAdjustMainRespDTO result = applyMainService.getByApplyNo(applyNo);
        if (Objects.isNull(result)) return null;
        result.setAttachments(attachmentService.listAttachments(applyNo, AttachmentTypeEnum.BUSINESS_POLICY.getCode()));
        result.setDetails(detailService.getByApplyNo(applyNo));

        return result;
    }

    @Override
    public BusinessPolicyApplyMainPO getApply(String applyNo) {
        BusinessPolicyApplyMainPO rs = applyMainService.getByNo(applyNo);
        if (Objects.isNull(rs)) return null;
        rs.setApplyDetails(detailService.listByApplyNo(applyNo));

        return rs;
    }

    @Override
    public void updateApply(BusinessPolicyApplyMainPO param) {
        applyMainService.updateById(param);
        if (CollUtil.isNotEmpty(param.getApplyDetails())) {
            param.getApplyDetails().forEach(v -> {
                v.setStatus(param.getStatus());
                v.setUpdateBy(param.getUpdateBy());
                v.setUpdateName(param.getUpdateName());
                v.setUpdateTime(param.getUpdateTime());
            });
            detailService.updateBatchById(param.getApplyDetails());
        }
    }

    @Override
    public void updatePolicyList(List<BusinessPolicyListPO> list) {
        policyListService.updateBatchById(list);
    }

    @Override
    public List<BusinessPolicyListPO> listByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) return null;

        return policyListService.listByIds(ids);
    }

    @Override
    public String getSubmitDomain(Long userId) {
        BaseSysUserRespDTO user = userManager.getByUserId(userId);
        return Objects.nonNull(user) ? user.getDomain() : null;
    }

    @Override
    public void changePolicyListImportStatus(List<Long> ids, ImportStatusEnum importStatus) {
        if(CollUtil.isEmpty(ids) || Objects.isNull(importStatus)) return;

        LambdaUpdateWrapper<BusinessPolicyListPO> edit = new LambdaUpdateWrapper<>();
        edit.in(BusinessPolicyListPO::getId,ids);
        edit.set(BusinessPolicyListPO::getImportStatus,importStatus.getCode());

        policyListService.update(edit);
    }

    @Override
    public void sendImportFailMsg(BusinessPolicyListPO policy,String businessNo,Long userId,String username){
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setIconType(IconTypeEnum.GENERAL.getCode());

        notice.setBusinessType(MessageClsEnum.BUSINESS_POLICY_IMPORT_TO_FSSC_FAILED.getCode());
        notice.setBusinessNo(businessNo);
        notice.setUserId(userId);
        notice.setUserName(username);
        notice.setTitle(SitMessageTemplateEnum.POLICY_IMPORT_FAIL.getTitle());

        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("businessNo",businessNo);
        templateParam.put("sapSupplierCode", policy.getSapSupplierCode());
        templateParam.put("divisionCode", policy.getDivisionCode());
        templateParam.put("businessSubclassCode", policy.getBusinessSubclassCode());

        notice.setContent(StrUtil.format(SitMessageTemplateEnum.POLICY_IMPORT_FAIL.getTemplate(), templateParam));

        mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(notice));
    }

    @Override
    public PageResponse<PolicyAdjustDetailRespDTO> policyApplyDetailList(QueryPolicyApplyReqDTO param) {
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(param.getUserId(), PC_INTERNAL_PAGE_BUSINESS_POLICY_APPLY_LIST);
        if(BooleanUtil.isTrue(dataPermission.isNo()))
            return PageResponse.toResult(param.getPageNum(),param.getPageSize(),0L,Collections.emptyList());
        if(!BooleanUtil.isTrue(dataPermission.isAll())){
            for (DataPermissionRespDTO.DataPermissionKeyRespDTO v : dataPermission.getKeys()) {
                if(CharSequenceUtil.equalsIgnoreCase(v.getKey(), DataPermissionKeyEnum.MANAGED_CATEGORY.getKey()))
                    param.setDataPermissionsSupplierCodes(policyBiz.getCategoryRelationSupplier(param.getUserId()));
                if(CharSequenceUtil.equalsIgnoreCase(v.getKey(), DataPermissionKeyEnum.BELONG_DIVISION.getKey()))
                    param.setDataPermissionDivisionCodes(CollUtil.isNotEmpty(v.getDivisionIds()) ? v.getDivisionIds() : StringConstant.DEFAULT_LIST);
            }
        }

        List<String> businessNos = null;
        if (CharSequenceUtil.isNotEmpty(param.getTicketNo())) {
            List<TicketInfoRespDTO> tickets = baseManager.listByLikeTicketNo(param.getTicketNo());
            if (CollUtil.isEmpty(tickets))
                return PageResponse.toResult(param.getPageNum(), param.getPageSize(), 0L, Collections.emptyList());
            businessNos = tickets.stream().map(v -> v.getBusinessNo()).collect(Collectors.toList());
        }

        LambdaQueryWrapper<BusinessPolicyApplyDetailPO> query = new LambdaQueryWrapper<>();
        query.like(CharSequenceUtil.isNotEmpty(param.getPolicyNo()),BusinessPolicyApplyDetailPO::getPolicyNo, param.getPolicyNo());
        query.in(CollUtil.isNotEmpty(businessNos), BusinessPolicyApplyDetailPO::getApplyNo, businessNos);
        query.eq(CharSequenceUtil.isNotEmpty(param.getType()), BusinessPolicyApplyDetailPO::getType, param.getType());
        query.eq(CharSequenceUtil.isNotEmpty(param.getStatus()), BusinessPolicyApplyDetailPO::getStatus, param.getStatus());
        query.like(CharSequenceUtil.isNotEmpty(param.getCreateName()), BusinessPolicyApplyDetailPO::getCreateName, param.getCreateName());
        query.ge(Objects.nonNull(param.getCreateStartTime()), BusinessPolicyApplyDetailPO::getCreateTime, param.getCreateStartTime());
        query.le(Objects.nonNull(param.getCreateEndTime()), BusinessPolicyApplyDetailPO::getCreateTime, param.getCreateEndTime());
        //数据权限
        /*query.and(CommonUtils.anyNotEmpty(param.getDataPermissionsSupplierCodes(),param.getDataPermissionDivisionCodes()),wp->{
            wp.or().in(CollUtil.isNotEmpty(param.getDataPermissionsSupplierCodes()),BusinessPolicyApplyDetailPO::getSapSupplierCode,param.getDataPermissionsSupplierCodes());
            wp.or().in(CollUtil.isNotEmpty(param.getDataPermissionDivisionCodes()),BusinessPolicyApplyDetailPO::getDivisionCode,param.getDataPermissionDivisionCodes());
        });*/
        query.and(wp -> {
            wp.or(CommonUtils.anyNotEmpty(param.getDataPermissionsSupplierCodes(), param.getDataPermissionDivisionCodes()), w -> {
                w.or().in(CollUtil.isNotEmpty(param.getDataPermissionsSupplierCodes()), BusinessPolicyApplyDetailPO::getSapSupplierCode, param.getDataPermissionsSupplierCodes());
                w.or().in(CollUtil.isNotEmpty(param.getDataPermissionDivisionCodes()), BusinessPolicyApplyDetailPO::getDivisionCode, param.getDataPermissionDivisionCodes());
            });
            wp.or().eq(BusinessPolicyApplyDetailPO::getCreateBy, param.getUserId()); // 添加创建人是当前登录人的条件
        });

        query.orderByDesc(BusinessPolicyApplyDetailPO::getCreateTime);

        Page<BusinessPolicyApplyDetailPO> page = detailService.page(new Page<>(param.getPageNum(), param.getPageSize()), query);
        if (CollUtil.isEmpty(page.getRecords()))
            return PageResponse.toResult(param.getPageNum(), param.getPageSize(), 0L, Collections.emptyList());

        List<String> applyNos = page.getRecords().stream().filter(v -> CharSequenceUtil.isNotBlank(v.getApplyNo())).map(v -> v.getApplyNo()).collect(Collectors.toList());
        Map<String, TicketInfoRespDTO> ticketMap = baseManager.getByBusinessNo(applyNos, TicketTypeEnum.BUSINESS_POLICY_ADD, TicketTypeEnum.BUSINESS_POLICY_ADJUSTMENT);

        List<PolicyAdjustDetailRespDTO> result = BeanUtil.copyToList(page.getRecords(), PolicyAdjustDetailRespDTO.class);
        for (PolicyAdjustDetailRespDTO v : result) {
            TicketInfoRespDTO ticket = CommonUtils.getValue(ticketMap, v.getApplyNo());
            v.setTicketNo(Objects.nonNull(ticket) ? ticket.getTicketNo() : null);
        }

        return PageResponse.toResult(param.getPageNum(), param.getPageSize(), page.getTotal(), result);
    }

    @Override
    public List<PolicyAdjustDetailRespDTO> listApplyDetailByIds(PolicyDetailSimpleReqDTO param) {
        if(CollUtil.isEmpty(param.getIds()))
            return Collections.emptyList();
        return BeanUtil.copyToList(detailService.listByIds(param.getIds()),PolicyAdjustDetailRespDTO.class);
    }

    @Override
    public void savePolicyList(List<BusinessPolicyListPO> policyList) {
        if(CollUtil.isEmpty(policyList)) return;

        policyListService.saveBatch(policyList);
    }

    private void verifyDetails(List<PolicyAdjustDetailReqDTO> details) {
        if (CollUtil.isEmpty(details))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_NOT_DATA));

        for (PolicyAdjustDetailReqDTO v : details) {
            //校验数据
            String message = ValidationUtils.validationOne(v, DefaultGroup.class);
            if (CharSequenceUtil.isNotEmpty(message))
                throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DATA_REQUIRED));

            this.verifyBill(v.getBill(), v.getBankEnterpriseLink(), v.getBillReceivable(), v.getOther());
        }
        //公共校验
        this.commonVerifyData(details);

        List<String> sapSupplierCodes = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getSapSupplierCode, details);
        List<String> divisionCodes = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getDivisionCode, details);
        List<String> businessSubclass = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getBusinessSubclassCode, details);

        List<Long> ignoreIds = details.stream().map(PolicyAdjustDetailReqDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        this.verifyExist(sapSupplierCodes, divisionCodes, businessSubclass,ignoreIds,details);
    }

    private void verifyBill(BigDecimal... bill) {
        if (!Objects.equals(new BigDecimal(100).compareTo(NumberUtil.add(bill)), 0))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_BILL_EQUALS));
    }

    private void verifyExist(List<String> sapSupplierCodes, List<String> divisionCodes, List<String> businessSubclass,List<Long> ignoreIds,List<PolicyAdjustDetailReqDTO> details) {
        LambdaQueryWrapper<BusinessPolicyApplyDetailPO> query = new LambdaQueryWrapper<>();
        query.select(BusinessPolicyApplyDetailPO::getId,BusinessPolicyApplyDetailPO::getStatus,BusinessPolicyApplyDetailPO::getSapSupplierCode,BusinessPolicyApplyDetailPO::getDivisionCode,BusinessPolicyApplyDetailPO::getBusinessSubclassCode);
        query.in(BusinessPolicyApplyDetailPO::getSapSupplierCode, sapSupplierCodes);
        query.in(BusinessPolicyApplyDetailPO::getDivisionCode, divisionCodes);
        query.in(BusinessPolicyApplyDetailPO::getBusinessSubclassCode, businessSubclass);
        query.notIn(BusinessPolicyApplyDetailPO::getStatus, PolicyStatusEnum.REJECTED.getCode(),PolicyStatusEnum.CANCEL.getCode());
        query.notIn(CollUtil.isNotEmpty(ignoreIds),BusinessPolicyApplyDetailPO::getId,ignoreIds);

        List<BusinessPolicyApplyDetailPO> list = detailService.list(query);
        if (CollUtil.isNotEmpty(list)) {
            if (list.stream().anyMatch(v ->
                    details.stream().anyMatch(v1 -> CharSequenceUtil.equals(v.getSapSupplierCode(), v1.getSapSupplierCode())
                            && CharSequenceUtil.equals(v.getDivisionCode(), v1.getDivisionCode())
                            && CharSequenceUtil.equals(v.getBusinessSubclassCode(), v1.getBusinessSubclassCode()))))
                throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DIVISION_SUBCLASS_ONE_DATA));
        }
    }

    private void sendTicket(PolicyAdjustMainReqDTO param) {
        CreateTicketMQ tickerMq = new CreateTicketMQ();
        tickerMq.setSubmitDesc(param.getApplyDesc());
        tickerMq.setSubmitRemark(param.getApplyRemark());

        TicketTypeEnum ticketType = TicketTypeEnum.BUSINESS_POLICY_ADD;
        if (PolicyApplyTypeEnum.ADJUST.getCode().equals(param.getApplyType())) {
            ticketType = TicketTypeEnum.BUSINESS_POLICY_ADJUSTMENT;
        }

        tickerMq.setTicketType(ticketType.getCode());
        tickerMq.setBusinessNo(param.getApplyNo());
        tickerMq.setSubmitBy(param.getUserId());
        tickerMq.setSubmitName(param.getUserRealName());

        BaseSysUserRespDTO user = userManager.getByUserId(param.getUserId());
        String submitDomain = Objects.nonNull(user) ? user.getDomain() : null;
        PolicyAdjustDetailReqDTO first = param.getDetails().get(0);

        Map<String, DictRespDTO> policyTypeMap = masterDataManager.listDictMap(StringConstant.POLICY_TYPE);

        List<CreateTicketMQ.ApprovalProcessVar> vars = new ArrayList<>();
        CreateTicketMQ.ApprovalProcessVar submitBy = new CreateTicketMQ.ApprovalProcessVar();
        submitBy.setKey("TGR");
        submitBy.setV(submitDomain);
        CreateTicketMQ.ApprovalProcessVar approvalBy = new CreateTicketMQ.ApprovalProcessVar();
        approvalBy.setKey("SQR");
        approvalBy.setV(submitDomain);
        CreateTicketMQ.ApprovalProcessVar division = new CreateTicketMQ.ApprovalProcessVar();
        division.setKey("GSDM");
        division.setV(first.getDivisionCode());
        //传给BPM的业务大类对应的是政策类型 FSSC正常对应
        CreateTicketMQ.ApprovalProcessVar categoryClass = new CreateTicketMQ.ApprovalProcessVar();
        categoryClass.setKey("YWDLBM");
        categoryClass.setV(param.getPolicyType());
        CreateTicketMQ.ApprovalProcessVar categoryClassName = new CreateTicketMQ.ApprovalProcessVar();
        categoryClassName.setKey("YWDLMC");
        DictRespDTO policyType = CommonUtils.getMapValue(policyTypeMap,param.getPolicyType());
        if(Objects.nonNull(policyType))
            categoryClassName.setV(policyType.getDataName());
        CreateTicketMQ.ApprovalProcessVar subClass = new CreateTicketMQ.ApprovalProcessVar();
        subClass.setKey("YWXLBM");
        subClass.setV(first.getBusinessSubclassCode());
        CreateTicketMQ.ApprovalProcessVar subClassName = new CreateTicketMQ.ApprovalProcessVar();
        subClassName.setKey("YWXLMC");
        subClassName.setV(first.getBusinessSubclassName());
        CreateTicketMQ.ApprovalProcessVar isPaymentDays = new CreateTicketMQ.ApprovalProcessVar();
        isPaymentDays.setKey("SFMZJTGDZQ");
        isPaymentDays.setV(BooleanUtil.isTrue(param.getIsPaymentDays()) ? "Y" : "N");

        vars.add(submitBy);
        vars.add(approvalBy);
        vars.add(division);
        vars.add(categoryClass);
        vars.add(categoryClassName);
        vars.add(subClass);
        vars.add(subClassName);
        vars.add(isPaymentDays);
        tickerMq.setProcessVars(vars);

        log.info("商务政策审批提交工单开始 tickerMq:{}", tickerMq);
        mqManager.sendTopic(AuditTopicConstants.CREATE_TICKET, JSONUtil.toJsonStr(tickerMq));
    }

    private void verifySubmitPolicy(PolicyAdjustMainReqDTO param) {
        if (CollUtil.isEmpty(param.getDetails()))
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_NOT_DATA));

        String type = param.getApplyType();

        //调整和编辑 ID不能为空
        if (PolicyApplyTypeEnum.ADJUST.getCode().equals(type) || PolicyApplyTypeEnum.UPDATE.getCode().equals(type)) {
            if (param.getDetails().stream().anyMatch(v -> Objects.isNull(v.getId())))
                throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_ID_NOT_NULL));
            if (param.getDetails().stream().anyMatch(v -> CharSequenceUtil.isBlank(v.getPolicyNo())))
                throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_NOT_BANK));
            //校验数据是否存在审批中的数据
            this.verifyApplyByApproving(CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getId, param.getDetails()),
                    type,
                    PolicyApplyTypeEnum.ADJUST.getCode().equals(type));
        }

        if (PolicyApplyTypeEnum.ADJUST.getCode().equals(type)) {
            this.verifySubmitDetails(param.getDetails());
        } else {
            this.verifyDetails(param.getDetails());
        }
    }

    private void verifySubmitDetails(List<PolicyAdjustDetailReqDTO> details) {
        //公共校验
        this.commonVerifyData(details);

        for (PolicyAdjustDetailReqDTO v : details) {
            this.verifyBill(v.getBillAfter(), v.getBankEnterpriseLinkAfter(), v.getBillReceivableAfter(), v.getOtherAfter());
        }

        /*List<String> sapSupplierCodes = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getSapSupplierCode, details);
        List<String> divisionCodes = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getDivisionCode, details);
        List<String> businessSubclass = CommonUtils.getDistinctByList(PolicyAdjustDetailReqDTO::getBusinessSubclassCode, details);

        List<Long> ignoreIds = details.stream().map(PolicyAdjustDetailReqDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        this.verifyExist(sapSupplierCodes, divisionCodes, businessSubclass,ignoreIds,details);*/
    }

    private void verifyApplyByApproving(List<Long> ids, String applyType, Boolean isAdjust) {
        LambdaQueryWrapper<BusinessPolicyApplyDetailPO> query = new LambdaQueryWrapper<>();
        query.select(BusinessPolicyApplyDetailPO::getApplyNo, BusinessPolicyApplyDetailPO::getId, BusinessPolicyApplyDetailPO::getPolicyListId);
        if(BooleanUtil.isTrue(isAdjust)){
            query.in(BusinessPolicyApplyDetailPO::getPolicyListId, ids);
        }else{
            query.in(BusinessPolicyApplyDetailPO::getId, ids);
        }
        List<BusinessPolicyApplyDetailPO> details = detailService.list(query);
        if (CollUtil.isEmpty(details)) return;

        List<String> applyNos = details.stream().map(BusinessPolicyApplyDetailPO::getApplyNo).filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(applyNos)) return;

        LambdaQueryWrapper<BusinessPolicyApplyMainPO> queryMain = new LambdaQueryWrapper<>();
        queryMain.in(BusinessPolicyApplyMainPO::getApplyNo, applyNos);
        queryMain.eq(BusinessPolicyApplyMainPO::getApplyType, applyType);
        queryMain.eq(BusinessPolicyApplyMainPO::getStatus, PolicyStatusEnum.APPROVING.getCode());
        if (applyMainService.count(queryMain) > 0)
            throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_EXISTS_APPROVING_DATA));
    }

    private void commonVerifyData(List<PolicyAdjustDetailReqDTO> details) {
        Map<String, Long> detailMap = details.stream()
                .collect(Collectors.groupingBy(v -> v.getSapSupplierCode() + "-" + v.getDivisionCode() + "-" + v.getBusinessSubclassCode(), Collectors.counting()));
        detailMap.forEach((k, v) -> {
            if (v > 1)
                throw new BizFailException(localeMessage.getMessage(LanguageConstant.POLICY_DIVISION_SUBCLASS_ONE_DATA));
        });
    }

    private void dealDetails(List<PolicyAdjustDetailReqDTO> details) {
        if (CollUtil.isEmpty(details)) return;
        List<String> divisionCodes = details.stream()
                .filter(v -> CharSequenceUtil.isNotBlank(v.getDivisionCode()) && CharSequenceUtil.isEmpty(v.getDivisionName()))
                .map(PolicyAdjustDetailReqDTO::getDivisionCode)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(divisionCodes)) {
            Map<String, SysDivisionRespDTO> divisionMap = divisionManager.getMapByDivisionCode(divisionCodes);
            if (CollUtil.isEmpty(divisionMap)) return;
            details.forEach(v -> {
                if (CharSequenceUtil.isNotBlank(v.getDivisionCode()) && CharSequenceUtil.isEmpty(v.getDivisionName())) {
                    SysDivisionRespDTO division = CommonUtils.getMapValue(divisionMap, v.getDivisionCode());
                    if (Objects.nonNull(division))
                        v.setDivisionName(division.getDivisionName());
                }
            });
        }
    }

    private Map<String, SupplierBasicSimpleInfoRespDTO> getSupplierSimpleMap(List<String> supplierCodes) {
        List<SupplierBasicSimpleInfoRespDTO> list = supplierBasicService.listBySupplierCodes(supplierCodes);
        if (CollUtil.isEmpty(list)) return null;

        return CommonUtils.getMapByList(SupplierBasicSimpleInfoRespDTO::getSapSupplierCode,list);
    }

    private Map<String,SupplierBasicInfoPO> getSupplierCpeMainId(List<String> sapSupplierCodes){
        if(CollUtil.isEmpty(sapSupplierCodes)) return null;
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.select(SupplierBasicInfoPO::getId,SupplierBasicInfoPO::getSapSupplierCode,SupplierBasicInfoPO::getCpeMainId,SupplierBasicInfoPO::getCpeMainName);
        query.in(SupplierBasicInfoPO::getSapSupplierCode,sapSupplierCodes);
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierBasicInfoPO> list = supplierBasicService.list(query);
        if(CollUtil.isEmpty(list)) return null;

        return list.stream().filter(v->Objects.nonNull(v.getCpeMainId()))
                .collect(Collectors.toMap(v->v.getSapSupplierCode(), Function.identity(),(v1, v2)->v1));
    }

    private void handleOtherData(List<PolicyListRespDTO> list) {
        if(CollUtil.isEmpty(list)) return;
        List<String> sapSupplierCodes = list.stream().map(v->v.getSapSupplierCode()).distinct().collect(Collectors.toList());
        Map<String,SupplierBasicInfoPO> cpeMainMap = this.getSupplierCpeMainId(sapSupplierCodes);
        list.forEach(v->{
            SupplierBasicInfoPO supplier = CommonUtils.getValue(cpeMainMap,v.getSapSupplierCode());
            if(Objects.nonNull(supplier)){
                v.setCpeMainId(supplier.getCpeMainId());
                v.setCpeMainName(supplier.getCpeMainName());
            }
        });
    }

}
