package com.weifu.srm.supplier.service.biz.suppliercategorybatchadjustapply;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.util.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import com.weifu.srm.supplier.enums.SupplierCategoryBatchAdjustApplyStatusEnum;
import com.weifu.srm.supplier.manager.SupplierCategoryRelationshipManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyNewItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyOldItemMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationshipSourceEnum;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyNewItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyOldItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;
import java.util.List;

/**
 * 处理供应商品类批量调整申请审核结果
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HandleSupplierCategoryBatchAdjustAuditRstBiz {

    private final SupplierCategoryBatchAdjustApplyMapperService supplierCategoryBatchAdjustApplyMapperService;
    private final SupplierCategoryBatchAdjustApplyOldItemMapperService supplierCategoryBatchAdjustApplyOldItemMapperService;
    private final SupplierCategoryBatchAdjustApplyNewItemMapperService supplierCategoryBatchAdjustApplyNewItemMapperService;
    private final SupplierCategoryRelationshipManager supplierCategoryRelationshipManager;
    private final TransactionTemplate transactionTemplate;

    public void execute(TicketStatusChangedMQ req) {
        if (!StringUtils.equals(req.getTicketType(), TicketTypeEnum.SUPPLIER_CATEGORY_RELATION_ADJUST.getCode())) {
            return;
        }

        String applyNo = req.getBusinessNo();
        log.info("处理供应商品类批量调整申请审核结果开始，applyNo={}，mq={}", applyNo, JacksonUtil.bean2Json(req));

        // 当前状态不是审核中，则不处理
        SupplierCategoryBatchAdjustApplyPO apply = supplierCategoryBatchAdjustApplyMapperService.getByApplyNo(applyNo);
        if (!SupplierCategoryBatchAdjustApplyStatusEnum.APPROVING.equalsCode(apply.getApplyStatus())) {
            log.warn("放弃处理供应商品类批量调整申请审核结果，当前状态不是审核中，applyNo={}", applyNo);
            return;
        }

        try {
            if (TicketStatusEnum.APPROVED.equalsCode(req.getStatus())) {
                updateStatusToApproved(req, apply); // 更新状态为“审核通过”
            } else if (TicketStatusEnum.REJECTED.equalsCode(req.getStatus())) {
                updateStatusToRejected(req); // 更新状态为“审核拒绝”
            } else if (TicketStatusEnum.CANCELED.equalsCode(req.getStatus())) {
                updateStatusToWithdrawn(req); // 更新状态为“已撤回”
            }
            log.info("处理供应商品类调整申请审核结果完成，applyNo={}", applyNo);
        } catch (Exception e) {
            log.error("处理供应商品类调整申请审核结果异常，applyNo={}", applyNo, e);
            throw e;
        }
    }

    /**
     * 更新状态为“审核通过”
     */
    private void updateStatusToApproved(TicketStatusChangedMQ mq, SupplierCategoryBatchAdjustApplyPO apply) {
        List<SupplierCategoryBatchAdjustApplyOldItemPO> oldItems = supplierCategoryBatchAdjustApplyOldItemMapperService.lambdaQuery()
                .eq(SupplierCategoryBatchAdjustApplyOldItemPO::getApplyNo, mq.getBusinessNo()).list();
        List<SupplierCategoryBatchAdjustApplyNewItemPO> newItems = supplierCategoryBatchAdjustApplyNewItemMapperService.lambdaQuery()
                .eq(SupplierCategoryBatchAdjustApplyNewItemPO::getApplyNo, mq.getBusinessNo()).list();

        Date current = new Date();
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                supplierCategoryBatchAdjustApplyMapperService.lambdaUpdate()
                        .set(SupplierCategoryBatchAdjustApplyPO::getApplyStatus, SupplierCategoryBatchAdjustApplyStatusEnum.APPROVED.getCode())
                        .set(SupplierCategoryBatchAdjustApplyPO::getUpdateBy, mq.getOperateBy())
                        .set(SupplierCategoryBatchAdjustApplyPO::getUpdateName, mq.getOperateName())
                        .set(SupplierCategoryBatchAdjustApplyPO::getUpdateTime, current)
                        .eq(SupplierCategoryBatchAdjustApplyPO::getId, apply.getId())
                        .update();

                // 删除旧关系，创建新关系
                supplierCategoryRelationshipManager.save(SupplierCategoryRelationshipSourceEnum.SUPPLIER_CATEGORY_RELATION_ADJUST
                        , apply.getApplyNo(), apply.getApplyBy(), apply.getApplyName(), apply.getApplyDesc(), oldItems, newItems, current);
            }
        });
    }

    /**
     * 更新状态为“审核拒绝”
     */
    private void updateStatusToRejected(TicketStatusChangedMQ req) {
        // 前置状态为审核中
        supplierCategoryBatchAdjustApplyMapperService.lambdaUpdate()
                .set(SupplierCategoryBatchAdjustApplyPO::getApplyStatus, SupplierCategoryBatchAdjustApplyStatusEnum.REJECTED.getCode())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateBy, req.getOperateBy())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateName, req.getOperateName())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateTime, new Date())
                .eq(SupplierCategoryBatchAdjustApplyPO::getApplyNo, req.getBusinessNo())
                .eq(SupplierCategoryBatchAdjustApplyPO::getApplyStatus, SupplierCategoryBatchAdjustApplyStatusEnum.APPROVING.getCode())
                .update();
    }

    /**
     * 更新状态为“已撤回”
     */
    private void updateStatusToWithdrawn(TicketStatusChangedMQ req) {
        // 前置状态为审核中
        supplierCategoryBatchAdjustApplyMapperService.lambdaUpdate()
                .set(SupplierCategoryBatchAdjustApplyPO::getApplyStatus, SupplierCategoryBatchAdjustApplyStatusEnum.WITHDRAWN.getCode())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateBy, req.getOperateBy())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateName, req.getOperateName())
                .set(SupplierCategoryBatchAdjustApplyPO::getUpdateTime, new Date())
                .eq(SupplierCategoryBatchAdjustApplyPO::getApplyNo, req.getBusinessNo())
                .eq(SupplierCategoryBatchAdjustApplyPO::getApplyStatus, SupplierCategoryBatchAdjustApplyStatusEnum.APPROVING.getCode())
                .update();
    }

}