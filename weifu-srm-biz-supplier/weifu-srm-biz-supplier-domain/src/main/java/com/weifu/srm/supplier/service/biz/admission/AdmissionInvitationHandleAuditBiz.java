package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.request.todolist.CreateTodoListReqDTO;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.integration.response.qixinbao.BusinessInformationRespDTO;
import com.weifu.srm.integration.response.qixinbao.ContactInfoRespDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionInvitationManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionTriggerManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.manager.remote.base.TodoListManager;
import com.weifu.srm.supplier.manager.remote.intergration.QiXinBaoManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierContactInfoMapperService;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.TodoTemplateEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierContactInfoPO;
import com.weifu.srm.supplier.response.SupplierAdmissionInvitationDetailRespDTO;
import com.weifu.srm.user.constants.MQUserTopicConstants;
import com.weifu.srm.user.mq.CreateSupplierUserMqDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionInvitationHandleAuditBiz {

    private final MQServiceManager mqService;
    private final SysUserManager sysUserManager;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final SupplierAdmissionInvitationManager supplierAdmissionInvitationManager;
    private final SupplierAdmissionInvitationInfoMapperService admissionInvitationInfoMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final QiXinBaoManager qiXinBaoManager;
    private final SupplierAdmissionTriggerManager supplierAdmissionTriggerManager;
    private final TodoListManager todoListManager;
    private final TransactionTemplate transactionTemplate;

    @Value("${environment.params.registerUrl}")
    private String registerUrl;

    /**
     * 处理准入邀请审批结果
     */
    public void handleAudit(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVING:
                log.debug("audit is processing = {}", ticketInfo);
                break;
            case CANCELED:
                admissionInvitationInfoMapperService.updateInvitationStatus(ticketInfo.getBusinessNo(), AdmissionInvitationStatusEnum.INIT_STATUS.getCode(), ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                break;
            case REJECTED:
                admissionInvitationInfoMapperService.updateInvitationStatus(ticketInfo.getBusinessNo(), AdmissionInvitationStatusEnum.REJECT_STATUS.getCode(), ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                break;
            case APPROVED:
                // 准入邀请信息
                SupplierAdmissionInvitationDetailRespDTO invitationDetail = supplierAdmissionInvitationManager.getInvitationDetail(ticketInfo.getBusinessNo());
                // 供应商联系人
                BaseSysUserRespDTO registerUser = sysUserManager.getSupplierUserByPhoneAndEmail(invitationDetail.getPhone(), invitationDetail.getSupplierRegistrationContactEmail());

                transactionTemplate.executeWithoutResult(status -> {
                    // 供应商
                    SupplierBasicInfoPO basicInfoPO = getSupplierInfo(invitationDetail, ticketInfo);
                    // 修改邀请状态冗余供应商ID
                    updateInvitationPass(ticketInfo,basicInfoPO.getId());
                    // 供应商联系人不存在
                    if (ObjectUtils.isEmpty(registerUser)) {
                        // 发送MQ给user进行用户注册
                        mqService.sendMQ(MQUserTopicConstants.CREATE_SUPPLIER_USER, JacksonUtil.bean2Json(boxRegisterUserMQ(ticketInfo, invitationDetail)));
                        return;
                    }
                    // 供应商状态为可用状态 & 注册联系人已注册
                    if (SupplierBasicStatusEnum.normalStatus.contains(SupplierBasicStatusEnum.getByCode(basicInfoPO.getStatus()))
                            && ObjectUtils.isNotEmpty(registerUser)) {
                        // 保存关联关系
                        userSupplierRelationshipManager.saveRegisterUser(basicInfoPO, registerUser.getId(), ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                        // 触发准入动作
                        supplierAdmissionTriggerManager.completedAdmissionInvitation(ticketInfo.getBusinessNo(), null);
                        return;
                    }
                    // 供应商联系人存在 & 供应商状态为草稿
                    if (SupplierBasicStatusEnum.DRAFT.getCode().equals(basicInfoPO.getStatus())) {
                        userSupplierRelationshipManager.saveRegisterUser(basicInfoPO, registerUser.getId(), ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                        // 如果已存在注册待办则不再重复发送
                        Long registerCount = todoListManager.searchTodoCountByTypes(basicInfoPO.getId(), registerUser.getId(), List.of(TodoClsEnum.SUPPLIER_REGISTER.getCode()));
                        if (registerCount >= 1) {
                            return;
                        }
                        // 发送供应商注册代办
                        mqService.sendMQ(CommunicationTopicConstants.CREATE_TODO, JacksonUtil.bean2Json(boxSupplierRegisterMQ(invitationDetail, registerUser.getId(), basicInfoPO.getId())));
                        // 发送供应商注册通知e-mail
                        mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(boxSupplierRegisterEmail(invitationDetail)));
                    }
                });
                break;
            default:
                log.error("***** audit message not support={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }


    private SupplierBasicInfoPO getSupplierInfo(SupplierAdmissionInvitationDetailRespDTO invitationDetail, TicketStatusChangedMQ ticketInfo) {
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getSupplierName, invitationDetail.getSupplierName()).one();
        // 供应商信息为空
        if (ObjectUtils.isNotEmpty(basicInfoPO)) {
            return basicInfoPO;
        }
        basicInfoPO = new SupplierBasicInfoPO();
        SupplierContactInfoPO contactInfoPO = new SupplierContactInfoPO();
        BaseEntityUtil.setCommon(basicInfoPO, ticketInfo.getOperateBy(), ticketInfo.getOperateName(), null);
        basicInfoPO.setSupplierName(invitationDetail.getSupplierName());
        basicInfoPO.setStatus(SupplierBasicStatusEnum.DRAFT.getCode());
        basicInfoPO.setSupplierShortName(invitationDetail.getSupplierShortName());
        basicInfoPO.setIsOverseas(invitationDetail.getDomesticForeignRelationship());
        basicInfoPO.setInvitationBy(invitationDetail.getAdmissionInvitationBy());
        basicInfoPO.setDivisionCode(invitationDetail.getDivisionCode());
        if (YesOrNoEnum.NO.equalsCode(invitationDetail.getDomesticForeignRelationship())) {
            // 境内供应商
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 联系人
            ContactInfoRespDTO contactInfoQiXinBao = qiXinBaoManager.getContactInfo(invitationDetail.getCreditCode());
            if (ObjectUtils.isNotEmpty(contactInfoQiXinBao)){
                contactInfoPO.setCompanyPhone(contactInfoQiXinBao.getTelephone());
            }
            // 工商照面
            boxBusinessInformation(basicInfoPO, contactInfoPO, simpleDateFormat, invitationDetail);
        }
        // 供应商基础信息
        supplierBasicInfoMapperService.save(basicInfoPO);
        contactInfoPO.setSupplierBasicMsgId(basicInfoPO.getId());
        contactInfoPO.setRegisterContactName(invitationDetail.getSupplierRegistrationContactName());
        contactInfoPO.setRegisterContactEmail(invitationDetail.getSupplierRegistrationContactEmail());
        contactInfoPO.setRegisterContactPhone(invitationDetail.getPhone());
        BaseEntityUtil.setCommon(contactInfoPO, ticketInfo.getOperateBy(), ticketInfo.getOperateName(), null);
        // 供应商注册联系人信息
        supplierContactInfoMapperService.save(contactInfoPO);
        return basicInfoPO;
    }

    private void boxBusinessInformation(SupplierBasicInfoPO basicInfoPO, SupplierContactInfoPO contactInfoPO, SimpleDateFormat simpleDateFormat,
                                        SupplierAdmissionInvitationDetailRespDTO invitationDetail) {
        try {
            // 启信宝-供应商照面
            BusinessInformationRespDTO businessInformation = qiXinBaoManager.getBusinessInformation(invitationDetail.getCreditCode());
            if (ObjectUtils.isNotEmpty(businessInformation)){
                basicInfoPO.setSupplierName(businessInformation.getFormat_name());
                basicInfoPO.setSupplierNameEN(businessInformation.getFenname());
                basicInfoPO.setRegisteredAddress(businessInformation.getAddress());
                basicInfoPO.setBusinessScope(businessInformation.getScope());
                basicInfoPO.setRegisteredAmt(new BigDecimal(businessInformation.getRegistCapi().split(" ")[0]));
                basicInfoPO.setRegisteredCurrency(businessInformation.getCurrency_unit());
                contactInfoPO.setLegalPersonName(businessInformation.getOperName());
                if (StringUtils.isNotBlank(businessInformation.getStartDate()) && !"-".equals(businessInformation.getStartDate())) {
                    basicInfoPO.setEstablishmentDate(simpleDateFormat.parse(businessInformation.getStartDate()));
                }
                if (StringUtils.isNotBlank(businessInformation.getTermStart()) && !"-".equals(businessInformation.getTermStart())) {
                    basicInfoPO.setBusinessPeriodStart(simpleDateFormat.parse(businessInformation.getTermStart()));
                }
                if (StringUtils.isNotBlank(businessInformation.getTermEnd()) && !"-".equals(businessInformation.getTermEnd())) {
                    basicInfoPO.setBusinessPeriodEnd(simpleDateFormat.parse(businessInformation.getTermEnd()));
                }
            }
            basicInfoPO.setCreditCode(invitationDetail.getCreditCode());
            basicInfoPO.setIsOverseas(invitationDetail.getDomesticForeignRelationship());
            basicInfoPO.setSupplierShortName(invitationDetail.getSupplierShortName());
        } catch (Exception e) {
            log.error("translate Qixinbao message error: ", e);
            throw new BizFailException(e.getMessage());
        }
    }

    private CreateSupplierUserMqDTO boxRegisterUserMQ(TicketStatusChangedMQ ticketInfo, SupplierAdmissionInvitationDetailRespDTO invitationInfo) {
        CreateSupplierUserMqDTO mqDTO = new CreateSupplierUserMqDTO();
        mqDTO.setBusinessNo(ticketInfo.getBusinessNo());
        mqDTO.setOperationTime(ticketInfo.getUpdateTime());
        mqDTO.setOperationUserId(ticketInfo.getOperateBy());
        mqDTO.setOperationUser(ticketInfo.getOperateName());
        mqDTO.setEmail(invitationInfo.getSupplierRegistrationContactEmail());
        mqDTO.setPhone(invitationInfo.getPhone());
        mqDTO.setRealName(invitationInfo.getSupplierRegistrationContactName());
        return mqDTO;
    }

    private CreateTodoListReqDTO boxSupplierRegisterMQ(SupplierAdmissionInvitationDetailRespDTO invitationInfo, Long userId, Long supplierId) {
        String content = TodoTemplateEnum.SUPPLIER_REGISTER_TODO.getContent();
        content = content.replace("${供应商名称}", invitationInfo.getSupplierName());
        CreateTodoListReqDTO todo = new CreateTodoListReqDTO();
        todo.setContent(content);
        todo.setBusinessNo(invitationInfo.getInvitationNo());
        todo.setBusinessType(TodoClsEnum.SUPPLIER_REGISTER.getCode());
        todo.setUserId(userId);
        todo.setSupplierId(supplierId);
        todo.setIconType(IconTypeEnum.GENERAL.getCode());
        return todo;
    }

    private CreateSendEmailTaskMQ boxSupplierRegisterEmail(SupplierAdmissionInvitationDetailRespDTO invitationDetail) {
        BaseSysUserRespDTO sponsor = sysUserManager.getUserDetailById(invitationDetail.getAdmissionInvitationBy());
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_REGISTER_INVITATION_CONTENT.getTitle());
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_REGISTER_INVITATION_CONTENT.getContent()
                .replace("${1}", sponsor.getRealName())
                .replace("${2}", sponsor.getEmail())
                .replace("${0}", registerUrl));
        emailReq.setRecipients(invitationDetail.getSupplierRegistrationContactEmail());
        emailReq.setBusinessNo(invitationDetail.getInvitationNo());
        emailReq.setBusinessType("准入邀请-供应商注册");
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private void updateInvitationPass(TicketStatusChangedMQ ticketInfo, Long supplierId) {
        admissionInvitationInfoMapperService.lambdaUpdate()
                .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, ticketInfo.getBusinessNo())
                .set(SupplierAdmissionInvitationRecordPO::getSupplierBasicMsgId, supplierId)
                .set(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.APPROVED_STATUS.getCode())
                .set(SupplierAdmissionInvitationRecordPO::getInvitationApprovedTime, ticketInfo.getUpdateTime())
                .set(SupplierAdmissionInvitationRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierAdmissionInvitationRecordPO::getUpdateName, ticketInfo.getOperateName())
                .update();
    }
}
