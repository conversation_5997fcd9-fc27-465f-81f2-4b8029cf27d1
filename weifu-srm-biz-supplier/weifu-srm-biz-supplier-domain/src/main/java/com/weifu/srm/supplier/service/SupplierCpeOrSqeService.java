package com.weifu.srm.supplier.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.SupplierCpeMainReqDTO;
import com.weifu.srm.supplier.request.SupplierCpeOrSqePageReqDTO;
import com.weifu.srm.supplier.request.SupplierSqeMainReqDTO;
import com.weifu.srm.supplier.response.SupplierBasicInfoCategorySimpleRespDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description sqe/cpe相关服务
 * @Version 1.0
 */
public interface SupplierCpeOrSqeService {
    /**
     * 设置cep主负责人
     *
     * @param supplierCpeMainReqDTO request参数
     */
    void cpeMain(SupplierCpeMainReqDTO supplierCpeMainReqDTO);

    /**
     * 设置sqe主负责人
     *
     * @param supplierSqeMainReqDTO request参数
     */
    void sqeMain(SupplierSqeMainReqDTO supplierSqeMainReqDTO);

    /**
     * 分页查询
     *
     * @param reqDTO request参数
     * @return 列表
     */
    PageResponse<SupplierBasicInfoCategorySimpleRespDTO> page(SupplierCpeOrSqePageReqDTO reqDTO);

    /**
     * 导出
     *
     * @param reqDTO   request参数
     * @param response 请求流
     */
    void exportCpe(SupplierCpeOrSqePageReqDTO reqDTO, HttpServletResponse response);
    /**
     * 导出
     *
     * @param reqDTO   request参数
     * @param response 请求流
     */
    void exportSqe(SupplierCpeOrSqePageReqDTO reqDTO, HttpServletResponse response);
}
