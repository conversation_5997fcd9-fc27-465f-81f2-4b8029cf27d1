package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.supplier.constants.StringConstant;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicyBiz {
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationService;
    private final CategoryManager categoryManager;



    /**
     *CPEjue
     */
    private static final List<String> CPE_CATEGORY_ROLE_LIST = Arrays.asList(CategoryRoleEnum.CPE.getCode(),
            CategoryRoleEnum.CPE_MASTER.getCode(),
            CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode());


    /**
     * 查询用户负责三级频率下的供应商
     *
     * @param userId 用户id
     * @return 供应商编码
     */
    public List<String> getCategoryRelationSupplier(Long userId) {
        List<CategoryEngineerResultDTO> engineers = categoryManager.queryCategoryEngineerByUserId(userId);
        if (CollUtil.isEmpty(engineers)) return StringConstant.DEFAULT_LIST;
        List<String> categoryCodes = engineers.stream()
                .filter(v -> CharSequenceUtil.isNotBlank(v.getCategoryCode()))
                .filter(v -> CollUtil.contains(CPE_CATEGORY_ROLE_LIST, v.getRoleId()))
                .map(v -> v.getCategoryCode())
                .distinct()
                .collect(Collectors.toList());
        List<SupplierCategoryRelationshipPO> relationshipList = supplierCategoryRelationService.listByCategoryCodes(categoryCodes);
        if (CollUtil.isEmpty(relationshipList)) return StringConstant.DEFAULT_LIST;
        return relationshipList.stream().map(v -> v.getSapSupplierCode()).distinct().collect(Collectors.toList());
    }
}
