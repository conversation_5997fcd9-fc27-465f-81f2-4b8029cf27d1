package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.communication.request.todolist.CreateTodoListReqDTO;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.integration.request.OrgUpdateDTO;
import com.weifu.srm.integration.request.bpm.BPMAddSupplierReqDTO;
import com.weifu.srm.integration.request.iam.IAMUserUpdateReqDTO;
import com.weifu.srm.supplier.constants.MQSupplierTopicConstants;
import com.weifu.srm.supplier.dto.SupplierBasicStatusUpdateDTO;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.SupplierBasicInfoDetailManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.manager.remote.intergration.BPMManager;
import com.weifu.srm.supplier.manager.remote.intergration.IAMManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.mq.SupplierAdmissionRegisterCompletedMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierContactInfoMapperService;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierContactInfoPO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SupplierRegisterHandleAuditBiz {
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final SupplierBasicInfoDetailManager supplierBasicInfoDetailManager;
    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final SysUserManager userService;
    private final MQServiceManager mqService;
    private final IAMManager iamManager;
    private final BPMManager bpmManager;
    private final AsyncTaskExecutor asyncTaskExecutor;
    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final TransactionTemplate transactionTemplate;


    public void handleAudit(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        // 供应商基础信息查询
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getRegisterNo, ticketInfo.getBusinessNo()).one();
        if (ObjectUtils.isEmpty(basicInfoPO)) {
            return;
        }
        // 注册联系人
        BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(basicInfoPO.getId());
        switch (statusEnum) {
            case APPROVING:
                log.debug("audit is processing = {}", ticketInfo);
                break;
            case REJECTED:
                // 修改供应商审批状态为拒绝
                basicInfoMapperService.updateSupplierStatus(basicInfoPO.getId(), SupplierBasicStatusEnum.REJECT_STATUS.getCode(), ticketInfo.getOperateBy(), ticketInfo.getOperateName());
                // 拒绝 发送注册待办
                mqService.sendMQ(CommunicationTopicConstants.CREATE_TODO, JacksonUtil.bean2Json(boxSupplierRegisterMQ(basicInfoPO, userInfo.getId(), basicInfoPO.getId())));
                // 拒绝时 发送供应商通知邮件，并且抄送准入邀请人
                mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(boxSupplierRegisterRejectEmail(basicInfoPO, ticketInfo)));
                break;
            case APPROVED:
                // 在BPM建立关联关系
                checkWhetherNeedUpdateRelationship(basicInfoPO,ticketInfo.getOperateBy());
                // 调用IAM注册供应商机构并绑定供应商与用户的关系
                getSupplierIAMCodeAndBindUser(basicInfoPO, userInfo);
                transactionTemplate.executeWithoutResult(status -> {
                    // 更新供应商状态与信息
                    updateSupplierInfo(basicInfoPO, ticketInfo);
                    // 发送注册成功站内通知
                    mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxNoticeMQ(basicInfoPO, ticketInfo.getTicketNo())));
                    // 发送通知邮件
                    mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(boxSupplierRegisterSuccessEmail(basicInfoPO, userInfo)));
                    // 发送供应商注册完成MQ
                    mqService.sendMQ(MQSupplierTopicConstants.SUPPLIER_ADMISSION_REGISTER_COMPLETED_TOPIC, JacksonUtil.bean2Json(boxSupplierRegisterCompletedMQ(basicInfoPO, ticketInfo)));
                });
                break;
            default:
                log.error("***** audit message not support={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }

    private void updateSupplierInfo(SupplierBasicInfoPO basicInfoPO, TicketStatusChangedMQ ticketInfo) {
        basicInfoPO.setStatus(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getCode());
        basicInfoPO.setSupplierClassification(SupplierGradeTypeEnum.NON_GRADE.getCode());
        basicInfoPO.setUpdateBy(ticketInfo.getOperateBy());
        basicInfoPO.setUpdateName(ticketInfo.getOperateName());
        SupplierBasicStatusUpdateDTO dto = new SupplierBasicStatusUpdateDTO();
        dto.setContent("供应商注册审核通过");
        dto.setChangeType(SupplierKeyInfoChangeTypeEnum.STATUS);
        dto.setSupplierBasicInfoPO(basicInfoPO);
        dto.setBusinessNo(basicInfoPO.getRegisterNo());
        dto.setRemark("注册");
        supplierBasicInfoDetailManager.updateSupplierBasicStatus(dto);
    }

    private void checkWhetherNeedUpdateRelationship(SupplierBasicInfoPO basicInfoPO,Long userId) {
        // 供应商联系人查询
        SupplierContactInfoPO contactInfoPO = supplierContactInfoMapperService.lambdaQuery()
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .one();
        boolean result = bpmManager.checkSupplierRelationship(basicInfoPO.getCreditCode(), basicInfoPO.getSupplierName());
        if (!result && basicInfoPO.getIsWeifuRelatedParty() == 1) {
            BPMAddSupplierReqDTO relationshipBindReq = new BPMAddSupplierReqDTO();
            relationshipBindReq.setUsci(basicInfoPO.getCreditCode());
            relationshipBindReq.setRelatedType("1");
            relationshipBindReq.setRelatedName(contactInfoPO.getLegalPersonName());
            BaseSysUserRespDTO userInfo = userService.getUserDetailById(userId);
            relationshipBindReq.setApplyer(userInfo.getDomain());
            relationshipBindReq.setSystem("SRM");
            bpmManager.sendTodoForSupplierRelationship(relationshipBindReq);
        }
    }

    private void getSupplierIAMCodeAndBindUser(SupplierBasicInfoPO basicInfoPO, BaseSysUserRespDTO userInfo) {
        List<SupplierBasicInfoPO> suppliers = userSupplierRelationshipManager.getSupplierListByUserId(userInfo.getId());
        List<String> supplierIAMCodes = suppliers.stream().map(SupplierBasicInfoPO::getSupplierIAMCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (StringUtils.isBlank(basicInfoPO.getSupplierIAMCode())) {
            // 注册机构
            OrgUpdateDTO orgRegisterDTO = new OrgUpdateDTO();
            orgRegisterDTO.setName(basicInfoPO.getSupplierName());
            orgRegisterDTO.setIsDisabled(false);
            String iamCode = iamManager.updateSupplierWithIAM(orgRegisterDTO);
            basicInfoPO.setSupplierIAMCode(iamCode);
            supplierIAMCodes.add(iamCode);
        }
        //调用iam更新关联关系
        IAMUserUpdateReqDTO orgUserBindDTO = new IAMUserUpdateReqDTO();
        orgUserBindDTO.setMultiAffiliateOrg(supplierIAMCodes);
        orgUserBindDTO.setLoginName(userInfo.getUserName());
        orgUserBindDTO.setIsDisabled(false);
        asyncTaskExecutor.submit(() -> iamManager.updateUserInfoWithIAM(orgUserBindDTO));
    }

    private List<CreateSiteMessageMQ> boxNoticeMQ(SupplierBasicInfoPO basicInfoPO, String ticketNo) {
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setBusinessNo(basicInfoPO.getRegisterNo());
        notice.setBusinessType(MessageClsEnum.REGISTER_SUCCESS.getCode());
        notice.setUserId(basicInfoPO.getCreateBy());
        notice.setSupplierId(basicInfoPO.getId());
        notice.setUserName(basicInfoPO.getCreateName());
        notice.setIconType(IconTypeEnum.GENERAL.getCode());
        notice.setTitle(NoticeTemplateEnum.SUPPLIER_REGISTER_SUCCESS_CONTENT.getTitle());
        String content = NoticeTemplateEnum.SUPPLIER_REGISTER_SUCCESS_CONTENT.getContent();
        content = content
                .replace("${工单类型}", TicketTypeEnum.SUPPLIER_ADMISSION_INVITATION.getDesc())
                .replace("${审核结果｝", TicketStatusEnum.APPROVED.getName())
                .replace("${工单编号}", ticketNo)
                .replace("${供应商名称}", basicInfoPO.getSupplierName());
        notice.setContent(content);
        return List.of(notice);
    }

    private CreateTodoListReqDTO boxSupplierRegisterMQ(SupplierBasicInfoPO basicInfoPO, Long userId, Long supplierId) {
        String content = TodoTemplateEnum.SUPPLIER_REGISTER_TODO.getContent();
        content = content.replace("${供应商名称}", basicInfoPO.getSupplierName());
        CreateTodoListReqDTO todo = new CreateTodoListReqDTO();
        todo.setContent(content);
        todo.setBusinessNo(basicInfoPO.getRegisterNo());
        todo.setBusinessType(TodoClsEnum.SUPPLIER_REGISTER.getCode());
        todo.setUserId(userId);
        todo.setSupplierId(supplierId);
        todo.setIconType(IconTypeEnum.GENERAL.getCode());
        return todo;
    }

    private CreateSendEmailTaskMQ boxSupplierRegisterSuccessEmail(SupplierBasicInfoPO basicInfoPO, BaseSysUserRespDTO userInfo) {
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_REGISTER_SUCCESS_CONTENT.getTitle().replace("${0}", basicInfoPO.getSupplierName()));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_REGISTER_SUCCESS_CONTENT.getContent().replace("${0}", basicInfoPO.getSupplierName()));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(basicInfoPO.getRegisterNo());
        emailReq.setBusinessType("准入邀请-供应商注册通过");
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private CreateSendEmailTaskMQ boxSupplierRegisterRejectEmail(SupplierBasicInfoPO basicInfoPO, TicketStatusChangedMQ ticketInfo) {
        List<SupplierAdmissionInvitationRecordPO> list = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .list();
        SupplierAdmissionInvitationRecordPO invitationRecordPO = list.get(0);
        BaseSysUserRespDTO invitationUser = userService.getUserDetailById(invitationRecordPO.getCreateBy());
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_REGISTER_FAIL_CONTENT.getTitle().replace("${0}", basicInfoPO.getSupplierName()));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_REGISTER_FAIL_CONTENT.getContent().replace("${0}", ticketInfo.getApprovalComment()));
        // 收件人-供应商
        emailReq.setRecipients(invitationRecordPO.getSupplierRegistrationContactEmail());
        // 抄送人-邀请发起人
        emailReq.setCcRecipients(invitationUser.getEmail());
        emailReq.setBusinessNo(basicInfoPO.getRegisterNo());
        emailReq.setBusinessType("准入邀请-供应商注册拒绝");
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private SupplierAdmissionRegisterCompletedMQ boxSupplierRegisterCompletedMQ(SupplierBasicInfoPO basicInfoPO, TicketStatusChangedMQ ticketInfo) {
        SupplierAdmissionRegisterCompletedMQ mq = new SupplierAdmissionRegisterCompletedMQ();
        mq.setBusinessNo(ticketInfo.getBusinessNo());
        mq.setSupplierName(basicInfoPO.getSupplierName());
        mq.setSupplierId(basicInfoPO.getId());
        mq.setOperationUserId(ticketInfo.getOperateBy());
        mq.setOperationUser(ticketInfo.getOperateName());
        mq.setOperationTime(ticketInfo.getUpdateTime());
        return mq;
    }
}
