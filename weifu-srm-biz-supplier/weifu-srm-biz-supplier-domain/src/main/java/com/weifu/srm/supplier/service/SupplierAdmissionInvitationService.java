package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.communication.mq.TodoStatusChangedMQ;
import com.weifu.srm.supplier.mq.*;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.user.mq.SupplierUserRegistrationResultMqDTO;

import java.util.List;

/**
 * 供应商准入邀请
 */
public interface SupplierAdmissionInvitationService {

    String commit(SupplierAdmissionInvitationReqDTO req);

    String save(SupplierAdmissionInvitationReqDTO req);

    PageResponse<SupplierAdmissionInvitationSearchListRespDTO> searchList(SupplierAdmissionInvitationSearchListReqDTO req);

    SupplierAdmissionInvitationDetailRespDTO searchDetailByInvitationNo(String invitationNo);

    List<String> searchSupplierNameWithNameKeyword(String supplierNameKeyword, Integer limit);

    TmpSupplierContactUserMessageRespDTO searchSupplierContactUserWithSupplierName(String supplierName);

    TmpSupplierLastTransactionPeriodRespDTO searchLastTransactionPeriod(String supplierName, List<String> categoryIds);

    List<AdmissionInvitationCategoryRespDTO> searchAdmissionCategory(Long userId, String supplierName, String admissionType, Integer isCenPurchase);

    AdmissionTypeMapSupplierTypeRespDTO searchAdmissionTypeMapSupplierType();

    void deleteByInvitationNo(String invitationNo);

    void dealDraftCMS(TodoStatusChangedMQ mq);

    void handleSupplierUserRegisterComplete(SupplierUserRegistrationResultMqDTO mq);

    void resendAdmissionInvitation(String invitationNo);

    SupplierInfoPartRespDTO getSupplierInformationByCreditCode(String creditCode);

    SupplierPotentialToQualifiedDetailRespDTO potentialToQualifiedDetail(SupplierPotentialToQualifiedQueryReqDTO req);

    void potentialToQualifiedSaveOrUpdate(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO);

    SupplierInfoPartRespDTO querySupplierBySupplierName(String supplierName);

    void admissionInvitationHandleAudit(TicketStatusChangedMQ mq);

    void supplierPotentialToQualifiedHandleAudit(TicketStatusChangedMQ mq);

    void supplierAdmissionUserRegisterHandle(SupplierAdmissionUserRegisterCompletedMQ mq);

    void supplierAdmissionRegisterHandle(SupplierAdmissionRegisterCompletedMQ mq);

}
