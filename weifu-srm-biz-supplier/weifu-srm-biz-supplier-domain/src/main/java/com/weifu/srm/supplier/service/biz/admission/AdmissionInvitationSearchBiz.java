package com.weifu.srm.supplier.service.biz.admission;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.supplier.convert.SupplierAdmissionInvitationConvert;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationSearchPO;
import com.weifu.srm.supplier.request.SupplierAdmissionInvitationSearchListReqDTO;
import com.weifu.srm.supplier.response.SupplierAdmissionInvitationSearchListRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionInvitationSearchBiz {

    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final SupplierAdmissionInvitationConvert invitationConvert;
    private final DataPermissionManager dataPermissionManager;

    private static final String PC_INTERNAL_BTN_ADMISSION_INVATION = "PC_INTERNAL_BTN_ADMISSION_INVATION";

    /**
     * 准入邀请列表查询
     */
    public PageResponse<SupplierAdmissionInvitationSearchListRespDTO> searchList(SupplierAdmissionInvitationSearchListReqDTO req) {
        List<SupplierAdmissionInvitationSearchListRespDTO> searchDTOs = new ArrayList<>();
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(req.getOperationId(), PC_INTERNAL_BTN_ADMISSION_INVATION);
        log.info("Supplier admission invitation data permission info：{}", JacksonUtil.bean2Json(dataPermission));
        if (dataPermission.isNo()) {
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, searchDTOs);
        }
        SupplierAdmissionInvitationSearchPO searchPO = invitationConvert.toSearchEntity(req);
        searchPO.setExcludeStatuses(List.of(AdmissionInvitationStatusEnum.INIT_STATUS.getCode()));
        // 查询准入邀请主数据
        Page<SupplierAdmissionInvitationSearchPO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<SupplierAdmissionInvitationSearchPO> pageResult = invitationInfoMapperService.queryList(page, searchPO, dataPermission.getKeys());
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            List<String> invitationNos = pageResult.getRecords().stream()
                    .map(SupplierAdmissionInvitationSearchPO::getInvitationNo)
                    .collect(Collectors.toList());
            // 查询准入邀请品类数据
            LambdaQueryWrapper<SupplierAdmissionCategoryRecordPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SupplierAdmissionCategoryRecordPO::getInvitationNo, invitationNos);
            List<SupplierAdmissionCategoryRecordPO> list = categoryRecordMapperService.list(wrapper);
            // 进行数据整合
            Map<String, List<SupplierAdmissionCategoryRecordPO>> invitationMapCategories = list.stream()
                    .collect(Collectors.groupingBy(SupplierAdmissionCategoryRecordPO::getInvitationNo));
            List<SupplierAdmissionInvitationSearchPO> records = pageResult.getRecords();
            for (SupplierAdmissionInvitationSearchPO recordPO : records) {
                List<SupplierAdmissionCategoryRecordPO> categories = invitationMapCategories.get(recordPO.getInvitationNo());
                if (categories == null) {
                    continue;
                }
                recordPO.setAdmissionCategories(invitationMapCategories.get(recordPO.getInvitationNo())
                        .stream().map(SupplierAdmissionCategoryRecordPO::getCategoryName)
                        .collect(Collectors.toList()));
            }
            searchDTOs = invitationConvert.toSearchDTOList(records);
        }
        return PageResponse.toResult(req.getPageNum(), req.getPageSize(), pageResult.getTotal(), searchDTOs);
    }
}
