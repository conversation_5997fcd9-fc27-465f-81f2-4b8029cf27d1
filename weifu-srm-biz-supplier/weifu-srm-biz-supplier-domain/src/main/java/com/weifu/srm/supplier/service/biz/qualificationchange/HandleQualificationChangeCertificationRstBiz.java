package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.repository.enums.NoticeTemplateEnum;
import com.weifu.srm.supplier.repository.enums.QualificationChangeStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.request.SysUserQueryReqDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.repository.constants.SupplierCommonConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandleQualificationChangeCertificationRstBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeCertificationItemMapperService qualificationChangeCertificationItemMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final MQServiceManager mqService;
    private final SysUserApi sysUserApi;
    private final CategoryApi categoryApi;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final SupplierCertificationMapperService supplierCertificationMapperService;

    public void handle(TicketStatusChangedMQ mq) {
        // 查询资质变更申请表
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 查询供应商基础表信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (TicketStatusEnum.APPROVING.equalsCode(mq.getStatus())) {
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getTicketNo, mq.getTicketNo())
                    .update();
        }
        // 审批通过
        if (TicketStatusEnum.APPROVED.equalsCode(mq.getStatus())) {
            // 查询工单最后一次审批详情
            LambdaUpdateWrapper<QualificationChangeApplyPO> applyPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            applyPOLambdaUpdateWrapper
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.AGREE_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime());
            // 获取资质证书修改详情
            QualificationChangeCertificationItemPO certificationItemPO = qualificationChangeCertificationItemMapperService.lambdaQuery()
                    .eq(QualificationChangeCertificationItemPO::getQualificationChangeNo, mq.getBusinessNo())
                    .eq(QualificationChangeCertificationItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();
            transactionTemplate.execute(transactionStatus -> {
                // 更新申请单审核状态
                qualificationChangeApplyMapperService.update(applyPOLambdaUpdateWrapper);
                if (certificationItemPO.getOperationType().equals("delete")) {
                    supplierCertificationMapperService.lambdaUpdate()
                            .eq(SupplierCertificationPO::getId, certificationItemPO.getCertificationId())
                            .set(SupplierCertificationPO::getIsDelete, YesOrNoEnum.YES.getCode())
                            .update();
                } else {
                    SupplierCertificationPO certificationPO = qualificationChangeConvert.toCertificationPO(certificationItemPO);
                    certificationPO.setSupplierBasicMsgId(supplierBasicInfoPO.getId());
                    certificationPO.setDataSource("QualificationChange");
                    BaseEntityUtil.setCommon(certificationPO, certificationItemPO.getCreateBy(), certificationItemPO.getCreateName(), certificationItemPO.getCreateTime());
                    certificationPO.setIsDelete(YesOrNoEnum.NO.getCode());
                    supplierCertificationMapperService.save(certificationPO);
                    // 查询出修改的临时附件，存入数据
                    AttachmentRecordPO attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                            .eq(AttachmentRecordPO::getBusinessNo, certificationItemPO.getId())
                            .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_CERTIFICATION_ADD)
                            .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                            .one();
                    attachmentRecordPO.setBusinessNo(certificationPO.getId()+"");
                    attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.SUPPLIER_CERTIFICATION);
                    attachmentRecordMapperService.save(attachmentRecordPO);
                }
                mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(changeApplyPO, certificationItemPO.getHasQualityCertification(), mq)));
                return null;
            });
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailSuccess(changeApplyPO,userInfo)));
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(mq.getStatus())) {
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.REJECT_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .update();
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailFail(changeApplyPO,userInfo, mq)));
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(mq.getStatus())) {
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.RECALL.getCode())
                    .update();
        }
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(QualificationChangeApplyPO changeApplyPO, Integer isQuality, TicketStatusChangedMQ ticketInfo) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        HashMap<Long, String> userMap = new HashMap<>();
        //问题序号642 质量类型通知人需为供应商关联品类下关联人
        LambdaQueryWrapper<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierCategoryRelationshipPOLambdaQueryWrapper
                .eq(SupplierCategoryRelationshipPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierCategoryRelationshipPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.list(supplierCategoryRelationshipPOLambdaQueryWrapper);
        List<String> categoryCodes = new ArrayList<>();
        if(CollUtil.isNotEmpty(supplierCategoryRelationshipPOS)){
            categoryCodes = supplierCategoryRelationshipPOS.stream()
                    .map(SupplierCategoryRelationshipPO::getCategoryCode)
                    .collect(Collectors.toList());
        }
        ApiResponse<List<CategoryEngineerResultDTO>> userOne= categoryApi.queryCategoryEngineerByCategoryCodes(categoryCodes);
        if (isQuality == 0) {
            ////SRM-1980 授权采购处长不进行通知 非质量时也不通知授权采购处长
            if(CollUtil.isNotEmpty(userOne.getData()))
                userOne.getData().stream()
                    .filter(v->!CharSequenceUtil.equalsIgnoreCase(v.getRoleId(), CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode()))
                    .forEach(categoryEngineerResultDTO -> userMap.put(categoryEngineerResultDTO.getUserId(), categoryEngineerResultDTO.getUserName()));
        } else {
            // 查询出所有的规划处长，规划工程师
            //SRM-1980 授权采购处长不进行通知
            List<String> noticeRoles =  Arrays.asList(CategoryRoleEnum.AUTHORIZED_PEM_DIRECTOR.getCode(), CategoryRoleEnum.SQE_MASTER.getCode(), CategoryRoleEnum.SQE.getCode());
            if(CollUtil.isNotEmpty(userOne.getData()))
                userOne.getData().stream()
                        .filter(v-> CollUtil.contains(noticeRoles,v.getRoleId()))
                        .forEach(categoryEngineerResultDTO -> userMap.put(categoryEngineerResultDTO.getUserId(), categoryEngineerResultDTO.getUserName()));
        }
        userMap.forEach((k, v) -> {
            CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
            notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
            notice.setBusinessNo(ticketInfo.getBusinessNo());
            if (isQuality == 0) {
                notice.setBusinessType(MessageClsEnum.SUPPLIER_NON_QUALITY_CERTIFICATE_ADJUST_EFFECTIVE.getCode());
            } else {
                notice.setBusinessType(MessageClsEnum.SUPPLIER_QUALITY_CERTIFICATE_ADJUST_EFFECTIVE.getCode());
            }
            notice.setUserId(k);
            notice.setUserName(v);
            notice.setIconType(IconTypeEnum.GENERAL.getCode());
            String content = null;
            if (isQuality == 0) {
                notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_UN_QUALITY_COMPLETE.getTitle());
                content = NoticeTemplateEnum.QUALIFICATION_CHANGE_UN_QUALITY_COMPLETE.getContent();
            } else {
                notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_QUALITY_COMPLETE.getTitle());
                content = NoticeTemplateEnum.QUALIFICATION_CHANGE_QUALITY_COMPLETE.getContent();
            }
            content = content
                    .replace("${供应商名称}", changeApplyPO.getSupplierName())
                    .replace("${供应商编码}", changeApplyPO.getSupplierCode())
                    .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT));
            notice.setContent(content);
            result.add(notice);
        });
        return result;
    }

    private CreateSendEmailTaskMQ sendEmailSuccess(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo) {
        CreateSendEmailTaskMQ emailReq  = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_CERTIFICATION_UPDATE_SUCCESS.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_CERTIFICATION_UPDATE_SUCCESS.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private CreateSendEmailTaskMQ sendEmailFail(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo, TicketStatusChangedMQ mq) {
        CreateSendEmailTaskMQ emailReq  = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_CERTIFICATION_UPDATE_FAIL.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_CERTIFICATION_UPDATE_FAIL.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT))
                .replace("${审批意见}", mq.getApprovalComment()));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

}
