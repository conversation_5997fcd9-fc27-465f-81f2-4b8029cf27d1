package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.masterdata.response.CategoryLevelRespDTO;
import com.weifu.srm.supplier.convert.SupplierAdmissionRecordConvert;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationTypeEnum;
import com.weifu.srm.supplier.repository.enums.AdmissionStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.request.AdmissionQuestionnaireWarehousePageQueryReqDTO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireWarehousePageRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionQuestionnaireWarehouseQueryBiz {

    private final CategoryManager categoryManager;
    private final DataPermissionManager dataPermissionManager;
    private final SupplierAdmissionRecordConvert admissionRecordConvert;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierAdmissionCategoryRecordMapperService supplierAdmissionCategoryRecordMapperService;

    private static final String PC_INTERNAL_PAGE_INVESTIGATION_STOCK_LIST = "PC_INTERNAL_PAGE_INVESTIGATION_STOCK_LIST";
    private static final String LANGUAGE_EN = "en";

    public PageResponse<AdmissionQuestionnaireWarehousePageRespDTO> queryWarehouse(AdmissionQuestionnaireWarehousePageQueryReqDTO req) {
        boolean isEnglish = LANGUAGE_EN.equals(LocaleContextHolder.getLocale().getLanguage());
        DataPermissionRespDTO dataPermission = new DataPermissionRespDTO();
        if (ObjectUtil.isNull(req.getSupplierId())) {
            dataPermission =  dataPermissionManager.queryUserDataPermission(req.getOperationBy(), PC_INTERNAL_PAGE_INVESTIGATION_STOCK_LIST);
            if (dataPermission.isNo()) {
                return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, null);
            }
        }
        Page<SupplierAdmissionRecordPO> page = new Page<>(req.getPageNum(), req.getPageSize());
        Page<SupplierAdmissionRecordPO> pageResult = supplierAdmissionRecordMapperService.queryWarehouse(page,req,dataPermission);
        List<SupplierAdmissionRecordPO> poList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(poList)){
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, null);
        }
        List<AdmissionQuestionnaireWarehousePageRespDTO>  respDTOList = admissionRecordConvert.toRespDtoList(poList);
        // 补充数据
        fullSupplierInfo(respDTOList,isEnglish);
        return PageResponse.toResult(req.getPageNum(),
                req.getPageSize(),
                pageResult.getTotal(),
                respDTOList);
    }
    private void fullSupplierInfo(List<AdmissionQuestionnaireWarehousePageRespDTO>  respDTOList, boolean isEnglish) {
        // 查询-准入品类
        List<String> invitationNos = respDTOList.stream().map(AdmissionQuestionnaireWarehousePageRespDTO::getInvitationNo).collect(Collectors.toList());
        List<SupplierAdmissionCategoryRecordPO> list = supplierAdmissionCategoryRecordMapperService.lambdaQuery()
                .in(SupplierAdmissionCategoryRecordPO::getInvitationNo, invitationNos)
                .list();
        List<String> categories = list.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).distinct().collect(Collectors.toList());
        List<CategoryLevelRespDTO> categoryLevelRespDTOS = categoryManager.parentCategory(categories);
        Map<String, List<SupplierAdmissionCategoryRecordPO>> invitationMapCategories = list.stream().collect(Collectors.groupingBy(SupplierAdmissionCategoryRecordPO::getInvitationNo));

        respDTOList.forEach(r-> {
            Optional<SupplierTypeEnum> supplierTypeEnum = Optional.ofNullable(SupplierTypeEnum.getByCode(r.getSupplierType()));
            supplierTypeEnum.ifPresent(l->r.setSupplierTypeDesc(isEnglish?l.getEnglishName():l.getChineseName()));
            Optional<AdmissionInvitationTypeEnum> admissionTypeEnum = Optional.ofNullable(AdmissionInvitationTypeEnum.getByCode(r.getAdmissionType()));
            admissionTypeEnum.ifPresent(p -> {
                r.setAdmissionTypeDesc(isEnglish?p.getEnglishName():p.getChineseName());
                if (!AdmissionInvitationTypeEnum.SAMPLE_ADMISSION_TYPE.equals(p)
                        && !AdmissionInvitationTypeEnum.TMP_ADMISSION_TYPE.equals(p)) {
                    r.setQuestionnaireNo(r.getAdmissionNo());
                }
            });
            Optional<AdmissionStatusEnum> admissionStatusEnum = Optional.ofNullable(AdmissionStatusEnum.getByCode(r.getAdmissionStatus()));
            admissionStatusEnum.ifPresent(k->r.setAdmissionStatusDesc(isEnglish?k.getEnglishName():k.getChineseName()));
            // 拼接-品类名称
            List<String> level3category = invitationMapCategories.get(r.getInvitationNo()).stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).distinct().collect(Collectors.toList());
            List<String> collect = categoryLevelRespDTOS.stream().filter(dto -> level3category.contains(dto.getLevelThirdCode()))
                    .map(s -> s.getLevelOneName().concat("-").concat(s.getLevelTwoName()).concat("-").concat(s.getLevelThirdName()))
                    .collect(Collectors.toList());
            r.setAdmissionCategoryNames(collect);
        });
    }
}
