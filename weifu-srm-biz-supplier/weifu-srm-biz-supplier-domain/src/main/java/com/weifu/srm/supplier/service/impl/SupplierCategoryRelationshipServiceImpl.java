package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.masterdata.response.CategoryResultDTO;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import com.weifu.srm.supplier.constants.ErrorCodeConstants;
import com.weifu.srm.supplier.convert.SupplierCategoryRelationshipConvert;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyTypeEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.manager.SupplierCategoryRelationshipManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.mapper.SupplierCategoryRelationshipMapper;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.request.ListSupplierOnAssociatedCategoryReqDTO;
import com.weifu.srm.supplier.request.QuerySupplierCategoryListReqDTO;
import com.weifu.srm.supplier.request.SupplierCategoryRelPageReqDTO;
import com.weifu.srm.supplier.request.SupplierCategoryRelationshipQueryReqDTO;
import com.weifu.srm.supplier.response.ListSupplierOnAssociatedCategoryRespDTO;
import com.weifu.srm.supplier.response.SupplierCategoryRelPageRespDTO;
import com.weifu.srm.supplier.response.SupplierCategoryRelationshipRespDTO;
import com.weifu.srm.supplier.service.SupplierCategoryRelationshipService;
import com.weifu.srm.user.constants.InternalUserRoleConstants;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCategoryRelationshipServiceImpl implements SupplierCategoryRelationshipService {

    private final SupplierCategoryRelationshipManager supplierCategoryRelationshipManager;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final SupplierCategoryRelationshipMapper supplierCategoryRelationshipMapper;
    private final SupplierCategoryRelationshipConvert supplierCategoryRelationshipConvert;
    private final CategoryManager categoryManager;
    private final DataPermissionManager dataPermissionManager;
    private final CategoryApi categoryApi;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final LocaleMessage localeMessage;

    private static final String PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_RELATION = "PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_RELATION";

    @Override
    public void scheduleSupplierCategoryStatus() {
        supplierCategoryRelationshipManager.updateAllSupplierCategoryStatus();
    }

    @Override
    public List<SupplierCategoryRelationshipRespDTO> querySupplierCategoryRelationship(QuerySupplierCategoryListReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getSupplierCodes()) || CollectionUtils.isEmpty(reqDTO.getCategoryCodes())) {
            return new ArrayList<>();
        }
        List<SupplierCategoryRelationshipPO> list = supplierCategoryRelationshipMapperService.lambdaQuery()
                .in(SupplierCategoryRelationshipPO::getSapSupplierCode, reqDTO.getSupplierCodes())
                .in(CollectionUtils.isNotEmpty(reqDTO.getCategoryCodes()), SupplierCategoryRelationshipPO::getCategoryCode, reqDTO.getCategoryCodes())
                .list();
        return supplierCategoryRelationshipConvert.toRespDTOList(list);
    }

    @Override
    public List<SupplierCategoryRelationshipRespDTO> listCategoryRelationshipByParam(SupplierCategoryRelationshipQueryReqDTO reqDTO) {
        List<SupplierCategoryRelationshipPO> list = supplierCategoryRelationshipMapperService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(reqDTO.getSupplierCodes()), SupplierCategoryRelationshipPO::getSapSupplierCode, reqDTO.getSupplierCodes())
                .in(StringUtils.isNotBlank(reqDTO.getCategoryCode()), SupplierCategoryRelationshipPO::getCategoryCode, reqDTO.getCategoryCode())
                .eq(SupplierCategoryRelationshipPO::getSupplierCategoryStatus, SupplierCategoryStatusEnum.QUALIFIED_STATUS.getCode())
                .list();
        return supplierCategoryRelationshipConvert.toRespDTOList(list);
    }

    @Override
    public List<ListSupplierOnAssociatedCategoryRespDTO> listSupplierOnAssociatedCategory(ListSupplierOnAssociatedCategoryReqDTO req) {
        List<CategoryEngineerResultDTO> categoryEngineers = categoryManager.queryCategoryEngineerByUserId(req.getUserId());
        if (CollectionUtils.isEmpty(categoryEngineers)) {
            return new ArrayList<>();
        }

        List<String> categoryCodes = categoryEngineers.stream()
                .filter(engineer -> StringUtils.equals(engineer.getRoleId(), InternalUserRoleConstants.CPE))
                .map(CategoryEngineerResultDTO::getCategoryCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryCodes)) {
            return new ArrayList<>();
        }

        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_ADD.equalsCode(req.getApplyType())) {
            // 根据三级品类编码查询所有同二级品类下的三级品类
            ApiResponse<List<CategoryWithAllLevelDTO>> apiResponse = categoryApi.listCategoryOnSameFather(categoryCodes);
            if (!apiResponse.getSucc()) {
                throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.QUERY_CATEGORY_INFO_EXCEPTION));
            }
            categoryCodes = apiResponse.getData().stream().map(CategoryWithAllLevelDTO::getThreeLevelCategoryCode).collect(Collectors.toList());
        }
        return supplierCategoryRelationshipMapper.listSupplierWithCategory(categoryCodes, req.getSearchCriteria(), req.getLimit());
    }

    @Override
    public PageResponse<SupplierCategoryRelPageRespDTO> page(SupplierCategoryRelPageReqDTO req) {
        DataPermissionRespDTO permission = dataPermissionManager.queryUserDataPermission(req.getOperateBy(), PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_RELATION);
        if (permission.isNo()) {
            log.info("分页查询供应商品类关系，无数据权限，userId={}", req.getOperateBy());
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, Collections.emptyList());
        }

        Page<SupplierCategoryRelPageRespDTO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<SupplierCategoryRelPageRespDTO> iPage = supplierCategoryRelationshipMapper.page(page, req, permission.getKeys());
        PageResponse<SupplierCategoryRelPageRespDTO> result = PageResponse.toResult(
                req.getPageNum(), req.getPageSize(), iPage.getTotal(), iPage.getRecords());
        // 补全品类信息
        fillCategoryInfo(iPage.getRecords());
        return result;
    }

    @Override
    public List<SupplierCategoryRelPageRespDTO> allBySupplierCode(SupplierCategoryRelPageReqDTO req) {
        DataPermissionRespDTO permission = dataPermissionManager.queryUserDataPermission(req.getOperateBy(), PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_RELATION);
        if (permission.isNo()) {
            log.info("分页查询供应商品类关系，无数据权限，userId={}", req.getOperateBy());
            return Collections.emptyList();
        }
        List<SupplierCategoryRelPageRespDTO> categories = supplierCategoryRelationshipMapper.allBySupplierCode(req, permission.getKeys());
        if (CollectionUtils.isEmpty(categories)) {
            return new ArrayList<>();
        }
        // 填充品类名称
        List<String> categoryCodes = categories.stream()
                .map(SupplierCategoryRelPageRespDTO::getThreeLevelCategoryCode).distinct().collect(Collectors.toList());
        List<CategoryResultDTO> categoryList = categoryManager.getCategoryList(categoryCodes);
        Map<String, String> codeMapName = categoryList.stream().collect(Collectors.toMap(CategoryResultDTO::getCategoryCode, CategoryResultDTO::getCategoryName, (dto1, dto2) -> dto1));
        for (SupplierCategoryRelPageRespDTO category : categories) {
            category.setThreeLevelCategoryName(codeMapName.get(category.getThreeLevelCategoryCode()));
        }
        return categories;
    }

    @Override
    public List<SupplierCategoryRelPageRespDTO> listForExport(SupplierCategoryRelPageReqDTO req) {
        DataPermissionRespDTO permission = dataPermissionManager.queryUserDataPermission(req.getOperateBy(), PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_RELATION);
        if (permission.isNo()) {
            log.info("查询供应商品类关系，无数据权限，userId={}", req.getOperateBy());
            return Collections.emptyList();
        }

        Integer pageSize = req.getPageSize();
        Integer offset = (req.getPageNum() - 1) * pageSize;
        List<SupplierCategoryRelPageRespDTO> relations = supplierCategoryRelationshipMapper.listForExport(req, permission.getKeys(), offset, pageSize);
        fillCategoryInfo(relations);
        return relations;
    }

    @Override
    public List<SupplierCategoryRelationshipRespDTO> querySupplierByCategoryCodeLevel3(String categoryCode) {
        List<SupplierCategoryRelationshipPO> list = supplierCategoryRelationshipMapperService.lambdaQuery()
                .eq(SupplierCategoryRelationshipPO::getCategoryCode, categoryCode)
                .list();
        List<SupplierCategoryRelationshipRespDTO> respDTOList = supplierCategoryRelationshipConvert.toRespDTOList(list);
        if (CollectionUtils.isEmpty(respDTOList)) {
            return Collections.emptyList();
        }
        // 补充供应商信息
        List<String> supplierCodes = respDTOList.stream().map(SupplierCategoryRelationshipRespDTO::getSapSupplierCode).collect(Collectors.toList());
        List<SupplierBasicInfoPO> supplierList = supplierBasicInfoMapperService.lambdaQuery()
                .in(SupplierBasicInfoPO::getSapSupplierCode, supplierCodes)
                .list();
        for (SupplierCategoryRelationshipRespDTO respDTO : respDTOList) {
            supplierList.stream()
                    .filter(supplier -> supplier.getSapSupplierCode().equals(respDTO.getSapSupplierCode()))
                    .findFirst()
                    .ifPresent(supplier -> respDTO.setSupplierName(supplier.getSupplierName()));
        }
        return respDTOList;
    }

    /**
     * 补全品类信息
     */
    private void fillCategoryInfo(List<SupplierCategoryRelPageRespDTO> relations) {
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }

        Set<String> categoryCodes = relations.stream()
                .map(SupplierCategoryRelPageRespDTO::getThreeLevelCategoryCode).collect(Collectors.toSet());
        List<CategoryWithAllLevelDTO> categorys = categoryManager.listCategory(new ArrayList<>(categoryCodes));
        Map<String, CategoryWithAllLevelDTO> categoryMap = categorys.stream()
                .collect(Collectors.toMap(CategoryWithAllLevelDTO::getThreeLevelCategoryCode, Function.identity()));
        for (SupplierCategoryRelPageRespDTO relation : relations) {
            relation.setSupplierCategoryStatusName(SupplierCategoryStatusEnum.getName(relation.getSupplierCategoryStatus(), LocaleContextHolder.getLocale()));

            CategoryWithAllLevelDTO category = categoryMap.get(relation.getThreeLevelCategoryCode());
            if (category == null) {
                continue;
            }

            relation.setOneLevelCategoryCode(category.getOneLevelCategoryCode());
            relation.setOneLevelCategoryName(category.getOneLevelCategoryName());
            relation.setOneLevelCategoryNameEn(category.getOneLevelCategoryNameEn());
            relation.setTwoLevelCategoryCode(category.getTwoLevelCategoryCode());
            relation.setTwoLevelCategoryName(category.getTwoLevelCategoryName());
            relation.setTwoLevelCategoryNameEn(category.getTwoLevelCategoryNameEn());
            relation.setThreeLevelCategoryCode(category.getThreeLevelCategoryCode());
            relation.setThreeLevelCategoryName(category.getThreeLevelCategoryName());
            relation.setThreeLevelCategoryNameEn(category.getThreeLevelCategoryNameEn());
        }
    }

}
