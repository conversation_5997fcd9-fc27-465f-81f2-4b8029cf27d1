package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.integration.response.cms.ContractItemRespDTO;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.price.enums.DataPermissionEnum;
import com.weifu.srm.supplier.constants.StringConstant;
import com.weifu.srm.supplier.convert.SupplierBasicConvert;
import com.weifu.srm.supplier.manager.CmsManager;
import com.weifu.srm.supplier.manager.SupplierBasicInfoDetailManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.performance.PerformanceManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.constants.SupplierUserRoleConstants;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierCategoryRelationShipChangeTypeEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGradeTypeEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.request.grey.SupplierGreyListSearchReqDTO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.response.grey.SupplierGreyListRespDTO;
import com.weifu.srm.supplier.service.QualificationChangeWeifuService;
import com.weifu.srm.supplier.service.SupplierBasicService;
import com.weifu.srm.supplier.service.SupplierGreyListService;
import com.weifu.srm.supplier.service.biz.SupplierAssociationRelationBiz;
import com.weifu.srm.supplier.service.biz.SupplierGetContractDetailBiz;
import com.weifu.srm.supplier.service.biz.admission.SupplierRegisterHandleAuditBiz;
import com.weifu.srm.supplier.service.biz.admission.SupplierRegisterSaveBiz;
import com.weifu.srm.supplier.service.biz.admission.SupplierRegisterStartBiz;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierBasicServiceImpl implements SupplierBasicService {

    public static final String QUERY_RESULT = "供应商360查询结果";
    public static final String BASIC = "基本信息";
    public static final String USER = "人员信息";
    public static final String FINANCIAL = "财务信息";
    public static final String ASSOCIATION = "关联方信息";
    public static final String CERTIFICATION = "资质证书信息";
    public static final String CATEGORY = "准入品类";
    public static final String PERFORMANCE = "历史绩效";
    public static final String HISTORY_GRADE = "历史分级";
    public static final String CONTRACT = "协议签署信息";
    public static final String LIFECYCLE = "生命周期";

    private final QualificationChangeWeifuService qualificationChangeWeifuService;
    private final SupplierRegisterStartBiz registerStartBiz;
    private final SupplierBasicInfoDetailManager supplierBasicInfoDetailManager;
    private final SupplierRegisterHandleAuditBiz supplierRegisterHandleAuditBiz;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierBasicConvert supplierBasicConvert;
    private final SupplierRegisterSaveBiz supplierRegisterSaveBiz;
    private final SupplierTagsMapperService supplierTagsMapperService;
    private final UserSupplierRelationshipInfoMapperService userSupplierRelationshipInfoMapperService;
    private final DataPermissionManager dataPermissionManager;
    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;
    private final CategoryApi categoryApi;
    private final CategoryManager categoryManager;
    private final SupplierGetContractDetailBiz supplierGetContractDetailBiz;
    private final SupplierKeyInfoChangeRecordMapperService supplierKeyInfoChangeRecordMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final SupplierCategoryChangeRecordMapperService supplierCategoryChangeRecordMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SupplierGreyListService supplierGreyListService;
    private final CmsManager cmsManager;
    private final SysUserManager sysUserManager;
    private final PerformanceManager performanceManager;
    private final SupplierAssociationRelationBiz supplierAssociationRelationBiz;

    @Override
    public String supplierRegisterCommit(SupplierRegisterReqDTO req) {
        return registerStartBiz.registerStart(req);
    }

    @Override
    public String supplierRegisterSave(SupplierRegisterReqDTO req) {
        return supplierRegisterSaveBiz.registerSave(req);
    }

    @Override
    public SupplierBasicDetailRespDTO searchSupplierBasicDetail(Long supplierId, String registerNo) {
        return supplierBasicInfoDetailManager.querySupplierDetailInfo(supplierId, registerNo);
    }

    @Override
    public void supplierRegisterHandleAudit(TicketStatusChangedMQ ticketStatusChangedMQ) {
        supplierRegisterHandleAuditBiz.handleAudit(ticketStatusChangedMQ);
    }

    @Override
    public PageResponse<SupplierBasicInfoListRespDTO> queryPage(SupplierBasicInfoListReqDTO reqDTO) {
        DataPermissionRespDTO pcInternalPageBasicApply = dataPermissionManager.queryUserDataPermission(reqDTO.getOperationUserId(), DataPermissionEnum.PC_INTERNAL_PAGE_SUPPLIER_LIST.getCode());
//        DataPermissionRespDTO pcInternalPageBasicApply = new DataPermissionRespDTO();
        log.info("用户权限======> {}", pcInternalPageBasicApply);
        Page<SupplierBasicInfoListSearchPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        // cpe和sqe无主责 筛选 字段为null的，
        List<String> cpeMainNameList = reqDTO.getCpeMainNames();
        if (CollUtil.isNotEmpty(cpeMainNameList) && cpeMainNameList.contains("无主责")) {
            cpeMainNameList.add("NOTHING");
            reqDTO.setCpeMainNames(cpeMainNameList);
        }
        List<String> sqeMainNameList = reqDTO.getSqeMainNames();
        if (CollUtil.isNotEmpty(sqeMainNameList) && sqeMainNameList.contains("无主责")) {
            sqeMainNameList.add("NOTHING");
            reqDTO.setSqeMainNames(sqeMainNameList);
        }
        SupplierBasicInfoListSearchPO searPO = supplierBasicConvert.toSearPO(reqDTO);
        Page<SupplierBasicInfoListSearchPO> pageResult = supplierBasicInfoMapperService.queryPageList(page, searPO, pcInternalPageBasicApply);
        List<SupplierBasicInfoListRespDTO> listSearRespDTO = supplierBasicConvert.toListSearRespDTO(pageResult.getRecords());
        List<String> sapSupplierCodeList = listSearRespDTO.stream().map(SupplierBasicInfoListRespDTO::getSapSupplierCode).collect(Collectors.toList());
        // 最新绩效
        Map<String, BigDecimal> stringBigDecimalMap = performanceManager.newYearScoreList(sapSupplierCodeList);

        for (SupplierBasicInfoListRespDTO dto :listSearRespDTO){
            try {
                if(stringBigDecimalMap.containsKey(dto.getSapSupplierCode())){
                    dto.setLatestPerformance(stringBigDecimalMap.get(dto.getSapSupplierCode()));
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(reqDTO.getTags()) || CollectionUtils.isEmpty(listSearRespDTO)) {
            return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), listSearRespDTO);
        }

        // 如果使用Tag作为过滤条件，查询出来的结果列表Tag将只有过滤Tag 需要单独查询其它Tag进行补充
        List<SupplierTagsPO> supplierTagsPOS = supplierTagsMapperService.searchTags(sapSupplierCodeList);
        Map<String, List<SupplierTagsPO>> collect = supplierTagsPOS.stream().collect(Collectors.groupingBy(SupplierTagsPO::getSupplierCode));
        for (SupplierBasicInfoListRespDTO supplierBasicInfoListRespDTO : listSearRespDTO) {
            if (ObjectUtils.isEmpty(supplierBasicInfoListRespDTO) || StringUtils.isBlank(supplierBasicInfoListRespDTO.getSapSupplierCode())) {
                continue;
            }
            List<SupplierTagsPO> tags = collect.get(supplierBasicInfoListRespDTO.getSapSupplierCode());
            if (CollectionUtils.isEmpty(tags)) {
                continue;
            }
            List<String> tagCodes = tags.stream().map(SupplierTagsPO::getTagCode).collect(Collectors.toList());
            supplierBasicInfoListRespDTO.setTags(tagCodes);
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), listSearRespDTO);
    }

    @Override
    public PageResponse<SupplierBasicInfoListRespDTO> queryPageForQC(SupplierBasicInfoListReqDTO reqDTO) {
        Page<SupplierBasicInfoListSearchPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        SupplierBasicInfoListSearchPO searPO = supplierBasicConvert.toSearPO(reqDTO);
        IPage<SupplierBasicInfoListSearchPO> pageResult = supplierBasicInfoMapperService.queryPageListForQC(page, searPO);
        List<SupplierBasicInfoListRespDTO> listSearRespDTO = supplierBasicConvert.toListSearRespDTO(pageResult.getRecords());
        if (CollectionUtils.isEmpty(reqDTO.getTags()) || CollectionUtils.isEmpty(listSearRespDTO)) {
            return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), listSearRespDTO);
        }
        // 如果使用Tag作为过滤条件，查询出来的结果列表Tag将只有过滤Tag 需要单独查询其它Tag进行补充
        List<SupplierTagsPO> supplierTagsPOS = supplierTagsMapperService.searchTags(listSearRespDTO.stream().map(SupplierBasicInfoListRespDTO::getSapSupplierCode).collect(Collectors.toList()));
        Map<String, List<SupplierTagsPO>> collect = supplierTagsPOS.stream().collect(Collectors.groupingBy(SupplierTagsPO::getSupplierCode));
        for (SupplierBasicInfoListRespDTO supplierBasicInfoListRespDTO : listSearRespDTO) {
            List<String> tagCodes = collect.get(supplierBasicInfoListRespDTO.getSapSupplierCode()).stream().map(SupplierTagsPO::getTagCode).collect(Collectors.toList());
            supplierBasicInfoListRespDTO.setTags(tagCodes);
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), listSearRespDTO);
    }

    @Override
    public PageResponse<SupplierBasicSelectRespDTO> selectList(SupplierBasicSelectReqDTO reqDTO) {
        Page<SupplierBasicInfoPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<SupplierBasicInfoPO> pageResult = supplierBasicInfoMapperService.queryByLikeNameAndCode(page, reqDTO);
        List<SupplierBasicSelectRespDTO> dtoList = supplierBasicConvert.toListSupplierBasicSelectRespDTO(pageResult.getRecords());
        for (SupplierBasicSelectRespDTO dto : dtoList) {
            SupplierBasicStatusEnum supplierBasicStatusEnum = SupplierBasicStatusEnum.getByCode(dto.getStatus());
            dto.setStatusName(supplierBasicStatusEnum != null ? supplierBasicStatusEnum.getChineseName() : null);
            if (StringUtils.isBlank(dto.getGrade())) {
                continue;
            }
            dto.setGrade(SupplierGradeTypeEnum.getByCode(dto.getGrade()).getChineseName());
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public PageResponse<SupplierBasicInfoInquiryOrderRespDTO> queryForInquiryOrder(SupplierForInquiryOrderQueryReqDTO reqDTO) {
        Page<SupplierBasicInfoInquiryOrderRespDTO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        if (Objects.equals(YesOrNoEnum.YES.getCode(), reqDTO.getIsExcludeSampleSupplier())) {
            reqDTO.setExcludeSupplierCategoryStatusAry(List.of(SupplierCategoryStatusEnum.SAMPLE_STATUS.getCode()));
        }
        reqDTO.setSynSapResult(YesOrNoEnum.YES.getCode());
        List<String> statusAry = new ArrayList<>();
        statusAry.add(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getCode());
        statusAry.add(SupplierBasicStatusEnum.POTENTIAL_SUPPLIER.getCode());
        statusAry.add(SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode());
        reqDTO.setStatus(statusAry);
        IPage<SupplierBasicInfoInquiryOrderRespDTO> pageData = supplierBasicInfoMapperService.queryForInquiryOrder(page, reqDTO);
        List<SupplierBasicInfoInquiryOrderRespDTO> inquiryOrderRespDTOs = pageData.getRecords();
        inquiryOrderRespDTOs = this.filterSupplier(reqDTO, inquiryOrderRespDTOs);
        this.fillSupplierData(reqDTO, inquiryOrderRespDTOs);
        // 排序...
        inquiryOrderRespDTOs = inquiryOrderRespDTOs.stream().sorted((x, y) -> {
            SupplierGradeTypeEnum xGradeType = SupplierGradeTypeEnum.getByCode(x.getSupplierClassification());
            SupplierGradeTypeEnum yGradeType = SupplierGradeTypeEnum.getByCode(y.getSupplierClassification());
            if (xGradeType == null || yGradeType == null) {
                return Integer.MAX_VALUE;
            }
            return xGradeType.getSorted().compareTo(yGradeType.getSorted());
        }).collect(Collectors.toList());
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageData.getTotal(), inquiryOrderRespDTOs);
    }

    private List<SupplierBasicInfoInquiryOrderRespDTO> filterSupplier(SupplierForInquiryOrderQueryReqDTO reqDTO,
                                                                      List<SupplierBasicInfoInquiryOrderRespDTO> inquiryOrderRespDTOs) {
        if (CollectionUtils.isEmpty(inquiryOrderRespDTOs) ||
                CollectionUtils.isEmpty(reqDTO.getDivisionIds()) ||
                StringUtils.isBlank(reqDTO.getCategorySecond())) {
            return inquiryOrderRespDTOs;
        }
        List<String> supplierCodes = inquiryOrderRespDTOs.stream()
                .map(SupplierBasicInfoInquiryOrderRespDTO::getSapSupplierCode)
                .collect(Collectors.toList());
        SupplierGreyListSearchReqDTO greyListApplyListReqDTO = new SupplierGreyListSearchReqDTO();
        greyListApplyListReqDTO.setSupplierCodes(supplierCodes);
        greyListApplyListReqDTO.setLimit(Integer.MAX_VALUE);
        greyListApplyListReqDTO.setLevel2CategoryCode(reqDTO.getCategorySecond());
        List<SupplierGreyListRespDTO> supplierGreyListRespDTOs = supplierGreyListService.searchGreyList(greyListApplyListReqDTO);
        if (CollectionUtils.isEmpty(supplierGreyListRespDTOs)) {
            return inquiryOrderRespDTOs;
        }
        Map<String, List<SupplierGreyListRespDTO>> groupMap = supplierGreyListRespDTOs.stream()
                .filter(o -> reqDTO.getDivisionIds().contains(o.getDivisionId()))
                .collect(Collectors.groupingBy(SupplierGreyListRespDTO::getSupplierCode));
        return inquiryOrderRespDTOs.stream()
                .filter(o -> CollectionUtils.isEmpty(groupMap.get(o.getSapSupplierCode())))
                .collect(Collectors.toList());
    }

    private void fillSupplierData(SupplierForInquiryOrderQueryReqDTO reqDTO, List<SupplierBasicInfoInquiryOrderRespDTO> inquiryOrderRespDTOs) {
        if (CollectionUtils.isEmpty(inquiryOrderRespDTOs)) {
            return;
        }
        List<String> similarMaterialCodeSuppliers = reqDTO.getSimilarMaterialCodeSuppliers();

        List<Long> supplierIds = inquiryOrderRespDTOs.stream()
                .map(SupplierBasicInfoInquiryOrderRespDTO::getSupplierId)
                .collect(Collectors.toList());
        // 注册联系人...
        List<UserSupplierRelationshipInfoPO> relationshipInfos = userSupplierRelationshipInfoMapperService.lambdaQuery()
                .in(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, supplierIds)
                .eq(UserSupplierRelationshipInfoPO::getRoleId, SupplierUserRoleConstants.SUPPLIER_REGISTER_CONTACTS)
                .list();
        Map<Long, UserSupplierRelationshipInfoPO> relationshipInfoMap = relationshipInfos.stream()
                .collect(Collectors.toMap(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, o -> o));

        List<SupplierRecommendedReqDTO> recommendedSuppliers = reqDTO.getRecommendedSuppliers();
        Map<String, String> recommendedSupplierMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(recommendedSuppliers)) {
            recommendedSupplierMap = recommendedSuppliers.stream()
                    .collect(Collectors.toMap(SupplierRecommendedReqDTO::getRecommendedSupplier,
                            SupplierRecommendedReqDTO::getRecommendedAttribute, (x, y) -> x));
        }

        for (SupplierBasicInfoInquiryOrderRespDTO inquiryOrderRespDTO : inquiryOrderRespDTOs) {
            // 标记供应商是否是相似物料号供应商...
            if (CollectionUtils.isNotEmpty(similarMaterialCodeSuppliers) &&
                    similarMaterialCodeSuppliers.contains(inquiryOrderRespDTO.getSapSupplierCode())) {
                inquiryOrderRespDTO.setIsSimilarMaterialCodeSupplier(YesOrNoEnum.YES.getCode());
            } else {
                inquiryOrderRespDTO.setIsSimilarMaterialCodeSupplier(YesOrNoEnum.NO.getCode());
            }
            // 标记供应商是否是推荐供应商...
            if (recommendedSupplierMap.containsKey(inquiryOrderRespDTO.getSupplierName())) {
                inquiryOrderRespDTO.setIsRecommendedSupplier(YesOrNoEnum.YES.getCode());
                inquiryOrderRespDTO.setRecommendedAttribute(recommendedSupplierMap.get(inquiryOrderRespDTO.getSupplierName()));
            } else {
                inquiryOrderRespDTO.setIsRecommendedSupplier(YesOrNoEnum.NO.getCode());
            }
            // 注册联系人...
            UserSupplierRelationshipInfoPO relationshipInfoPO = relationshipInfoMap.get(inquiryOrderRespDTO.getSupplierId());
            if (relationshipInfoPO != null) {
                inquiryOrderRespDTO.setSupplierUserId(relationshipInfoPO.getSysUserId());
            }
        }
    }

    @Override
    public List<SupplierBasicInfoInquiryOrderRespDTO> queryForSourcingBySupplierCodes(List<String> supplierCodes, String categoryCode) {
        List<SupplierBasicInfoInquiryOrderRespDTO> inquiryOrderRespDTOs =
                supplierBasicInfoMapperService.queryForSourcingBySupplierCodes(supplierCodes);
        if (CollectionUtils.isNotEmpty(inquiryOrderRespDTOs)) {
            List<Long> supplierIds = inquiryOrderRespDTOs.stream()
                    .map(SupplierBasicInfoInquiryOrderRespDTO::getSupplierId)
                    .collect(Collectors.toList());
            // 注册联系人...
            List<UserSupplierRelationshipInfoPO> userRelationships = userSupplierRelationshipInfoMapperService.lambdaQuery()
                    .in(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, supplierIds)
                    .eq(UserSupplierRelationshipInfoPO::getRoleId, SupplierUserRoleConstants.SUPPLIER_REGISTER_CONTACTS)
                    .list();
            Map<Long, UserSupplierRelationshipInfoPO> userRelationshipInfoMap = userRelationships.stream()
                    .collect(Collectors.toMap(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, o -> o, (x, y) -> x));

            Map<String, SupplierCategoryRelationshipPO> categoryRelationshipMap = new HashMap<>();
            if (StringUtils.isNoneBlank(categoryCode)) {
                List<SupplierCategoryRelationshipPO> categoryRelationships = supplierCategoryRelationshipMapperService
                        .queryBySapSupplierCodeList(supplierCodes);
                categoryRelationshipMap = categoryRelationships.stream()
                        .filter(o -> Objects.equals(categoryCode, o.getCategoryCode()))
                        .collect(Collectors.toMap(SupplierCategoryRelationshipPO::getSapSupplierCode, o -> o, (x, y) -> x));
            }

            for (SupplierBasicInfoInquiryOrderRespDTO inquiryOrderRespDTO : inquiryOrderRespDTOs) {
                UserSupplierRelationshipInfoPO userRelationshipInfoPO = userRelationshipInfoMap.get(inquiryOrderRespDTO.getSupplierId());
                if (userRelationshipInfoPO != null) {
                    inquiryOrderRespDTO.setSupplierUserId(userRelationshipInfoPO.getSysUserId());
                }
                SupplierCategoryRelationshipPO categoryRelationship = categoryRelationshipMap.get(inquiryOrderRespDTO.getSapSupplierCode());
                if (categoryRelationship != null) {
                    inquiryOrderRespDTO.setSupplierCategoryStatus(categoryRelationship.getSupplierCategoryStatus());
                    inquiryOrderRespDTO.setAdmissionCompleteTime(categoryRelationship.getAdmissionCompleteTime());
                }
            }
        }
        return inquiryOrderRespDTOs;
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> listBySupplierCodes(List<String> supplierCodes) {
        return supplierBasicInfoMapperService.listBySupplierCodes(supplierCodes);
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> listEligibleBySupplierCodes(List<String> supplierCodes) {
        List<SupplierBasicInfoPO> poList = supplierBasicInfoMapperService.listEligibleBySupplierCodes(supplierCodes);
        return BeanUtil.copyToList(poList, SupplierBasicSimpleInfoRespDTO.class);
    }

    @Override
    public List<String> searchSupplierCode(String codeKey, List<String> codes) {
        if (StringUtils.isNotBlank(codeKey)) {
            return supplierBasicInfoMapperService.lambdaQuery().like(SupplierBasicInfoPO::getSapSupplierCode, codeKey).list().stream().map(SupplierBasicInfoPO::getSapSupplierCode).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(codes)) {
            return supplierBasicInfoMapperService.lambdaQuery().in(SupplierBasicInfoPO::getSapSupplierCode, codes).list().stream().map(SupplierBasicInfoPO::getSapSupplierCode).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<SupplierBasicInfoRespDTO> getSupplierInfoByCodes(List<String> supplierCodes) {
        return supplierBasicInfoDetailManager.getSupplierInfoByCodes(supplierCodes);
    }

    @Override
    public List<SupplierGetAllHistoryGradeRespDTO> getAllHistoryGrade(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        return supplierGradeAdjustApplyItemMapperService.getAllHistoryGrade(reqDTO);
    }

    @Override
    public List<SupplierGetAllCategoryRespDTO> getAllCategory(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, reqDTO.getSupplierCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.lambdaQuery()
                .eq(SupplierCategoryRelationshipPO::getSapSupplierCode, reqDTO.getSupplierCode())
                .eq(SupplierCategoryRelationshipPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        List<String> categoryCodelist = supplierCategoryRelationshipPOS.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toList());
        ApiResponse<List<CategoryWithAllLevelDTO>> listApiResponse = categoryApi.listCategory(categoryCodelist);
        List<SupplierGetAllCategoryRespDTO> result = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(listApiResponse.getData())) {
            listApiResponse.getData().forEach(categoryWithAllLevelDTO -> {
                SupplierGetAllCategoryRespDTO supplierGetAllCategoryRespDTO = new SupplierGetAllCategoryRespDTO();
                supplierGetAllCategoryRespDTO.setSupplierCode(reqDTO.getSupplierCode());
                supplierGetAllCategoryRespDTO.setSupplierName(supplierBasicInfoPO.getSupplierName());
                supplierGetAllCategoryRespDTO.setOneLevelCategoryName(categoryWithAllLevelDTO.getOneLevelCategoryName());
                supplierGetAllCategoryRespDTO.setTwoLevelCategoryName(categoryWithAllLevelDTO.getTwoLevelCategoryName());
                supplierGetAllCategoryRespDTO.setThreeLevelCategoryName(categoryWithAllLevelDTO.getThreeLevelCategoryName());
                // 品类资质
                supplierCategoryRelationshipPOS.forEach(supplierCategoryRelationshipPO -> {
                    if (supplierCategoryRelationshipPO.getCategoryCode().equals(categoryWithAllLevelDTO.getThreeLevelCategoryCode())) {
                        supplierGetAllCategoryRespDTO.setCategoryStatus(supplierCategoryRelationshipPO.getSupplierCategoryStatus());
                    }
                });
                result.add(supplierGetAllCategoryRespDTO);
            });
        }
        return result;
    }

    @Override
    public List<SupplierGetHistoryCategoryChangeRespDTO> getHistoryCategoryChange(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        List<SupplierCategoryChangeRecordPO> changeRecords = supplierCategoryChangeRecordMapperService.lambdaQuery()
                .eq(SupplierCategoryChangeRecordPO::getSapSupplierCode, reqDTO.getSupplierCode())
                .eq(SupplierCategoryChangeRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .orderByDesc(SupplierCategoryChangeRecordPO::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(changeRecords)) {
            return new ArrayList<>();
        }

        Set<String> categoryCodes = changeRecords.stream().map(SupplierCategoryChangeRecordPO::getCategoryCode).collect(Collectors.toSet());
        Map<String, CategoryWithAllLevelDTO> categoryMap = categoryManager.mapCategory(new ArrayList<>(categoryCodes));

        List<SupplierGetHistoryCategoryChangeRespDTO> result = new ArrayList<>();
        for (SupplierCategoryChangeRecordPO changeRecord : changeRecords) {
            SupplierGetHistoryCategoryChangeRespDTO respDTO = new SupplierGetHistoryCategoryChangeRespDTO();

            CategoryWithAllLevelDTO category = categoryMap.get(changeRecord.getCategoryCode());
            if (category != null) {
                respDTO.setOneLevelCategoryName(category.getOneLevelCategoryName());
                respDTO.setTwoLevelCategoryName(category.getTwoLevelCategoryName());
                respDTO.setThreeLevelCategoryName(category.getThreeLevelCategoryName());
            }
            respDTO.setChangeType(SupplierCategoryRelationShipChangeTypeEnum.getName(changeRecord.getChangeType(), LocaleContextHolder.getLocale()));
            respDTO.setCategoryStatus(changeRecord.getSupplierCategoryStatus());
            respDTO.setChangeDate(changeRecord.getCreateTime());
            result.add(respDTO);
        }
        return result;
    }

    @Override
    public List<SupplierGetHistoryPerformanceRespDTO> getHistoryPerformance(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        return null;
    }

    @Override
    public SupplierGetContractDetailRespDTO getContractDetail(SupplierGetAllHistoryGradeReqDTO reqDTO) {
        return supplierGetContractDetailBiz.getContractDetail(reqDTO);
    }

    @Override
    public List<SupplierGetLifeCycleRespDTO> getLifeCycle(Long id) {
        List<SupplierKeyInfoChangeRecordPO> supplierKeyInfoChangeRecordPOS = supplierKeyInfoChangeRecordMapperService.lambdaQuery()
                .eq(SupplierKeyInfoChangeRecordPO::getSupplierBasicMsgId, id)
                .eq(SupplierKeyInfoChangeRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        // 查询基本信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, id)
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        ArrayList<SupplierGetLifeCycleRespDTO> result = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(supplierKeyInfoChangeRecordPOS)) {
            supplierKeyInfoChangeRecordPOS.forEach(changeRecord -> {
                SupplierGetLifeCycleRespDTO respDTO = new SupplierGetLifeCycleRespDTO();
                respDTO.setSupplierCode(supplierBasicInfoPO.getSapSupplierCode());
                respDTO.setSupplierName(supplierBasicInfoPO.getSupplierName());
                respDTO.setLifeCycleDate(changeRecord.getCreateTime());
                respDTO.setLifeCycleType(changeRecord.getContent());
                respDTO.setRemark(changeRecord.getRemark());
                result.add(respDTO);
            });
        }
        return result;
    }

    @Override
    public List<SupplierBasicInfoListRespExportDTO> getDetailById(List<Long> supplierIds) {
        List<SupplierBasicInfoListRespExportDTO> result = supplierBasicInfoMapperService.getDetailById(supplierIds);
        // 如果使用Tag作为过滤条件，查询出来的结果列表Tag将只有过滤Tag 需要单独查询其它Tag进行补充
        List<String> codes = result.stream().map(SupplierBasicInfoListRespExportDTO::getSapSupplierCode).collect(Collectors.toList());
        List<SupplierTagsPO> supplierTagsPOS = supplierTagsMapperService.searchTags(codes);
        Map<String, List<SupplierTagsPO>> collect = supplierTagsPOS.stream().collect(Collectors.groupingBy(SupplierTagsPO::getSupplierCode));

        Map<String, BigDecimal> stringBigDecimalMap = performanceManager.newYearScoreList(codes);

        for (SupplierBasicInfoListRespExportDTO dto :result){
            try {
                if(stringBigDecimalMap.containsKey(dto.getSapSupplierCode())){
                    dto.setLatestPerformance(stringBigDecimalMap.get(dto.getSapSupplierCode()));
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        }

        for (SupplierBasicInfoListRespExportDTO supplierBasicInfoListRespDTO : result) {
            List<SupplierTagsPO> tagsList = collect.get(supplierBasicInfoListRespDTO.getSapSupplierCode());
            if (CollUtil.isEmpty(tagsList)) {
                continue;
            }
            String tags = StringUtils.join(tagsList.stream().map(SupplierTagsPO::getTagCode).collect(Collectors.toList()), ",");
            supplierBasicInfoListRespDTO.setTags(tags);
        }
        return result;
    }

    private void setFileName(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String encodeFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodeFileName);
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> queryEffectiveSupplier(SupplierBasicSelectReqDTO reqDTO) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getCode());
        statusAry.add(SupplierBasicStatusEnum.POTENTIAL_SUPPLIER.getCode());
        statusAry.add(SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode());
        reqDTO.setStatus(statusAry);
        List<SupplierBasicSimpleInfoRespDTO> simpleInfoRespDTOs = supplierBasicInfoMapperService.queryEffectiveSupplier(reqDTO);
        if (CollectionUtils.isNotEmpty(simpleInfoRespDTOs)) {
            simpleInfoRespDTOs = simpleInfoRespDTOs.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getSapSupplierCode()))
                    .collect(Collectors.toList());
        }
        return simpleInfoRespDTOs;
    }

    @Override
    public void updateLogo(LogoAttachmentMessageReqDTO reqDTO) {
        // 删除logo附件
        attachmentRecordMapperService.lambdaUpdate()
                .eq(AttachmentRecordPO::getBusinessNo, reqDTO.getSupplierId() + "")
                .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.SUPPLIER_LOGO)
                .set(AttachmentRecordPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        // 新增logo附件
        AttachmentRecordPO attachmentRecordPO = new AttachmentRecordPO();
        attachmentRecordPO.setBusinessNo(reqDTO.getSupplierId() + "");
        attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.SUPPLIER_LOGO);
        attachmentRecordPO.setFileName(reqDTO.getFileName());
        attachmentRecordPO.setFileOriginalName(reqDTO.getFileOriginalName());
        attachmentRecordPO.setFileUrl(reqDTO.getFileUrl());
        BaseEntityUtil.setCommon(attachmentRecordPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
        attachmentRecordPO.setIsDelete(YesOrNoEnum.NO.getCode());
        attachmentRecordMapperService.save(attachmentRecordPO);
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> queryByKeyword(String keyword) {
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.and(StringUtils.isNotBlank(keyword), wp -> {
            wp.like(SupplierBasicInfoPO::getSapSupplierCode, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierName, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierShortName, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierNameEN, keyword);
        });
        query.ne(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.LEAVED.getCode());
        query.and(wp -> wp.isNotNull(SupplierBasicInfoPO::getSapSupplierCode).ne(SupplierBasicInfoPO::getSapSupplierCode, StringUtils.EMPTY));

        List<SupplierBasicInfoPO> list = supplierBasicInfoMapperService.list(query);
        if (CollUtil.isEmpty(list)) return Collections.emptyList();

        return BeanUtil.copyToList(list, SupplierBasicSimpleInfoRespDTO.class);
    }

    @Override
    public SupplierGetSqeAndCpeRespDTO getSqeAndCpe() {
        List<SupplierBasicInfoPO> sqeSupplierBasicInfoPOS = supplierBasicInfoMapperService.lambdaQuery()
                .ne(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.DRAFT.getCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .isNotNull(SupplierBasicInfoPO::getSqeMainName)
                .list();
        List<SupplierBasicInfoPO> cpeSupplierBasicInfoPOS = supplierBasicInfoMapperService.lambdaQuery()
                .ne(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.DRAFT.getCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .isNotNull(SupplierBasicInfoPO::getCpeMainName)
                .list();
        List<String> sqeNames = sqeSupplierBasicInfoPOS.stream().map(SupplierBasicInfoPO::getSqeMainName).distinct().collect(Collectors.toList());
        String nothing = "无主责";
        sqeNames.add(nothing);
        List<String> cpeNames = cpeSupplierBasicInfoPOS.stream().map(SupplierBasicInfoPO::getCpeMainName).distinct().collect(Collectors.toList());
        cpeNames.add(nothing);
        SupplierGetSqeAndCpeRespDTO result = new SupplierGetSqeAndCpeRespDTO();
        result.setCpeNames(cpeNames);
        result.setSqeNames(sqeNames);
        return result;
    }

    @Override
    public List<SupplierAndContractRespDTO> querySupplierInfo(QuerySupplierReqDTO param) {
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.select(SupplierBasicInfoPO::getId, SupplierBasicInfoPO::getSapSupplierCode, SupplierBasicInfoPO::getSupplierName, SupplierBasicInfoPO::getSupplierNameEN);
        query.eq(StringUtils.isNotBlank(param.getSapSupplierCode()), SupplierBasicInfoPO::getSapSupplierCode, param.getSapSupplierCode());
        query.like(StringUtils.isNotEmpty(param.getSupplierName()), SupplierBasicInfoPO::getSupplierName, param.getSupplierName());
        query.isNotNull(SupplierBasicInfoPO::getSapSupplierCode);
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());

        List<SupplierBasicInfoPO> list = supplierBasicInfoMapperService.list(query);

        if (CollUtil.isEmpty(list)) return null;

        List<SupplierAndContractRespDTO> result = BeanUtil.copyToList(list, SupplierSimpleInfoRespDTO.class)
                .stream().map(v -> new SupplierAndContractRespDTO(v)).collect(Collectors.toList());
        if (CharSequenceUtil.isBlank(param.getDivisionCode())) return result;

        List<String> supplierCodes = list.stream()
                .filter(v -> StringUtils.isNotBlank(v.getSapSupplierCode()))
                .map(SupplierBasicInfoPO::getSapSupplierCode).collect(Collectors.toList());

        Map<String, List<ContractItemRespDTO>> contractMap = cmsManager.queryCmsContractInfo(supplierCodes, param.getDivisionCode());

        for (SupplierAndContractRespDTO v : result) {
            v.setDivisionCode(param.getDivisionCode());
            String key = CharSequenceUtil.format("{}-{}", v.getSupplierInfo().getSapSupplierCode(), param.getDivisionCode());
            List<ContractItemRespDTO> contractList = getMapValue(contractMap, key);
            v.setExistsContract(CollUtil.isNotEmpty(contractList));
            v.setContract(BeanUtil.copyToList(contractList, SupplierContractItemRespDTO.class));
        }

        return result;
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> listByIds(List<Long> ids) {
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.select(SupplierBasicInfoPO::getId, SupplierBasicInfoPO::getSapSupplierCode, SupplierBasicInfoPO::getStatus,
                SupplierBasicInfoPO::getSupplierName, SupplierBasicInfoPO::getSupplierNameEN, SupplierBasicInfoPO::getSupplierShortName);
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.in(SupplierBasicInfoPO::getId, ids);

        List<SupplierBasicInfoPO> list = supplierBasicInfoMapperService.list(query);
        if (CollUtil.isEmpty(list)) return Collections.emptyList();

        return BeanUtil.copyToList(list, SupplierBasicSimpleInfoRespDTO.class);
    }


    /**
     * 2024-11-28 商务政策新建 针对集采、非集采角色选择供应商数据处理
     * 新建商务政策选择供应商数据
     * 1.如果用户是集采CPE or cpe组长（品类组长）,需判断用户是否为供应商的主责CPE,是则返回数据
     * 2.如果用户是非集采CPE or 采购主管 or 采购经理，允许选择所有供应商数据
     * 3。如果用户 既是 （非集采CPE or 采购主管 or 采购经理）又是集采CPE,不做任何限制（实际业务不会出现）
     */
    @Override
    public List<SupplierBasicSimpleInfoRespDTO> queryByKeywordAndCpeMain(String keyword, Long userId) {
        SysUserDetailRespDTO user = sysUserManager.findUserDetailById(userId);
        if(Objects.isNull(user)) return Collections.emptyList();
        //判断用户角色是否包含 集采CPE 或 非集采上述三个角色
        Boolean isExistRole = verifyExistRole(user.getRoleList(),StringConstant.CPE,StringConstant.CPE_MASTER,
                StringConstant.UN_CONCENTRATED_CPE,StringConstant.SENIOR_PURCHASE_MANAGER,StringConstant.PURCHASE_MANAGER);
        if(!BooleanUtil.isTrue(isExistRole)) return Collections.emptyList();
        //是否存在 非集采上述三个角色
        Boolean isExistUnJcRole = verifyExistRole(user.getRoleList(), StringConstant.UN_CONCENTRATED_CPE,StringConstant.SENIOR_PURCHASE_MANAGER,StringConstant.PURCHASE_MANAGER);
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        //isExistUnJcRole=false 则可认为只存在集采角色
        query.eq(BooleanUtil.isFalse(isExistUnJcRole),SupplierBasicInfoPO::getCpeMainId, userId);
        query.and(StringUtils.isNotBlank(keyword), wp -> {
            wp.like(SupplierBasicInfoPO::getSapSupplierCode, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierName, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierShortName, keyword)
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierNameEN, keyword);
        });
        query.ne(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.LEAVED.getCode());
        query.and(wp -> wp.isNotNull(SupplierBasicInfoPO::getSapSupplierCode).ne(SupplierBasicInfoPO::getSapSupplierCode, StringUtils.EMPTY));

        List<SupplierBasicInfoPO> list = supplierBasicInfoMapperService.list(query);
        if (CollUtil.isEmpty(list)) return Collections.emptyList();

        return BeanUtil.copyToList(list, SupplierBasicSimpleInfoRespDTO.class);
    }

    @Override
    public QualificationChangeQueryAssociationRespDTO querySupplierAssociation(String supplierCode) {
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getSapSupplierCode, supplierCode).one();
        return supplierAssociationRelationBiz.querySupplierAssociation(basicInfoPO);
    }

    @Override
    public List<SupplierBasicSimpleInfoRespDTO> queryByKeywordAndLimit(QuerySupplierKeywordReqDTO param) {
        LambdaQueryWrapper<SupplierBasicInfoPO> query = new LambdaQueryWrapper<>();
        query.and(StringUtils.isNotEmpty(param.getKeyword()), wp -> {
            wp.like(SupplierBasicInfoPO::getSapSupplierCode, param.getKeyword())
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierName, param.getKeyword())
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierShortName, param.getKeyword())
                    .or()
                    .like(SupplierBasicInfoPO::getSupplierNameEN, param.getKeyword());
        });
        query.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.ne(SupplierBasicInfoPO::getStatus, SupplierBasicStatusEnum.LEAVED.getCode());
        query.and(wp -> wp.isNotNull(SupplierBasicInfoPO::getSapSupplierCode).ne(SupplierBasicInfoPO::getSapSupplierCode, StringUtils.EMPTY));
        //限制返回条数
        IPage<SupplierBasicInfoPO> page = new Page<>(1L,param.getLimit());

        IPage<SupplierBasicInfoPO> pageRs = supplierBasicInfoMapperService.page(page,query);
        if (CollUtil.isEmpty(pageRs.getRecords())) return Collections.emptyList();

        return BeanUtil.copyToList(pageRs.getRecords(), SupplierBasicSimpleInfoRespDTO.class);
    }

    @Override
    public QualificationChangeQueryAssociationRespDTO queryAssociationBySupplierId(Long supplierId) {
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.getById(supplierId);
        return supplierAssociationRelationBiz.querySupplierAssociation(basicInfoPO);
    }

    private static <K, V> V getMapValue(Map<K, V> map, K key) {
        return Objects.nonNull(map) && Objects.nonNull(key) ? map.get(key) : null;
    }

    private static Boolean verifyExistRole(List<SysUserDetailRespDTO.SysUserDetailRoleRespDTO> roles,String... roleCode){
        if(CollUtil.isEmpty(roles) || Objects.isNull(roleCode)) return false;
        return roles.stream().anyMatch(v->CollUtil.contains(Arrays.asList(roleCode),v.getRoleId()));
    }
}
