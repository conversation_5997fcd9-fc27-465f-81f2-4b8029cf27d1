package com.weifu.srm.supplier.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.integration.api.FSSCApi;
import com.weifu.srm.integration.request.fssc.PolicyReqDTO;
import com.weifu.srm.supplier.repository.po.BusinessPolicyListPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class FSSCManager {
    private final FSSCApi fsscApi;

    public Boolean importFSSC(BusinessPolicyListPO policy,String submitDomain) {

        PolicyReqDTO sapParam = BeanUtil.toBean(policy, PolicyReqDTO.class);
        sapParam.setSourceId(StrUtil.toString(policy.getId()));
        sapParam.setSubmitDomain(submitDomain);

        log.info("商务政策同步FSSC id:{} sapParam:{}", policy.getId(), sapParam);
        ApiResponse<Boolean> resp = fsscApi.paymentProvision(sapParam);
        log.info("商务政策同步FSSC id:{} resp:{}", policy.getId(), resp);

        if (!BooleanUtil.isTrue(resp.getSucc()))
            throw new BizFailException(resp.getMsg());

        return resp.getData();
    }
}
