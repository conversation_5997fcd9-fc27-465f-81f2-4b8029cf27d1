package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 供应商基础服务
 */
public interface SupplierBasicService {

    String supplierRegisterCommit(SupplierRegisterReqDTO req);

    String supplierRegisterSave(SupplierRegisterReqDTO req);

    SupplierBasicDetailRespDTO searchSupplierBasicDetail(@RequestParam(value = "supplierId", required = false) Long supplierId,
                                                         @RequestParam(value = "registerNo", required = false) String registerNo);

    void supplierRegisterHandleAudit(TicketStatusChangedMQ ticketStatusChangedMQ);

    PageResponse<SupplierBasicInfoListRespDTO> queryPage(SupplierBasicInfoListReqDTO reqDTO);

    PageResponse<SupplierBasicInfoListRespDTO> queryPageForQC(SupplierBasicInfoListReqDTO reqDTO);

    PageResponse<SupplierBasicSelectRespDTO> selectList(SupplierBasicSelectReqDTO reqDTO);

    PageResponse<SupplierBasicInfoInquiryOrderRespDTO> queryForInquiryOrder(SupplierForInquiryOrderQueryReqDTO reqDTO);

    List<SupplierBasicInfoInquiryOrderRespDTO> queryForSourcingBySupplierCodes(List<String> supplierCodes, String categoryCode);

    List<SupplierBasicSimpleInfoRespDTO> listBySupplierCodes(List<String> supplierCodes);

    List<SupplierBasicSimpleInfoRespDTO> listEligibleBySupplierCodes(List<String> supplierCodes);

    List<String> searchSupplierCode(String codeKey, List<String> codes);

    List<SupplierBasicInfoRespDTO> getSupplierInfoByCodes(List<String> supplierCodes);

    List<SupplierGetAllHistoryGradeRespDTO> getAllHistoryGrade(SupplierGetAllHistoryGradeReqDTO reqDTO);

    List<SupplierGetAllCategoryRespDTO> getAllCategory(SupplierGetAllHistoryGradeReqDTO reqDTO);

    List<SupplierGetHistoryCategoryChangeRespDTO> getHistoryCategoryChange(SupplierGetAllHistoryGradeReqDTO reqDTO);

    List<SupplierGetHistoryPerformanceRespDTO> getHistoryPerformance(SupplierGetAllHistoryGradeReqDTO reqDTO);

    SupplierGetContractDetailRespDTO getContractDetail(SupplierGetAllHistoryGradeReqDTO reqDTO);

    List<SupplierGetLifeCycleRespDTO> getLifeCycle(Long id);

    List<SupplierBasicInfoListRespExportDTO> getDetailById(List<Long> supplierIds);

    List<SupplierBasicSimpleInfoRespDTO> queryEffectiveSupplier(SupplierBasicSelectReqDTO reqDTO);

    void updateLogo(LogoAttachmentMessageReqDTO reqDTO);

    List<SupplierBasicSimpleInfoRespDTO> queryByKeyword(String keyword);

    SupplierGetSqeAndCpeRespDTO getSqeAndCpe();

    List<SupplierAndContractRespDTO> querySupplierInfo(QuerySupplierReqDTO param);

    List<SupplierBasicSimpleInfoRespDTO> listByIds(List<Long> ids);

    List<SupplierBasicSimpleInfoRespDTO> queryByKeywordAndCpeMain(String keyword, Long userId);

    QualificationChangeQueryAssociationRespDTO querySupplierAssociation(String supplierCode);

    List<SupplierBasicSimpleInfoRespDTO> queryByKeywordAndLimit(QuerySupplierKeywordReqDTO param);

    QualificationChangeQueryAssociationRespDTO queryAssociationBySupplierId(Long supplierId);


}
