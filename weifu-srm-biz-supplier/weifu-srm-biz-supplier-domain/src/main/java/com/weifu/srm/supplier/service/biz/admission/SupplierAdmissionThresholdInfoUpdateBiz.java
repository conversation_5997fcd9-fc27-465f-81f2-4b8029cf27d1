package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.supplier.repository.atomicservice.ThresholdChangeRecordMapperService;
import com.weifu.srm.supplier.manager.ThresholdInfoManager;
import com.weifu.srm.supplier.repository.po.ThresholdChangeRecordPO;
import com.weifu.srm.supplier.repository.po.ThresholdInfoPO;
import com.weifu.srm.supplier.request.SupplierAdmissionThresholdInfoReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAdmissionThresholdInfoUpdateBiz {

    private final ThresholdInfoManager thresholdInfoManager;
    private final ThresholdChangeRecordMapperService thresholdChangeRecordMapperService;

    @Transactional
    public void update(SupplierAdmissionThresholdInfoReqDTO req){
        // 更新阈值，并获取旧阈值数据
        ThresholdInfoPO dto = thresholdInfoManager.addOrUpdate(req);
        // 阈值修改记录
        ThresholdChangeRecordPO recordPO = new ThresholdChangeRecordPO();
        recordPO.setAnnualPurchaseNewAmt(req.getAnnualPurchaseAmt());
        recordPO.setAnnualPurchaseNewCnt(req.getAnnualPurchaseCnt());
        recordPO.setMastPurchaseNewAmt(req.getMastPurchaseAmt());
        recordPO.setTransactionNewPeriod(req.getTransactionPeriod());
        recordPO.setCreateBy(req.getOperationUserId());
        recordPO.setUpdateBy(req.getOperationUserId());
        recordPO.setUpdateName(req.getOperationUser());
        recordPO.setCreateName(req.getOperationUser());
        if (dto != null) {
            recordPO.setAnnualPurchaseOldCnt(dto.getAnnualPurchaseCnt());
            recordPO.setAnnualPurchaseOldAmt(dto.getAnnualPurchaseAmt());
            recordPO.setMastPurchaseOldAmt(dto.getMastPurchaseAmt());
            recordPO.setTransactionOldPeriod(dto.getTransactionPeriod());
        }
        thresholdChangeRecordMapperService.save(recordPO);
    }



}
