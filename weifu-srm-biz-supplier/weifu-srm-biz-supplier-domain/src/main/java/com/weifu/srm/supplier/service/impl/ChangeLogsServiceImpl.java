package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.repository.atomicservice.ChangeLogsMapperService;
import com.weifu.srm.supplier.convert.ChangeLogsConvert;
import com.weifu.srm.supplier.repository.po.ChangeLogsPO;
import com.weifu.srm.supplier.request.ChangeLogsReqDTO;
import com.weifu.srm.supplier.response.ChangeLogsRespDTO;
import com.weifu.srm.supplier.service.ChangeLogsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/7/09 14:38
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class ChangeLogsServiceImpl implements ChangeLogsService {
    private final ChangeLogsMapperService changeLogsMapperService;
    private final ChangeLogsConvert changeLogsConvert;
    @Override
    public PageResponse<ChangeLogsRespDTO> queryByTableKeyAndColumnKey(ChangeLogsReqDTO reqDTO, String tableName) {
        Page<ChangeLogsPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        Page<ChangeLogsPO> pageResult = changeLogsMapperService
                .queryByTableKeyAndColumnKey(page, tableName, reqDTO.getBizNo());
        List<ChangeLogsRespDTO> respDTOList = changeLogsConvert.toResult(pageResult.getRecords());
        return PageResponse.toResult(reqDTO.getPageNum(),
                reqDTO.getPageSize(),
                pageResult.getTotal(),
                respDTOList);
    }
}
