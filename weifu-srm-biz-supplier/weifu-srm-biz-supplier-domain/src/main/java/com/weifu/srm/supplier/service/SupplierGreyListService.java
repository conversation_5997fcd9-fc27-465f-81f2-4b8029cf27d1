package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyCancelReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyListReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListSearchReqDTO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.response.grey.*;

import java.util.List;

public interface SupplierGreyListService {

    PageResponse<SupplierGreyListApplyListRespDTO> queryList(SupplierGreyListApplyListReqDTO req);

    String apply(SupplierGreyListApplyReqDTO req);

    String applyCancel(SupplierGreyListApplyCancelReqDTO req);

    SupplierGreyListApplyDetailRespDTO queryDetail(String applyNo);

    SupplierGreyListItemCategoryDivisionRespDTO queryGreyListItemCategoryBySupplierCode(String supplierCode);

    SupplierGreyListItemDivisionCategoryRespDTO queryGreyListItemDivisionBySupplierCode(String supplierCode);

    List<SupplierGreyListItemRespDTO> cancelGreyListCollection(List<Integer> limitIds);

    void handleGreyListApply(TicketStatusChangedMQ ticketInfo);

    void handleGreyListApplyCancel(TicketStatusChangedMQ ticketInfo);

    void scheduleCheckGreyList();

    List<SupplierGreyListApplyListRespDTO> exportGreyListApplyList(SupplierGreyListApplyListReqDTO req);

    List<SupplierGreyListRespDTO> searchGreyList(SupplierGreyListSearchReqDTO reqDTO);
}
