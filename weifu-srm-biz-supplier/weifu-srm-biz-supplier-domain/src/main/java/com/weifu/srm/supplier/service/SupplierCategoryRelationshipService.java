package com.weifu.srm.supplier.service;

import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.ListSupplierOnAssociatedCategoryReqDTO;
import com.weifu.srm.supplier.request.QuerySupplierCategoryListReqDTO;
import com.weifu.srm.supplier.request.SupplierCategoryRelPageReqDTO;
import com.weifu.srm.supplier.request.SupplierCategoryRelationshipQueryReqDTO;
import com.weifu.srm.supplier.response.ListSupplierOnAssociatedCategoryRespDTO;
import com.weifu.srm.supplier.response.SupplierCategoryRelPageRespDTO;
import com.weifu.srm.supplier.response.SupplierCategoryRelationshipRespDTO;

import java.util.List;

public interface SupplierCategoryRelationshipService {

    void scheduleSupplierCategoryStatus();

    List<SupplierCategoryRelationshipRespDTO> querySupplierCategoryRelationship(QuerySupplierCategoryListReqDTO req);

    List<SupplierCategoryRelationshipRespDTO> listCategoryRelationshipByParam(SupplierCategoryRelationshipQueryReqDTO req);

    /**
     * 查询用户作为CPE（品类工程师）负责的品类所关联的供应商
     * 查询类型为三级品类扩充时，品类范围调整为负责的三级品类对应的二级品类下的所有三级品类
     */
    List<ListSupplierOnAssociatedCategoryRespDTO> listSupplierOnAssociatedCategory(ListSupplierOnAssociatedCategoryReqDTO req);

    /**
     * 分页查询供应商与品类的关系（不返回品类资质为临时供应商的已过期记录）
     */
    PageResponse<SupplierCategoryRelPageRespDTO> page(SupplierCategoryRelPageReqDTO req);

    /**
     *查询供应商与品类的关系（不返回品类资质为临时供应商的已过期记录）
     */
    List<SupplierCategoryRelPageRespDTO> allBySupplierCode(SupplierCategoryRelPageReqDTO req);

    /**
     * 查询供应商与品类的关系（不返回品类资质为临时供应商的已过期记录）For导入
     */
    List<SupplierCategoryRelPageRespDTO> listForExport(SupplierCategoryRelPageReqDTO req);

    /**
     *根据三级品类编码，查询对应的供应商资质信息
     */
    List<SupplierCategoryRelationshipRespDTO> querySupplierByCategoryCodeLevel3(String categoryCode);

}
