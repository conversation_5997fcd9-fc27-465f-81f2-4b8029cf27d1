package com.weifu.srm.supplier.service.biz.leave;

import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.constants.MQSupplierTopicConstants;
import com.weifu.srm.supplier.dto.SupplierBasicStatusUpdateDTO;
import com.weifu.srm.supplier.manager.LeaveManager;
import com.weifu.srm.supplier.manager.SupplierBasicInfoDetailManager;
import com.weifu.srm.supplier.manager.remote.intergration.SAPManager;
import com.weifu.srm.supplier.mq.SupplierLeavedMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierLeaveRecordDetailMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierLeaveRecordMapperService;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierLeaveRecordDetailPO;
import com.weifu.srm.supplier.repository.po.SupplierLeaveRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 处理灰名单审批结果
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaitLeaveApplyHandleBiz {

    private final SupplierLeaveRecordMapperService leaveRecordMapperService;
    private final SupplierLeaveRecordDetailMapperService leaveRecordDetailMapperService;
    private final LeaveManager leaveManager;
    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoDetailManager supplierBasicInfoDetailManager;
    private final MqManager mqManager;

    public void handWaitLeave(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            log.info("this audit is process  status={}", ticketInfo);
            leaveRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierLeaveRecordDetailPO::getWaitExitNo, ticketInfo.getBusinessNo())
                    .set(SupplierLeaveRecordDetailPO::getWaitExitTicketNo, ticketInfo.getTicketNo())
                    .update();
            return;
        }
        transactionTemplate.execute(status -> {
            updateAuditStatus(ticketInfo);
            return null;
        });
        // 拒绝/关闭不进行其它操作
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus()) || TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        // 查询需要已经满足处理条件的数据
        Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<SupplierLeaveRecordDetailPO> list = leaveRecordDetailMapperService.lambdaQuery()
                .eq(SupplierLeaveRecordDetailPO::getWaitExitNo, ticketInfo.getBusinessNo())
                .eq(SupplierLeaveRecordDetailPO::getStatus, SupplierLeaveStatusEnum.WAIT_LEAVE_AUDITED_WAITING.getCode())
                .le(SupplierLeaveRecordDetailPO::getWaitExitValidTime, currentDate)
                .list();
        for (SupplierLeaveRecordDetailPO leaveRecord : list) {
            transactionTemplate.executeWithoutResult(status -> {
                // 更新申请明细状态
                updateLeaveDetailStatus(leaveRecord.getId(), ticketInfo);
                // 更新基础表状态
                updateBasicInfoStatus(leaveRecord.getWaitExitNo(), leaveRecord.getSupplierCode(),ticketInfo);

                SupplierLeavedMQ mq = new SupplierLeavedMQ();
                mq.setBusinessNo(leaveRecord.getWaitExitNo());
                mq.setSupplierCode(leaveRecord.getSupplierCode());
                mq.setLeaveOrWaitLeaved(YesOrNoEnum.NO.getCode());
                mq.setOperationUser(ticketInfo.getOperateName());
                mq.setOperationUserId(ticketInfo.getOperateBy());
                mq.setOperationTime(ticketInfo.getUpdateTime());
                // 发送站内信
                leaveManager.sendLeaveNotice(leaveRecord,mq);
                // 发送供应商待退出通知MQ
                mqManager.sendTopic(MQSupplierTopicConstants.SUPPLIER_LEAVED_LIST_TOPIC, JacksonUtil.bean2Json(mq));
            });
        }
    }

    private String getAuditStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return CommonStatusEnum.AGREE_STATUS.getCode();
            case CANCELED:
                return CommonStatusEnum.DRAFT.getCode();
            case REJECTED:
                return CommonStatusEnum.REJECT_STATUS.getCode();
            default:
                log.error("not support status={}", ticketInfo);
                throw new BizFailException("not support status");
        }
    }

    private String getLeaveDetailStatus(TicketStatusChangedMQ ticketInfo) {
        TicketStatusEnum statusEnum = TicketStatusEnum.getByCode(ticketInfo.getStatus());
        switch (statusEnum) {
            case APPROVED:
                return SupplierLeaveStatusEnum.WAIT_LEAVE_AUDITED_WAITING.getCode();
            case REJECTED:
                return SupplierLeaveStatusEnum.WAIT_LEAVE_AUDIT_REJECT.getCode();
            default:
                throw new BizFailException("not support status");
        }
    }

    private void updateAuditStatus(TicketStatusChangedMQ ticketInfo) {
        // 修改申请表
        leaveRecordMapperService.lambdaUpdate()
                .eq(SupplierLeaveRecordPO::getApplyNo, ticketInfo.getBusinessNo())
                .set(SupplierLeaveRecordPO::getStatus, getAuditStatus(ticketInfo))
                .set(SupplierLeaveRecordPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierLeaveRecordPO::getUpdateName, ticketInfo.getOperateName())
                .update();
        if (TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            leaveRecordDetailMapperService.lambdaUpdate()
                    .eq(SupplierLeaveRecordDetailPO::getWaitExitNo, ticketInfo.getBusinessNo())
                    .set(SupplierLeaveRecordDetailPO::getIsDelete, YesOrNoEnum.YES.getCode())
                    .set(SupplierLeaveRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierLeaveRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                    .update();
            return;
        }
        // 修改明细表
        leaveRecordDetailMapperService.lambdaUpdate()
                .eq(SupplierLeaveRecordDetailPO::getWaitExitNo, ticketInfo.getBusinessNo())
                .set(SupplierLeaveRecordDetailPO::getStatus, getLeaveDetailStatus(ticketInfo))
                .set(SupplierLeaveRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierLeaveRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                .update();
    }

    private void updateLeaveDetailStatus(Long limitId, TicketStatusChangedMQ ticketInfo) {
        // 限制生效
        leaveRecordDetailMapperService.lambdaUpdate()
                .eq(SupplierLeaveRecordDetailPO::getId, limitId)
                .set(SupplierLeaveRecordDetailPO::getStatus, SupplierLeaveStatusEnum.WAIT_LEAVE.getCode())
                .set(SupplierLeaveRecordDetailPO::getUpdateBy, ticketInfo.getOperateBy())
                .set(SupplierLeaveRecordDetailPO::getUpdateName, ticketInfo.getOperateName())
                .update();
    }

    private void updateBasicInfoStatus(String businessNo, String supplierCode, TicketStatusChangedMQ ticketInfo) {
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoDetailManager.getSupplierBasicInfoByCode(supplierCode);
        supplierBasicInfoPO.setStatus(SupplierBasicStatusEnum.WAIT_LEAVE.getCode());
        BaseEntityUtil.setCommonForU(supplierBasicInfoPO, ticketInfo.getOperateBy(), ticketInfo.getOperateName(),null);
        SupplierBasicStatusUpdateDTO req = new SupplierBasicStatusUpdateDTO();
        req.setBusinessNo(businessNo);
        req.setRemark(SupplierBasicStatusEnum.getByCode(supplierBasicInfoPO.getStatus()).getChineseName().concat("->").concat(SupplierBasicStatusEnum.WAIT_LEAVE.getChineseName()));
        req.setContent("供应商待退出生效");
        req.setChangeType(SupplierKeyInfoChangeTypeEnum.STATUS);
        req.setSupplierBasicInfoPO(supplierBasicInfoPO);
        supplierBasicInfoDetailManager.updateSupplierBasicStatus(req);
    }

}
