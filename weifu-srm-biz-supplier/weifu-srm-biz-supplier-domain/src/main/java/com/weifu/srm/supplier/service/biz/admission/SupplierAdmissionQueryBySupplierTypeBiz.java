package com.weifu.srm.supplier.service.biz.admission;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.supplier.manager.SupplierAdmissionQueryByCodeManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireFindLatestRespDTO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireQueryByCodeRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/7/18 12:48
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class SupplierAdmissionQueryBySupplierTypeBiz {

    private final SupplierAdmissionQueryByCodeManager supplierAdmissionQueryByCodeManager;

    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;

    /**
     *  根据suppleierType 查询相同类型最近申请时间的数据
     * @param supplierType
     * @return
     */
    public AdmissionQuestionnaireFindLatestRespDTO findLatestBySupplierType(String supplierType, String supplierCode) {
        SupplierAdmissionRecordPO po = supplierAdmissionRecordMapperService.findLatestBySupplierType(supplierType, supplierCode);
        if (ObjectUtil.isNull(po)){
            return null;
        }
        return supplierAdmissionQueryByCodeManager.getDetail(po.getAdmissionNo(),new AdmissionQuestionnaireQueryByCodeRespDTO());
    }
}
