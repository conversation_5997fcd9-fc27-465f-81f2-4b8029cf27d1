package com.weifu.srm.supplier.service.biz;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.integration.response.qixinbao.RelatedRelationshipRespDTO;
import com.weifu.srm.masterdata.api.DictDataApi;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.remote.intergration.QiXinBaoManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAssociationRelationMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.po.SupplierAssociationRelationPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.response.QualificationChangeQueryAssociationRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAssociationRelationBiz {

    public static final String QIXINBAO = "QIXINBAO";
    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final SupplierAssociationRelationMapperService associationRelationMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final QiXinBaoManager qiXinBaoManager;
    private final TransactionTemplate transactionTemplate;
    private final DictDataApi dictDataApi;

    public QualificationChangeQueryAssociationRespDTO querySupplierAssociation(SupplierBasicInfoPO basicInfoPO) {
        if (ObjectUtils.isEmpty(basicInfoPO)) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_NOT_EXIST));
        }
        QualificationChangeQueryAssociationRespDTO result = new QualificationChangeQueryAssociationRespDTO();
        result.setSupplierCode(basicInfoPO.getSapSupplierCode());
        result.setSupplierName(basicInfoPO.getSupplierName());
        result.setIsWeifuRelatedParty(basicInfoPO.getIsWeifuRelatedParty());
        result.setDirectRelatedPartyTypeDesc(basicInfoPO.getDirectRelatedPartyTypeDesc());
        result.setIsRelatedToWeifuSupplier(basicInfoPO.getIsRelatedToWeifuSupplier());
        // 已查询过启信宝/ 或者境外供应商
        if (YesOrNoEnum.YES.equalsCode(basicInfoPO.getIsOverseas())) {
            if (YesOrNoEnum.NO.equalsCode(basicInfoPO.getIsRelatedToWeifuSupplier())) {
                return result;
            }
            // 查询关联关系数据
            List<SupplierAssociationRelationPO> list = associationRelationMapperService.lambdaQuery().eq(SupplierAssociationRelationPO::getSupplierBasicMsgId, basicInfoPO.getId()).list();
            if (CollectionUtils.isEmpty(list)) {
                return result;
            }
            Map<String, String> relationName = getRelationName();
            List<QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO> respList = qualificationChangeConvert.toList(list);
            for (QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO associationRelationItemRespDTO : respList) {
                associationRelationItemRespDTO.setAssociationType(relationName.get(associationRelationItemRespDTO.getAssociationType()));
            }
            result.setAssociationRelationItemRespDTOList(respList);
            return result;
        }
        // 未查询过启信宝的境内供应商
        queryQixinbao(basicInfoPO);
        // 查询关联关系数据
        List<SupplierAssociationRelationPO> list = associationRelationMapperService.lambdaQuery().eq(SupplierAssociationRelationPO::getSupplierBasicMsgId, basicInfoPO.getId()).list();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        Map<String, String> relationName = getRelationName();
        List<QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO> respList = qualificationChangeConvert.toList(list);
        for (QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO associationRelationItemRespDTO : respList) {
            associationRelationItemRespDTO.setAssociationType(relationName.get(associationRelationItemRespDTO.getAssociationType()));
        }
        result.setAssociationRelationItemRespDTOList(respList);
        return result;
    }

    private void queryQixinbao(SupplierBasicInfoPO basicInfoPO) {
        RelatedRelationshipRespDTO relatedRelation = qiXinBaoManager.getRelatedRelation(basicInfoPO.getCreditCode());
        List<SupplierAssociationRelationPO> newRelation = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(relatedRelation.getSummery())) {
            log.info("query qixinbao,data result:{}", JSONUtil.toJsonStr(relatedRelation));
            Map<String, String> releationMap = processData(relatedRelation);
            List<String> nameList = new ArrayList<>(releationMap.keySet());
            if (CollectionUtils.isNotEmpty(nameList)) {
                List<SupplierBasicInfoPO> list = basicInfoMapperService.lambdaQuery().in(SupplierBasicInfoPO::getSupplierName, nameList).list();
                if (CollectionUtils.isNotEmpty(list)) {
                    newRelation = list.stream().map(supplierBasicInfoPO -> {
                        SupplierAssociationRelationPO relationPO = new SupplierAssociationRelationPO();
                        BaseEntityUtil.setCommon(relationPO, -1L, "system", new Date());
                        relationPO.setSupplierBasicMsgId(basicInfoPO.getId());
                        relationPO.setAssociationSupplierName(supplierBasicInfoPO.getSupplierName());
                        relationPO.setSapSupplierCode(basicInfoPO.getSapSupplierCode());
                        relationPO.setSourceType(QIXINBAO);
                        relationPO.setAssociationType(releationMap.get(supplierBasicInfoPO.getSupplierName()));
                        relationPO.setIsDelete(YesOrNoEnum.NO.getCode());
                        return relationPO;
                    }).collect(Collectors.toList());
                }
            }
        }
        List<SupplierAssociationRelationPO> finalNewRelation = newRelation;
        List<SupplierAssociationRelationPO> existList = associationRelationMapperService.lambdaQuery()
                .eq(SupplierAssociationRelationPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .eq(SupplierAssociationRelationPO::getSourceType, QIXINBAO)
                .list();
        transactionTemplate.executeWithoutResult(status -> {
            if (CollectionUtils.isNotEmpty(existList)) {
                List<Long> ids = existList.stream().map(SupplierAssociationRelationPO::getId).collect(Collectors.toList());
                associationRelationMapperService.removeByIds(ids);
            }
            if (CollectionUtils.isNotEmpty(finalNewRelation)) {
                associationRelationMapperService.saveBatch(finalNewRelation);
            }
            basicInfoMapperService.lambdaUpdate()
                    .eq(SupplierBasicInfoPO::getId, basicInfoPO.getId())
                    .set(SupplierBasicInfoPO::getIsQueryRelation, YesOrNoEnum.YES.getCode())
                    .set(SupplierBasicInfoPO::getIsRelatedToWeifuSupplier, CollectionUtils.isNotEmpty(finalNewRelation) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                    .update();
        });
    }

    private Map<String, String> processData(RelatedRelationshipRespDTO relatedRelation) {
        Map<String, String> releationMap = Maps.newHashMap();
        // E:表示企业
        for (RelatedRelationshipRespDTO.RelatedRelationship relatedRelationship : relatedRelation.getSummery()) {
            if (!"E".equals(relatedRelationship.getEntity())) {
                continue;
            }
            List<RelatedRelationshipRespDTO.RelatedRelationship.RelationNode> nodes = relatedRelationship.getNodes();
            if (CollectionUtils.isEmpty(nodes)) {
                continue;
            }
            for (RelatedRelationshipRespDTO.RelatedRelationship.RelationNode node : nodes) {
                releationMap.put(node.getName(), relatedRelationship.getRelation());
            }
        }
        return releationMap;
    }

    private Map<String, String> getRelationName() {
        ApiResponse<Map<String, List<DictDataShowResDTO>>> dictDataList = dictDataApi.getDictDataList("TYPE_WEIFU_SUPPLIER_RELATION", LocaleContextHolder.getLocale().toString());
        if (Boolean.TRUE.equals(dictDataList.getSucc())) {
            List<DictDataShowResDTO> dictDataShowResDTOS = dictDataList.getData().get("TYPE_WEIFU_SUPPLIER_RELATION");
            if (CollectionUtils.isNotEmpty(dictDataShowResDTOS)) {
                return dictDataShowResDTOS.stream().collect(Collectors.toMap(DictDataShowResDTO::getDataValue, DictDataShowResDTO::getDataShowName, (v1, v2) -> v2));
            }
        }
        throw new BizFailException(localeMessage.getMessage(dictDataList.getMsg()));
    }
}
