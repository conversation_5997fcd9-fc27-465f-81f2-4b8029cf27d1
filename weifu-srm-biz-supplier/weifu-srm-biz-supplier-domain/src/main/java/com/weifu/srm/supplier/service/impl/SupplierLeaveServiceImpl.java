package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.manager.remote.intergration.SAPManager;
import com.weifu.srm.supplier.mq.SupplierLeavedMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierLeaveRecordDetailMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGradeTypeEnum;
import com.weifu.srm.supplier.repository.enums.SupplierLeaveStatusEnum;
import com.weifu.srm.supplier.request.leave.SupplierLeaveApplyListReqDTO;
import com.weifu.srm.supplier.request.leave.SupplierLeaveExistReqDTO;
import com.weifu.srm.supplier.request.leave.SupplierLeaveWaitExitReqDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveApplyListRespDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveDetailRespDTO;
import com.weifu.srm.supplier.response.leave.SupplierLeaveItemRespDTO;
import com.weifu.srm.supplier.service.SupplierLeaveService;
import com.weifu.srm.supplier.service.biz.leave.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierLeaveServiceImpl implements SupplierLeaveService {

    private final SupplierWaitLeaveApplyBiz supplierWaitLeaveApplyBiz;
    private final SupplierLeaveApplyBiz supplierLeaveApplyBiz;
    private final SupplierLeaveDetailBiz supplierLeaveDetailBiz;
    private final SupplierLeaveCollectionBiz supplierLeaveCollectionBiz;
    private final SupplierLeaveRecordDetailMapperService leaveRecordDetailMapperService;
    private final LeaveApplyHandleBiz leaveApplyHandleBiz;
    private final WaitLeaveApplyHandleBiz waitLeaveApplyHandleBiz;
    private final ScheduleUpdateWaitLeaveBiz scheduleUpdateWaitLeaveBiz;
    private final SupplierLeavedBiz supplierLeavedBiz;
    private final SAPManager sapManager;

    @Override
    public PageResponse<SupplierLeaveApplyListRespDTO> queryListPage(SupplierLeaveApplyListReqDTO req) {
        Page<SupplierLeaveApplyListReqDTO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<SupplierLeaveApplyListRespDTO> pageResult = leaveRecordDetailMapperService.queryListPage(page, req);
        if (pageResult.getTotal() == 0) {
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, null);
        }
        List<SupplierLeaveApplyListRespDTO> records = pageResult.getRecords();
       for (SupplierLeaveApplyListRespDTO leaveItem : records) {
            leaveItem.setSupplierGrade(SupplierGradeTypeEnum.getByCode(leaveItem.getSupplierGrade()).getChineseName());
        }
        return PageResponse.toResult(req.getPageNum(), req.getPageSize(), pageResult.getTotal(), records);
    }

    @Override
    public String applyWaiting(SupplierLeaveWaitExitReqDTO req) {
        return supplierWaitLeaveApplyBiz.waitLeaveApply(req);
    }

    @Override
    public String applyLeave(SupplierLeaveExistReqDTO req) {
       return supplierLeaveApplyBiz.leaveApply(req);
    }

    @Override
    public SupplierLeaveDetailRespDTO queryDetail(String applyNo) {
        return supplierLeaveDetailBiz.searchLeaveApplyDetailByApplyNo(applyNo);
    }

    @Override
    public List<SupplierLeaveItemRespDTO> leaveCollection(List<Integer> limitIds) {
        return supplierLeaveCollectionBiz.leaveCollection(limitIds);
    }

    @Override
    public List<SupplierLeaveApplyListRespDTO> exportLeaveApplyList(SupplierLeaveApplyListReqDTO req) {
        List<SupplierLeaveApplyListRespDTO> records = leaveRecordDetailMapperService.queryList(req);
        if (CollectionUtils.isEmpty(records)){
            return records;
        }
       for (SupplierLeaveApplyListRespDTO leaveRecord : records) {
            leaveRecord.setSupplierStatus(SupplierBasicStatusEnum.getByCode(leaveRecord.getSupplierStatus()).getChineseName());
            leaveRecord.setStatus(SupplierLeaveStatusEnum.getByCode(leaveRecord.getStatus()).getChineseName());
            leaveRecord.setSupplierGrade(SupplierGradeTypeEnum.getByCode(leaveRecord.getSupplierGrade()).getChineseName());
        }
        return records;
    }

    @Override
    public void handWaitLeLeaveApply(TicketStatusChangedMQ ticketInfo) {
        waitLeaveApplyHandleBiz.handWaitLeave(ticketInfo);
    }

    @Override
    public void handleLeaveApply(TicketStatusChangedMQ ticketInfo) {
        leaveApplyHandleBiz.handleLeave(ticketInfo);
    }

    @Override
    public void scheduleCheckWaitLeave() {
        scheduleUpdateWaitLeaveBiz.processWaitValidData();
    }

    @Override
    public void handleLeaved(SupplierLeavedMQ mq) {
        supplierLeavedBiz.supplierLeaved(mq);
    }

    @Override
    public void handleSupplierLeaveForSap(SupplierLeavedMQ mq) {
        if (YesOrNoEnum.YES.equalsCode(mq.getLeaveOrWaitLeaved())){
            sapManager.syncSupplierFreeze(mq.getSupplierCode());
        } else {
            sapManager.syncSupplierBlackListToSAP(mq.getSupplierCode(), YesOrNoEnum.YES.getCode().toString());
        }
    }
}
