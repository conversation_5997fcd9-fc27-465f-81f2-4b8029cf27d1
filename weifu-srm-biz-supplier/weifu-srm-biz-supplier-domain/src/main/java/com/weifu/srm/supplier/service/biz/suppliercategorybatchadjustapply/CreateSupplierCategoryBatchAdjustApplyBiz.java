package com.weifu.srm.supplier.service.biz.suppliercategorybatchadjustapply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.enums.SupplierCategoryBatchAdjustApplyStatusEnum;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO.AfterAdjustItemReqDTO;
import static com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO.BeforeAdjustItemReqDTO;

/**
 * 创建供应商品类关系批量调整申请
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreateSupplierCategoryBatchAdjustApplyBiz {

    private final static String APPLY_NO_CONFIG = "supplier-category-batch-adjust-apply-no";
    private final static String ADJUSTING_ERROR_MSG = "供应商品类数据正在进行调整中，无法提交申请！供应商编码：";

    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierCategoryBatchAdjustApplyMapperService supplierCategoryBatchAdjustApplyMapperService;
    private final SupplierCategoryBatchAdjustApplyOldItemMapperService supplierCategoryBatchAdjustApplyOldItemMapperService;
    private final SupplierCategoryBatchAdjustApplyNewItemMapperService supplierCategoryBatchAdjustApplyNewItemMapperService;
    private final SupplierCategoryAdjustApplyItemMapperService supplierCategoryAdjustApplyItemMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final MqManager mqManager;
    private final IdService idService;

    public String execute(CreateSupplierCategoryBatchAdjustApplyReqDTO req) {
        log.info("创建供应商品类关系批量调整申请开始，req={}", JacksonUtil.bean2Json(req));
        check(req);

        Map<String, SupplierBasicInfoPO> supplierMap = querySupplierBasicInfo(req);

        Date current = new Date();
        String applyNo = generateApplyNo();

        SupplierCategoryBatchAdjustApplyPO apply = BeanUtil.toBean(req, SupplierCategoryBatchAdjustApplyPO.class);
        apply.setApplyNo(applyNo);
        apply.setApplyTime(current);
        apply.setApplyStatus(SupplierCategoryBatchAdjustApplyStatusEnum.APPROVING.getCode());
        BaseEntityUtil.setCommon(apply, req.getApplyBy(), req.getApplyName(), current);

        List<SupplierCategoryBatchAdjustApplyOldItemPO> oldItems = new ArrayList<>();
        for (BeforeAdjustItemReqDTO item:req.getOldItems()) {
            SupplierCategoryBatchAdjustApplyOldItemPO oldItem = BeanUtil.toBean(item, SupplierCategoryBatchAdjustApplyOldItemPO.class);
            oldItem.setApplyNo(applyNo);
            BaseEntityUtil.setCommon(oldItem, req.getApplyBy(), req.getApplyName(), current);
            oldItems.add(oldItem);

            SupplierBasicInfoPO supplier = supplierMap.get(item.getSapSupplierCode());
            if (supplier == null) {
                throw new BizFailException("供应商不存在："+ item.getSapSupplierCode());
            }
            oldItem.setSupplierName(supplier.getSupplierName());
            oldItem.setSupplierNameEn(supplier.getSupplierNameEN());
        }

        List<SupplierCategoryBatchAdjustApplyNewItemPO> newItems = new ArrayList<>();
        for (AfterAdjustItemReqDTO item:req.getNewItems()) {
            SupplierCategoryBatchAdjustApplyNewItemPO newItem = BeanUtil.toBean(item, SupplierCategoryBatchAdjustApplyNewItemPO.class);
            newItem.setApplyNo(applyNo);
            BaseEntityUtil.setCommon(newItem, req.getApplyBy(), req.getApplyName(), current);
            newItems.add(newItem);

            SupplierBasicInfoPO supplier = supplierMap.get(item.getSapSupplierCode());
            if (supplier == null) {
                throw new BizFailException("供应商不存在："+ item.getSapSupplierCode());
            }
            newItem.setSupplierName(supplier.getSupplierName());
            newItem.setSupplierNameEn(supplier.getSupplierNameEN());
        }

        List<AttachmentRecordPO> files = BeanUtil.copyToList(req.getFiles(), AttachmentRecordPO.class);
        if (CollectionUtils.isNotEmpty(files)) {
            for (AttachmentRecordPO file:files) {
                file.setBusinessNo(applyNo);
                file.setBusinessType(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_BATCH_ADJUST);
                BaseEntityUtil.setCommon(file, req.getApplyBy(), req.getApplyName(), current);
            }
        }

        // 保存到数据库
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                supplierCategoryBatchAdjustApplyMapperService.save(apply);

                supplierCategoryBatchAdjustApplyOldItemMapperService.saveBatch(oldItems);
                supplierCategoryBatchAdjustApplyNewItemMapperService.saveBatch(newItems);

                if (CollectionUtils.isNotEmpty(files)) {
                    attachmentRecordMapperService.saveBatch(files);
                }

                // 发送创建工单MQ
                CreateTicketMQ ticket = new CreateTicketMQ();
                ticket.setTicketType(TicketTypeEnum.SUPPLIER_CATEGORY_RELATION_ADJUST.getCode());
                ticket.setBusinessNo(applyNo);
                ticket.setSubmitBy(req.getApplyBy());
                ticket.setSubmitName(req.getApplyName());
                ticket.setSubmitDesc(req.getApplyDesc());
                mqManager.sendTopic(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(ticket));
            }
        });
        log.info("创建供应商品类关系批量调整申请完成，applyNo={}", applyNo);
        return applyNo;
    }

    private String generateApplyNo() {
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long num = idService.nextId(APPLY_NO_CONFIG + "-" + date, APPLY_NO_CONFIG);
        String suffix = String.format("%04d",num);
        return "BCQA" + date + suffix;
    }

    /**
     * 1.每个供应商调整前的品类资质需要是“合格供应商”
     * 2.每个供应商调整后的品类资质，需要在调整前的范围内
     * 3.判断调整后的供应商品类关系是否已经存在
     * 4.判断调整后的供应商品类关系是否已在存在于审批中的业务单（包含“三级品类扩充”、“三级品类删除”、“一级/二级品类资质删除”，理论上只有“三级品类扩充”才有可能触发此逻辑）
     * 5.判断调整前的供应商品类关系是否存在于审批中的业务单（包含“三级品类扩充”、“三级品类删除”、“一级/二级品类资质删除”、“供应商品类批量调整”）
     */
    private void check(CreateSupplierCategoryBatchAdjustApplyReqDTO req) {
        List<AfterAdjustItemReqDTO> newItems = req.getNewItems();
        List<BeforeAdjustItemReqDTO> oldItems = req.getOldItems();

        // 1.每个供应商调整前的品类资质需要是“合格供应商”
        // 2.每个供应商调整后的品类资质，需要在调整前的范围内
        checkSupplierCategoryStatus(newItems, oldItems);

        // 3.判断调整后的供应商品类关系是否已经存在
        checkNewRelationIsExists(newItems);

        // 4.判断调整后的供应商品类关系是否已在存在于审批中的业务单（包含“三级品类扩充”、“三级品类删除”、“一级/二级品类资质删除”，理论上只有“三级品类扩充”才有可能触发此逻辑）
        checkAfterRelationIsApproving(newItems);

        // 5-1.判断调整前的供应商品类关系是否存在于审批中的业务单-品类批量调整
        // 5-2.判断调整前的供应商品类关系是否存在于审批中的业务单-品类扩充或删除
        checkBeforeRelationIsApproving(oldItems);
    }

    /**
     * 每个供应商调整前的品类资质需要是“合格供应商”
     */
    private void checkSupplierCategoryStatus(List<AfterAdjustItemReqDTO> newItems, List<BeforeAdjustItemReqDTO> oldItems) {
        for (BeforeAdjustItemReqDTO item:oldItems) {
            if (!SupplierCategoryStatusEnum.QUALIFIED_STATUS.equalsCode(item.getSupplierCategoryStatus())) {
                throw new BizFailException("只能调整品类资质是“合格供应商”的供应商品类关系");
            }
        }

        Map<String, List<String>> existSupplierCategoryStatusMap = getExistSupplierCategoryStatusMap(oldItems);
        for (AfterAdjustItemReqDTO item:newItems) {
            String sapSupplierCode = item.getSapSupplierCode();
            List<String> existStatus = existSupplierCategoryStatusMap.get(sapSupplierCode);
            if (!CollectionUtils.containsAny(existStatus, item.getSupplierCategoryStatus())) {
                throw new BizFailException("供应商【"+ sapSupplierCode + "】调整后的品类资质，需要在调整前的范围内");
            }
        }
    }

    /**
     * 判断调整后的供应商品类关系是否已经存在
     */
    private void checkNewRelationIsExists(List<AfterAdjustItemReqDTO> newItems) {
        List<SupplierCategoryRelationshipPO> relationships = new ArrayList<>();
        for (AfterAdjustItemReqDTO item:newItems) {
            SupplierCategoryRelationshipPO relationship = new SupplierCategoryRelationshipPO();
            relationship.setSapSupplierCode(item.getSapSupplierCode());
            relationship.setCategoryCode(item.getCategoryCode());
            relationships.add(relationship);
        }
        List<SupplierCategoryRelationshipPO> exitsRels = supplierCategoryRelationshipMapperService.list(relationships);
        if (CollectionUtils.isNotEmpty(exitsRels)) {
            StringBuffer buffer = new StringBuffer();
            for (SupplierCategoryRelationshipPO relationship:exitsRels) {
                if (buffer.length() != 0) {
                    buffer.append("，");
                }
                buffer.append(relationship.getSapSupplierCode());
            }
            throw new BizFailException("以下供应商已经拥有该三级品类的资质无法进行调整！供应商编码：" + buffer.toString());
        }
    }

    /**
     * 判断调整后的供应商品类关系是否已在存在于审批中的业务单（包含“三级品类扩充”、“三级品类删除”、“一级/二级品类资质删除”，理论上只有“三级品类扩充”才有可能触发此逻辑）
     */
    private void checkAfterRelationIsApproving(List<AfterAdjustItemReqDTO> newItems) {
        List<SupplierCategoryAdjustApplyItemPO> relationships4 = new ArrayList<>();
        for (AfterAdjustItemReqDTO item:newItems) {
            SupplierCategoryAdjustApplyItemPO relationship = new SupplierCategoryAdjustApplyItemPO();
            relationship.setSapSupplierCode(item.getSapSupplierCode());
            relationship.setThreeLevelCategoryCode(item.getCategoryCode());
            relationships4.add(relationship);
        }
        List<SupplierCategoryAdjustApplyItemPO> adjustingRels3 = supplierCategoryAdjustApplyItemMapperService.listOnApproving(relationships4);
        if (CollectionUtils.isNotEmpty(adjustingRels3)) {
            StringBuffer buffer = new StringBuffer();
            for (SupplierCategoryAdjustApplyItemPO relationship:adjustingRels3) {
                if (buffer.length() != 0) {
                    buffer.append("，");
                }
                buffer.append(relationship.getSapSupplierCode());
            }
            throw new BizFailException(ADJUSTING_ERROR_MSG + buffer.toString());
        }
    }

    /**
     * 1.判断调整前的供应商品类关系是否存在于审批中的业务单-品类批量调整
     * 2.判断调整前的供应商品类关系是否存在于审批中的业务单-品类扩充或删除
     */
    private void checkBeforeRelationIsApproving(List<BeforeAdjustItemReqDTO> oldItems) {
        // 1.判断调整前的供应商品类关系是否存在于审批中的业务单-品类批量调整
        List<SupplierCategoryBatchAdjustApplyOldItemPO> relationships2 = new ArrayList<>();
        for (BeforeAdjustItemReqDTO item:oldItems) {
            SupplierCategoryBatchAdjustApplyOldItemPO relationship = new SupplierCategoryBatchAdjustApplyOldItemPO();
            relationship.setSapSupplierCode(item.getSapSupplierCode());
            relationship.setCategoryCode(item.getCategoryCode());
            relationships2.add(relationship);
        }
        List<SupplierCategoryBatchAdjustApplyOldItemPO> adjustingRels = supplierCategoryBatchAdjustApplyOldItemMapperService.listOnApproving(relationships2);
        if (CollectionUtils.isNotEmpty(adjustingRels)) {
            StringBuffer buffer = new StringBuffer();
            for (SupplierCategoryBatchAdjustApplyOldItemPO relationship:adjustingRels) {
                if (buffer.length() != 0) {
                    buffer.append("，");
                }
                buffer.append(relationship.getSapSupplierCode());
            }
            throw new BizFailException(ADJUSTING_ERROR_MSG + buffer.toString());
        }

        // 2.判断调整前的供应商品类关系是否存在于审批中的业务单-品类扩充或删除
        List<SupplierCategoryAdjustApplyItemPO> relationships3 = new ArrayList<>();
        for (BeforeAdjustItemReqDTO item:oldItems) {
            SupplierCategoryAdjustApplyItemPO relationship = new SupplierCategoryAdjustApplyItemPO();
            relationship.setSapSupplierCode(item.getSapSupplierCode());
            relationship.setThreeLevelCategoryCode(item.getCategoryCode());
            relationships3.add(relationship);
        }
        List<SupplierCategoryAdjustApplyItemPO> adjustingRels2 = supplierCategoryAdjustApplyItemMapperService.listOnApproving(relationships3);
        if (CollectionUtils.isNotEmpty(adjustingRels2)) {
            StringBuffer buffer = new StringBuffer();
            for (SupplierCategoryAdjustApplyItemPO relationship:adjustingRels2) {
                if (buffer.length() != 0) {
                    buffer.append("，");
                }
                buffer.append(relationship.getSapSupplierCode());
            }
            throw new BizFailException(ADJUSTING_ERROR_MSG + buffer.toString());
        }
    }

    /**
     * 获取供应商已存在的品类资质
     */
    private Map<String, List<String>> getExistSupplierCategoryStatusMap(List<BeforeAdjustItemReqDTO> oldItems) {
        return oldItems.stream()
                .collect(Collectors.groupingBy(
                        BeforeAdjustItemReqDTO::getSapSupplierCode, // 分组依据
                        Collectors.mapping(
                                BeforeAdjustItemReqDTO::getSupplierCategoryStatus, // 要收集的字段
                                Collectors.toList() // 收集方式
                        )
                ));
    }

    private Map<String, SupplierBasicInfoPO> querySupplierBasicInfo(CreateSupplierCategoryBatchAdjustApplyReqDTO req) {
        List<String> sapSupplierCodes = req.getOldItems().stream().map(BeforeAdjustItemReqDTO::getSapSupplierCode).collect(Collectors.toList());
        List<SupplierBasicInfoPO> suppliers = supplierBasicInfoMapperService.lambdaQuery()
                .in(SupplierBasicInfoPO::getSapSupplierCode, sapSupplierCodes).list();
        Map<String, SupplierBasicInfoPO> supplierMap = new HashMap<>();
        for (SupplierBasicInfoPO supplier:suppliers) {
            supplierMap.put(supplier.getSapSupplierCode(), supplier);
        }
        return supplierMap;
    }

}
