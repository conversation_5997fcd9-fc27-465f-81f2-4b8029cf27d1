package com.weifu.srm.supplier.service.biz;

import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.integration.response.qixinbao.BusinessInformationRespDTO;
import com.weifu.srm.supplier.manager.remote.intergration.QiXinBaoManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.response.SupplierInfoPartRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierInfoWithCreditCodeBiz {

    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final QiXinBaoManager qiXinBaoManager;
    private final LocaleMessage localeMessage;

    public SupplierInfoPartRespDTO getSupplierInformationByCreditCode(String creditCode) {
        SupplierInfoPartRespDTO result = new SupplierInfoPartRespDTO();
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getCreditCode, creditCode)
                .one();
        // 基础表存在数据
        if (ObjectUtils.isNotEmpty(basicInfoPO)) {
            if (SupplierBasicStatusEnum.LEAVED.getCode().equals(basicInfoPO.getStatus())) {
                log.error("this supplier ={} had exited",basicInfoPO);
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_HAD_EXIT));
            }
            result.setSupplierName(basicInfoPO.getSupplierName());
            result.setSupplierShortName(basicInfoPO.getSupplierShortName());
            result.setDomesticForeignRelationship(basicInfoPO.getIsOverseas());
            return result;
        }
        SupplierAdmissionInvitationRecordPO invitationRecordPO = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getCreditCode, creditCode)
                .last("limit 1").one();
        // 邀请表存在数据
        if (ObjectUtils.isNotEmpty(invitationRecordPO)) {
            result.setSupplierName(invitationRecordPO.getSupplierName());
            result.setSupplierShortName(invitationRecordPO.getSupplierShortName());
            result.setDomesticForeignRelationship(invitationRecordPO.getDomesticForeignRelationship());
            return result;
        }
        // 启信宝数据
        if (StringUtils.isBlank(result.getSupplierName())) {
            BusinessInformationRespDTO businessInformation = qiXinBaoManager.getBusinessInformation(creditCode);
            if (ObjectUtils.isNotEmpty(businessInformation)){
                result.setSupplierName(businessInformation.getFormat_name());
            }
        }
        return result;
    }
}
