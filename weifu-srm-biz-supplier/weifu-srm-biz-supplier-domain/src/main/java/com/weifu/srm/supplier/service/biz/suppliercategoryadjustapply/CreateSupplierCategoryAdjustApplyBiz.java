package com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.audit.mq.CreateTicketMQ.ApprovalProcessVar;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.constants.ErrorCodeConstants;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyTypeEnum;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.CreateSupplierCategoryAdjustApplyReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.request.suppliercategoryadjustapply.CreateSupplierCategoryAdjustApplyReqDTO.CreateSupplierCategoryAdjustApplyItemReqDTO;

/**
 * 创建供应商品类关系调整申请
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreateSupplierCategoryAdjustApplyBiz {

    private final static String APPLY_NO_CONFIG = "supplier-category-adjust-apply-no";

    private final TransactionTemplate transactionTemplate;
    private final SupplierCategoryAdjustApplyMapperService supplierCategoryAdjustApplyMapperService;
    private final SupplierCategoryAdjustApplyItemMapperService supplierCategoryAdjustApplyItemMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final CategoryApi categoryApi;
    private final CategoryManager categoryManager;
    private final MqManager mqManager;
    private final IdService idService;
    private final LocaleMessage localeMessage;

    public String execute(CreateSupplierCategoryAdjustApplyReqDTO req) {
        log.info("创建供应商品类关系调整申请开始，req={}", JacksonUtil.bean2Json(req));
        check(req);

        Date current = new Date();
        String applyNo = generateApplyNo();

        SupplierCategoryAdjustApplyPO apply = BeanUtil.toBean(req, SupplierCategoryAdjustApplyPO.class);
        apply.setApplyNo(applyNo);
        apply.setApplyTime(current);
        apply.setApplyStatus(SupplierCategoryAdjustApplyStatusEnum.APPROVING.getCode());
        BaseEntityUtil.setCommon(apply, req.getApplyBy(), req.getApplyName(), current);

        // 统一转换成三级品类
        List<SupplierCategoryAdjustApplyItemPO> items = convertLevel(applyNo, current, req);

        List<AttachmentRecordPO> files = BeanUtil.copyToList(req.getFiles(), AttachmentRecordPO.class);
        if (CollectionUtils.isNotEmpty(files)) {
            for (AttachmentRecordPO file:files) {
                file.setBusinessNo(applyNo);
                file.setBusinessType(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_COMMON);
                BaseEntityUtil.setCommon(file, req.getApplyBy(), req.getApplyName(), current);
            }
        }

        List<ApprovalProcessVar> processVars = buildProcessVars(req.getApplyType(), items);

        // 保存到数据库
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                supplierCategoryAdjustApplyMapperService.save(apply);

                supplierCategoryAdjustApplyItemMapperService.saveBatch(items);

                if (CollectionUtils.isNotEmpty(files)) {
                    attachmentRecordMapperService.saveBatch(files);
                }

                // 发送创建工单MQ
                CreateTicketMQ ticket = new CreateTicketMQ();
                ticket.setTicketType(calcTicketType(req.getApplyType()));
                ticket.setBusinessNo(applyNo);
                ticket.setSubmitBy(req.getApplyBy());
                ticket.setSubmitName(req.getApplyName());
                ticket.setSubmitDesc(req.getApplyDesc());
                ticket.setSubmitRemark(req.getApplyRemark());
                ticket.setProcessVars(processVars);
                mqManager.sendTopic(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(ticket));
            }
        });
        log.info("创建供应商品类关系调整申请完成，applyNo={}", applyNo);
        return applyNo;
    }

    public List<ApprovalProcessVar> buildProcessVars(String applyType, List<SupplierCategoryAdjustApplyItemPO> items) {
        List<String> categoryCodes = items.stream().map(SupplierCategoryAdjustApplyItemPO::getThreeLevelCategoryCode).collect(Collectors.toList());

        Set<String> plzzList = new HashSet<>();
        Set<String> plzzCategoryCodes = new HashSet<>();
        Set<String> cgczList = new HashSet<>();
        Set<String> cgczCategoryCodes = new HashSet<>();
        List<CategoryEngineerResultDTO> categoryEngineers = categoryManager.queryCategoryEngineerByCategoryCodes(categoryCodes);
        for (CategoryEngineerResultDTO categoryEngineer:categoryEngineers) {
            if (StringUtils.equals(CategoryRoleEnum.CPE_MASTER.getCode(), categoryEngineer.getRoleId())) {
                plzzList.add(categoryEngineer.getUserDomain());
                plzzCategoryCodes.add(categoryEngineer.getCategoryCode());
            } else if (StringUtils.equals(CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode(), categoryEngineer.getRoleId())) {
                cgczList.add(categoryEngineer.getUserDomain());
                cgczCategoryCodes.add(categoryEngineer.getCategoryCode());
            }
        }

        for (SupplierCategoryAdjustApplyItemPO item:items) {
            String categoryCode = item.getThreeLevelCategoryCode();
            if (!plzzCategoryCodes.contains(categoryCode)) {
                throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.FIND_APPROVER_FAILED_CHECK_CONFIGURATION, new String[]{item.getThreeLevelCategoryName()}));
            }
            if (!cgczCategoryCodes.contains(categoryCode)) {
                throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.FIND_APPROVER_FAILED_CHECK_CONFIGURATION, new String[]{item.getThreeLevelCategoryName()}));
            }
        }

        List<ApprovalProcessVar> processVars = new ArrayList<>();
        ApprovalProcessVar plzzVar = new ApprovalProcessVar();
        plzzVar.setKey("PLZZ");
        plzzVar.setV(String.join(" ", plzzList));
        processVars.add(plzzVar);

        ApprovalProcessVar cgczVar = new ApprovalProcessVar();
        cgczVar.setKey(SupplierCategoryAdjustApplyTypeEnum.HIGH_LEVEL_CATEGORY_DEL.equalsCode(applyType)?"ZZCLCGCCZ":"CGCZ");
        cgczVar.setV(String.join(" ", cgczList));
        processVars.add(cgczVar);
        return processVars;
    }

    private String generateApplyNo() {
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long num = idService.nextId(APPLY_NO_CONFIG + "-" + date, APPLY_NO_CONFIG);
        String suffix = String.format("%04d",num);
        return "CQA" + date + suffix;
    }

    /**
     * 计算工单类型
     */
    private String calcTicketType(String applyType) {
        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_ADD.equalsCode(applyType)
                || SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_DEL.equalsCode(applyType)) {
            return TicketTypeEnum.SUPPLIER_CATEGORY_QUALIFICATION_CHANGE.getCode();
        } else {
            return TicketTypeEnum.SUPPLIER_CATEGORY_DELETE.getCode();
        }
    }

    private void check(CreateSupplierCategoryAdjustApplyReqDTO req) {
        SupplierCategoryAdjustApplyPO apply = supplierCategoryAdjustApplyMapperService.getApproving(req.getSapSupplierCode());
        if (apply != null) {
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.SUPPLIER_HAS_PENDING_ADJUSTMENT_APPLICATION));
        }
        // 调整类型为三级品类删除时，三级品类别删除后，同一二级品类下至少还得保留一个三级品类
        if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_DEL.equalsCode(req.getApplyType())) {
            List<SupplierCategoryRelationshipPO> relationships = supplierCategoryRelationshipMapperService.queryBySapSupplierCodeList(List.of(req.getSapSupplierCode()));
            List<String> categoryCodes = relationships.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toList());
            List<CategoryWithAllLevelDTO> categorys = categoryManager.listCategory(categoryCodes);

            // 供应商所有品类关系，按照二级品类分组
            Map<String, List<CategoryWithAllLevelDTO>> categoryMap = categorys.stream().collect(Collectors.groupingBy(CategoryWithAllLevelDTO::getTwoLevelCategoryCode));

            // 要删除的供应商品类关系，按照二级品类分组
            Map<String, List<CategoryWithAllLevelDTO>> deleteTwoCategoryMap = new HashMap<>();
            for (CreateSupplierCategoryAdjustApplyItemReqDTO item:req.getItems()) {
                for (CategoryWithAllLevelDTO category:categorys) {
                    if (!StringUtils.equals(item.getCategoryCode(), category.getThreeLevelCategoryCode())) {
                        continue;
                    }
                    String twoLevelCategoryCode = category.getTwoLevelCategoryCode();
                    List<CategoryWithAllLevelDTO> deleteCategorys = deleteTwoCategoryMap.get(twoLevelCategoryCode);
                    if (deleteCategorys == null) {
                        deleteCategorys = new ArrayList<>();
                        deleteTwoCategoryMap.put(twoLevelCategoryCode, deleteCategorys);
                    }
                    deleteCategorys.add(category);
                }
            }

            for (Map.Entry<String, List<CategoryWithAllLevelDTO>> deleteTwoCategory:deleteTwoCategoryMap.entrySet()) {
                String twoLevelCategoryCode = deleteTwoCategory.getKey();
                // 在同一二级品类下的要删除的三级品类关系
                List<CategoryWithAllLevelDTO> subDeleteCategorys = deleteTwoCategory.getValue();
                // 供应商在同一二级品类下的所有三级品类关系
                List<CategoryWithAllLevelDTO> subCategorys = categoryMap.get(twoLevelCategoryCode);
                if (CollectionUtils.size(subDeleteCategorys) >= CollectionUtils.size(subCategorys)) {
                    throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.SUPPLIER_CATEGORY_ADJ_THIRD_LEVEL_CATEGORY_CANNOT_BE_EMPTY, new String[] {subCategorys.get(0).getTwoLevelCategoryName()}));
                }
            }
        }
    }

    private List<SupplierCategoryAdjustApplyItemPO> convertLevel(String applyNo, Date current, CreateSupplierCategoryAdjustApplyReqDTO req) {
        // 将品类统一转换为三级品类
        List<CreateSupplierCategoryAdjustApplyItemReqDTO> items = req.getItems();
        List<String> categoryCodes = items.stream().map(CreateSupplierCategoryAdjustApplyItemReqDTO::getCategoryCode).collect(Collectors.toList());
        ApiResponse<List<CategoryWithAllLevelDTO>> apiResponse = categoryApi.listCategory(categoryCodes);
        if (!apiResponse.getSucc()) {
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.QUERY_CATEGORY_INFO_EXCEPTION));
        }

        List<SupplierCategoryRelationshipPO> relationships = supplierCategoryRelationshipMapperService.queryBySapSupplierCodeList(Arrays.asList(req.getSapSupplierCode()));
        Set<String> existCategoryCodeMap = relationships.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toSet());

        List<SupplierCategoryAdjustApplyItemPO> applyItems = new ArrayList<>();
        for (CategoryWithAllLevelDTO category:apiResponse.getData()) {
            // 品类删除时，如果不是与供应商已存在关系的三级品类，则不计入明细里
            if (SupplierCategoryAdjustApplyTypeEnum.THREE_LEVEL_CATEGORY_DEL.equalsCode(req.getApplyType())
                    || SupplierCategoryAdjustApplyTypeEnum.HIGH_LEVEL_CATEGORY_DEL.equalsCode(req.getApplyType())) {
                if (!existCategoryCodeMap.contains(category.getThreeLevelCategoryCode())) {
                    continue;
                }
            }

            SupplierCategoryAdjustApplyItemPO applyItem = BeanUtil.toBean(category, SupplierCategoryAdjustApplyItemPO.class);
            applyItem.setApplyNo(applyNo);
            applyItem.setSapSupplierCode(req.getSapSupplierCode());
            BaseEntityUtil.setCommon(applyItem, req.getApplyBy(), req.getApplyName(), current);
            applyItems.add(applyItem);
        }
        return applyItems;
    }

}
