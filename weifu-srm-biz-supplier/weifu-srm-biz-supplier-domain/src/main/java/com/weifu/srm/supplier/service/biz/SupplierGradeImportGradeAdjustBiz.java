package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGradeFileErrorEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGradeTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierBlackListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.response.SupplierGradeImportCheckCertificateRespDTO;
import com.weifu.srm.supplier.response.SupplierGradeImportRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGradeImportGradeAdjustBiz {

    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierBlackListMapperService supplierBlackListMapperService;
    private final SupplierGreyListMapperService supplierGreyListMapperService;

    public SupplierGradeImportRespDTO importGradeAdjust(MultipartFile file) {
        SupplierGradeImportRespDTO result = new SupplierGradeImportRespDTO();
        String[] split = Objects.requireNonNull(file.getOriginalFilename()).split("\\.");
        if (!split[split.length - 1].equals("xlsx")) {
            throw new BizFailException(SupplierGradeFileErrorEnum.NOT_XLSX_FILE.getContent());
        }
        // sheet3 总分与分级结果
        getSheet3Content(file, result);
        List<String> supplierCodes = result.getSupplierGradeImportCheckCertificateRespDTOList()
                .stream().map(SupplierGradeImportCheckCertificateRespDTO::getSupplierCode)
                .collect(Collectors.toList());
        int failCount = result.getSupplierGradeImportCheckCertificateRespDTOList().size();
        int successCount = 0;
        int batchSize = 500;
        List<List<SupplierBasicInfoPO>> basicMsgList = new ArrayList<>();
        for (int i = 0; i < supplierCodes.size(); i += batchSize) {
            int end = Math.min(i + batchSize, supplierCodes.size());
            List<String> batchIds = supplierCodes.subList(i, end);
            LambdaQueryWrapper<SupplierBasicInfoPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
            lambdaQueryWrapper.in(SupplierBasicInfoPO::getSapSupplierCode, batchIds);
            basicMsgList.add(supplierBasicInfoMapperService.list(lambdaQueryWrapper));
        }
        List<SupplierBasicInfoPO> supplierBasicInfoPOList = basicMsgList.stream().flatMap(Collection::stream).collect(Collectors.toList());
        // 获取灰名单供应商列表
        List<SupplierGreyListPO> greyListPOList = supplierGreyListMapperService.lambdaQuery()
                .eq(SupplierGreyListPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        List<String> greySupplierList = greyListPOList.stream().map(SupplierGreyListPO::getSupplierCode).collect(Collectors.toList());
        // 获取黑名单供应商列表
        List<SupplierBlackListPO> blackListPOList = supplierBlackListMapperService.lambdaQuery()
                .eq(SupplierBlackListPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        List<String> blackSupplierList = blackListPOList.stream().map(SupplierBlackListPO::getSupplierCode).collect(Collectors.toList());
        for (SupplierGradeImportCheckCertificateRespDTO supplierGradeImportCheckCertificateRespDTO : result.getSupplierGradeImportCheckCertificateRespDTOList()) {
            supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResult(0);
            supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResultDesc("供应商编码不存在");
            SupplierBasicInfoPO supplierBasicInfo = supplierBasicInfoPOList.stream()
                    .filter(supplierInfo -> supplierInfo.getSapSupplierCode().equals(supplierGradeImportCheckCertificateRespDTO.getSupplierCode()))
                    .findFirst().orElse(null);
            if (supplierBasicInfo == null) {
                continue;
            }
            if (!Objects.equals(SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode(), supplierBasicInfo.getStatus())) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeBefore(supplierBasicInfo.getSupplierClassification());
                supplierGradeImportCheckCertificateRespDTO.setSupplierStatus(supplierBasicInfo.getStatus());
                supplierGradeImportCheckCertificateRespDTO.setSupplierName(supplierBasicInfo.getSupplierName());
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResult(0);
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResultDesc("供应商状态不是合格供应商");
            } else if (greySupplierList.contains(supplierGradeImportCheckCertificateRespDTO.getSupplierCode())) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeBefore(supplierBasicInfo.getSupplierClassification());
                supplierGradeImportCheckCertificateRespDTO.setSupplierStatus(supplierBasicInfo.getStatus());
                supplierGradeImportCheckCertificateRespDTO.setSupplierName(supplierBasicInfo.getSupplierName());
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResult(0);
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResultDesc("供应商在灰名单内");
            } else if (blackSupplierList.contains(supplierGradeImportCheckCertificateRespDTO.getSupplierCode())) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeBefore(supplierBasicInfo.getSupplierClassification());
                supplierGradeImportCheckCertificateRespDTO.setSupplierStatus(supplierBasicInfo.getStatus());
                supplierGradeImportCheckCertificateRespDTO.setSupplierName(supplierBasicInfo.getSupplierName());
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResult(0);
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResultDesc("供应商在黑名单内");
            } else {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeBefore(supplierBasicInfo.getSupplierClassification());
                supplierGradeImportCheckCertificateRespDTO.setSupplierStatus(supplierBasicInfo.getStatus());
                supplierGradeImportCheckCertificateRespDTO.setSupplierName(supplierBasicInfo.getSupplierName());
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResult(1);
                supplierGradeImportCheckCertificateRespDTO.setSupplierCheckResultDesc("");
                successCount++;
                failCount--;
            }
        }
        result.setSupplierFailCount(failCount);
        result.setSupplierSuccessCount(successCount);
        return result;
    }

    public static String getValue(Cell cell) {
        String reString = "0";
        if (cell != null) {
            log.info("getValue {}", cell.getCellType());
            switch (cell.getCellType()) {
                case STRING:
                    reString = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    reString = NumberToTextConverter.toText(cell.getNumericCellValue());
                    break;
                case FORMULA:
                    switch (cell.getCachedFormulaResultType()) {
                        case STRING:
                            reString = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            reString = NumberToTextConverter.toText(cell.getNumericCellValue());
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
        }
        return reString;
    }

    private void getSheet3Content(MultipartFile file, SupplierGradeImportRespDTO result) {
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(3);
            if (!sheet.getSheetName().equals("总分与分级结果")) {
                throw new BizFailException(SupplierGradeFileErrorEnum.UNABLE_TO_BATCH_UPLOAD.getContent());
            }
            if (!sheet.getRow(5).getCell(1).getStringCellValue().equals("供应商编码")) {
                throw new BizFailException(SupplierGradeFileErrorEnum.UNABLE_TO_BATCH_UPLOAD.getContent());
            }
            if (!sheet.getRow(5).getCell(5).getStringCellValue().equals("供应商定级结果")) {
                throw new BizFailException(SupplierGradeFileErrorEnum.UNABLE_TO_BATCH_UPLOAD.getContent());
            }
            int rowIndex = 6;
            int rowCount = sheet.getPhysicalNumberOfRows();
            if (sheet.getRow(6).getCell(1) == null) {
                throw new BizFailException(SupplierGradeFileErrorEnum.FAIL_TO_UPLOAD.getContent());
            }
            List<SupplierGradeImportCheckCertificateRespDTO> supplierGradeImportCheckCertificateRespDTOList = new ArrayList<>();
            for (; rowIndex < rowCount; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }
                Cell cell1 = row.getCell(1);
                if (ObjectUtils.isNotEmpty(cell1) && ObjectUtil.equals("0", getValue(cell1))) {
                    break;
                }
                // get row content
                getSheet3RowContent(row, supplierGradeImportCheckCertificateRespDTOList);
            }
            result.setSupplierGradeImportCheckCertificateRespDTOList(supplierGradeImportCheckCertificateRespDTOList);
        } catch (Exception e) {
            log.error("importGradeAdjust has error!", e);
            throw new BizFailException(SupplierGradeFileErrorEnum.FAIL_TO_UPLOAD.getContent());
        }
    }

    private void getSheet3RowContent(Row row, List<SupplierGradeImportCheckCertificateRespDTO> supplierGradeImportCheckCertificateRespDTOList) {
        Cell cell1 = row.getCell(1);
        log.debug("info {}", cell1);
        SupplierGradeImportCheckCertificateRespDTO supplierGradeImportCheckCertificateRespDTO = new SupplierGradeImportCheckCertificateRespDTO();
        if (ObjectUtils.isNotEmpty(cell1)) {
            String value = getValue(cell1);
            supplierGradeImportCheckCertificateRespDTO.setSupplierCode(value);
        }
        if (row.getCell(5) == null) {
            throw new BizFailException(SupplierGradeFileErrorEnum.FAIL_TO_UPLOAD.getContent());
        } else {
            String value = row.getCell(5).getStringCellValue().split(". ")[1];
            if (SupplierGradeTypeEnum.BASE_SUPPLIER.getChineseName().equals(value)) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeAfter(SupplierGradeTypeEnum.BASE_SUPPLIER.getCode());
            }
            if (SupplierGradeTypeEnum.CORE_SUPPLIER.getChineseName().equals(value)) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeAfter(SupplierGradeTypeEnum.CORE_SUPPLIER.getCode());
            }
            if (SupplierGradeTypeEnum.STRATEGY_SUPPLIER.getChineseName().equals(value)) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeAfter(SupplierGradeTypeEnum.STRATEGY_SUPPLIER.getCode());
            }
            if (SupplierGradeTypeEnum.NON_GRADE.getChineseName().equals(value)) {
                supplierGradeImportCheckCertificateRespDTO.setSupplierGradeAfter(SupplierGradeTypeEnum.NON_GRADE.getCode());
            }
        }
        supplierGradeImportCheckCertificateRespDTOList.add(supplierGradeImportCheckCertificateRespDTO);
    }

}
