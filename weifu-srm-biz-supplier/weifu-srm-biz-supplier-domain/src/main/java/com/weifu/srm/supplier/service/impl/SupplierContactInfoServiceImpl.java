package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.enums.SupplierContactTypeEnum;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierContactInfoMapperService;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierContactInfoPO;
import com.weifu.srm.supplier.request.SupplierContactQueryReqDTO;
import com.weifu.srm.supplier.request.SupplierFinanceContactQueryReqDTO;
import com.weifu.srm.supplier.response.SupplierContactAllRespDTO;
import com.weifu.srm.supplier.response.SupplierContactSupplierCodeRespDTO;
import com.weifu.srm.supplier.response.SupplierFinanceContactRespDTO;
import com.weifu.srm.supplier.service.SupplierContactInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商质量问题解决表 服务类
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierContactInfoServiceImpl implements SupplierContactInfoService {
    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;


    @Override
    public List<SupplierContactAllRespDTO> listByQuery(SupplierContactQueryReqDTO reqDTO) {
        List<SupplierContactInfoPO> poList = supplierContactInfoMapperService.list(Wrappers.<SupplierContactInfoPO>lambdaQuery()
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(reqDTO.getSupplierBasicMsgId() != null, SupplierContactInfoPO::getSupplierBasicMsgId, reqDTO.getSupplierBasicMsgId()));

        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayList();
        }
        List<SupplierContactAllRespDTO> dtoList = buildSupplierContactResp(poList);
        if (StringUtils.isNotBlank(reqDTO.getType())) {
            dtoList = dtoList.stream().filter(dto -> dto.getType().equals(reqDTO.getType())).collect(Collectors.toList());
        }
        return dtoList;
    }

    @Override
    public SupplierContactAllRespDTO getSupplierContactInfo(Long supplierBasicMsgId, String type) {
        List<SupplierContactInfoPO> poList = supplierContactInfoMapperService.list(Wrappers.<SupplierContactInfoPO>lambdaQuery()
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, supplierBasicMsgId));
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        List<SupplierContactAllRespDTO> dtoList = buildSupplierContactResp(poList);
        dtoList = dtoList.stream().filter(dto -> dto.getType().equals(type)).collect(Collectors.toList());
        return CollectionUtils.isEmpty(dtoList) ? null : dtoList.get(0);
    }

    @Override
    public List<SupplierContactSupplierCodeRespDTO> listSupplierContactInfoBySupplierCodes(List<String> supplierCodes, String roleType) {
        List<SupplierBasicInfoPO> supplierBasicInfoList = supplierBasicInfoMapperService.queryCertificate(supplierCodes);
        if (CollectionUtils.isEmpty(supplierBasicInfoList)) {
            return Collections.emptyList();
        }
        Map<Long, String> mapperMap = Maps.newHashMap();
        List<Long> ids = Lists.newArrayList();
        for (SupplierBasicInfoPO supplierBasicInfo : supplierBasicInfoList) {
            mapperMap.put(supplierBasicInfo.getId(), supplierBasicInfo.getSapSupplierCode());
            ids.add(supplierBasicInfo.getId());
        }
        List<SupplierContactInfoPO> poList = supplierContactInfoMapperService.list(Wrappers.<SupplierContactInfoPO>lambdaQuery().in(SupplierContactInfoPO::getSupplierBasicMsgId, ids));
        List<SupplierContactAllRespDTO> dtoList = buildSupplierContactResp(poList);
        List<SupplierContactSupplierCodeRespDTO> resultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (SupplierContactAllRespDTO dto : dtoList) {
                if (StringUtils.isNotBlank(roleType) && !roleType.equals(dto.getType())) {
                    continue;
                }
                SupplierContactSupplierCodeRespDTO supplierContactSupplierCodeResp = BeanUtil.copyProperties(dto, SupplierContactSupplierCodeRespDTO.class);
                supplierContactSupplierCodeResp.setSupplierCode(mapperMap.get(dto.getSupplierBasicMsgId()));
                resultList.add(supplierContactSupplierCodeResp);
            }
        }
        return resultList;
    }

    @Override
    public PageResponse<SupplierFinanceContactRespDTO> pageFinancialContact(SupplierFinanceContactQueryReqDTO param) {
        return supplierContactInfoMapperService.pageFinancialContact(param);
    }

    private List<SupplierContactAllRespDTO> buildSupplierContactResp(List<SupplierContactInfoPO> poList) {
        List<SupplierContactAllRespDTO> dtoList = Lists.newArrayList();
        for (SupplierContactInfoPO po : poList) {
            dtoList.add(buildSupplierContactResp(SupplierContactTypeEnum.LEGAL_PERSON.getCode(), po.getSupplierBasicMsgId(),
                    po.getLegalPersonName(), po.getLegalPersonEmail(), po.getLegalPersonPhone(), null));
            dtoList.add(buildSupplierContactResp(SupplierContactTypeEnum.REGISTER.getCode(), po.getSupplierBasicMsgId(),
                    po.getRegisterContactName(), po.getRegisterContactEmail(), po.getRegisterContactPhone(), po.getRegisterContactPosition()));
            dtoList.add(buildSupplierContactResp(SupplierContactTypeEnum.BUSINESS.getCode(), po.getSupplierBasicMsgId(),
                    po.getBusinessContactName(), po.getBusinessContactEmail(), po.getBusinessContactPhone(), po.getBusinessContactPosition()));
            dtoList.add(buildSupplierContactResp(SupplierContactTypeEnum.QUALITY.getCode(), po.getSupplierBasicMsgId(),
                    po.getQualityContactName(), po.getQualityContactEmail(), po.getQualityContactPhone(), po.getBusinessContactPosition()));
            dtoList.add(buildSupplierContactResp(SupplierContactTypeEnum.FINANCE.getCode(), po.getSupplierBasicMsgId(),
                    po.getFinanceContactName(), po.getFinanceContactEmail(), po.getFinanceContactPhone(), po.getFinanceContactPosition()));
        }
        return dtoList;
    }

    private SupplierContactAllRespDTO buildSupplierContactResp(String type, Long supplierBasicMsgId, String name,
                                                               String email, String phone, String position) {
        SupplierContactAllRespDTO supplierContactAllRespDTO = new SupplierContactAllRespDTO();
        supplierContactAllRespDTO.setSupplierBasicMsgId(supplierBasicMsgId);
        supplierContactAllRespDTO.setType(type);
        supplierContactAllRespDTO.setName(name);
        supplierContactAllRespDTO.setEmail(email);
        supplierContactAllRespDTO.setPhone(phone);
        supplierContactAllRespDTO.setPosition(position);
        return supplierContactAllRespDTO;
    }
}
