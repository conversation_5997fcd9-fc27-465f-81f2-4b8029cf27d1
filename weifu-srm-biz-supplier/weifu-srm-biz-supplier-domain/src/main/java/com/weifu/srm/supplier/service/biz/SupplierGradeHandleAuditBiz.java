package com.weifu.srm.supplier.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.enums.NoticeTemplateEnum;
import com.weifu.srm.supplier.repository.enums.SupplierGradeWorkStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyPO;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.request.SysUserQueryReqDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGradeHandleAuditBiz {
    @Value("${environment.params.supplierGradeLink:todo}")
    private String supplierGradeLink;

    private final SupplierGradeAdjustApplyMapperService supplierGradeAdjustApplyMapperService;
    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;
    private final SupplierBasicInfoMapperService supplierBasicMsgMapperService;
    private final TransactionTemplate transactionTemplate;
    private final MQServiceManager mqService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final SysUserApi sysUserApi;
    private final CategoryApi categoryApi;


    /**
     * 处理准入审批结果
     */
    public void handleAudit(TicketStatusChangedMQ ticketInfo) {
        if (TicketStatusEnum.APPROVING.equalsCode(ticketInfo.getStatus())) {
            return;
        }
        // 查询出供应商主表的信息
        LambdaQueryWrapper<SupplierGradeAdjustApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierGradeAdjustApplyPO::getApplyNo, ticketInfo.getBusinessNo());
        SupplierGradeAdjustApplyPO supplierGradeAdjustApplyPO = supplierGradeAdjustApplyMapperService.getOne(queryWrapper);
        // 审批通过
        if (TicketStatusEnum.APPROVED.equalsCode(ticketInfo.getStatus())) {
            // 更新供应商主表的分级
            LambdaQueryWrapper<SupplierGradeAdjustApplyItemPO> itemQueryWrapper = new LambdaQueryWrapper<>();
            itemQueryWrapper.eq(SupplierGradeAdjustApplyItemPO::getApplyNo, supplierGradeAdjustApplyPO.getApplyNo());
            // 查询出对应的分级调整列表
            List<SupplierGradeAdjustApplyItemPO> supplierGradeAdjustApplyItemPOS = supplierGradeAdjustApplyItemMapperService.list(itemQueryWrapper);
            List<String> supplierCodes = supplierGradeAdjustApplyItemPOS.stream()
                    .map(SupplierGradeAdjustApplyItemPO::getSupplierCode)
                    .collect(Collectors.toList());
            LambdaQueryWrapper<SupplierBasicInfoPO> supplierBasicMsgPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierBasicMsgPOLambdaQueryWrapper.in(SupplierBasicInfoPO::getSapSupplierCode, supplierCodes);
            // 查询出对应供应商主表的数据，进行批量保存
            List<SupplierBasicInfoPO> supplierBasicMsgPOS = supplierBasicMsgMapperService.list(supplierBasicMsgPOLambdaQueryWrapper);
            supplierBasicMsgPOS.forEach(supplierBasicMsgPO -> {
                for (SupplierGradeAdjustApplyItemPO supplierGradeAdjustApplyItemPO : supplierGradeAdjustApplyItemPOS) {
                    if (supplierBasicMsgPO.getSapSupplierCode().equals(supplierGradeAdjustApplyItemPO.getSupplierCode())) {
                        supplierBasicMsgPO.setSupplierClassification(supplierGradeAdjustApplyItemPO.getSupplierGradeAfter());
                        supplierBasicMsgPO.setUpdateTime(new Date());
                    }
                }
            });
            transactionTemplate.execute(transactionStatus -> {
                // 更新申请单审核状态
                LambdaUpdateWrapper<SupplierGradeAdjustApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper
                        .eq(SupplierGradeAdjustApplyPO::getApplyNo, ticketInfo.getBusinessNo())
                        .set(SupplierGradeAdjustApplyPO::getTicketNo, ticketInfo.getTicketNo())
                        .set(SupplierGradeAdjustApplyPO::getApplyStatus, SupplierGradeWorkStatusEnum.APPROVE_STATUS.getCode())
                        .set(SupplierGradeAdjustApplyPO::getUpdateBy, ticketInfo.getOperateBy())
                        .set(SupplierGradeAdjustApplyPO::getUpdateName, ticketInfo.getOperateName())
                        .set(SupplierGradeAdjustApplyPO::getUpdateTime, new Date())
                        .set(SupplierGradeAdjustApplyPO::getPublishTime, new Date());
                supplierGradeAdjustApplyMapperService.update(updateWrapper);
                // 更新供应商主表等级
                supplierBasicMsgMapperService.updateBatchById(supplierBasicMsgPOS);
                // 通知对应的人
                mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(supplierCodes,ticketInfo)));
                return null;
            });
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(ticketInfo.getStatus())) {
            // 更新申请单审核状态
            LambdaUpdateWrapper<SupplierGradeAdjustApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SupplierGradeAdjustApplyPO::getApplyNo, ticketInfo.getBusinessNo())
                    .set(SupplierGradeAdjustApplyPO::getApplyStatus, SupplierGradeWorkStatusEnum.REJECT_STATUS.getCode())
                    .set(SupplierGradeAdjustApplyPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierGradeAdjustApplyPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierGradeAdjustApplyPO::getUpdateTime, new Date())
                    .set(SupplierGradeAdjustApplyPO::getPublishTime, new Date());
            supplierGradeAdjustApplyMapperService.update(updateWrapper);
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(ticketInfo.getStatus())) {
            // 更新申请单审核状态
            LambdaUpdateWrapper<SupplierGradeAdjustApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SupplierGradeAdjustApplyPO::getApplyNo, ticketInfo.getBusinessNo())
                    .set(SupplierGradeAdjustApplyPO::getApplyStatus, SupplierGradeWorkStatusEnum.RECALL_STATUS.getCode())
                    .set(SupplierGradeAdjustApplyPO::getUpdateBy, ticketInfo.getOperateBy())
                    .set(SupplierGradeAdjustApplyPO::getUpdateName, ticketInfo.getOperateName())
                    .set(SupplierGradeAdjustApplyPO::getUpdateTime, new Date())
                    .set(SupplierGradeAdjustApplyPO::getPublishTime, new Date());
            supplierGradeAdjustApplyMapperService.update(updateWrapper);
        }
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(List<String> supplierCodes, TicketStatusChangedMQ ticketInfo) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        LambdaQueryWrapper<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierCategoryRelationshipPOLambdaQueryWrapper
                .in(SupplierCategoryRelationshipPO::getSapSupplierCode, supplierCodes)
                .eq(SupplierCategoryRelationshipPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.list(supplierCategoryRelationshipPOLambdaQueryWrapper);
        List<String> categoryCodes = supplierCategoryRelationshipPOS.stream()
                .map(SupplierCategoryRelationshipPO::getCategoryCode)
                .collect(Collectors.toList());
        ApiResponse<List<CategoryEngineerResultDTO>> userOne = categoryApi.queryCategoryEngineerByCategoryCodes(categoryCodes);
        // 查询出所有的规划处长，规划工程师
        SysUserQueryReqDTO var1 = new SysUserQueryReqDTO();
        var1.setRoleIds(Arrays.asList("PLAN_ENGINEER", "PLAN_DIRECTOR"));
        ApiResponse<List<BaseSysUserRespDTO>> userTwo = sysUserApi.findUser(var1);
        // 去重
        HashMap<Long, String> userMap = new HashMap<>();
        userOne.getData().forEach(categoryEngineerResultDTO -> userMap.put(categoryEngineerResultDTO.getUserId(), categoryEngineerResultDTO.getUserName()));
        userTwo.getData().forEach(baseSysUserRespDTO -> userMap.put(baseSysUserRespDTO.getId(), baseSysUserRespDTO.getUserName()));
        userMap.forEach((k, v) -> {
            CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
            notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
            notice.setBusinessNo(ticketInfo.getBusinessNo());
            notice.setBusinessType(MessageClsEnum.SUPPLIER_RANK_UPDATE.getCode());
            notice.setUserId(k);
            notice.setUserName(v);
            notice.setIconType(IconTypeEnum.GENERAL.getCode());
            notice.setTitle(NoticeTemplateEnum.SUPPLIER_ADJUST_RESULT_CONTENT.getTitle());
            String content = NoticeTemplateEnum.SUPPLIER_ADJUST_RESULT_CONTENT.getContent();
            notice.setContent(content);
            result.add(notice);
        });
        return result;
    }

}
