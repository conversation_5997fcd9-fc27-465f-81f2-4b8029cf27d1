package com.weifu.srm.supplier.service;
import com.weifu.srm.supplier.response.SupplierRegistryInfoRespDTO;
import com.weifu.srm.supplier.response.SupplierRoleRespDTO;
import com.weifu.srm.supplier.response.UserRelationSupplierRespDTO;
import com.weifu.srm.supplier.response.UserSupplierRelationshipInfoRespDTO;

import java.util.List;

public interface SupplierWithUserService {

    List<SupplierRegistryInfoRespDTO> searchSupplierRegistryListByUserId(Long userId);

    boolean checkRegisterUserUsed(String supplierName, String phone, String email);

    List<SupplierRoleRespDTO> querySupplierRoleByUserId(Long userId);

    List<UserRelationSupplierRespDTO> listByUserIds(List<Long> userIds);
    List<UserSupplierRelationshipInfoRespDTO> listBySupplierBasicInfoIdAndRoleId(Long supplierBasicInfoId, String roleId);
}
