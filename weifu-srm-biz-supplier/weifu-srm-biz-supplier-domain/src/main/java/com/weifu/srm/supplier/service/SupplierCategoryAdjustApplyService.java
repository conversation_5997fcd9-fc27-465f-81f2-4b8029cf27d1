package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.CreateSupplierCategoryAdjustApplyReqDTO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageReqDTO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyUploadFileReqDTO;
import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyDetailRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryTreeRespDTO;

import java.util.List;

/**
 * 供应商品类关系调整申请服务类
 */
public interface SupplierCategoryAdjustApplyService {

    /**
     * 分页查询供应商品类关系调整申请
     */
    PageResponse<SupplierCategoryAdjustApplyPageRespDTO> page(SupplierCategoryAdjustApplyPageReqDTO req);

    /**
     * 查询与供应商关联的品类树（为了删除）
     * @param sapSupplierCode 供应商编码（实际是CCMS生成，然后由SRM同步给Sap的）
     */
    List<SupplierCategoryTreeRespDTO> queryCategoryTreeForDel(String sapSupplierCode);

    /**
     * 查询与供应商关联的品类树（为了扩充）
     * @param sapSupplierCode 供应商编码（实际是CCMS生成，然后由SRM同步给Sap的）
     */
    List<SupplierCategoryTreeRespDTO> queryCategoryTreeForAdd(String sapSupplierCode);

    /**
     * 创建供应商品类关系调整申请
     */
    String create(CreateSupplierCategoryAdjustApplyReqDTO req);

    /**
     * 处理工单审批结果
     */
    void handleSupplierCategoryAdjustApplyRst(TicketStatusChangedMQ mq);

    /**
     * 上传供应商品类关系调整申请会议决议文件
     */
    void uploadMeetingResolutionFile(SupplierCategoryAdjustApplyUploadFileReqDTO req);

    /**
     * 查询申请详情
     */
    SupplierCategoryAdjustApplyDetailRespDTO queryApplyDetail(String applyNo);

    /**
     * 查询会议决议文件
     */
    List<AttachmentMessageRespDTO> queryMeetingResolutionFile(String applyNo);

}
