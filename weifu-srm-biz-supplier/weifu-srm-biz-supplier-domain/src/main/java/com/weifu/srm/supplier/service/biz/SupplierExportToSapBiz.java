package com.weifu.srm.supplier.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.integration.response.bpm.SapImportRespDTO;
import com.weifu.srm.supplier.manager.SupplierAdmissionManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.po.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/31 22:17
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class SupplierExportToSapBiz {
    private final SupplierAdmissionRecordMapperService recordMapperService;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final SupplierContactInfoMapperService contactInfoMapperService;
    private final SupplierFinancialInfoMapperService financialInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService admissionCategoryRecordMapperService;
    private final SupplierAdmissionManager supplierAdmissionManager;

    public SapImportRespDTO supplierExportToSapBiz(String admissionNo) {
        SupplierAdmissionRecordPO recordPO = recordMapperService.findByAdmissionNo(admissionNo);
        if (ObjectUtils.isEmpty(recordPO) || YesOrNoEnum.YES.equalsCode(recordPO.getSapStatus())) {
            SapImportRespDTO sapImportRespDTO = new SapImportRespDTO();
            sapImportRespDTO.setSapStatus(YesOrNoEnum.NO.getCode());
            sapImportRespDTO.setSapDesc("供应商准入记录不存在，或供应商已完成SAP导入");
            return sapImportRespDTO;
        }
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.getById(recordPO.getSupplierBasicMsgId());
        if (YesOrNoEnum.YES.equalsCode(basicInfoPO.getSynSapResult())){
            SapImportRespDTO sapImportRespDTO = new SapImportRespDTO();
            sapImportRespDTO.setSapStatus(YesOrNoEnum.NO.getCode());
            sapImportRespDTO.setSapDesc("供应商已完成SAP导入");
            return sapImportRespDTO;
        }
        SupplierContactInfoPO contactInfoPO = contactInfoMapperService.getOne(new LambdaQueryWrapper<SupplierContactInfoPO>()
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode()));
        List<SupplierFinancialInfoPO> financialInfoList = financialInfoMapperService.list(new LambdaQueryWrapper<SupplierFinancialInfoPO>()
                .eq(SupplierFinancialInfoPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .eq(SupplierFinancialInfoPO::getIsDelete, YesOrNoEnum.NO.getCode()));
        List<SupplierAdmissionCategoryRecordPO> categoryCodePOs = admissionCategoryRecordMapperService
                .lambdaQuery().eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, recordPO.getInvitationNo())
                .eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, YesOrNoEnum.NO.getCode())
                .list();
        List<String> categories = categoryCodePOs.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList());
        supplierAdmissionManager.synSupplier2SAP(recordPO, financialInfoList, contactInfoPO, basicInfoPO, categories);
        return new SapImportRespDTO();
    }
}
