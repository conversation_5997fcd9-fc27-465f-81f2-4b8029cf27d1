package com.weifu.srm.supplier.service.biz.admission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.exception.ParamErrorException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.SupplierAdmissionInvitationConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionCategoryManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionInvitationManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.purchase.SampleOrderManager;
import com.weifu.srm.supplier.manager.remote.user.SysDivisionManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.CreateTicketConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.AdmissionAndCategoryRespDTO;
import com.weifu.srm.supplier.request.AdmissionCategoryReqDTO;
import com.weifu.srm.supplier.request.QueryInvitation;
import com.weifu.srm.supplier.request.SupplierAdmissionInvitationReqDTO;


import com.weifu.srm.user.response.division.FindPurchaseManagerRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionInvitationCommitBiz {

    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierBlackListMapperService supplierBlackListMapperService;
    private final ThresholdInfoMapperService thresholdInfoMapperService;
    private final SupplierAdmissionCategoryManager supplierAdmissionCategoryManager;
    private final MQServiceManager mqServiceManager;
    private final TransactionTemplate transactionTemplate;
    private final CategoryManager categoryManager;
    private final SysUserManager sysUserManager;
    private final LocaleMessage localeMessage;
    private final SampleOrderManager sampleOrderManager;
    private final SupplierAdmissionInvitationManager admissionInvitationManager;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SupplierAdmissionInvitationConvert supplierAdmissionInvitationConvert;
    private final SysDivisionManager sysDivisionManager;


    /**
     * 发起准入邀请
     *
     * @param req
     * @return
     */
    public String invitationCommit(SupplierAdmissionInvitationReqDTO req) {
        admissionInvitationManager.checkRegisterUser(req);
        this.verifyCommit(req);

        // 准入邀请提交验证
        List<CategoryEngineerResultDTO> categoryList = checkInvitation(req);
        // 生成准入邀请单号
        req.setInvitationNo(admissionInvitationManager.getAndCheckInvitationNo(req));
        // 修改准入邀请数据
        SupplierAdmissionInvitationRecordPO entity = supplierAdmissionInvitationConvert.toEntity(req);
        entity.setSupplierAdmissionInvitationStatus(AdmissionInvitationStatusEnum.INVITATION_AUDIT_STATUS.getCode());
        BaseEntityUtil.setCommon(entity, req.getOperationUserId(), req.getOperationUser(), null);
        // 存储数据
        return transactionTemplate.execute(status -> {
                    // 准入邀请基础信息更新
                    UpdateWrapper<SupplierAdmissionInvitationRecordPO> basicUpdateWrapper = new UpdateWrapper<>();
                    basicUpdateWrapper.lambda().eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, req.getInvitationNo());
                    invitationInfoMapperService.saveOrUpdate(entity, basicUpdateWrapper);
                    // 删除原来的品类+附件关联数据
                    UpdateWrapper<SupplierAdmissionCategoryRecordPO> categoryUpdateWrapper = new UpdateWrapper<>();
                    categoryUpdateWrapper.lambda().eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, req.getInvitationNo());
                    categoryRecordMapperService.remove(categoryUpdateWrapper);
                    UpdateWrapper<AttachmentRecordPO> attachmentUpdateWrapper = new UpdateWrapper<>();
                    attachmentUpdateWrapper.lambda().eq(AttachmentRecordPO::getBusinessNo, req.getInvitationNo());
                    attachmentRecordMapperService.remove(attachmentUpdateWrapper);
                    // 准入品类数据
                    categoryRecordMapperService.saveBatch(admissionInvitationManager.boxSuppCategory(req));
                    // 附件数据
                    attachmentRecordMapperService.saveBatch(admissionInvitationManager.boxSuppAttachment(req));
                    // 发送MQ给工单系统生成准入邀请审批工单
                    CreateTicketMQ createTicketMQ = boxCreateTicketMQ(req, categoryList);
                    mqServiceManager.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
                    return req.getInvitationNo();
                }
        );
    }


    /**
     * SRM-1979,SRM-1981
     *首先判断同一供应商、同一品类是否有“审批通过”的准入邀请数据；
     * 如果有“审批通过”的准入邀请数据，再left join查询准入数据，获取准入单号；
     * 如果查询出了一条及以上（理论上不会出现1条以上）的数据准入单号为null，则返回给前端报错：
     * “供应商有同一品类的准入在流程中，无法重新邀请！”；"
     */
    private void verifyCommit(SupplierAdmissionInvitationReqDTO param){
        String creditCode = param.getCreditCode();
        String supplierName = param.getSupplierName();
        Integer domesticForeignRelationship = param.getDomesticForeignRelationship();

        QueryInvitation parma1 = new QueryInvitation();
        parma1.setCreditCode(creditCode);
        parma1.setSupplierName(supplierName);
        parma1.setDomesticForeignRelationship(domesticForeignRelationship);
        if(CollUtil.isNotEmpty(param.getAdmissionCategories())){
            parma1.setCategoryCodes(param.getAdmissionCategories().stream().map(v->v.getCategoryCode()).collect(Collectors.toList()));
        }
        List<AdmissionAndCategoryRespDTO> list = categoryRecordMapperService.getInvitation(parma1);
        if(CollUtil.isNotEmpty(list)){
            if(list.stream().filter(v-> CharSequenceUtil.isBlank(v.getAdmissionNo())).count() >= 1L){
                throw new BizFailException(localeMessage.getMessage("supplier.invitation.commit"));
            }
        }
    }

    private List<CategoryEngineerResultDTO> checkInvitation(SupplierAdmissionInvitationReqDTO req) {
        // 供应商状态验证
        SupplierBasicInfoPO basicInfoPO = checkBlackList(req.getSupplierName());
        // 检查参数中的条件必填
        checkParams(req, basicInfoPO);
        // 检查供应商+品类是否存在邀请中
        checkReInvitation(req);
        // 检查所选择品类是否属于同一个工程师管理
        List<String> categoryCodes = req.getAdmissionCategories().stream().map(AdmissionCategoryReqDTO::getCategoryCode).collect(Collectors.toList());
        return categoryManager.queryCategoryEngineerByCategoryCodes(categoryCodes);
    }

    /**
     * 检查准入邀请参数
     */
    private void checkParams(SupplierAdmissionInvitationReqDTO req, SupplierBasicInfoPO basicInfoPO) {
        // 检查用户输入的供应商注册联系人信息
        sysUserManager.getSupplierUserByPhoneAndEmail(req.getPhone(), req.getSupplierRegistrationContactEmail());
        // 若客户类型为指定供应商 则客户指定证明附件为必填
        if (SupplierTypeEnum.SPECIAL_SUPPLIER.getCode().equals(req.getSupplierType())
                && CollectionUtils.isEmpty(req.getCustomerDesignationProofAttachments())) {
            throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        if (AdmissionInvitationTypeEnum.BATCH_PRODUCTION_ADMISSION_TYPE.getCode().equals(req.getAdmissionType())) {
            return;
        }
        ThresholdInfoPO thresholdInfo = thresholdInfoMapperService.getOne(null);
        // 样件准入
        if (AdmissionInvitationTypeEnum.SAMPLE_ADMISSION_TYPE.getCode().equals(req.getAdmissionType())) {
            // 年度采购次数
            if (req.getAnnualPurchaseCnt() == null
                    || req.getAnnualPurchaseCnt().compareTo(0L) < 1
                    || req.getAnnualPurchaseCnt().compareTo(thresholdInfo.getAnnualPurchaseCnt()) > 0) {
                throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
            }
            // 年度采购额- 不为空 大于0 小于阈值
            compareAmtWithSpecial(req.getAnnualPurchaseAmt(), thresholdInfo.getAnnualPurchaseAmt());
            // 最大单次采购额
            compareAmtWithSpecial(req.getMastPurchaseAmt(), thresholdInfo.getMastPurchaseAmt());
            // 样件订单限额
            sampleOrderLimitCheck(req, basicInfoPO, thresholdInfo);
            return;
        }
        // 临时准入
        if (AdmissionInvitationTypeEnum.TMP_ADMISSION_TYPE.getCode().equals(req.getAdmissionType())) {
            // 交易周期 是否超过设定阈值
            Long transactionPeriod = thresholdInfo.getTransactionPeriod();
            Date currentDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            if (req.getAdmissionStartTime() == null || req.getAdmissionEndTime() == null
                    || currentDate.compareTo(req.getAdmissionStartTime()) > 0
                    || currentDate.compareTo(req.getAdmissionEndTime()) > 0
                    || req.getAdmissionStartTime().compareTo(req.getAdmissionEndTime()) >= 0) {
                throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
            }
            LocalDate startDate = req.getAdmissionStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            int i = startDate.plusMonths(transactionPeriod).compareTo(req.getAdmissionEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            if (i < 0 || StringUtils.isBlank(req.getTmpPurchaseOrg())) {
                throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
            }

        }

    }

    private void sampleOrderLimitCheck(SupplierAdmissionInvitationReqDTO req, SupplierBasicInfoPO basicInfoPO, ThresholdInfoPO thresholdInfo) {
        if (ObjectUtils.isNotEmpty(basicInfoPO) && StringUtils.isNotBlank(basicInfoPO.getSapSupplierCode())) {
            BigDecimal yearTotal = sampleOrderManager.querySupplierAndCategorySampleOrderLimit(basicInfoPO.getSapSupplierCode());
            // 年度限额
            if (BigDecimal.ZERO.compareTo(yearTotal) == 0) {
                return;
            }
            // 样件订单交易额 + 本次样件准入交易额 大于 年度限额
            BigDecimal total = yearTotal.add(req.getAnnualPurchaseAmt());
            if (total.compareTo(thresholdInfo.getAnnualPurchaseAmt()) > 0) {
                throw new BizFailException(localeMessage.getMessage("sample.order.amt.over", new String[]{total.toPlainString()}));
            }
        }
    }

    private void compareAmtWithSpecial(BigDecimal currentAmt, BigDecimal thresholdInfo) {
        if (currentAmt == null
                || currentAmt.compareTo(BigDecimal.ZERO) < 1
                || currentAmt.compareTo(thresholdInfo) > 0) {
            throw new ParamErrorException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
    }

    /**
     * 品类重复检查
     */
    private void checkReInvitation(SupplierAdmissionInvitationReqDTO req) {
        List<String> categories = req.getAdmissionCategories().stream().map(AdmissionCategoryReqDTO::getCategoryCode).collect(Collectors.toList());
        List<SupplierCategoryRelationshipPO> relationship = supplierAdmissionCategoryManager.getSupplierCategoryRelationshipPOs(req.getSupplierName());
        List<SupplierAdmissionCategoryRecordSearchPO> records = categoryRecordMapperService.searchCategoryBySupplierNameAndCategories(req.getSupplierName(), categories);
        if (CollectionUtils.isEmpty(relationship) && CollectionUtils.isEmpty(records)) {
            return;
        }
        supplierAdmissionCategoryManager.checkCategoryCanOrNotAdmission(req.getInvitationNo(), categories, req.getSupplierName(), null, req.getAdmissionType());
    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(SupplierAdmissionInvitationReqDTO req, List<CategoryEngineerResultDTO> categoryList) {
        String domain = sysUserManager.getUserDetailById(req.getOperationUserId()).getDomain();
        List<CreateTicketMQ.ApprovalProcessVar> listParams = new ArrayList<>(16);
        // 工单发起人
        CreateTicketMQ.ApprovalProcessVar one = new CreateTicketMQ.ApprovalProcessVar();
        one.setKey(CreateTicketConstants.SUBMIT_USER_ACCOUNT);
        one.setV(domain);
        // 工单审批人
        CreateTicketMQ.ApprovalProcessVar two = new CreateTicketMQ.ApprovalProcessVar();
        String auditUsers = getAuditUser(req, categoryList);
        two.setKey(CreateTicketConstants.APPROVE_USER_ACCOUNT);
        two.setV(auditUsers);
        listParams.add(one);
        listParams.add(two);
        return getCreateTicketMQ(req, listParams);
    }

    private static CreateTicketMQ getCreateTicketMQ(SupplierAdmissionInvitationReqDTO req, List<CreateTicketMQ.ApprovalProcessVar> listParams) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(req.getInvitationNo());
        mq.setTicketType(TicketTypeEnum.SUPPLIER_ADMISSION_INVITATION.getCode());
        mq.setSubmitBy(req.getOperationUserId());
        mq.setProcessVars(listParams);
        mq.setSubmitName(req.getOperationUser());
        mq.setSubmitDesc(CreateTicketConstants.TITLE_NAME
                .replace("${供应商名称}", req.getSupplierName())
                .replace("${工单类型}", TicketTypeEnum.SUPPLIER_ADMISSION_INVITATION.getDesc()));
        return mq;
    }

    private String getAuditUser(SupplierAdmissionInvitationReqDTO req, List<CategoryEngineerResultDTO> categoryList) {
        log.info("req = {}, categoryList={}", req, categoryList);
        if (YesOrNoEnum.NO.equalsCode(req.getIsCenPurchase())) {
            // 非集采审批人为 邀请发起人所属事业部采购经理
            FindPurchaseManagerRespDTO sysDivision = sysDivisionManager.getSysDivision(req.getOperationUserId());
            if (ObjectUtils.isEmpty(sysDivision) || StringUtils.isBlank(sysDivision.getDomain()) || StringUtils.isBlank(sysDivision.getSeniorPurchaseManagerUserDomain())) {
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.INVITATION_DIVISION_NOT_COMPLETE));
            }
            return sysDivision.getSeniorPurchaseManagerUserDomain();
        }
        // 过滤无用数据
        Map<String, List<CategoryEngineerResultDTO>> codeMap = categoryList.stream()
                // 过滤非品类工程师相关数据
                .filter(category -> CategoryRoleEnum.CPE.equalsCode(category.getRoleId())
                        || CategoryRoleEnum.CPE_MASTER.equalsCode(category.getRoleId())
                        || CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.equalsCode(category.getRoleId()))
                // 对过滤后的数据安品类code进行分组
                .collect(Collectors.groupingBy(CategoryEngineerResultDTO::getCategoryCode));
        HashSet<String> domains = getIds(req.getOperationUserId(), codeMap);
        return String.join(" ", domains);
    }

    private HashSet<String> getIds(Long userId, Map<String, List<CategoryEngineerResultDTO>> codeMap) {
        HashSet<String> domains = new HashSet<>(16);
        for (Map.Entry<String, List<CategoryEngineerResultDTO>> codeMapList : codeMap.entrySet()) {
            List<CategoryEngineerResultDTO> categoryEngineerResultDTOS = codeMapList.getValue();
            CategoryEngineerResultDTO master = categoryEngineerResultDTOS.stream()
                    .filter(dto -> Objects.equals(dto.getUserId(), userId)
                            && CategoryRoleEnum.CPE_MASTER.equalsCode(dto.getRoleId()))
                    .findFirst()
                    .orElse(null);
            if (ObjectUtils.isNotEmpty(master)) {
                CategoryEngineerResultDTO director = categoryEngineerResultDTOS.stream()
                        .filter(dto -> CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.equalsCode(dto.getRoleId()))
                        .findFirst()
                        .orElse(new CategoryEngineerResultDTO());
                domains.add(director.getUserDomain());
                continue;
            }

            CategoryEngineerResultDTO mater = categoryEngineerResultDTOS.stream()
                    .filter(dto -> CategoryRoleEnum.CPE_MASTER.equalsCode(dto.getRoleId()))
                    .findFirst()
                    .orElse(new CategoryEngineerResultDTO());
            domains.add(mater.getUserDomain());
        }
        if (CollectionUtils.isEmpty(domains)){
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.CATEGORY_CPE_NOT_EXIST));
        }
        return domains;
    }

    private SupplierBasicInfoPO checkBlackList(String supplierName) {
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSupplierName, supplierName)
                .one();
        if (ObjectUtils.isEmpty(basicInfoPO) || StringUtils.isBlank(basicInfoPO.getSapSupplierCode())) {
            return basicInfoPO;
        }
        if (SupplierBasicStatusEnum.LEAVED.getCode().equals(basicInfoPO.getStatus()) || SupplierBasicStatusEnum.WAIT_LEAVE.getCode().equals(basicInfoPO.getStatus())) {
            log.error(" this supplier supplierName ={} had exited ord wait exit ", supplierName);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_HAD_EXIT));
        }
        List<SupplierBlackListPO> blackList = supplierBlackListMapperService.lambdaQuery()
                .eq(SupplierBlackListPO::getSupplierCode, basicInfoPO.getSapSupplierCode())
                .list();
        if (CollectionUtils.isNotEmpty(blackList)) {
            log.error(" this supplier supplierName ={} is blackList supplier", supplierName);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.BLACK_LIST_USER));
        }
        return basicInfoPO;
    }


}
