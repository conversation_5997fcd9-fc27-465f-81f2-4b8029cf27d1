package com.weifu.srm.supplier.dto;

import com.weifu.srm.supplier.repository.enums.SupplierKeyInfoChangeTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import lombok.Data;

@Data
public class SupplierBasicStatusUpdateDTO {
    /**变化基本信息*/
    private SupplierBasicInfoPO supplierBasicInfoPO;
    /**业务单号*/
    private String businessNo;
    /**变化内容文字描述*/
    private String content;
    /**备注信息*/
    private String remark;
    /**仅在需要进行 变动信息记录时才传入该信息*/
    private SupplierKeyInfoChangeTypeEnum changeType;

}
