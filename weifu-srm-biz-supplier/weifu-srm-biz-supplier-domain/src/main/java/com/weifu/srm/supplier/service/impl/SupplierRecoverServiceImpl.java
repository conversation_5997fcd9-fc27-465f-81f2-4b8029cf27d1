package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.convert.RecoverConvert;
import com.weifu.srm.supplier.manager.AttachmentManager;
import com.weifu.srm.supplier.manager.remote.intergration.SAPManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.mq.SupplierRecoverMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierRecoverRecordMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierRecoverRecordPO;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyListReqDTO;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyReqDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordDetailRespDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordListRespDTO;
import com.weifu.srm.supplier.service.SupplierRecoverService;
import com.weifu.srm.supplier.service.biz.recover.SupplierRecoverApplyBiz;
import com.weifu.srm.supplier.service.biz.recover.SupplierRecoverApplyHandleBiz;
import com.weifu.srm.supplier.service.biz.recover.SupplierRecoverBiz;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierRecoverServiceImpl implements SupplierRecoverService {

    private final SupplierRecoverApplyBiz supplierRecoverApplyBiz;
    private final SupplierRecoverRecordMapperService supplierRecoverRecordMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierRecoverApplyHandleBiz supplierRecoverApplyHandleBiz;
    private final LocaleMessage localeMessage;
    private final RecoverConvert recoverConvert;
    private final AttachmentManager attachmentManager;
    private final DataPermissionManager dataPermissionManager;
    private final SupplierRecoverBiz supplierRecoverBiz;
    private final SAPManager sapManager;

    @Override
    public PageResponse<SupplierRecoverRecordListRespDTO> queryListPage(SupplierRecoverApplyListReqDTO req) {
        Page<SupplierRecoverApplyListReqDTO> page = new Page<>(req.getPageNum(), req.getPageSize());
        // 查询当前登录用户的数据权限权限
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(req.getUserId(), "PC_INTERNAL_PAGE_RESTORE_SUPPLIER_APPLY_LIST");
        IPage<SupplierRecoverRecordListRespDTO> pageResult = supplierRecoverRecordMapperService.queryListPage(page, req, dataPermission);
        List<SupplierRecoverRecordListRespDTO> records = pageResult.getRecords();
        if(pageResult.getTotal() == 0 ){
            return PageResponse.toResult(req.getPageNum(),req.getPageSize(),0L, null);
        }
        return PageResponse.toResult(req.getPageNum(),req.getPageSize(),pageResult.getTotal(), records);
    }

    @Override
    public String applyRecover(SupplierRecoverApplyReqDTO req) {
        return supplierRecoverApplyBiz.applyRecover(req);
    }

    @Override
    public SupplierRecoverRecordDetailRespDTO applyDetail(String applyNo) {
        SupplierRecoverRecordPO recordDetail = supplierRecoverRecordMapperService.lambdaQuery().eq(SupplierRecoverRecordPO::getApplyNo, applyNo).one();
        if (ObjectUtils.isEmpty(recordDetail)){
            log.error("this applyNo not exist apply record={}",applyNo);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getSapSupplierCode, recordDetail.getSupplierCode()).one();
        SupplierRecoverRecordDetailRespDTO respDTO = recoverConvert.toRespDTO(recordDetail);
        respDTO.setSupplierName(basicInfoPO.getSupplierName());
        respDTO.setStatus(basicInfoPO.getStatus());
        respDTO.setApplyAttachments(attachmentManager.getAttachmentsByBusinessNoAndBusType(applyNo, AttachmentBizTypeConstants.SUPPLIER_RECOVERY_APPLY_FILE));
        return respDTO;
    }

    @Override
    public void handleRecoverAudit(TicketStatusChangedMQ ticketStatusChangedMQ) {
        supplierRecoverApplyHandleBiz.handleRecoverAudit(ticketStatusChangedMQ);
    }

    @Override
    public void recover(SupplierRecoverMQ mq) {
        supplierRecoverBiz.recover(mq);
    }

    @Override
    public void handleSupplierRecoverForSap(SupplierRecoverMQ mq) {
        sapManager.syncSupplierUnFreeze(mq.getSupplierCode());
    }
}
