package com.weifu.srm.supplier.service.biz.admission;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.integration.request.cms.QueryCmsContractReqDTO;
import com.weifu.srm.integration.request.cms.SupplierItemReqDTO;
import com.weifu.srm.integration.response.cms.CmsContractInfoRespDTO;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.manager.remote.intergration.CMSManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.request.AdmissionQuestionnairePageQueryReqDTO;
import com.weifu.srm.supplier.response.AdmissionQuestionnairePageQueryRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/17 17:58
 * @Description
 * @Version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAdmissionPageQueryBiz {
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final CMSManager cmsManager;
    private final DataPermissionManager dataPermissionManager;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;


    public PageResponse<AdmissionQuestionnairePageQueryRespDTO> queryPage(AdmissionQuestionnairePageQueryReqDTO req) {
        Page<AdmissionQuestionnairePageQueryRespDTO> page = new Page<>(req.getPageNum(), req.getPageSize());
        List<DataPermissionRespDTO.DataPermissionKeyRespDTO> keys = new ArrayList<>();
        if (ObjectUtils.isEmpty(req.getSupplierId())) {
            DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(req.getOperationBy(), "PC_INTERNAL_PAGE_ADMISSION_LIST");
            if (dataPermission.isNo()) {
                return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, null);
            }
            keys = dataPermission.getKeys();
        }
        Page<AdmissionQuestionnairePageQueryRespDTO> pageQueryResults = supplierAdmissionRecordMapperService.queryPage(page, req, keys);
        if (CollectionUtils.isEmpty(pageQueryResults.getRecords())){
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, Collections.emptyList());
        }
        List<AdmissionQuestionnairePageQueryRespDTO> pageQueryRespDTOList = pageQueryResults.getRecords();
        // 补充漏缺准入信息
        supAdmissionRespDTOMessage(pageQueryRespDTOList);
        // 通过供应商Code 批量查询合同记录
        supContractMessage(pageQueryRespDTOList);
        // 设置枚举Desc
        fullEnumDesc(pageQueryRespDTOList);
        return PageResponse.toResult(req.getPageNum(),
                req.getPageSize(),
                pageQueryResults.getTotal(),
                pageQueryResults.getRecords());
    }

    private void supContractMessage(List<AdmissionQuestionnairePageQueryRespDTO> pageQueryRespDTOList){
        List<String> supplierCodeList = pageQueryRespDTOList.stream()
                .map(AdmissionQuestionnairePageQueryRespDTO::getSupplierCode)
                .filter(supplierCode -> !StringUtil.isNullOrEmpty(supplierCode))
                .collect(Collectors.toList());
        QueryCmsContractReqDTO reqSecurityDTO = new QueryCmsContractReqDTO();
        QueryCmsContractReqDTO reqHonestDTO = new QueryCmsContractReqDTO();

        List<SupplierItemReqDTO> itemReqDTOS = new ArrayList<>();
        supplierCodeList.forEach(r -> {
            SupplierItemReqDTO supplierItemReqDTO = new SupplierItemReqDTO();
            supplierItemReqDTO.setSupplierCode(r);
            itemReqDTOS.add(supplierItemReqDTO);
        });
        reqSecurityDTO.setContractCreateFor(ContractTypeEnum.SECRECY.getCreateFor());
        reqSecurityDTO.setSupplierItems(itemReqDTOS);
        reqHonestDTO.setContractCreateFor(ContractTypeEnum.HONEST.getCreateFor());
        reqHonestDTO.setSupplierItems(itemReqDTOS);
        List<CmsContractInfoRespDTO> securityResp = cmsManager.getSupplierContractInfo(reqSecurityDTO);
        List<CmsContractInfoRespDTO> honestResp = cmsManager.getSupplierContractInfo(reqHonestDTO);

        pageQueryRespDTOList.stream().filter(k -> !StringUtil.isNullOrEmpty(k.getSupplierCode())).forEach(r -> {
            r.setIntegrityContractStatus(ContractStatusEnum.PENDING.getName());
            r.setSecretContractStatus(ContractStatusEnum.PENDING.getName());
            Optional<CmsContractInfoRespDTO> honestContractOptional = honestResp.stream()
                    .filter(c -> r.getSupplierCode().equals(c.getSupplierCode()))
                    .findFirst();
            honestContractOptional.ifPresent(j -> {
                if (j.getContractItems().size() > YesOrNoEnum.NO.getCode()) {
                    r.setIntegrityContractStatus(ContractStatusEnum.SIGNED.getName());
                }
            });
            Optional<CmsContractInfoRespDTO> securityContractOptional = securityResp.stream()
                    .filter(c -> r.getSupplierCode().equals(c.getSupplierCode()))
                    .findFirst();
            securityContractOptional.ifPresent(j -> {
                if (j.getContractItems().size() > YesOrNoEnum.NO.getCode()) {
                    r.setSecretContractStatus(ContractStatusEnum.SIGNED.getName());
                }
            });
        });
    }

    private void supAdmissionRespDTOMessage(List<AdmissionQuestionnairePageQueryRespDTO> pageQueryRespDTOList){
        List<String> admissionNos = pageQueryRespDTOList.stream().map(AdmissionQuestionnairePageQueryRespDTO::getAdmissionNo).distinct().collect(Collectors.toList());
        List<SupplierAdmissionRecordPO> admissionList = supplierAdmissionRecordMapperService.lambdaQuery()
                .in(SupplierAdmissionRecordPO::getAdmissionNo, admissionNos)
                .list();
        for (AdmissionQuestionnairePageQueryRespDTO admissionRespDTO : pageQueryRespDTOList) {
            admissionList.stream().filter(admissionRecordPO -> admissionRespDTO.getAdmissionNo().equals(admissionRecordPO.getAdmissionNo())).findFirst().ifPresent(admissionRecordPO -> {
                admissionRespDTO.setSapDesc(admissionRecordPO.getSapDesc());
                admissionRespDTO.setCreateBy(admissionRecordPO.getCreateBy().toString());
                admissionRespDTO.setCreateName(admissionRecordPO.getCreateName());
                admissionRespDTO.setUpdateBy(admissionRecordPO.getUpdateBy().toString());
                admissionRespDTO.setUpdateName(admissionRecordPO.getUpdateName());
                admissionRespDTO.setCreateTime(admissionRecordPO.getCreateTime());
                admissionRespDTO.setUpdateTime(admissionRecordPO.getUpdateTime());
            });
        }
        List<String> supplierCodes = pageQueryRespDTOList.stream().map(AdmissionQuestionnairePageQueryRespDTO::getSupplierCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierCodes)){
            return;
        }
        List<SupplierBasicInfoPO> list = supplierBasicInfoMapperService.lambdaQuery()
                .in(SupplierBasicInfoPO::getSapSupplierCode, supplierCodes)
                .list();
        Map<String, Integer> supplierCodeMapSapSynResult = list.stream().collect(Collectors.toMap(SupplierBasicInfoPO::getSapSupplierCode, SupplierBasicInfoPO::getSynSapResult, (dto1, dto2) -> dto1));
        for (AdmissionQuestionnairePageQueryRespDTO admissionPo : pageQueryRespDTOList) {
            if (StringUtils.isNotBlank(admissionPo.getSupplierCode())){
                admissionPo.setSapStatus(supplierCodeMapSapSynResult.get(admissionPo.getSupplierCode()));
            }
        }
    }

    private void fullEnumDesc(List<AdmissionQuestionnairePageQueryRespDTO> pageQueryRespDTOList){
        pageQueryRespDTOList.forEach(r -> {
            Optional<SupplierTypeEnum> supplierTypeEnum = Optional.ofNullable(SupplierTypeEnum.getByCode(r.getSupplierType()));
            supplierTypeEnum.ifPresent(l -> r.setSupplierTypeDesc(l.getName()));
            Optional<SupplierCategoryStatusEnum> statusEnum = Optional.ofNullable(SupplierCategoryStatusEnum.getByCode(r.getSupplierStatus()));
            statusEnum.ifPresent(j -> r.setSupplierStatusDesc(j.getName()));
            Optional<AdmissionInvitationTypeEnum> admissionTypeEnum = Optional.ofNullable(AdmissionInvitationTypeEnum.getByCode(r.getAdmissionType()));
            admissionTypeEnum.ifPresent(p -> {
                r.setAdmissionTypeDesc(p.getName());
                if (!AdmissionInvitationTypeEnum.SAMPLE_ADMISSION_TYPE.equals(p)
                        && !AdmissionInvitationTypeEnum.TMP_ADMISSION_TYPE.equals(p)) {
                    r.setQuestionnaireNo(r.getAdmissionNo());
                }
            });
            Optional<AdmissionStatusEnum> admissionStatusEnum = Optional.ofNullable(AdmissionStatusEnum.getByCode(r.getAdmissionStatus()));
            admissionStatusEnum.ifPresent(k -> r.setAdmissionStatusDesc(k.getName()));

        });
    }
}
