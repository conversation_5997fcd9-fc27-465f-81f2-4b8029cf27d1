package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.remote.intergration.BPMManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.QualificationChangeUpdateAssociationReqDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.response.SysUserWithPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeUpdateAssociationBiz {

    private final LocaleMessage localeMessage;
    private final KafkaTemplate<String,String> kafkaTemplate;
    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeAssociationItemMapperService qualificationChangeAssociationItemMapperService;
    private final QualificationChangeAssociationDetailItemMapperService qualificationChangeAssociationDetailItemMapperService;
    private final SupplierAssociationRelationMapperService supplierAssociationRelationMapperService;
    private final BPMManager bpmManager;
    private final MQServiceManager mqService;
    private final SysUserApi sysUserApi;
    private final IdService idService;

    public void updateAssociation(QualificationChangeUpdateAssociationReqDTO reqDTO, String submitSource) {
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        // 校验是否有审批中的数据
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getChangeType, QualificationChangeTypeEnum.ASSOCIATION_UPDATE.getCode())
                .eq(QualificationChangeApplyPO::getSupplierCode, basicInfoPO.getSapSupplierCode())
                .eq(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.SUBMIT.getCode())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNotEmpty(changeApplyPO)) {
            throw new BizFailException(QualificationChangeCheckoutFinancialEnum.ASSOCIATION_ALREADY_EXIST.getContent());
        }
        // 判断是否需要走审批流
        if (basicInfoPO.getIsWeifuRelatedParty().equals(reqDTO.getIsWeifuRelatedParty())
                && basicInfoPO.getDirectRelatedPartyTypeDesc().equals(reqDTO.getDirectRelatedPartyTypeDesc())) {
            // 先修改basic表的数据，然后根据修改
            basicInfoMapperService.lambdaUpdate()
                    .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                    .set(SupplierBasicInfoPO::getIsRelatedToWeifuSupplier, reqDTO.getIsRelatedToWeifuSupplier())
                    .set(SupplierBasicInfoPO::getUpdateBy, reqDTO.getOperationUserId())
                    .set(SupplierBasicInfoPO::getUpdateName, reqDTO.getOperationUser())
                    .set(SupplierBasicInfoPO::getUpdateTime, new Date())
                    .update();
            // 将原来的数据都删除掉
            supplierAssociationRelationMapperService.lambdaUpdate()
                    .eq(SupplierAssociationRelationPO::getSapSupplierCode, basicInfoPO.getSapSupplierCode())
                    .set(SupplierAssociationRelationPO::getIsDelete, YesOrNoEnum.YES.getCode())
                    .update();
            // 然后再新增数据
            if (reqDTO.getIsRelatedToWeifuSupplier() == 1) {
                List<SupplierAssociationRelationPO> supplierAssociationRelationPOS = new ArrayList<>();
                reqDTO.getQualificationChangeUpdateAssociationItemReqDTOS().forEach(items -> {
                    SupplierAssociationRelationPO associationItempPO = qualificationChangeConvert.toAssociationItempPO(items);
                    associationItempPO.setSupplierBasicMsgId(associationItempPO.getSupplierBasicMsgId() == null ? basicInfoPO.getId() : associationItempPO.getSupplierBasicMsgId());
                    associationItempPO.setSapSupplierCode(basicInfoPO.getSapSupplierCode());
                    BaseEntityUtil.setCommon(associationItempPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
                    associationItempPO.setIsDelete(YesOrNoEnum.NO.getCode());
                    supplierAssociationRelationPOS.add(associationItempPO);
                });
                supplierAssociationRelationMapperService.saveBatch(supplierAssociationRelationPOS);
            }
        } else {
            String qualificationChangeNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.QUALIFICATION_CHANGE.getBizTypeCodee(),idService);
            // 需要存入资质变更申请表
            QualificationChangeApplyPO qualificationChangeApplyPO = new QualificationChangeApplyPO();
            qualificationChangeApplyPO.setQualificationChangeNo(qualificationChangeNo);
            qualificationChangeApplyPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
            qualificationChangeApplyPO.setSupplierName(basicInfoPO.getSupplierName());
            qualificationChangeApplyPO.setChangeType(QualificationChangeTypeEnum.ASSOCIATION_UPDATE.getCode());
            qualificationChangeApplyPO.setChangeStatus(QualificationChangeStatusEnum.SUBMIT.getCode());
            qualificationChangeApplyPO.setSubmitSource(submitSource);
            qualificationChangeApplyPO.setIsDelete(YesOrNoEnum.NO.getCode());
            BaseEntityUtil.setCommon(qualificationChangeApplyPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
            QualificationChangeAssociationItemPO qualificationChangeAssociationItemPO = new QualificationChangeAssociationItemPO();
            qualificationChangeAssociationItemPO.setQualificationChangeNo(qualificationChangeNo);
            qualificationChangeAssociationItemPO.setIsWeifuRelatedParty(reqDTO.getIsWeifuRelatedParty());
            qualificationChangeAssociationItemPO.setDirectRelatedPartyTypeDesc(reqDTO.getDirectRelatedPartyTypeDesc());
            qualificationChangeAssociationItemPO.setIsRelatedToWeifuSupplier(reqDTO.getIsRelatedToWeifuSupplier());
            qualificationChangeAssociationItemPO.setIsDelete(YesOrNoEnum.NO.getCode());
            BaseEntityUtil.setCommon(qualificationChangeAssociationItemPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
            // 获取董办结果存入数据库
            boolean b = bpmManager.checkSupplierRelationship(basicInfoPO.getCreditCode(), basicInfoPO.getSupplierName());
            qualificationChangeAssociationItemPO.setBoardDirectosResult(b?1:0);
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                // 先修改basic表的数据，然后根据修改
                basicInfoMapperService.lambdaUpdate()
                        .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                        .set(SupplierBasicInfoPO::getIsRelatedToWeifuSupplier, reqDTO.getIsRelatedToWeifuSupplier())
                        .set(SupplierBasicInfoPO::getUpdateBy, reqDTO.getOperationUserId())
                        .set(SupplierBasicInfoPO::getUpdateName, reqDTO.getOperationUser())
                        .set(SupplierBasicInfoPO::getUpdateTime, new Date())
                        .update();
                // 将原来的数据都删除掉
                supplierAssociationRelationMapperService.lambdaUpdate()
                        .eq(SupplierAssociationRelationPO::getSapSupplierCode, basicInfoPO.getSapSupplierCode())
                        .set(SupplierAssociationRelationPO::getIsDelete, YesOrNoEnum.YES.getCode())
                        .update();
                // 然后再新增数据
                if (reqDTO.getIsRelatedToWeifuSupplier() == 1) {
                    List<SupplierAssociationRelationPO> supplierAssociationRelationPOS = new ArrayList<>();
                    reqDTO.getQualificationChangeUpdateAssociationItemReqDTOS().forEach(items -> {
                        SupplierAssociationRelationPO associationItempPO = qualificationChangeConvert.toAssociationItempPO(items);
                        associationItempPO.setSapSupplierCode(basicInfoPO.getSapSupplierCode());
                        associationItempPO.setSupplierBasicMsgId(associationItempPO.getSupplierBasicMsgId() == null ? basicInfoPO.getId() : associationItempPO.getSupplierBasicMsgId());
                        BaseEntityUtil.setCommon(associationItempPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
                        associationItempPO.setIsDelete(YesOrNoEnum.NO.getCode());
                        supplierAssociationRelationPOS.add(associationItempPO);
                    });
                    supplierAssociationRelationMapperService.saveBatch(supplierAssociationRelationPOS);
                }
                // 存入数据到资质变更
                qualificationChangeAssociationItemMapperService.save(qualificationChangeAssociationItemPO);
                if (qualificationChangeAssociationItemPO.getIsRelatedToWeifuSupplier() == 1) {
                    List<QualificationChangeAssociationDetailItemPO> qualificationChangeAssociationDetailItemPOS = new ArrayList<>();
                    reqDTO.getQualificationChangeUpdateAssociationItemReqDTOS().forEach(item -> {
                        QualificationChangeAssociationDetailItemPO updateAssociationItemPO = qualificationChangeConvert.toUpdateAssociationItemPO(item);
                        updateAssociationItemPO.setQualificationChangeNo(qualificationChangeNo);
                        updateAssociationItemPO.setIsDelete(YesOrNoEnum.NO.getCode());
                        BaseEntityUtil.setCommon(updateAssociationItemPO, reqDTO.getOperationUserId(), reqDTO.getOperationUser(), new Date());
                        qualificationChangeAssociationDetailItemPOS.add(updateAssociationItemPO);
                    });
                    qualificationChangeAssociationDetailItemMapperService.saveBatch(qualificationChangeAssociationDetailItemPOS);
                }
                // 发送MQ给工单系统生成准入邀请审批工单
                CreateTicketMQ createTicketMQ = boxCreateTicketMQ(reqDTO, qualificationChangeNo);
                log.info("start send MQ to ticket service create ticket ={}",createTicketMQ);
                kafkaTemplate.send(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
                log.info("end send MQ to ticket service create ticket ={}",createTicketMQ);
                // 存入数据到资质变更申请表中
                qualificationChangeApplyMapperService.save(qualificationChangeApplyPO);
            });
        }
    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(QualificationChangeUpdateAssociationReqDTO reqDTO, String qualificationChangeNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(qualificationChangeNo);
        mq.setTicketType(TicketTypeEnum.RELATED_PARTY_INFO_ADJUSTMENT.getCode());
        mq.setSupplierId(reqDTO.getSupplierId());
        mq.setSubmitBy(reqDTO.getOperationUserId());
        mq.setSubmitName(reqDTO.getOperationUser());
        return mq;
    }

}
