package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionCategoryManager;
import com.weifu.srm.supplier.manager.SupplierAdmissionTriggerManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionInvitationResendBiz {

    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryManager supplierAdmissionCategoryManager;
    private final SupplierAdmissionCategoryRecordMapperService admissionCategoryRecordMapperService;
    private final SupplierAdmissionTriggerManager admissionTriggerManager;
    private final MQServiceManager mqServiceManager;
    private final LocaleMessage localeMessage;

    @Transactional
    public void resendAdmissionInvitation(String invitationNo) {
        SupplierAdmissionInvitationRecordPO invitationRecordPO = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo)
                .one();
        if (ObjectUtils.isEmpty(invitationRecordPO) || !AdmissionInvitationStatusEnum.REFUSE_STATUS.getCode().equals(invitationRecordPO.getSupplierAdmissionInvitationStatus())) {
            log.error("supplier admission invitation not exist or status not support resend={}", invitationRecordPO);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_STATUS_NOT_SUPPORT));
        }
        List<SupplierAdmissionCategoryRecordPO> supplierAdmissionCategoryRecordPOS = admissionCategoryRecordMapperService.listByInvitationNo(invitationNo);
        List<String> categoryCodes = supplierAdmissionCategoryRecordPOS.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList());
        // 检查品类邀请品类是否可以重发
        supplierAdmissionCategoryManager.checkCategoryCanOrNotAdmission(invitationNo,categoryCodes, invitationRecordPO.getSupplierName(), invitationRecordPO.getSupplierBasicMsgId(), invitationRecordPO.getAdmissionType());

        // 修改状态，
        invitationInfoMapperService.lambdaUpdate()
                .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo)
                .set(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.INVITATION_STATUS.getCode())
                .update();
        // 触发准入
        admissionTriggerManager.completedAdmissionInvitation(invitationNo,null);
    }
}
