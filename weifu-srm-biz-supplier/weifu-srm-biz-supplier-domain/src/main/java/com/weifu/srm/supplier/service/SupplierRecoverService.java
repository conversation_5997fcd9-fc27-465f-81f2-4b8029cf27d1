package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.mq.SupplierRecoverMQ;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyListReqDTO;
import com.weifu.srm.supplier.request.recover.SupplierRecoverApplyReqDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordDetailRespDTO;
import com.weifu.srm.supplier.response.recover.SupplierRecoverRecordListRespDTO;

public interface SupplierRecoverService {

    PageResponse<SupplierRecoverRecordListRespDTO> queryListPage(SupplierRecoverApplyListReqDTO req);

    String applyRecover(SupplierRecoverApplyReqDTO req);

    SupplierRecoverRecordDetailRespDTO applyDetail(String applyNo);

    void handleRecoverAudit(TicketStatusChangedMQ ticketStatusChangedMQ);

    void recover(SupplierRecoverMQ mq);

    void handleSupplierRecoverForSap(SupplierRecoverMQ mq);
}
