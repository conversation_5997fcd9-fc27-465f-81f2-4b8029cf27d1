package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.ErrorCode;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.masterdata.api.DictDataApi;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.supplier.constants.ServiceConstants;
import com.weifu.srm.supplier.convert.QuestionnaireTemplateConvert;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldSampleMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateMapperService;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldPO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldSamplePO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplatePO;
import com.weifu.srm.supplier.request.QuestionnaireTemplateReqDTO;
import com.weifu.srm.supplier.request.QuestionnaireTemplateSaveReqDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateDetailRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateFieldsRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateRespDTO;
import com.weifu.srm.supplier.service.QuestionnaireTemplateService;
import com.weifu.srm.supplier.service.biz.QuestionnaireTemplateBeDefaultBiz;
import com.weifu.srm.supplier.service.biz.QuestionnaireTemplateSaveBiz;
import com.weifu.srm.supplier.service.biz.QuestionnaireTemplateSearchByCodeBiz;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class QuestionnaireTemplateServiceImpl implements QuestionnaireTemplateService {

    private final QuestionnaireTemplateConvert questionnaireTemplateConvert;
    private final QuestionnaireTemplateMapperService questionnaireTemplateMapperService;
    private final QuestionnaireTemplateFieldMapperService questionnaireTemplateFieldMapperService;
    private final QuestionnaireTemplateFieldSampleMapperService queryTemplateFieldSampleService;
    private final QuestionnaireTemplateSaveBiz questionnaireTemplateSaveBiz;
    private final QuestionnaireTemplateBeDefaultBiz questionnaireTemplateBeDefaultBiz;
    private final QuestionnaireTemplateSearchByCodeBiz questionnaireTemplateSearchByCodeBiz;
    private final DictDataApi dictDataApi;
    @Override
    public PageResponse<QuestionnaireTemplateRespDTO> queryList(QuestionnaireTemplateReqDTO reqDTO) {
        Page<QuestionnaireTemplatePO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        QuestionnaireTemplatePO questionnaireTemplatePO = questionnaireTemplateConvert.toPo(reqDTO);
        Page<QuestionnaireTemplatePO> questionnaireTemplateS =
                questionnaireTemplateMapperService.queryList(page, questionnaireTemplatePO);
        List<QuestionnaireTemplateRespDTO> respDTOList =
                questionnaireTemplateConvert.toResult(questionnaireTemplateS.getRecords());
        ApiResponse<Map<String, List<DictDataShowResDTO>>> dictListResp = dictDataApi.getDictDataList(ServiceConstants.APPLICABLE_TYPE, LocaleContextHolder.getLocale().toString());

        if (Boolean.TRUE.equals(dictListResp.getSucc())){
            Map<String, List<DictDataShowResDTO>> dicDataMap = dictListResp.getData();
            List<DictDataShowResDTO> dictList = dicDataMap.get(ServiceConstants.APPLICABLE_TYPE);
            respDTOList.forEach(r-> dictList.stream()
                .filter(a -> Objects.equals(a.getDataValue(), r.getSupplierType()))
                .findFirst().ifPresent(k->r.setSupplierTypeDesc(k.getDataShowName())));
        }
        return PageResponse.toResult(reqDTO.getPageNum(),
                reqDTO.getPageSize(),
                questionnaireTemplateS.getTotal(),
                respDTOList);
    }
    @Override
    public List<QuestionnaireTemplateFieldsRespDTO> tempLateSampleFields() {
        List<QuestionnaireTemplateFieldSamplePO> poList = queryTemplateFieldSampleService.queryTemplateFieldSampList();
        return questionnaireTemplateConvert.toFiledSampleResult(poList);
    }

    @Override
    public String saveTemplate(QuestionnaireTemplateSaveReqDTO saveReqDTO) {
        return questionnaireTemplateSaveBiz.saveTemplate(saveReqDTO);
    }

    @Override
    public QuestionnaireTemplateDetailRespDTO queryTemplateFieldsByCode(String templateCode) {
         return questionnaireTemplateSearchByCodeBiz.queryTemplateFieldsByCode(templateCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteTemplateByCode(List<String> templateCode) {
        LambdaUpdateWrapper<QuestionnaireTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(QuestionnaireTemplatePO::getTemplateCode, templateCode);
        updateWrapper.set(QuestionnaireTemplatePO::getIsDelete, YesOrNoEnum.YES.getCode());
        LambdaUpdateWrapper<QuestionnaireTemplateFieldPO> updateFieldWrapper = new LambdaUpdateWrapper<>();
        updateFieldWrapper.in(QuestionnaireTemplateFieldPO::getTemplateCode, templateCode);
        updateFieldWrapper.set(QuestionnaireTemplateFieldPO::getIsDelete, YesOrNoEnum.YES.getCode());
        questionnaireTemplateMapperService.updateTemplate(updateWrapper);
        questionnaireTemplateFieldMapperService.updateTemplateField(updateFieldWrapper);
        return Boolean.TRUE;
    }

    @Override
    public String enableTemplate(String templateCode, Integer isEnable) {
        LambdaUpdateWrapper<QuestionnaireTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(QuestionnaireTemplatePO::getTemplateCode, templateCode);
        updateWrapper.eq(QuestionnaireTemplatePO::getIsDelete, YesOrNoEnum.NO.getCode());
        updateWrapper.set(QuestionnaireTemplatePO::getEnable, isEnable);
        Integer count = questionnaireTemplateMapperService.updateTemplate(updateWrapper);
        if (count.equals(YesOrNoEnum.NO.getCode())) {
            throw new  BizFailException(ErrorCode.C_DATA_NOT_EXIST.getMsg());
        }
        return templateCode;
    }

    @Override
    public String makeDefaultTemplate(String templateCode, Integer isDefault) {
        return questionnaireTemplateBeDefaultBiz.makeDefaultTemplate(templateCode, isDefault);
    }
}
