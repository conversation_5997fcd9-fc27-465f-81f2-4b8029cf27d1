package com.weifu.srm.supplier.service.biz.qualificationchange;

import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.integration.api.CCMSApi;
import com.weifu.srm.integration.api.SAPApi;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.QualificationChangeManager;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeBasicItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeFinancialItemMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.QualificationChangeTypeEnum;
import com.weifu.srm.supplier.repository.po.QualificationChangeApplyPO;
import com.weifu.srm.supplier.repository.po.QualificationChangeBasicItemPO;
import com.weifu.srm.supplier.repository.po.QualificationChangeFinancialItemPO;
import com.weifu.srm.supplier.request.QualificationChangeQueryUpdateReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeExportSapBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final LocaleMessage localeMessage;
    private final CCMSApi ccmsApi;
    private final SAPApi sapApi;
    private final QualificationChangeBasicItemMapperService qualificationChangeBasicItemMapperService;
    private final QualificationChangeFinancialItemMapperService qualificationChangeFinancialItemMapperService;
    private final QualificationChangeManager qualificationChangeManager;

    public void exportSap(QualificationChangeQueryUpdateReqDTO reqDTO) {
        if (reqDTO.getQualificationChangeNo() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }

        // 查询申请单列表数据
        QualificationChangeApplyPO changeApply = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 基本
        if (changeApply.getChangeType().equals(QualificationChangeTypeEnum.BASIC_UPDATE.getCode())) {
            // 查询供应商基本信息修改详情
            QualificationChangeBasicItemPO changeBasicItem = qualificationChangeBasicItemMapperService.lambdaQuery()
                    .eq(QualificationChangeBasicItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                    .eq(QualificationChangeBasicItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();

            // 同步至IAM、SAP、CCMS
            qualificationChangeManager.basicInfoToIamAndSapAndCcms(changeBasicItem, changeApply, reqDTO.getOperateBy(), reqDTO.getOperateName());
        }
        // 财务
        if (changeApply.getChangeType().equals(QualificationChangeTypeEnum.FINANCIAL_UPDATE.getCode())) {
            // 查询供应商基本信息修改详情
            List<QualificationChangeFinancialItemPO> changeFinancialItems = qualificationChangeFinancialItemMapperService.lambdaQuery()
                    .eq(QualificationChangeFinancialItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                    .eq(QualificationChangeFinancialItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();

            // 同步至SAP、CCMS
            qualificationChangeManager.financialInfoToSapAndCcms(changeFinancialItems, changeApply, reqDTO.getOperateBy(), reqDTO.getOperateName());
        }
    }

}
