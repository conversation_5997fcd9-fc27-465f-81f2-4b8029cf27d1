package com.weifu.srm.supplier.service.biz.admission;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.UserSupplierRelationshipInfoMapperService;
import com.weifu.srm.supplier.repository.constants.SupplierUserRoleConstants;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO;
import com.weifu.srm.supplier.response.TmpSupplierContactUserMessageRespDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class AdmissionInvitationContactUserQueryBiz {

    private final UserSupplierRelationshipInfoMapperService userSupplierRelationshipInfoMapperService;
    private final SupplierAdmissionInvitationInfoMapperService admissionInvitationInfoMapperService;
    private final SysUserManager sysUserManager;

    public TmpSupplierContactUserMessageRespDTO querySupplierContactUserBySupplierName(String supplierName) {
        // 查询标准联系人
        UserSupplierRelationshipInfoPO one = userSupplierRelationshipInfoMapperService.lambdaQuery()
                .eq(UserSupplierRelationshipInfoPO::getSupplierName, supplierName)
                .eq(UserSupplierRelationshipInfoPO::getRoleId, SupplierUserRoleConstants.SUPPLIER_REGISTER_CONTACTS)
                .one();
        TmpSupplierContactUserMessageRespDTO tmpUserInfo = new TmpSupplierContactUserMessageRespDTO();
        if (ObjectUtils.isNotEmpty(one)) {
            BaseSysUserRespDTO userInfo = sysUserManager.getUserDetailById(one.getSysUserId());
            tmpUserInfo.setPhone(userInfo.getPhone());
            tmpUserInfo.setSupplierRegistrationContactName(userInfo.getRealName());
            tmpUserInfo.setSupplierRegistrationContactEmail(userInfo.getEmail());
            return tmpUserInfo;
        }


        // 不存在标准联系人查询 是否存在未完成的邀请记录，从中获取注册联系人信息
        // bug修复 查询联系人 过滤邀请状态在允许范围内的联系人数据
        List<String> supplierAdmissionInvitationStatus  = Arrays.asList(AdmissionInvitationStatusEnum.INVITATION_AUDIT_STATUS.getCode(),
                AdmissionInvitationStatusEnum.INVITATION_STATUS.getCode(),AdmissionInvitationStatusEnum.AGREE_STATUS.getCode(),
                AdmissionInvitationStatusEnum.APPROVED_STATUS.getCode());
        List<SupplierAdmissionInvitationRecordPO> invitations = admissionInvitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierName, supplierName)
                .in(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus,supplierAdmissionInvitationStatus)
                .orderByDesc(SupplierAdmissionInvitationRecordPO::getCreateTime)
                .list();
        if (CollectionUtils.isEmpty(invitations)) {
            return tmpUserInfo;
        }
        SupplierAdmissionInvitationRecordPO invitationRecordPO = invitations.get(0);
        tmpUserInfo.setPhone(invitationRecordPO.getPhone());
        tmpUserInfo.setSupplierRegistrationContactName(invitationRecordPO.getSupplierRegistrationContactName());
        tmpUserInfo.setSupplierRegistrationContactEmail(invitationRecordPO.getSupplierRegistrationContactEmail());
        return tmpUserInfo;
    }

}
