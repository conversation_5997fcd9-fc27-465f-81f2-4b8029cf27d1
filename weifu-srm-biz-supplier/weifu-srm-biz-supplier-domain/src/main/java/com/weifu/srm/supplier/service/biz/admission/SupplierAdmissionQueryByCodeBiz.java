package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.masterdata.response.CategoryLevelRespDTO;
import com.weifu.srm.supplier.manager.SupplierAdmissionQueryByCodeManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireQueryByCodeRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/18 12:48
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class SupplierAdmissionQueryByCodeBiz {

    private final SupplierAdmissionQueryByCodeManager supplierAdmissionQueryByCodeManager;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final CategoryManager categoryManager;


    public AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByCode(String admissionNo) {
        SupplierAdmissionRecordPO admissionRecordPO = supplierAdmissionRecordMapperService.findByAdmissionNo(admissionNo);
        AdmissionQuestionnaireQueryByCodeRespDTO respDTO = supplierAdmissionQueryByCodeManager.queryAdmissionByCode(admissionRecordPO);
        List<SupplierAdmissionCategoryRecordPO> categories = categoryRecordMapperService.listByInvitationNo(admissionRecordPO.getInvitationNo());
        List<CategoryLevelRespDTO> categoryLevelRespDTOS = categoryManager.parentCategory(categories.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList()));
        respDTO.setAdmissionCategoryNames(categoryLevelRespDTOS.stream().map(s ->  s.getLevelOneName().concat("-").concat(s.getLevelTwoName()).concat("-").concat(s.getLevelThirdName())).collect(Collectors.toList()));
        return respDTO;
    }
}
