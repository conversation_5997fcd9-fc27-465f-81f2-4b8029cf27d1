package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.communication.enums.TodoStatusEnum;
import com.weifu.srm.communication.mq.TodoStatusChangedMQ;
import com.weifu.srm.integration.api.CMSApi;
import com.weifu.srm.integration.request.cms.DealDraftCMSReqDTO;
import com.weifu.srm.integration.request.cms.QueryCmsContractReqDTO;
import com.weifu.srm.integration.request.cms.SupplierItemReqDTO;
import com.weifu.srm.integration.response.cms.CmsContractInfoRespDTO;
import com.weifu.srm.integration.response.cms.DealDraftCMSRespDTO;
import com.weifu.srm.supplier.manager.remote.intergration.CMSManager;
import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.enums.ContractTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class DealDraftCMSBiz {

    private final SupplierAdmissionRecordMapperService supplierAdmissionMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SysUserManager sysUserManager;
    private final CMSApi cmsApi;
    private final CMSManager cmsManager;

    public void handle(TodoStatusChangedMQ mq) {
        if (!TodoStatusEnum.TO_DO.equalsCode(mq.getStatus())) {
            // 仅处理合同待办创建
          return;
        }
        // 查询准入记录
        SupplierAdmissionRecordPO admissionPO = supplierAdmissionMapperService.findByAdmissionNo(mq.getBusinessNo());
        if (ObjectUtil.isEmpty(admissionPO)) {
            return;
        }
        // 查询供应商基础数据
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.getById(admissionPO.getSupplierBasicMsgId());
        if (StringUtils.isBlank(basicInfoPO.getSapSupplierCode())) {
            log.info("this supplier now can not generate contract ={}", basicInfoPO);
            return;
        }
        BaseSysUserRespDTO invitationUser = sysUserManager.getUserDetailById(Long.parseLong(admissionPO.getAdmissionInvitationBy()));
        SupplierItemReqDTO supplierItemReqDTO = new SupplierItemReqDTO();
        supplierItemReqDTO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        // 廉洁协议
        if (ContractTypeEnum.HONEST.getCreateFor().equals(mq.getExt())){
            QueryCmsContractReqDTO reqHonestDTO = new QueryCmsContractReqDTO();
            reqHonestDTO.setContractCreateFor(ContractTypeEnum.HONEST.getCreateFor());
            reqHonestDTO.setSupplierItems(List.of(supplierItemReqDTO));
            List<CmsContractInfoRespDTO> honestResp = cmsManager.getSupplierContractInfo(reqHonestDTO);
            boolean existHonest = CollectionUtils.isNotEmpty(honestResp) && CollectionUtils.isNotEmpty(honestResp.get(0).getContractItems());
            if (existHonest ) {
                log.info("存在有效的廉洁协议");
                return;
            }
            DealDraftCMSReqDTO dealDraftCMSReqDTO = getDealDraftCMSReqDTO(basicInfoPO, mq.getTodoNo(), ContractTypeEnum.HONEST.getCreateFor(), invitationUser.getDomain());
            ApiResponse<DealDraftCMSRespDTO> dealDraftCMSRespDTOApiResponse = cmsApi.dealDraft(dealDraftCMSReqDTO);
            log.info("调用CMS系统起草廉洁协议结果 ==================> {}", dealDraftCMSRespDTOApiResponse.getData());
        }
        // 安全协议
        if (ContractTypeEnum.SECRECY.getCreateFor().equals(mq.getExt())){
            QueryCmsContractReqDTO reqSecurityDTO = new QueryCmsContractReqDTO();
            reqSecurityDTO.setContractCreateFor(ContractTypeEnum.SECRECY.getCreateFor());
            reqSecurityDTO.setSupplierItems(List.of(supplierItemReqDTO));
            List<CmsContractInfoRespDTO> securityResp = cmsManager.getSupplierContractInfo(reqSecurityDTO);
            boolean existSecrecy = CollectionUtils.isNotEmpty(securityResp) && CollectionUtils.isNotEmpty(securityResp.get(0).getContractItems());
            if (existSecrecy) {
                log.info("存在有效的保密协议");
                return;
            }
            DealDraftCMSReqDTO dealDraftCMSReqDTO = getDealDraftCMSReqDTO(basicInfoPO, mq.getTodoNo(), ContractTypeEnum.SECRECY.getCreateFor(), invitationUser.getDomain());
            ApiResponse<DealDraftCMSRespDTO> dealDraftCMSRespDTOApiResponse = cmsApi.dealDraft(dealDraftCMSReqDTO);
            log.info("调用CMS系统起草保密协议结果 ==================> {}", dealDraftCMSRespDTOApiResponse.getData());

        }
    }

    private static DealDraftCMSReqDTO getDealDraftCMSReqDTO(SupplierBasicInfoPO basicInfoPO, String taskNo, String contractCreateFor, String todoUserDomain) {
        DealDraftCMSReqDTO dealDraftCMSReqDTO = new DealDraftCMSReqDTO();
        dealDraftCMSReqDTO.setTaskNo(taskNo);
        dealDraftCMSReqDTO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        dealDraftCMSReqDTO.setSupplierName(basicInfoPO.getSupplierName());
        // 默认值：无锡威孚高科技集团股份有限公司
        dealDraftCMSReqDTO.setCustomerName("无锡威孚高科技集团股份有限公司");
        dealDraftCMSReqDTO.setCustomerCode("WF00");
        dealDraftCMSReqDTO.setCreatorAccount(todoUserDomain);
        // 廉洁协议：HONEST; 保密协议：SECRECY
        dealDraftCMSReqDTO.setContractCreateFor(contractCreateFor);
        return dealDraftCMSReqDTO;
    }
}
