package com.weifu.srm.supplier.service.impl;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.convert.GreyListConvert;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListMapperService;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyCancelReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyListReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListApplyReqDTO;
import com.weifu.srm.supplier.request.grey.SupplierGreyListSearchReqDTO;
import com.weifu.srm.supplier.response.grey.*;
import com.weifu.srm.supplier.service.SupplierGreyListService;
import com.weifu.srm.supplier.service.biz.grey.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierGreyListServiceImpl implements SupplierGreyListService {

    private final SupplierGreyListApplyBiz supplierGreyListApplyBiz;
    private final GreyListDivisionItemBiz greyListDivisionItemBiz;
    private final GreyListCategoryItemBiz greyListCategoryItemBiz;
    private final SupplierGreyListSearchBiz supplierGreyListSearchBiz;
    private final SupplierGreyListCancelCollectionBiz greyListCancelCollectionBiz;
    private final SupplierGreyListCancelBiz supplierGreyListCancelBiz;
    private final GreyListDetailBiz greyListDetailBiz;
    private final GreyListApplyHandleBiz greyListApplyHandleBiz;
    private final ScheduleUpdateGreyListValidBiz scheduleUpdateGreyListValidBiz;
    private final GreyListApplyListExportBiz greyListApplyListExportBiz;
    private final GreyListApplyCancelHandleBiz greyListApplyCancelHandleBiz;
    private final SupplierGreyListMapperService greyListMapperService;
    private final GreyListConvert greyListConvert;

    @Override
    public PageResponse<SupplierGreyListApplyListRespDTO> queryList(SupplierGreyListApplyListReqDTO req) {
        return supplierGreyListSearchBiz.queryList(req);
    }

    @Override
    public String apply(SupplierGreyListApplyReqDTO req) {
       return supplierGreyListApplyBiz.apply(req);
    }

    @Override
    public String applyCancel(SupplierGreyListApplyCancelReqDTO req) {
       return supplierGreyListCancelBiz.applyCancel(req);
    }

    @Override
    public SupplierGreyListApplyDetailRespDTO queryDetail(String applyNo) {
        return greyListDetailBiz.searchGreyListApplyDetailByApplyNo(applyNo);
    }

    @Override
    public SupplierGreyListItemCategoryDivisionRespDTO queryGreyListItemCategoryBySupplierCode(String supplierCode) {
        return greyListCategoryItemBiz.queryGreyListItemCategoryBySupplierCode(supplierCode);
    }

    @Override
    public SupplierGreyListItemDivisionCategoryRespDTO queryGreyListItemDivisionBySupplierCode(String supplierCode) {
        return greyListDivisionItemBiz.queryGreyListItemDivisionBySupplierCode(supplierCode);
    }

    @Override
    public List<SupplierGreyListItemRespDTO> cancelGreyListCollection(List<Integer> limitIds) {
        return greyListCancelCollectionBiz.cancelGreyListCollection(limitIds);
    }

    @Override
    public void handleGreyListApply(TicketStatusChangedMQ ticketInfo) {
        greyListApplyHandleBiz.handleGreyListApply(ticketInfo);
    }

    @Override
    public void handleGreyListApplyCancel(TicketStatusChangedMQ ticketInfo) {
        greyListApplyCancelHandleBiz.handleGreyListApplyCancel(ticketInfo);
    }

    @Override
    public void scheduleCheckGreyList() {
        scheduleUpdateGreyListValidBiz.processWaitValidData();
    }

    @Override
    public List<SupplierGreyListApplyListRespDTO> exportGreyListApplyList(SupplierGreyListApplyListReqDTO req) {
        return greyListApplyListExportBiz.queryList(req);
    }

    @Override
    public List<SupplierGreyListRespDTO> searchGreyList(SupplierGreyListSearchReqDTO reqDTO) {
        List<SupplierGreyListPO> list = greyListMapperService.lambdaQuery()
                .in(SupplierGreyListPO::getSupplierCode, reqDTO.getSupplierCodes())
                .eq(StringUtils.isNotBlank(reqDTO.getDivisionId()), SupplierGreyListPO::getDivisionId, reqDTO.getDivisionId())
                .eq(StringUtils.isNotBlank(reqDTO.getLevel2CategoryCode()), SupplierGreyListPO::getLevel2CategoryCode, reqDTO.getLevel2CategoryCode())
                .last("limit " + reqDTO.getLimit().toString())
                .list();
        if (CollectionUtils.isEmpty(list)){
           return Collections.emptyList();
        }
        return greyListConvert.toGreyListRespDTOs(list);
    }
}
