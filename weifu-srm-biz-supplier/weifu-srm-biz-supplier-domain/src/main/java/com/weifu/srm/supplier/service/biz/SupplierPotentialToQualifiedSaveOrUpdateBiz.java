package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.CommonStatusEnum;
import com.weifu.srm.supplier.repository.enums.SupplierBizEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.repository.utils.BizNoGenerateUtil;
import com.weifu.srm.supplier.request.SupplierPotentialToQualifiedDetailReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierPotentialToQualifiedSaveOrUpdateBiz {

    private final SupplierPotentialToQualifiedRecordMapperService toQualifiedRecordMapperService;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService supplierAdmissionCategoryRecordMapperService;
    private final RedisUtil redisUtil;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final TransactionTemplate transactionTemplate;
    private final MQServiceManager mqManager;
    private final LocaleMessage localeMessage;

    private static final String SUBMIT_STATUS = "submit";

    @Transactional
    public void potentialToQualifiedSaveOrUpdate(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO) {
        // 查询准入表数据
        LambdaQueryWrapper<SupplierAdmissionRecordPO> recordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        recordPOLambdaQueryWrapper.eq(SupplierAdmissionRecordPO::getAdmissionNo, supplierPotentialToQualifiedDetailReqDTO.getAdmissionNo());
        recordPOLambdaQueryWrapper.eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierAdmissionRecordPO supplierAdmissionRecordPO = supplierAdmissionRecordMapperService.getOne(recordPOLambdaQueryWrapper);
        if (ObjectUtil.isNotNull(supplierAdmissionRecordPO) && !supplierAdmissionRecordPO.getSupplierCategoryStatus().equals(SupplierCategoryStatusEnum.POTENTIAL_STATUS.getCode())) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_CATEGORY_STATUS_NOT_SUPPORT));
        }
        // 数据更新并提交
        if (StringUtils.isNotBlank(supplierPotentialToQualifiedDetailReqDTO.getPcNo())) {
            updateApply(supplierPotentialToQualifiedDetailReqDTO);
            return;
        }
        // 不存在已保存的记录
        // 供应商基础信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, supplierAdmissionRecordPO.getSupplierBasicMsgId())
                .one();
        // 查询品类数据
        List<SupplierAdmissionCategoryRecordPO> categoryRecordPOList = supplierAdmissionCategoryRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, supplierAdmissionRecordPO.getInvitationNo())
                .list();
        // 组装数据
        String businessNo = BizNoGenerateUtil.generateBizNo(redisUtil, SupplierBizEnum.SUPPLIER_POTENTIAL_TO_QUALIFIED);
        SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO = new SupplierPotentialToQualifiedRecordPO();
        supplierPotentialToQualifiedRecordPO.setCreateBy(supplierPotentialToQualifiedDetailReqDTO.getOperationUserId());
        supplierPotentialToQualifiedRecordPO.setCreateName(supplierPotentialToQualifiedDetailReqDTO.getOperationUser());
        supplierPotentialToQualifiedRecordPO.setUpdateBy(supplierPotentialToQualifiedDetailReqDTO.getOperationUserId());
        supplierPotentialToQualifiedRecordPO.setUpdateName(supplierPotentialToQualifiedDetailReqDTO.getOperationUser());
        supplierPotentialToQualifiedRecordPO.setAdmissionNo(supplierAdmissionRecordPO.getAdmissionNo());
        supplierPotentialToQualifiedRecordPO.setBusinessNo(businessNo);
        supplierPotentialToQualifiedRecordPO.setApplyDesc(supplierPotentialToQualifiedDetailReqDTO.getApplyDesc());
        supplierPotentialToQualifiedRecordPO.setApplyRemark(supplierPotentialToQualifiedDetailReqDTO.getApplyRemark());
        supplierPotentialToQualifiedRecordPO.setApplyAttachments(AttachmentBizTypeConstants.POTENTIAL_TO_QUALIFIED_FIX);
        supplierPotentialToQualifiedRecordPO.setAdmissionCompleteTime(supplierAdmissionRecordPO.getAdmissionCompleteTime());
        supplierPotentialToQualifiedRecordPO.setApplyStatus(SUBMIT_STATUS.equals(supplierPotentialToQualifiedDetailReqDTO.getSubmitStatus()) ? CommonStatusEnum.AUDIT_STATUS.getCode() : CommonStatusEnum.DRAFT.getCode());
        if (ObjectUtil.isNotNull(supplierBasicInfoPO)) {
            supplierPotentialToQualifiedRecordPO.setSupplierName(supplierBasicInfoPO.getSupplierName());
            supplierPotentialToQualifiedRecordPO.setSapSupplierCode(supplierBasicInfoPO.getSapSupplierCode());
        }
        if (CollUtil.isNotEmpty(categoryRecordPOList)) {
            supplierPotentialToQualifiedRecordPO.setAdmissionCategory(StringUtils.join(categoryRecordPOList.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryName).collect(Collectors.toList()), "、"));
            supplierPotentialToQualifiedRecordPO.setAdmissionCategoryCode(StringUtils.join(categoryRecordPOList.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList()), "、"));
        }
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                // 保存申请记录
                toQualifiedRecordMapperService.save(supplierPotentialToQualifiedRecordPO);
                // 保存附件
                if (CollectionUtils.isNotEmpty(supplierPotentialToQualifiedDetailReqDTO.getAttachmentMessageRespDTOList())) {
                    List<AttachmentRecordPO> attachmentRecordPOS = boxAttachments(supplierPotentialToQualifiedDetailReqDTO, businessNo);
                    attachmentRecordMapperService.saveBatch(attachmentRecordPOS);
                }
                // 发送MQ
                if (SUBMIT_STATUS.equals(supplierPotentialToQualifiedDetailReqDTO.getSubmitStatus())) {
                    // 发送MQ创建工单
                    CreateTicketMQ createTicketMQ = boxCreateTicketMQ(supplierPotentialToQualifiedDetailReqDTO, businessNo);
                    mqManager.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
                }
            }
        });
    }
    private void updateApply(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO){
        SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO = toQualifiedRecordMapperService.lambdaQuery()
                .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, supplierPotentialToQualifiedDetailReqDTO.getPcNo())
                .one();
        supplierPotentialToQualifiedRecordPO.setApplyDesc(supplierPotentialToQualifiedDetailReqDTO.getApplyDesc());
        supplierPotentialToQualifiedRecordPO.setApplyRemark(supplierPotentialToQualifiedDetailReqDTO.getApplyRemark());
        supplierPotentialToQualifiedRecordPO.setUpdateBy(supplierPotentialToQualifiedDetailReqDTO.getOperationUserId());
        supplierPotentialToQualifiedRecordPO.setUpdateName(supplierPotentialToQualifiedDetailReqDTO.getOperationUser());
        supplierPotentialToQualifiedRecordPO.setUpdateTime(null);
        supplierPotentialToQualifiedRecordPO.setApplyStatus(SUBMIT_STATUS.equals(supplierPotentialToQualifiedDetailReqDTO.getSubmitStatus()) ? CommonStatusEnum.AUDIT_STATUS.getCode() : CommonStatusEnum.DRAFT.getCode());
        toQualifiedRecordMapperService.updateById(supplierPotentialToQualifiedRecordPO);
        // 删除原有的附件，插入新的附件
        attachmentRecordMapperService.removeByBizNo(supplierPotentialToQualifiedDetailReqDTO.getPcNo());
        if (CollectionUtils.isNotEmpty(supplierPotentialToQualifiedDetailReqDTO.getAttachmentMessageRespDTOList())) {
            List<AttachmentRecordPO> attachmentRecordPOS = boxAttachments(supplierPotentialToQualifiedDetailReqDTO, supplierPotentialToQualifiedDetailReqDTO.getPcNo());
            attachmentRecordMapperService.saveBatch(attachmentRecordPOS);
        }
        if (SUBMIT_STATUS.equals(supplierPotentialToQualifiedDetailReqDTO.getSubmitStatus())) {
            // 发送MQ创建工单
            CreateTicketMQ createTicketMQ = boxCreateTicketMQ(supplierPotentialToQualifiedDetailReqDTO, supplierPotentialToQualifiedDetailReqDTO.getPcNo());
            mqManager.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
        }
    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO, String businessNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(businessNo);
        mq.setTicketType(TicketTypeEnum.SUPPLIER_TO_QUALIFIED.getCode());
        mq.setSubmitBy(supplierPotentialToQualifiedDetailReqDTO.getOperationUserId());
        mq.setSubmitName(supplierPotentialToQualifiedDetailReqDTO.getOperationUser());
        mq.setSubmitDesc(supplierPotentialToQualifiedDetailReqDTO.getApplyDesc());
        mq.setSubmitRemark(supplierPotentialToQualifiedDetailReqDTO.getApplyRemark());
        return mq;
    }

    private List<AttachmentRecordPO> boxAttachments(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO, String businessNo) {
        return supplierPotentialToQualifiedDetailReqDTO.getAttachmentMessageRespDTOList().stream().map(attachmentMessageRespDTO -> {
            AttachmentRecordPO attachmentRecordPO = attachmentMessageConvert.toPO(attachmentMessageRespDTO);
            BaseEntityUtil.setCommon(attachmentRecordPO, supplierPotentialToQualifiedDetailReqDTO.getOperationUserId(), supplierPotentialToQualifiedDetailReqDTO.getOperationUser(), null);
            attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.POTENTIAL_TO_QUALIFIED_FIX);
            attachmentRecordPO.setBusinessNo(businessNo);
            return attachmentRecordPO;
        }).collect(Collectors.toList());
    }
}
