package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyTypeEnum;
import com.weifu.srm.supplier.manager.AttachmentManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.CreateSupplierCategoryAdjustApplyReqDTO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageReqDTO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyUploadFileReqDTO;
import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyDetailRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyItemRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryTreeRespDTO;
import com.weifu.srm.supplier.service.SupplierCategoryAdjustApplyService;
import com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCategoryAdjustApplyServiceImpl implements SupplierCategoryAdjustApplyService {

    private final PageSupplierCategoryAdjustApplyBiz pageSupplierCategoryAdjustApplyBiz;
    private final CreateSupplierCategoryAdjustApplyBiz createSupplierCategoryAdjustApplyBiz;
    private final QueryCategoryTreeForDelBiz queryCategoryTreeForDelBiz;
    private final QueryCategoryTreeForAddBiz queryCategoryTreeForAddBiz;
    private final UploadMeetingResolutionFileBiz uploadMeetingResolutionFileBiz;
    private final HandleSupplierCategoryAdjustAuditRstBiz handleSupplierCategoryAdjustAuditRstBiz;
    private final SupplierCategoryAdjustApplyMapperService supplierCategoryAdjustApplyMapperService;
    private final SupplierCategoryAdjustApplyItemMapperService supplierCategoryAdjustApplyItemMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final AttachmentManager attachmentManager;

    @Override
    public PageResponse<SupplierCategoryAdjustApplyPageRespDTO> page(SupplierCategoryAdjustApplyPageReqDTO req) {
        return pageSupplierCategoryAdjustApplyBiz.execute(req);
    }

    @Override
    public List<SupplierCategoryTreeRespDTO> queryCategoryTreeForDel(String sapSupplierCode) {
        return queryCategoryTreeForDelBiz.execute(sapSupplierCode);
    }

    @Override
    public List<SupplierCategoryTreeRespDTO> queryCategoryTreeForAdd(String sapSupplierCode) {
        return queryCategoryTreeForAddBiz.execute(sapSupplierCode);
    }

    @Override
    public String create(CreateSupplierCategoryAdjustApplyReqDTO req) {
        return createSupplierCategoryAdjustApplyBiz.execute(req);
    }

    @Override
    public void handleSupplierCategoryAdjustApplyRst(TicketStatusChangedMQ mq) {
        handleSupplierCategoryAdjustAuditRstBiz.execute(mq);
    }

    @Override
    public void uploadMeetingResolutionFile(SupplierCategoryAdjustApplyUploadFileReqDTO req) {
        uploadMeetingResolutionFileBiz.execute(req);
    }

    @Override
    public SupplierCategoryAdjustApplyDetailRespDTO queryApplyDetail(String applyNo) {
        List<SupplierCategoryAdjustApplyItemPO> applyItems = supplierCategoryAdjustApplyItemMapperService.lambdaQuery()
                .eq(SupplierCategoryAdjustApplyItemPO::getApplyNo, applyNo).list();
        List<SupplierCategoryAdjustApplyItemRespDTO> items = BeanUtil.copyToList(applyItems, SupplierCategoryAdjustApplyItemRespDTO.class);

        List<AttachmentMessageRespDTO> files = attachmentManager.getAttachmentsByBusinessNoAndBusType(applyNo,
                Arrays.asList(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_COMMON,
                        AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_MEETING_RESOLUTION));

        SupplierCategoryAdjustApplyPO apply = supplierCategoryAdjustApplyMapperService.lambdaQuery()
                .eq(SupplierCategoryAdjustApplyPO::getApplyNo, applyNo).one();

        SupplierBasicInfoPO supplier = supplierBasicInfoMapperService.getBySapSupplierCode(apply.getSapSupplierCode());
        SupplierCategoryAdjustApplyDetailRespDTO result = BeanUtil.toBean(apply, SupplierCategoryAdjustApplyDetailRespDTO.class);
        result.setSupplierName(supplier.getSupplierName());
        result.setSupplierNameEn(supplier.getSupplierNameEN());
        result.setFiles(files);
        result.setItems(items);
        result.setApplyTypeName(SupplierCategoryAdjustApplyTypeEnum.getName(result.getApplyType(), LocaleContextHolder.getLocale()));
        result.setApplyStatusName(SupplierCategoryAdjustApplyStatusEnum.getName(result.getApplyStatus(), LocaleContextHolder.getLocale()));
        return result;
    }

    @Override
    public List<AttachmentMessageRespDTO> queryMeetingResolutionFile(String applyNo) {
        return attachmentManager.getAttachmentsByBusinessNoAndBusType(applyNo, AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_MEETING_RESOLUTION);
    }

}
