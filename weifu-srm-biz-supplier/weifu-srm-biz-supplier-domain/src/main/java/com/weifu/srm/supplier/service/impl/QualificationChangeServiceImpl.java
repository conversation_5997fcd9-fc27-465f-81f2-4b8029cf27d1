package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.convert.SupplierBasicConvert;
import com.weifu.srm.supplier.convert.SupplierFinancialInfoConvert;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.constants.SupplierCommonConstants;
import com.weifu.srm.supplier.repository.enums.QualificationChangeSubmitSourceEnum;
import com.weifu.srm.supplier.repository.enums.QualificationChangeUserTypeEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.request.certification.SupplierCerForPerResDTO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.response.certification.SupplierCerForPerReqDTO;
import com.weifu.srm.supplier.service.QualificationChangeService;
import com.weifu.srm.supplier.service.biz.qualificationchange.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeServiceImpl implements QualificationChangeService {

    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final SupplierAdmissionInvitationInfoMapperService supplierAdmissionInvitationInfoMapperService;
    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final SupplierBasicConvert supplierBasicConvert;
    private final SupplierFinancialInfoConvert supplierFinancialInfoConvert;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final SupplierFinancialInfoMapperService supplierFinancialInfoMapperService;
    private final SupplierAssociationRelationMapperService supplierAssociationRelationMapperService;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final AdmissionQuestionnaireCertificationMapperService admissionQuestionnaireCertificationMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final QualificationChangeUpdateUserBiz qualificationChangeUpdateUserBiz;
    private final QualificationChangeUpdateBasicBiz qualificationChangeUpdateBasicBiz;
    private final QualificationChangeUpdateFinancialBiz qualificationChangeUpdateFinancialBiz;
    private final QualificationChangeUpdateAssociationBiz qualificationChangeUpdateAssociationBiz;
    private final QualificationChangeUpdateCertificationBiz qualificationChangeUpdateCertificationBiz;
    private final SupplierKeyInfoChangeRecordMapperService supplierKeyInfoChangeRecordMapperService;
    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeBasicItemMapperService qualificationChangeBasicItemMapperService;
    private final QualificationChangeFinancialItemMapperService qualificationChangeFinancialItemMapperService;
    private final QualificationChangeAssociationItemMapperService qualificationChangeAssociationItemMapperService;
    private final QualificationChangeAssociationDetailItemMapperService qualificationChangeAssociationDetailItemMapperService;
    private final QualificationChangeCertificationItemMapperService qualificationChangeCertificationItemMapperService;
    private final HandleQualificationChangeBasicRstBiz handleQualificationChangeBasicRstBiz;
    private final HandleQualificationChangeFinancialRstBiz handleQualificationChangeFinancialRstBiz;
    private final HandleQualificationChangeAssociationRstBiz handleQualificationChangeAssociationRstBiz;
    private final HandleQualificationChangeCertificationRstBiz handleQualificationChangeCertificationRstBiz;
    private final SupplierCertificationMapperService supplierCertificationMapperService;

    @Override
    public QualificationChangeQueryBasicRespDTO queryBasic(QualificationChangeQueryBasicReqDTO reqDTO) {
        if (reqDTO.getSupplierId() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        QualificationChangeQueryBasicRespDTO result = supplierBasicConvert.toQualificationChangeBasicRespDTO(basicInfoPO);
        // 查询供应商联系信息表信息
        SupplierContactInfoPO supplierContactInfoPO = supplierContactInfoMapperService.lambdaQuery()
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNotEmpty(supplierContactInfoPO)) {
            result.setCompanyPhone(supplierContactInfoPO.getCompanyPhone());
            result.setCompanyFax(supplierContactInfoPO.getCompanyFax());
            result.setLegalPersonName(supplierContactInfoPO.getLegalPersonName());
        }
        // 查询供应商准入表信息
        List<SupplierAdmissionRecordPO> supplierAdmissionRecordPOS = supplierAdmissionRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionRecordPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        supplierAdmissionRecordPOS.forEach(supplierAdmissionRecordPO -> {
            // 首次准入时间
            if (ObjectUtil.isNotNull(supplierAdmissionRecordPO.getIsFirstAdmission()) && supplierAdmissionRecordPO.getIsFirstAdmission() == 1) {
                result.setFirstAdmissionTime(supplierAdmissionRecordPO.getAdmissionCompleteTime());
            }
        });
        // 查询供应商核心信息变动记录表信息
        SupplierKeyInfoChangeRecordPO supplierKeyInfoChangeRecordPO = supplierKeyInfoChangeRecordMapperService.lambdaQuery()
                .eq(SupplierKeyInfoChangeRecordPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierKeyInfoChangeRecordPO::getChangeType, "REGISTER")
                .eq(SupplierKeyInfoChangeRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNotEmpty(supplierKeyInfoChangeRecordPO)) {
            result.setRegisterTime(supplierKeyInfoChangeRecordPO.getCreateTime());
        }

        // 查询附件相关信息
        LambdaQueryWrapper<AttachmentRecordPO> attachmentRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessNo, basicInfoPO.getRegisterNo());
        attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.BUSINESS_LICENSE);
        attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.list(attachmentRecordPOLambdaQueryWrapper);
        result.setBusinessLicenseAttachmentList(attachmentMessageConvert.toList(attachmentRecordPOList));

        //处理查询最新经营附件数据
        this.handleSupplierBusinessLicenseAttachment(basicInfoPO.getSapSupplierCode(),result);

        return result;
    }

    @Override
    public List<QualificationChangeQueryUserRespDTO> queryUser(QualificationChangeQueryBasicReqDTO reqDTO) {
        if (reqDTO.getSupplierId() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        // 查询供应商联系信息表信息
        SupplierContactInfoPO supplierContactInfoPO = supplierContactInfoMapperService.lambdaQuery()
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (supplierContactInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        List<QualificationChangeQueryUserRespDTO> result = new ArrayList<>();
        QualificationChangeQueryUserRespDTO legalPerson = new QualificationChangeQueryUserRespDTO();
        legalPerson.setName(supplierContactInfoPO.getLegalPersonName());
        legalPerson.setType(QualificationChangeUserTypeEnum.LEGAL_PERSON.getCode());
        legalPerson.setPhone(supplierContactInfoPO.getLegalPersonPhone());
        legalPerson.setEmail(supplierContactInfoPO.getLegalPersonEmail());
        result.add(legalPerson);
        QualificationChangeQueryUserRespDTO registerContact = new QualificationChangeQueryUserRespDTO();
        registerContact.setName(supplierContactInfoPO.getRegisterContactName());
        registerContact.setType(QualificationChangeUserTypeEnum.REGISTER_CONTACT.getCode());
        registerContact.setPhone(supplierContactInfoPO.getRegisterContactPhone());
        registerContact.setEmail(supplierContactInfoPO.getRegisterContactEmail());
        registerContact.setPosition(supplierContactInfoPO.getRegisterContactPosition());
        result.add(registerContact);
        QualificationChangeQueryUserRespDTO businessContact = new QualificationChangeQueryUserRespDTO();
        businessContact.setName(supplierContactInfoPO.getBusinessContactName());
        businessContact.setType(QualificationChangeUserTypeEnum.BUSINESS_CONTACT.getCode());
        businessContact.setPhone(supplierContactInfoPO.getBusinessContactPhone());
        businessContact.setEmail(supplierContactInfoPO.getBusinessContactEmail());
        businessContact.setPosition(supplierContactInfoPO.getBusinessContactPosition());
        result.add(businessContact);
        QualificationChangeQueryUserRespDTO qualityContact = new QualificationChangeQueryUserRespDTO();
        qualityContact.setName(supplierContactInfoPO.getQualityContactName());
        qualityContact.setType(QualificationChangeUserTypeEnum.QUALITY_CONTACT.getCode());
        qualityContact.setPhone(supplierContactInfoPO.getQualityContactPhone());
        qualityContact.setEmail(supplierContactInfoPO.getQualityContactEmail());
        qualityContact.setPosition(supplierContactInfoPO.getQualityContactPosition());
        result.add(qualityContact);
        QualificationChangeQueryUserRespDTO financeContact = new QualificationChangeQueryUserRespDTO();
        financeContact.setName(supplierContactInfoPO.getFinanceContactName());
        financeContact.setType(QualificationChangeUserTypeEnum.FINANCE_CONTACT.getCode());
        financeContact.setPhone(supplierContactInfoPO.getFinanceContactPhone());
        financeContact.setEmail(supplierContactInfoPO.getFinanceContactEmail());
        financeContact.setPosition(supplierContactInfoPO.getFinanceContactPosition());
        result.add(financeContact);
        return result;
    }

    @Override
    public List<QualificationChangeQueryFinancialRespDTO> queryFinancial(QualificationChangeQueryBasicReqDTO reqDTO) {
        if (reqDTO.getSupplierId() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        // 查询供应商财务信息表信息
        List<SupplierFinancialInfoPO> supplierFinancialInfoPOS = supplierFinancialInfoMapperService.lambdaQuery()
                .eq(SupplierFinancialInfoPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierFinancialInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        if (ObjectUtil.isAllNotEmpty(supplierFinancialInfoPOS)) {
            List<QualificationChangeQueryFinancialRespDTO> result = supplierFinancialInfoConvert.toQualificationChangeFinancialRespDTO(supplierFinancialInfoPOS);
            result.forEach(qualificationChangeQueryFinancialRespDTO -> {
                // 查询附件相关信息
                LambdaQueryWrapper<AttachmentRecordPO> attachmentRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessNo, qualificationChangeQueryFinancialRespDTO.getId());
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.INVOICE_INFORMATION);
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
                List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.list(attachmentRecordPOLambdaQueryWrapper);
                qualificationChangeQueryFinancialRespDTO.setFinancialAttachmentList(attachmentMessageConvert.toList(attachmentRecordPOList));
            });
            return result;
        } else {
            return null;
        }
    }

    @Override
    public QualificationChangeQueryAssociationRespDTO queryAssociation(QualificationChangeQueryBasicReqDTO reqDTO) {
        if (reqDTO.getSupplierId() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        QualificationChangeQueryAssociationRespDTO result = new QualificationChangeQueryAssociationRespDTO();
        result.setIsWeifuRelatedParty(basicInfoPO.getIsWeifuRelatedParty());
        result.setDirectRelatedPartyTypeDesc(basicInfoPO.getDirectRelatedPartyTypeDesc());
        result.setIsRelatedToWeifuSupplier(basicInfoPO.getIsRelatedToWeifuSupplier());
        if (basicInfoPO.getIsRelatedToWeifuSupplier() != null && basicInfoPO.getIsRelatedToWeifuSupplier() == 1) {
            // 查询供应商关联关系表信息
            List<SupplierAssociationRelationPO> supplierAssociationRelationPOS = supplierAssociationRelationMapperService.lambdaQuery()
                    .eq(SupplierAssociationRelationPO::getSapSupplierCode, basicInfoPO.getSapSupplierCode())
                    .eq(SupplierAssociationRelationPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            List<QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO> associationRelationItemRespDTOS = qualificationChangeConvert.toQueryAssociationRespDTO(supplierAssociationRelationPOS);
            result.setAssociationRelationItemRespDTOList(associationRelationItemRespDTOS);
        }
        return result;
    }

    @Override
    public List<QualificationChangeQueryCertificationRespDTO> queryCertification(QualificationChangeQueryBasicReqDTO reqDTO) {
        if (reqDTO.getSupplierId() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        // 查询供应商资质证书信息
        List<SupplierCertificationPO> certificationPOS = supplierCertificationMapperService.lambdaQuery()
                .eq(SupplierCertificationPO::getSupplierBasicMsgId, reqDTO.getSupplierId())
                .eq(SupplierCertificationPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        List<QualificationChangeQueryCertificationRespDTO> result = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(certificationPOS)) {
            certificationPOS.forEach(certificationPO -> {
                QualificationChangeQueryCertificationRespDTO queryCertificationRespDTO = qualificationChangeConvert.toQueryCertificationRespDTO(certificationPO);
                // 查询附件相关信息
                queryCertificationRespDTO.setCertificationId(certificationPO.getId());
                LambdaQueryWrapper<AttachmentRecordPO> attachmentRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessNo, certificationPO.getId().toString());
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.SUPPLIER_CERTIFICATION);
                attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
                List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.list(attachmentRecordPOLambdaQueryWrapper);
                queryCertificationRespDTO.setCertificationAttachmentList(attachmentMessageConvert.toList(attachmentRecordPOList));
                result.add(queryCertificationRespDTO);
            });
        }
        return result;
    }

    @Override
    public void updateBasic(QualificationChangeUpdateBasicReqDTO reqDTO) {
        qualificationChangeUpdateBasicBiz.updateBasic(reqDTO, QualificationChangeSubmitSourceEnum.SUPPLIER.getCode());
    }

    @Override
    public void updateUser(QualificationChangeUpdateUserReqDTO reqDTO) {
        qualificationChangeUpdateUserBiz.updateUser(reqDTO);
    }

    @Override
    public void updateFinancial(QualificationChangeUpdateFinancialReqDTO reqDTO) {
        qualificationChangeUpdateFinancialBiz.updateFinancial(reqDTO, QualificationChangeSubmitSourceEnum.SUPPLIER.getCode());
    }

    @Override
    public void updateAssociation(QualificationChangeUpdateAssociationReqDTO reqDTO) {
        qualificationChangeUpdateAssociationBiz.updateAssociation(reqDTO, QualificationChangeSubmitSourceEnum.SUPPLIER.getCode());
    }

    @Override
    public void updateCertification(QualificationChangeUpdateCertificationReqDTO reqDTO) {
        qualificationChangeUpdateCertificationBiz.updateCertification(reqDTO, QualificationChangeSubmitSourceEnum.SUPPLIER.getCode());
    }

    @Override
    public PageResponse<QualificationChangeQueryApplyRespDTO> queryApply(QualificationChangeQueryApplyReqDTO reqDTO) {
        Page<QualificationChangeApplyPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        SupplierBasicInfoPO supplierBasicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (StringUtils.isEmpty(supplierBasicInfoPO.getSapSupplierCode())) {
            return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), 0L, new ArrayList<>());
        }
        Page<QualificationChangeApplyPO> qualificationChangeApplyPOPage = qualificationChangeApplyMapperService.queryList(page, reqDTO, supplierBasicInfoPO.getSapSupplierCode());
        List<QualificationChangeQueryApplyRespDTO> queryApplyRespDTO = qualificationChangeConvert.toQueryApplyRespDTO(qualificationChangeApplyPOPage.getRecords());
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), qualificationChangeApplyPOPage.getTotal(), queryApplyRespDTO);
    }

    @Override
    public QualificationChangeQueryBasicUpdateRespDTO queryBasicUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        if (reqDTO.getQualificationChangeNo() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        QualificationChangeBasicItemPO qualificationChangeBasicItemPO = qualificationChangeBasicItemMapperService.lambdaQuery()
                .eq(QualificationChangeBasicItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                .eq(QualificationChangeBasicItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        QualificationChangeQueryBasicUpdateRespDTO result = qualificationChangeConvert.toQueryBasicUpdateRespDTO(qualificationChangeBasicItemPO);
        AttachmentRecordPO attachmentRecordPO;
        if (StringUtil.isNullOrEmpty(result.getBusinessLicenseAttachmentBefore())) {
            // 查询基础表信息
            SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                    .eq(SupplierBasicInfoPO::getSapSupplierCode, result.getSapSupplierCode())
                    .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();
            // 查询附件表信息
            attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                    .eq(AttachmentRecordPO::getBusinessNo, basicInfoPO.getRegisterNo())
                    .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.BUSINESS_LICENSE)
                    .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();

        } else {
            // 查询附件表信息
            attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                    .eq(AttachmentRecordPO::getBusinessNo, result.getBusinessLicenseAttachmentBefore())
                    .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_BASIC)
                    .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();
        }
        if(Objects.nonNull(attachmentRecordPO)) {
            AttachmentMessageRespDTO attachmentMessageRespDTO = attachmentMessageConvert.toDTO(attachmentRecordPO);
            List<AttachmentMessageRespDTO> attachmentMessageRespDTOS = new ArrayList<>();
            attachmentMessageRespDTOS.add(attachmentMessageRespDTO);
            result.setBusinessLicenseAttachmentList(attachmentMessageRespDTOS);
        }
        //2025-01-09 问题： 供应商资质变更，供应商上传新的营业执照复现，审批时不显示。TK20250102000126
        //修改备注:供应商端发起基本信息调整申请 附件信息（attachment_record）中业务号存入的是资质变更基本信息详情（qualification_change_basic_item）的id
        //代码兼容查询处理
        if(CollUtil.isEmpty(result.getBusinessLicenseAttachmentList()))
            result.setBusinessLicenseAttachmentList(
                    BeanUtil.copyToList(attachmentRecordMapperService
                            .listAttachments(String.valueOf(qualificationChangeBasicItemPO.getId()),
                                    AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_BASIC),AttachmentMessageRespDTO.class));
        return result;
    }

    @Override
    public List<QualificationChangeQueryFinancialUpdateRespDTO> queryFinancialUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        if (reqDTO.getQualificationChangeNo() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        List<QualificationChangeFinancialItemPO> qualificationChangeFinancialItemPOList = qualificationChangeFinancialItemMapperService.lambdaQuery()
                .eq(QualificationChangeFinancialItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                .eq(QualificationChangeFinancialItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        List<Long> financialIdS = qualificationChangeFinancialItemPOList.stream().map(QualificationChangeFinancialItemPO::getId).collect(Collectors.toList());
        List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_FINANCIAL)
                .in(AttachmentRecordPO::getBusinessNo, financialIdS)
                .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        ArrayList<QualificationChangeQueryFinancialUpdateRespDTO> result = new ArrayList<>();
        qualificationChangeFinancialItemPOList.forEach(qualificationChangeFinancialItemPO -> {
            QualificationChangeQueryFinancialUpdateRespDTO queryFinancialUpdateRespDTO = qualificationChangeConvert.toQueryFinancialUpdateRespDTO(qualificationChangeFinancialItemPO);
            attachmentRecordPOList.forEach(attachmentRecordPO -> {
                if (attachmentRecordPO.getBusinessNo().equals(qualificationChangeFinancialItemPO.getId()+"")) {
                    AttachmentMessageRespDTO attachmentMessageRespDTO = attachmentMessageConvert.toDTO(attachmentRecordPO);
                    List<AttachmentMessageRespDTO> attachmentMessageRespDTOS = new ArrayList<>();
                    attachmentMessageRespDTOS.add(attachmentMessageRespDTO);
                    queryFinancialUpdateRespDTO.setFinancialAttachmentList(attachmentMessageRespDTOS);
                }
            });
            result.add(queryFinancialUpdateRespDTO);
        });
        return result;
    }

    @Override
    public QualificationChangeQueryAssociationUpdateRespDTO queryAssociationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        if (reqDTO.getQualificationChangeNo() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        QualificationChangeAssociationItemPO qualificationChangeAssociationItemPO = qualificationChangeAssociationItemMapperService.lambdaQuery()
                .eq(QualificationChangeAssociationItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                .eq(QualificationChangeAssociationItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        QualificationChangeQueryAssociationUpdateRespDTO result = qualificationChangeConvert.toQueryAssociationUpdateRespDTO(qualificationChangeAssociationItemPO);
        if (result.getIsRelatedToWeifuSupplier() == 1) {
            List<QualificationChangeAssociationDetailItemPO> qualificationChangeAssociationDetailItemPOList = qualificationChangeAssociationDetailItemMapperService.lambdaQuery()
                    .eq(QualificationChangeAssociationDetailItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                    .eq(QualificationChangeAssociationDetailItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            List<QualificationChangeQueryAssociationUpdateRespDTO.AssociationRelationItemRespDTO> queryAssociationDetailUpdateRespDTO = qualificationChangeConvert.toQueryAssociationDetailUpdateRespDTO(qualificationChangeAssociationDetailItemPOList);
            result.setAssociationRelationItemRespDTOList(queryAssociationDetailUpdateRespDTO);
        }
        return result;
    }

    @Override
    public QualificationChangeQueryCertificationUpdateRespDTO queryCertificationUpdate(QualificationChangeQueryUpdateReqDTO reqDTO) {
        if (reqDTO.getQualificationChangeNo() == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        QualificationChangeCertificationItemPO certificationItemPO = qualificationChangeCertificationItemMapperService.lambdaQuery()
                .eq(QualificationChangeCertificationItemPO::getQualificationChangeNo, reqDTO.getQualificationChangeNo())
                .eq(QualificationChangeCertificationItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        QualificationChangeQueryCertificationUpdateRespDTO result = qualificationChangeConvert.toQueryCertificationUpdateRespDTO(certificationItemPO);
        if (result.getOperationType().equals("add")) {
            Long businessNo = certificationItemPO.getId();
            AttachmentRecordPO attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                    .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_CERTIFICATION_ADD)
                    .eq(AttachmentRecordPO::getBusinessNo, ObjectUtils.isNotEmpty(businessNo)?businessNo.toString():businessNo)
                    .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();
            AttachmentMessageRespDTO attachmentMessageRespDTO = attachmentMessageConvert.toDTO(attachmentRecordPO);
            List<AttachmentMessageRespDTO> attachmentMessageRespDTOS = new ArrayList<>();
            attachmentMessageRespDTOS.add(attachmentMessageRespDTO);
            result.setCertificationAttachmentList(attachmentMessageRespDTOS);
        }
        return result;
    }

    @Override
    public void handleQualificationChangeBasicRst(TicketStatusChangedMQ mq) {
        handleQualificationChangeBasicRstBiz.handle(mq);
    }

    @Override
    public void handleQualificationChangeFinancialRst(TicketStatusChangedMQ mq) {
        handleQualificationChangeFinancialRstBiz.handle(mq);
    }

    @Override
    public void handleQualificationChangeAssociationRst(TicketStatusChangedMQ mq) {
        handleQualificationChangeAssociationRstBiz.handle(mq);
    }

    @Override
    public void handleQualificationChangeCertificateRst(TicketStatusChangedMQ mq) {
        handleQualificationChangeCertificationRstBiz.handle(mq);
    }

    @Override
    public List<SupplierCerForPerReqDTO> findQualityCertificationListBySupplierNotExpiry(SupplierCerForPerResDTO req) {
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, req.getSupplierNo())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        String dateFormat = DateUtil.dateFormat(req.getNowDate(), SupplierCommonConstants.DATE_FORMAT);
        // 查询供应商资质证书信息
        List<SupplierCertificationPO> certificationPOS = supplierCertificationMapperService.lambdaQuery()
                .eq(SupplierCertificationPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .eq(SupplierCertificationPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(SupplierCertificationPO::getHasQualityCertification, YesOrNoEnum.YES.getCode())
                .le(SupplierCertificationPO::getCertificationStartDate, dateFormat)
                .ge(SupplierCertificationPO::getCertificationExpiryDate, dateFormat)
                .list();
        return certificationPOS.stream().map(SupplierCertificationPO::getCertificationType)
                .distinct().map(e->{
                    SupplierCerForPerReqDTO supplierCerForPerReqDTO = new SupplierCerForPerReqDTO();
                    supplierCerForPerReqDTO.setCertificationType(e);
                    return supplierCerForPerReqDTO;
                }).collect(Collectors.toList());
    }

    /**
     * 查询最新审批通过的基本信息变更 且上传了营业执照附件的附件数据
     */
    private void handleSupplierBusinessLicenseAttachment(String supplierCode, QualificationChangeQueryBasicRespDTO result) {
        List<SupplierBasicUpdateAttachmentRespDTO> businessLicense = attachmentRecordMapperService.queryBusinessLicenseAttachments(supplierCode);
        if(CollUtil.isNotEmpty(businessLicense))
            result.setBusinessLicenseAttachmentList(BeanUtil.copyToList(businessLicense,AttachmentMessageRespDTO.class));
    }

}
