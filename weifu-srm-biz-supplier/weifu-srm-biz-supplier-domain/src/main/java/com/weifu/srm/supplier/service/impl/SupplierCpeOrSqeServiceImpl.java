package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.communication.api.TodoListApi;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.request.sitemessage.SendSiteMessageReqDTO;
import com.weifu.srm.communication.request.todolist.SetTodoListToDoneReqDTO;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryResultDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.supplier.convert.SupplierBasicInfoConvert;
import com.weifu.srm.supplier.enums.AccountNoticeTemplateEnum;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.request.SupplierCpeMainReqDTO;
import com.weifu.srm.supplier.request.SupplierCpeOrSqePageReqDTO;
import com.weifu.srm.supplier.request.SupplierSqeMainReqDTO;
import com.weifu.srm.supplier.response.CategoryRespDTO;
import com.weifu.srm.supplier.response.SupplierBasicInfoCategorySimpleRespDTO;
import com.weifu.srm.supplier.response.SupplierCpeExcelRespDTO;
import com.weifu.srm.supplier.response.SupplierSqeExcelRespDTO;
import com.weifu.srm.supplier.service.SupplierCpeOrSqeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description SupplierCpeOrSqeServiceImpl
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCpeOrSqeServiceImpl implements SupplierCpeOrSqeService {

    public static final String COMMA = ",";
    public static final String CATEGORY_LEVEL_THREE = "3";
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final SupplierBasicInfoConvert supplierBasicInfoConvert;
    private final CategoryApi categoryApi;
    private final TodoListApi todoListApi;
    private final MqManager mqManager;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cpeMain(SupplierCpeMainReqDTO supplierCpeMainReqDTO) {
        SupplierBasicInfoPO po = new SupplierBasicInfoPO();
        po.setId(supplierCpeMainReqDTO.getId());
        po.setCpeMainId(supplierCpeMainReqDTO.getCpeMainId());
        po.setCpeMainName(supplierCpeMainReqDTO.getCpeMainName());
        po.setUpdateBy(supplierCpeMainReqDTO.getOperationBy());
        po.setUpdateName(supplierCpeMainReqDTO.getOperationByName());
        po.setUpdateTime(DateUtil.date());
        supplierBasicInfoMapperService.updateById(po);
        this.sendSitMsg(AccountNoticeTemplateEnum.ASSIGNED_AS_LEAD_CPE, supplierCpeMainReqDTO.getId(),
                supplierCpeMainReqDTO.getCpeMainId(), supplierCpeMainReqDTO.getCpeMainName());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sqeMain(SupplierSqeMainReqDTO supplierSqeMainReqDTO) {
        if (supplierSqeMainReqDTO.getSqeMainId() <= 0) {
            supplierSqeMainReqDTO.setSqeMainId(null);
            supplierSqeMainReqDTO.setSqeMainName(null);
        }
        supplierBasicInfoMapperService.lambdaUpdate()
                .set(SupplierBasicInfoPO::getSqeMainId, supplierSqeMainReqDTO.getSqeMainId())
                .set(SupplierBasicInfoPO::getSqeMainName, supplierSqeMainReqDTO.getSqeMainName())
                .set(SupplierBasicInfoPO::getUpdateBy, supplierSqeMainReqDTO.getOperationBy())
                .set(SupplierBasicInfoPO::getUpdateName, supplierSqeMainReqDTO.getOperationByName())
                .set(SupplierBasicInfoPO::getUpdateTime, DateUtil.date())
                .eq(SupplierBasicInfoPO::getId, supplierSqeMainReqDTO.getId())
                .update();
        SupplierBasicInfoPO po = supplierBasicInfoMapperService.getById(supplierSqeMainReqDTO.getId());
        SetTodoListToDoneReqDTO reqDTO = new SetTodoListToDoneReqDTO();
        reqDTO.setBusinessNo(po.getSapSupplierCode());
        reqDTO.setBusinessType(TodoClsEnum.SUPPLIER_AWAITING_SQE_ASSIGNMENT.getCode());
        todoListApi.setToDone(reqDTO);
        if (supplierSqeMainReqDTO.getSqeMainId() != null) {
            this.sendSitMsg(AccountNoticeTemplateEnum.ASSIGNED_AS_LEAD_SQE, supplierSqeMainReqDTO.getId(),
                    supplierSqeMainReqDTO.getSqeMainId(), supplierSqeMainReqDTO.getSqeMainName());
        }
    }

    @Override
    public PageResponse<SupplierBasicInfoCategorySimpleRespDTO> page(SupplierCpeOrSqePageReqDTO reqDTO) {
        Page<SupplierBasicInfoPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        LambdaQueryWrapper<SupplierBasicInfoPO> queryWrapper = getSupplierBasicInfoLambdaQueryWrapper(reqDTO);
        IPage<SupplierBasicInfoPO> pageResult = supplierBasicInfoMapperService.page(page, queryWrapper);
        List<SupplierBasicInfoCategorySimpleRespDTO> dtoList;
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
            dtoList = supplierBasicInfoConvert.toSupplierBasicInfoSimpleRespDTO(pageResult.getRecords());
            fillCategory(dtoList);
        } else {
            dtoList = Lists.newArrayList();
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }


    @Override
    public void exportCpe(SupplierCpeOrSqePageReqDTO reqDTO, HttpServletResponse response) {
        PageResponse<SupplierBasicInfoCategorySimpleRespDTO> pageResponse = page(reqDTO);
        List<SupplierCpeExcelRespDTO> dtoList = supplierBasicInfoConvert.toSupplierCpeExcelRespDTO(pageResponse.getList());
        try {
            EasyExcelFactory.write(response.getOutputStream(), SupplierCpeExcelRespDTO.class).sheet("cpe").doWrite(dtoList);
        } catch (IOException ioe) {
            log.error("export cpe excel failed!");
            throw new BizFailException(ioe.getMessage());
        }
    }

    @Override
    public void exportSqe(SupplierCpeOrSqePageReqDTO reqDTO, HttpServletResponse response) {
        PageResponse<SupplierBasicInfoCategorySimpleRespDTO> pageResponse = page(reqDTO);
        List<SupplierSqeExcelRespDTO> dtoList = supplierBasicInfoConvert.toSupplierSqeExcelRespDTO(pageResponse.getList());
        try {
            EasyExcelFactory.write(response.getOutputStream(), SupplierSqeExcelRespDTO.class).sheet("sqe").doWrite(dtoList);
        } catch (IOException ioe) {
            log.error("export sqe excel failed!");
            throw new BizFailException(ioe.getMessage());
        }
    }

    private void fillCategory(List<SupplierBasicInfoCategorySimpleRespDTO> dtoList) {
        fillStatusName(dtoList);
        List<String> supplierCodeList = dtoList.stream().map(SupplierBasicInfoCategorySimpleRespDTO::getSapSupplierCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierCodeList)) {
            return;
        }
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipList = supplierCategoryRelationshipMapperService.queryBySapSupplierCodeList(supplierCodeList);
        Map<String, List<SupplierCategoryRelationshipPO>> supplierCodeMap = supplierCategoryRelationshipList.stream().collect(Collectors.groupingBy(SupplierCategoryRelationshipPO::getSapSupplierCode));
        List<String> categoryCodeList = supplierCategoryRelationshipList.stream().map(SupplierCategoryRelationshipPO::getCategoryCode).collect(Collectors.toList());
        ApiResponse<List<CategoryResultDTO>> apiResponse = categoryApi.queryByCategoryCodes(categoryCodeList);
        if (apiResponse == null || CollectionUtils.isEmpty(apiResponse.getData())) {
            return;
        }
        List<CategoryResultDTO> categoryResultList = apiResponse.getData();
        Map<String, CategoryResultDTO> categoryResultMap = categoryResultList.stream().collect(Collectors.toMap(CategoryResultDTO::getCategoryCode, categoryResultDTO -> categoryResultDTO));
        for (SupplierBasicInfoCategorySimpleRespDTO respDTO : dtoList) {
            List<SupplierCategoryRelationshipPO> relationshipList = supplierCodeMap.get(respDTO.getSapSupplierCode());
            if (CollectionUtils.isNotEmpty(relationshipList)) {
                fillCategory(respDTO, relationshipList, categoryResultMap);
            }
        }
    }

    private void fillStatusName(List<SupplierBasicInfoCategorySimpleRespDTO> dtoList) {
        for (SupplierBasicInfoCategorySimpleRespDTO dto : dtoList) {
            SupplierBasicStatusEnum supplierBasicStatusEnum = SupplierBasicStatusEnum.getByCode(dto.getStatus());
            if (supplierBasicStatusEnum != null) {
                dto.setStatusName(supplierBasicStatusEnum.getChineseName());
                dto.setStatusNameEn(supplierBasicStatusEnum.getEnglishName());
            }
        }
    }

    private static void fillCategory(SupplierBasicInfoCategorySimpleRespDTO respDTO, List<SupplierCategoryRelationshipPO> relationshipList, Map<String, CategoryResultDTO> categoryResultMap) {
        List<Long> categoryIdRespList = Lists.newArrayList();
        List<String> categoryNameList = Lists.newArrayList();
        List<String> categoryNameEnList = Lists.newArrayList();
        for (SupplierCategoryRelationshipPO relationship : relationshipList) {
            CategoryResultDTO categoryResultDTO = categoryResultMap.get(relationship.getCategoryCode());
            if (categoryResultDTO == null) {
                continue;
            }
            CategoryRespDTO categoryRespDTO = new CategoryRespDTO();
            categoryRespDTO.setId(categoryResultDTO.getId());
            categoryRespDTO.setCategoryName(categoryResultDTO.getCategoryName());
            categoryRespDTO.setCategoryNameEn(categoryResultDTO.getCategoryNameEn());
            categoryRespDTO.setCategoryCode(categoryResultDTO.getCategoryCode());
            categoryIdRespList.add(categoryRespDTO.getId());
            if (CATEGORY_LEVEL_THREE.equals(categoryResultDTO.getCategoryLevel())) {
                categoryNameList.add(categoryResultDTO.getCategoryName());
                categoryNameEnList.add(categoryResultDTO.getCategoryNameEn());
            }
        }
        respDTO.setCategoryIdList(categoryIdRespList);
        respDTO.setCategoryNames(CollectionUtils.isEmpty(categoryNameList) ? null : String.join(COMMA, categoryNameList));
        respDTO.setCategoryNameEns(CollectionUtils.isEmpty(categoryNameEnList) ? null : String.join(COMMA, categoryNameEnList));
    }


    private LambdaQueryWrapper<SupplierBasicInfoPO> getSupplierBasicInfoLambdaQueryWrapper(SupplierCpeOrSqePageReqDTO reqDTO) {
        LambdaQueryWrapper<SupplierBasicInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(!StringUtil.isNullOrEmpty(reqDTO.getSupplierName()), SupplierBasicInfoPO::getSupplierName, reqDTO.getSupplierName());
        queryWrapper.like(!StringUtil.isNullOrEmpty(reqDTO.getSapSupplierCode()), SupplierBasicInfoPO::getSapSupplierCode, reqDTO.getSapSupplierCode());
        queryWrapper.like(!StringUtil.isNullOrEmpty(reqDTO.getSqeMainName()), SupplierBasicInfoPO::getSqeMainName, reqDTO.getSqeMainName());
        queryWrapper.like(!StringUtil.isNullOrEmpty(reqDTO.getCpeMainName()), SupplierBasicInfoPO::getCpeMainName, reqDTO.getCpeMainName());
        queryWrapper.isNull(reqDTO.getNullCpe() != null && reqDTO.getNullCpe(), SupplierBasicInfoPO::getCpeMainId);
        queryWrapper.isNull(reqDTO.getNullSqe() != null && reqDTO.getNullSqe(), SupplierBasicInfoPO::getSqeMainId);
        queryWrapper.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getSupplierCodeList()), SupplierBasicInfoPO::getSapSupplierCode, reqDTO.getSupplierCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getSupplierNameCodeList()), SupplierBasicInfoPO::getSapSupplierCode, reqDTO.getSupplierNameCodeList());
        if (!StringUtil.isNullOrEmpty(reqDTO.getCategoryCode())) {
            List<SupplierCategoryRelationshipPO> relationshipList = supplierCategoryRelationshipMapperService.queryByCategoryCode(reqDTO.getCategoryCode());
            List<String> sapSupplierCodeList = null;
            if (CollectionUtils.isNotEmpty(relationshipList)) {
                sapSupplierCodeList = relationshipList.stream().map(SupplierCategoryRelationshipPO::getSapSupplierCode).distinct().collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(relationshipList)) {
                sapSupplierCodeList = Lists.newArrayList();
                sapSupplierCodeList.add("NULL");
            }
            queryWrapper.in(SupplierBasicInfoPO::getSapSupplierCode, sapSupplierCodeList);

        }
        List<String> statusList = Lists.newArrayList();
        statusList.add(SupplierBasicStatusEnum.REGISTERED_SUPPLIER.getCode());
        statusList.add(SupplierBasicStatusEnum.POTENTIAL_SUPPLIER.getCode());
        statusList.add(SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode());
        statusList.add(SupplierBasicStatusEnum.WAIT_LEAVE.getCode());
        queryWrapper.isNotNull(SupplierBasicInfoPO::getSapSupplierCode);
        queryWrapper.ne(SupplierBasicInfoPO::getSapSupplierCode, "");
        queryWrapper.in(SupplierBasicInfoPO::getStatus, statusList);
        queryWrapper.orderByDesc(SupplierBasicInfoPO::getId);
        log.info("getSupplierBasicInfoLambdaQueryWrapper:{}", queryWrapper.getSqlSelect());
        return queryWrapper;
    }

    private void sendSitMsg(AccountNoticeTemplateEnum templateEnum, Long supplierId, Long userId, String username) {
        SupplierBasicInfoPO basicInfo = supplierBasicInfoMapperService.getById(supplierId);
        SendSiteMessageReqDTO msgReqDTO = new SendSiteMessageReqDTO();
        msgReqDTO.setTitle(templateEnum.getTitle());
        msgReqDTO.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        msgReqDTO.setBusinessNo(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_FORMAT));
        msgReqDTO.setBusinessType(templateEnum.getMessageClsEnum().getCode());
        msgReqDTO.setUserId(userId);
        msgReqDTO.setUserName(username);
        msgReqDTO.setIconType(templateEnum.getIconTypeEnum().getCode());
        buildSitMessageContentBody(msgReqDTO, templateEnum, basicInfo.getSapSupplierCode(), basicInfo.getSupplierName());
        mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(Collections.singletonList(msgReqDTO)));
    }

    private void buildSitMessageContentBody(SendSiteMessageReqDTO msgReqDTO, AccountNoticeTemplateEnum templateEnum, String... params) {
        String contentBody = templateEnum.getContent();
        if (params != null && params.length > 0) {
            for (int i = 0; i < params.length; i++) {
                contentBody = contentBody.replace("${" + i + "}", params[i]);
            }
        }
        msgReqDTO.setContent(contentBody);
    }
}
