package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.audit.api.TicketApi;
import com.weifu.srm.audit.response.TicketSimpleRespDTO;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.CommonStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.SupplierPotentialToQualifiedQueryReqDTO;
import com.weifu.srm.supplier.response.SupplierPotentialToQualifiedDetailRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierPotentialToQualifiedDetailBiz {

    private final SupplierPotentialToQualifiedRecordMapperService potentialToQualifiedRecordMapperService;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService supplierAdmissionCategoryRecordMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final TicketApi ticketApi;
    private final LocaleMessage localeMessage;

    public SupplierPotentialToQualifiedDetailRespDTO queryDetail(SupplierPotentialToQualifiedQueryReqDTO req) {
        if (StringUtils.isBlank(req.getAdmissionNo()) && StringUtils.isBlank(req.getPcNo())) {
            log.error("准入单号与潜在转合格申请单号不能同时为空");
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        SupplierPotentialToQualifiedRecordPO supplierPotentialToQualifiedRecordPO = null;
        if (StringUtils.isNotBlank(req.getAdmissionNo())) {
            // 查询是否存在审批中的申请
            supplierPotentialToQualifiedRecordPO = potentialToQualifiedRecordMapperService.lambdaQuery()
                    .eq(SupplierPotentialToQualifiedRecordPO::getAdmissionNo, req.getAdmissionNo())
                    .in(SupplierPotentialToQualifiedRecordPO::getApplyStatus, CommonStatusEnum.DRAFT.getCode(), CommonStatusEnum.AUDIT_STATUS.getCode())
                    .one();
        }
        if (StringUtils.isNotBlank(req.getPcNo())) {
            // 查询是否存在审批中的申请
            supplierPotentialToQualifiedRecordPO = potentialToQualifiedRecordMapperService.lambdaQuery()
                    .eq(SupplierPotentialToQualifiedRecordPO::getBusinessNo, req.getPcNo())
                    .one();
        }
        SupplierPotentialToQualifiedDetailRespDTO result = new SupplierPotentialToQualifiedDetailRespDTO();
        // 框架协议 +  价格协议数据
        result.setAgreementName("框架协议/价格协议");
        result.setAgreementType("");
        result.setAgreementNo("");
        String admissionNo = null;
        if (null == supplierPotentialToQualifiedRecordPO) {
            admissionNo = req.getAdmissionNo();
        } else {
            admissionNo = supplierPotentialToQualifiedRecordPO.getAdmissionNo();
        }
        // 查询准入表数据
        SupplierAdmissionRecordPO supplierAdmissionRecordPO = supplierAdmissionRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionRecordPO::getAdmissionNo, admissionNo)
                .one();
        checkAdmissionRecord(supplierAdmissionRecordPO, result);
        // 没有保存的数据，走准入列表查询供应商数据，品类等相关数据逻辑
        if (null == supplierPotentialToQualifiedRecordPO) {
            result.setAdmissionNo(req.getAdmissionNo());
            result.setAdmissionCompleteTime(supplierAdmissionRecordPO.getAdmissionCompleteTime());
            // 查询供应商主表的数据
            SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                    .eq(SupplierBasicInfoPO::getId, supplierAdmissionRecordPO.getSupplierBasicMsgId())
                    .one();
            if (ObjectUtil.isNotNull(supplierBasicInfoPO)) {
                result.setSupplierName(supplierBasicInfoPO.getSupplierName());
                result.setSapSupplierCode(supplierBasicInfoPO.getSapSupplierCode());
            }
            // 查询品类表的数据
            LambdaQueryWrapper<SupplierAdmissionCategoryRecordPO> supplierCategoryRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierCategoryRecordPOLambdaQueryWrapper.eq(SupplierAdmissionCategoryRecordPO::getInvitationNo, supplierAdmissionRecordPO.getInvitationNo());
            supplierCategoryRecordPOLambdaQueryWrapper.eq(SupplierAdmissionCategoryRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
            List<SupplierAdmissionCategoryRecordPO> categoryRecordPOList = supplierAdmissionCategoryRecordMapperService.list(supplierCategoryRecordPOLambdaQueryWrapper);
            if (CollUtil.isNotEmpty(categoryRecordPOList)) {
                result.setAdmissionCategory(StringUtils.join(categoryRecordPOList.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryName).collect(Collectors.toList()), "、"));
            }
            result.setSubmitStatus(YesOrNoEnum.NO.getCode());
        } else {
            result.setAdmissionNo(supplierPotentialToQualifiedRecordPO.getAdmissionNo());
            result.setAdmissionCompleteTime(supplierPotentialToQualifiedRecordPO.getAdmissionCompleteTime());
            result.setSupplierName(supplierPotentialToQualifiedRecordPO.getSupplierName());
            result.setPcNo(supplierPotentialToQualifiedRecordPO.getBusinessNo());
            result.setSapSupplierCode(supplierPotentialToQualifiedRecordPO.getSapSupplierCode());
            result.setAdmissionCategory(supplierPotentialToQualifiedRecordPO.getAdmissionCategory());
            result.setApplyDesc(supplierPotentialToQualifiedRecordPO.getApplyDesc());
            result.setApplyRemark(supplierPotentialToQualifiedRecordPO.getApplyRemark());
            result.setSubmitStatus(CommonStatusEnum.DRAFT.getCode().equals(supplierPotentialToQualifiedRecordPO.getApplyStatus()) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            // 查询附件相关信息
            List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.lambdaQuery()
                    .eq(AttachmentRecordPO::getBusinessNo, supplierPotentialToQualifiedRecordPO.getBusinessNo())
                    .eq(AttachmentRecordPO::getBusinessType, supplierPotentialToQualifiedRecordPO.getApplyAttachments())
                    .list();
            result.setAttachmentFileList(attachmentMessageConvert.toList(attachmentRecordPOList));
            result.setApplyStatus(supplierPotentialToQualifiedRecordPO.getApplyStatus());
        }
        return result;
    }

    private void checkAdmissionRecord(SupplierAdmissionRecordPO supplierAdmissionRecordPO, SupplierPotentialToQualifiedDetailRespDTO result) {
        if (ObjectUtils.isEmpty(supplierAdmissionRecordPO)) {
            return;
        }
        result.setInvitationNo(supplierAdmissionRecordPO.getInvitationNo());
        ArrayList<String> strings = new ArrayList<>();
        strings.add("TEMPORARY_SUPPLIER_ADMISSION");
        strings.add("BATCH_SUPPLIER_ADMISSION");
        strings.add("SAMPLE_SUPPLIER_ADMISSION");
        ApiResponse<List<TicketSimpleRespDTO>> listApiResponse = ticketApi.listTicket(strings, supplierAdmissionRecordPO.getAdmissionNo());
        log.info("queryDetail ==== > 查询工单号：{}", listApiResponse.getData());
        listApiResponse.getData().forEach(ticketSimpleRespDTO -> {
            if (supplierAdmissionRecordPO.getAdmissionType().equals("TMP_ADMISSION_TYPE")
                    && ticketSimpleRespDTO.getTicketType().equals("TEMPORARY_SUPPLIER_ADMISSION")) {
                result.setTicketNo(ticketSimpleRespDTO.getTicketNo());
            }
            if (supplierAdmissionRecordPO.getAdmissionType().equals("BATCH_PRODUCTION_ADMISSION_TYPE")
                    && ticketSimpleRespDTO.getTicketType().equals("BATCH_SUPPLIER_ADMISSION")) {
                result.setTicketNo(ticketSimpleRespDTO.getTicketNo());
            }
            if (supplierAdmissionRecordPO.getAdmissionType().equals("SAMPLE_ADMISSION_TYPE")
                    && ticketSimpleRespDTO.getTicketType().equals("SAMPLE_SUPPLIER_ADMISSION")) {
                result.setTicketNo(ticketSimpleRespDTO.getTicketNo());
            }
        });

    }

}
