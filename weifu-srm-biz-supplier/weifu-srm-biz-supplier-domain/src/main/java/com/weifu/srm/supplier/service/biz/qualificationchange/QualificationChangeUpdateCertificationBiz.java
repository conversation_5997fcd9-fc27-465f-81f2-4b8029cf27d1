package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.api.TodoListApi;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.enums.TodoClsEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.communication.request.todolist.CreateTodoListReqDTO;
import com.weifu.srm.communication.request.todolist.ListOnTodoReqDTO;
import com.weifu.srm.communication.response.todolist.TodoListListRespDTO;
import com.weifu.srm.composite.api.SystemParameterApi;
import com.weifu.srm.composite.enums.SystemParameterEnum;
import com.weifu.srm.composite.response.SystemParameterRespDTO;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeCertificationItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.QualificationChangeApplyPO;
import com.weifu.srm.supplier.repository.po.QualificationChangeCertificationItemPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.request.QualificationChangeUpdateCertificationReqDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.request.SysUserQueryReqDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeUpdateCertificationBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeCertificationItemMapperService qualificationChangeCertificationItemMapperService;
    private final LocaleMessage localeMessage;
    private final TransactionTemplate transactionTemplate;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final SysUserApi sysUserApi;
    private final SystemParameterApi systemParameterApi;
    private final TodoListApi todoListApi;
    private final MQServiceManager mqService;
    private final IdService idService;

    private static final String SUPPLIER_NAME = "${供应商名称}";
    private static final String SUPPLIER_CODE = "${供应商编码}";
    private static final String OPERATION_TYPE = "delete";

    public void updateCertification(QualificationChangeUpdateCertificationReqDTO reqDTO, String submitSource) {
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        if (reqDTO.getOperationType().equals(OPERATION_TYPE)) {
            List<QualificationChangeApplyPO> qualificationChangeApplyPOS = qualificationChangeApplyMapperService.lambdaQuery()
                    .eq(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.SUBMIT.getCode())
                    .like(QualificationChangeApplyPO::getChangeType, QualificationChangeTypeEnum.QUALITY_QUALIFICATION_DELETE.getCode())
                    .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            if (ObjectUtil.isNotEmpty(qualificationChangeApplyPOS)) {
                List<String> qualificationChangeNoS = qualificationChangeApplyPOS.stream().map(QualificationChangeApplyPO::getQualificationChangeNo).collect(Collectors.toList());
                List<QualificationChangeCertificationItemPO> qualificationChangeCertificationItemPOS = qualificationChangeCertificationItemMapperService.lambdaQuery()
                        .in(QualificationChangeCertificationItemPO::getQualificationChangeNo, qualificationChangeNoS)
                        .eq(QualificationChangeCertificationItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                        .list();
                List<Long> deleteCertificationIdS = qualificationChangeCertificationItemPOS.stream().map(QualificationChangeCertificationItemPO::getCertificationId).collect(Collectors.toList());
                if (deleteCertificationIdS.contains(reqDTO.getCertificationId())) {
                    throw new BizFailException("已有删除的数据，不可重复提交删除！");
                }
            }
        }
        String qualificationChangeNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.QUALIFICATION_CHANGE.getBizTypeCodee(),idService);
        // 需要存入资质变更申请表
        QualificationChangeApplyPO qualificationChangeApplyPO = new QualificationChangeApplyPO();
        qualificationChangeApplyPO.setQualificationChangeNo(qualificationChangeNo);
        qualificationChangeApplyPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        qualificationChangeApplyPO.setSupplierName(basicInfoPO.getSupplierName());
        qualificationChangeApplyPO.setSubmitSource(submitSource);
        qualificationChangeApplyPO.setChangeType(getChangeType(reqDTO));
        qualificationChangeApplyPO.setChangeStatus(QualificationChangeStatusEnum.SUBMIT.getCode());
        qualificationChangeApplyPO.setCreateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setCreateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setCreateTime(new Date());
        qualificationChangeApplyPO.setUpdateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setUpdateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setUpdateTime(new Date());
        qualificationChangeApplyPO.setIsDelete(YesOrNoEnum.NO.getCode());
        QualificationChangeCertificationItemPO updateCertificationPO = qualificationChangeConvert.toUpdateCertificationPO(reqDTO);
        updateCertificationPO.setQualificationChangeNo(qualificationChangeNo);
        updateCertificationPO.setCreateBy(reqDTO.getOperationUserId());
        updateCertificationPO.setCreateName(reqDTO.getOperationUser());
        updateCertificationPO.setCreateTime(new Date());
        updateCertificationPO.setUpdateBy(reqDTO.getOperationUserId());
        updateCertificationPO.setUpdateName(reqDTO.getOperationUser());
        updateCertificationPO.setUpdateTime(new Date());
        updateCertificationPO.setIsDelete(YesOrNoEnum.NO.getCode());
        transactionTemplate.execute(transactionStatus -> {
            // 存入数据到资质变更
            qualificationChangeCertificationItemMapperService.save(updateCertificationPO);
            // 存入数据到附件中
            if (reqDTO.getOperationType().equals("add")) {
                AttachmentRecordPO attachmentRecordInsertPO = attachmentMessageConvert.toPO(reqDTO.getCertificationAttachmentList().get(0));
                attachmentRecordInsertPO.setBusinessNo(updateCertificationPO.getId()+"");
                attachmentRecordInsertPO.setBusinessType(AttachmentBizTypeConstants.QUALIFICATION_CHANGE_CERTIFICATION_ADD);
                attachmentRecordInsertPO.setCreateBy(reqDTO.getOperationUserId());
                attachmentRecordInsertPO.setCreateName(reqDTO.getOperationUser());
                attachmentRecordInsertPO.setCreateTime(new Date());
                attachmentRecordInsertPO.setUpdateBy(reqDTO.getOperationUserId());
                attachmentRecordInsertPO.setUpdateName(reqDTO.getOperationUser());
                attachmentRecordInsertPO.setUpdateTime(new Date());
                attachmentRecordInsertPO.setIsDelete(YesOrNoEnum.NO.getCode());
                attachmentRecordMapperService.save(attachmentRecordInsertPO);
            }
            // 发送MQ给工单系统生成准入邀请审批工单
            sendNoticeAndCreateTicket(reqDTO, basicInfoPO, qualificationChangeApplyPO, qualificationChangeNo);
            // 存入数据到资质变更申请表中
            qualificationChangeApplyMapperService.save(qualificationChangeApplyPO);
            return null;
        });
    }
    private String getChangeType(QualificationChangeUpdateCertificationReqDTO reqDTO){
        if (reqDTO.getOperationType().equals("add")) {
            if (reqDTO.getHasQualityCertification() == 0) {
                return QualificationChangeTypeEnum.UN_QUALITY_QUALIFICATION_ADD.getCode();
            } else {
                return QualificationChangeTypeEnum.QUALITY_QUALIFICATION_ADD.getCode();
            }
        } else {
            if (reqDTO.getHasQualityCertification() == 0) {
                return QualificationChangeTypeEnum.UN_QUALITY_QUALIFICATION_DELETE.getCode();
            } else {
                return QualificationChangeTypeEnum.QUALITY_QUALIFICATION_DELETE.getCode();
            }
        }
    }

    private void sendNoticeAndCreateTicket(QualificationChangeUpdateCertificationReqDTO reqDTO,
                                           SupplierBasicInfoPO basicInfoPO,
                                           QualificationChangeApplyPO qualificationChangeApplyPO,
                                           String qualificationChangeNo){
        CreateTicketMQ createTicketMQ = null;
        if (reqDTO.getHasQualityCertification() == 1) {
            createTicketMQ = qulifityCreateTicketMQ(reqDTO, basicInfoPO, qualificationChangeNo);
            // 通知对应审批的人
            mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(qulifityBoxMessageMQ(qualificationChangeApplyPO, basicInfoPO)));
        } else {
            createTicketMQ = unqulifityCreateTicketMQ(reqDTO, basicInfoPO, qualificationChangeNo);
            // 通知对应审批的人
            mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(unqulifityBoxMessageMQ(qualificationChangeApplyPO, basicInfoPO)));
        }
        log.info("start send MQ to ticket service create ticket ={}",createTicketMQ);
        mqService.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
        log.info("end send MQ to ticket service create ticket ={}",createTicketMQ);
    }

    /**
     * 质量资质证书发送工单创建MQ
     */
    private CreateTicketMQ qulifityCreateTicketMQ(QualificationChangeUpdateCertificationReqDTO reqDTO, SupplierBasicInfoPO basicInfoPO, String qualificationChangeNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        List<CreateTicketMQ.ApprovalProcessVar> processVars = new ArrayList<>();
        CreateTicketMQ.ApprovalProcessVar jcVar = new CreateTicketMQ.ApprovalProcessVar();
        jcVar.setKey("GYSZLGCS");
        String domain = "";
        if (ObjectUtil.isNotNull(basicInfoPO.getSqeMainId())) {
            ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(basicInfoPO.getSqeMainId());
            domain = userDetails.getData().getDomain();
        } else {
            // 若存在无法找到该供应商主责SQE的情况，则让角色“主责SQE backup”进行审批
            ApiResponse<SystemParameterRespDTO> byParameterName = systemParameterApi.getByParameterName(SystemParameterEnum.MAIN_SQE_BACKUP.getCode());
            ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(Long.valueOf(byParameterName.getData().getParameterValue()));
            domain = userDetails.getData().getDomain();
            // 创建待办工单
            // 先查询是否有待办
            SysUserQueryReqDTO var1 = new SysUserQueryReqDTO();
            var1.setRoleIds(Arrays.asList("PEM_DIRECTOR"));
            ApiResponse<List<BaseSysUserRespDTO>> user = sysUserApi.findUser(var1);
            Long userId = user.getData().get(0).getId();
            ListOnTodoReqDTO listOnTodoReqDTO = new ListOnTodoReqDTO();
            listOnTodoReqDTO.setUserId(userId);
            ApiResponse<List<TodoListListRespDTO>> listApiResponse = todoListApi.listOnTodo(listOnTodoReqDTO);
            int i = 1;
            if (ObjectUtil.isNotEmpty(listApiResponse.getData())) {
                TodoListListRespDTO filter = listApiResponse.getData().stream().filter(todoListListRespDTO ->
                        todoListListRespDTO.getBusinessNo().equals(basicInfoPO.getSapSupplierCode())
                                && todoListListRespDTO.getBusinessType().equals(TodoClsEnum.SUPPLIER_AWAITING_SQE_ASSIGNMENT.getCode())
                ).findFirst().orElse(null);
                if (ObjectUtils.isNotEmpty(filter)){
                    i = 0;
                }
            }
            if (i == 1) {
                CreateTodoListReqDTO createTodoListReqDTO = new CreateTodoListReqDTO();
                createTodoListReqDTO.setBusinessNo(basicInfoPO.getSapSupplierCode());
                createTodoListReqDTO.setBusinessType(TodoClsEnum.SUPPLIER_AWAITING_SQE_ASSIGNMENT.getCode());
                // 用户ID
                createTodoListReqDTO.setUserId(userId);
                String content = TodoTemplateEnum.SUPPLIER_SET_SQE_TODO.getContent();
                content = content
                        .replace(SUPPLIER_NAME, basicInfoPO.getSupplierName())
                        .replace(SUPPLIER_CODE, basicInfoPO.getSapSupplierCode());
                // 消息内容
                createTodoListReqDTO.setContent(content);
                createTodoListReqDTO.setIconType(IconTypeEnum.GENERAL.getCode());
                // 发送代办
                ApiResponse<String> stringApiResponse = todoListApi.create(createTodoListReqDTO);
                log.info("创建待办结果 ==================》{}", stringApiResponse.getData());
            }
        }
        jcVar.setV(domain);
        processVars.add(jcVar);
        mq.setProcessVars(processVars);
        mq.setBusinessNo(qualificationChangeNo);
        mq.setTicketType(TicketTypeEnum.QUALITY_CERTIFICATE_INFO_ADJUSTMENT.getCode());
        mq.setSupplierId(reqDTO.getSupplierId());
        mq.setSubmitBy(reqDTO.getOperationUserId());
        mq.setSubmitName(reqDTO.getOperationUser());
        if (reqDTO.getOperationType().equals(OPERATION_TYPE)) {
            if (StringUtils.isNotEmpty(reqDTO.getSubmitDesc())) {
                mq.setSubmitDesc("删除说明：" + reqDTO.getSubmitDesc());
            } else {
                mq.setSubmitDesc(reqDTO.getSubmitDesc());
            }
        } else {
            mq.setSubmitDesc(reqDTO.getSubmitDesc());
        }
        return mq;
    }

    /**
     * 非质量资质证书发送工单创建MQ
     */
    private CreateTicketMQ unqulifityCreateTicketMQ(QualificationChangeUpdateCertificationReqDTO reqDTO, SupplierBasicInfoPO basicInfoPO, String qualificationChangeNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        List<CreateTicketMQ.ApprovalProcessVar> processVars = new ArrayList<>();
        CreateTicketMQ.ApprovalProcessVar jcVar = new CreateTicketMQ.ApprovalProcessVar();
        jcVar.setKey("PLCGGCS");
        String domain = "";
        if (ObjectUtil.isNotNull(basicInfoPO.getCpeMainId())) {
            ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(basicInfoPO.getCpeMainId());
            domain = userDetails.getData().getDomain();
        }
        jcVar.setV(domain);
        processVars.add(jcVar);
        mq.setProcessVars(processVars);
        mq.setBusinessNo(qualificationChangeNo);
        mq.setTicketType(TicketTypeEnum.NON_QUALITY_CERTIFICATE_INFO_ADJUSTMENT.getCode());
        mq.setSupplierId(reqDTO.getSupplierId());
        mq.setSubmitBy(reqDTO.getOperationUserId());
        mq.setSubmitName(reqDTO.getOperationUser());
        if (reqDTO.getOperationType().equals(OPERATION_TYPE)) {
            if (StringUtils.isNotEmpty(reqDTO.getSubmitDesc())) {
                mq.setSubmitDesc("删除说明：" + reqDTO.getSubmitDesc());
            } else {
                mq.setSubmitDesc(reqDTO.getSubmitDesc());
            }
        } else {
            mq.setSubmitDesc(reqDTO.getSubmitDesc());
        }
        return mq;
    }

    private List<CreateSiteMessageMQ> qulifityBoxMessageMQ(QualificationChangeApplyPO qualificationChangeApplyPO, SupplierBasicInfoPO basicInfoPO) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setBusinessNo(qualificationChangeApplyPO.getQualificationChangeNo());
        notice.setBusinessType(MessageClsEnum.SUPPLIER_QUALITY_CERTIFICATE_ADJUST_APPLY.getCode());
        if (ObjectUtil.isNotNull(basicInfoPO.getSqeMainId())) {
            ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(basicInfoPO.getSqeMainId());
            notice.setUserId(basicInfoPO.getSqeMainId());
            notice.setUserName(userDetails.getData().getUserName());
        } else {
            // 若存在无法找到该供应商主责SQE的情况，则让角色“主责SQE backup”进行审批
            ApiResponse<SystemParameterRespDTO> byParameterName = systemParameterApi.getByParameterName(SystemParameterEnum.MAIN_SQE_BACKUP.getCode());
            ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(Long.valueOf(byParameterName.getData().getParameterValue()));
            notice.setUserId(Long.valueOf(byParameterName.getData().getParameterValue()));
            notice.setUserName(userDetails.getData().getUserName());
        }
        notice.setIconType(IconTypeEnum.GENERAL.getCode());
        notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_QUALITY.getTitle());
        String content = NoticeTemplateEnum.QUALIFICATION_CHANGE_QUALITY.getContent();
        content = content
                .replace(SUPPLIER_NAME, qualificationChangeApplyPO.getSupplierName())
                .replace(SUPPLIER_CODE, qualificationChangeApplyPO.getSupplierCode());
        notice.setContent(content);
        result.add(notice);
        return result;
    }

    private List<CreateSiteMessageMQ> unqulifityBoxMessageMQ(QualificationChangeApplyPO qualificationChangeApplyPO, SupplierBasicInfoPO basicInfoPO) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
        notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
        notice.setBusinessNo(qualificationChangeApplyPO.getQualificationChangeNo());
        notice.setBusinessType(MessageClsEnum.SUPPLIER_NON_QUALITY_CERTIFICATE_ADJUST_APPLY.getCode());
        ApiResponse<SysUserDetailRespDTO> userDetails = sysUserApi.findUserDetails(basicInfoPO.getCpeMainId());
        notice.setUserId(basicInfoPO.getCpeMainId());
        notice.setUserName(userDetails.getData().getUserName());
        notice.setIconType(IconTypeEnum.GENERAL.getCode());
        notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_UN_QUALITY.getTitle());
        String content = NoticeTemplateEnum.QUALIFICATION_CHANGE_UN_QUALITY.getContent();
        content = content
                .replace(SUPPLIER_NAME, qualificationChangeApplyPO.getSupplierName())
                .replace(SUPPLIER_CODE, qualificationChangeApplyPO.getSupplierCode());
        notice.setContent(content);
        result.add(notice);
        return result;
    }

}
