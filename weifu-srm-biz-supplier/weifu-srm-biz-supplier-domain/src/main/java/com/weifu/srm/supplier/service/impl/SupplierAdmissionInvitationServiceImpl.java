package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.communication.mq.TodoStatusChangedMQ;
import com.weifu.srm.supplier.convert.SupplierAdmissionCategoryConvert;
import com.weifu.srm.supplier.manager.SupplierAdmissionInvitationManager;
import com.weifu.srm.supplier.mq.*;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.AdmissionInvitationStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.TmpSupplierLastTransactionPeriodResultPO;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.service.SupplierAdmissionInvitationService;
import com.weifu.srm.supplier.service.biz.*;
import com.weifu.srm.supplier.service.biz.admission.*;
import com.weifu.srm.user.mq.SupplierUserRegistrationResultMqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAdmissionInvitationServiceImpl implements SupplierAdmissionInvitationService {

    private final AdmissionInvitationCommitBiz invitationCommitBiz;
    private final SupplierAdmissionInvitationManager invitationManager;
    private final SupplierAdmissionInvitationInfoMapperService invitationMapperService;
    private final SupplierAdmissionCategoryConvert admissionCategoryConvert;
    private final AdmissionCategoryCompleteBiz admissionCategoryCompleteBiz;
    private final AdmissionTypeWithSupplierTypeBiz admissionTypeWithSupplierTypeBiz;
    private final DealDraftCMSBiz dealDraftCMSBiz;
    private final SupplierPotentialToQualifiedDetailBiz supplierPotentialToQualifiedDetailBiz;
    private final SupplierPotentialToQualifiedSaveOrUpdateBiz supplierPotentialToQualifiedSaveOrUpdateBiz;
    private final AdmissionInvitationContactUserQueryBiz admissionInvitationContactUserQueryBiz;
    private final UserRegisterCompleteBiz userRegisterCompleteBiz;
    private final AdmissionInvitationResendBiz admissionInvitationResendBiz;
    private final AdmissionInvitationHandleAuditBiz invitationHandleAuditBiz;
    private final SupplierPotentialToQualifiedHandleAuditBiz supplierPotentialToQualifiedHandleAuditBiz;
    private final LocaleMessage localeMessage;
    private final SupplierInfoWithCreditCodeBiz supplierInfoWithCreditCodeBiz;
    private final AdmissionInvitationSaveBiz admissionInvitationSaveBiz;
    private final AdmissionInvitationSearchBiz admissionInvitationSearchBiz;
    private final SupplierAdmissionUserRegisterHandleAuditBiz admissionUserRegisterHandleAuditBiz;
    private final SupplierAdmissionRegisterHandleAuditBiz admissionRegisterHandleAuditBiz;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;


    @Override
    public String commit(SupplierAdmissionInvitationReqDTO req) {
        return invitationCommitBiz.invitationCommit(req);
    }

    @Override
    public String save(SupplierAdmissionInvitationReqDTO req) {
        return admissionInvitationSaveBiz.invitationSave(req);
    }

    @Override
    public PageResponse<SupplierAdmissionInvitationSearchListRespDTO> searchList(SupplierAdmissionInvitationSearchListReqDTO req) {
        return admissionInvitationSearchBiz.searchList(req);
    }

    @Override
    public SupplierAdmissionInvitationDetailRespDTO searchDetailByInvitationNo(String invitationNo) {
        return invitationManager.getInvitationDetail(invitationNo);
    }

    @Override
    public List<String> searchSupplierNameWithNameKeyword(String supplierNameKeyword, Integer limit) {
        limit = ObjectUtils.isEmpty(limit) ? 20 : limit;
        List<String> invitationList = invitationMapperService.selectSupplierNameByKeyword(supplierNameKeyword, limit);
        List<SupplierBasicInfoPO> basicInfoPOList = supplierBasicInfoMapperService.lambdaQuery().like(SupplierBasicInfoPO::getSupplierName, supplierNameKeyword).last(" limit " + limit).list();
        if (CollectionUtils.isNotEmpty(basicInfoPOList)){
            List<String> names = basicInfoPOList.stream().map(SupplierBasicInfoPO::getSupplierName).collect(Collectors.toList());
            invitationList.addAll(names);
        }
        if (CollectionUtils.isNotEmpty(invitationList)){
            return invitationList.stream().distinct().sorted().collect(Collectors.toList());
        }
        return invitationList;
    }

    @Override
    public TmpSupplierContactUserMessageRespDTO searchSupplierContactUserWithSupplierName(String supplierName) {
        // 查询准入邀请表中的注册联系人信息
        return admissionInvitationContactUserQueryBiz.querySupplierContactUserBySupplierName(supplierName);
    }

    @Override
    public TmpSupplierLastTransactionPeriodRespDTO searchLastTransactionPeriod(String supplierName, List<String> categoryIds) {
        if (StringUtils.isBlank(supplierName)) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        TmpSupplierLastTransactionPeriodResultPO resultPO = invitationMapperService.queryLastTransactionPeriod(supplierName);
        if (ObjectUtils.isEmpty(resultPO)) {
            return new TmpSupplierLastTransactionPeriodRespDTO();
        }
        return admissionCategoryConvert.toDTO(resultPO);
    }

    @Override
    public List<AdmissionInvitationCategoryRespDTO> searchAdmissionCategory(Long userId, String supplierName,
                                                                            String admissionType, Integer isCenPurchase) {
        return admissionCategoryCompleteBiz.searchCompleteAdmissionCategory(userId, supplierName, admissionType, isCenPurchase);
    }

    @Override
    public AdmissionTypeMapSupplierTypeRespDTO searchAdmissionTypeMapSupplierType() {
        return admissionTypeWithSupplierTypeBiz.getAdmissionTypeMapSupplierType();
    }

    @Override
    public void deleteByInvitationNo(String invitationNo) {
        if (StringUtils.isBlank(invitationNo)) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        QueryWrapper<SupplierAdmissionInvitationRecordPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, invitationNo);
        boolean remove = invitationMapperService.remove(queryWrapper);
        if (!remove) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.DELETE_DATA_FAIL));
        }
    }

    @Override
    public void dealDraftCMS(TodoStatusChangedMQ mq) {
        dealDraftCMSBiz.handle(mq);
    }

    @Override
    public void handleSupplierUserRegisterComplete(SupplierUserRegistrationResultMqDTO mq) {
        userRegisterCompleteBiz.handleSupplierUserRegisterComplete(mq);
    }

    @Override
    public void resendAdmissionInvitation(String invitationNo) {
        admissionInvitationResendBiz.resendAdmissionInvitation(invitationNo);
    }

    @Override
    public SupplierInfoPartRespDTO getSupplierInformationByCreditCode(String creditCode) {
        return supplierInfoWithCreditCodeBiz.getSupplierInformationByCreditCode(creditCode);
    }

    @Override
    public SupplierPotentialToQualifiedDetailRespDTO potentialToQualifiedDetail(SupplierPotentialToQualifiedQueryReqDTO req) {
        return supplierPotentialToQualifiedDetailBiz.queryDetail(req);
    }

    @Override
    public void potentialToQualifiedSaveOrUpdate(SupplierPotentialToQualifiedDetailReqDTO supplierPotentialToQualifiedDetailReqDTO) {
        supplierPotentialToQualifiedSaveOrUpdateBiz.potentialToQualifiedSaveOrUpdate(supplierPotentialToQualifiedDetailReqDTO);
    }

    @Override
    public SupplierInfoPartRespDTO querySupplierBySupplierName(String supplierName) {
        SupplierInfoPartRespDTO result = new SupplierInfoPartRespDTO();
        List<SupplierAdmissionInvitationRecordPO> invitationRecordPOList = invitationMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getSupplierName, supplierName)
                .notIn(SupplierAdmissionInvitationRecordPO::getSupplierAdmissionInvitationStatus, AdmissionInvitationStatusEnum.INIT_STATUS.getCode(),
                        AdmissionInvitationStatusEnum.REJECT_STATUS.getCode())
                .list();
        if (CollectionUtils.isNotEmpty(invitationRecordPOList)) {
            result.setDomesticForeignRelationship(invitationRecordPOList.get(0).getDomesticForeignRelationship());
            result.setSupplierShortName(invitationRecordPOList.get(0).getSupplierShortName());
            return result;
        }
        // 邀请列表查询为空时，查询基础表(初始导入数据，在邀请表中没有初始数据)
        List<SupplierBasicInfoPO> basicInfoPOList = supplierBasicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getSupplierName, supplierName).list();
        if (CollectionUtils.isNotEmpty(basicInfoPOList)) {
            result.setDomesticForeignRelationship(basicInfoPOList.get(0).getIsOverseas());
            result.setSupplierShortName(basicInfoPOList.get(0).getSupplierShortName());
            return result;
        }
        return result;
    }

    @Override
    public void admissionInvitationHandleAudit(TicketStatusChangedMQ mq) {
        invitationHandleAuditBiz.handleAudit(mq);
    }

    @Override
    public void supplierPotentialToQualifiedHandleAudit(TicketStatusChangedMQ mq) {
        supplierPotentialToQualifiedHandleAuditBiz.handleAudit(mq);
    }

    @Override
    public void supplierAdmissionUserRegisterHandle(SupplierAdmissionUserRegisterCompletedMQ mq) {
        admissionUserRegisterHandleAuditBiz.handleAudit(mq);
    }

    @Override
    public void supplierAdmissionRegisterHandle(SupplierAdmissionRegisterCompletedMQ mq) {
        admissionRegisterHandleAuditBiz.handleAudit(mq);
    }

}
