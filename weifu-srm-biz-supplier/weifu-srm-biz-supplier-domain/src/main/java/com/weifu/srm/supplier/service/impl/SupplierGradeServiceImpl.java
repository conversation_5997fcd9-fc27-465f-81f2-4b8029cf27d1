package com.weifu.srm.supplier.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.masterdata.api.DictDataApi;
import com.weifu.srm.masterdata.response.DictDataShowResDTO;
import com.weifu.srm.supplier.constants.ServiceConstants;
import com.weifu.srm.supplier.convert.SupplierGradeConvert;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBlackListMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGreyListMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierBlackListPO;
import com.weifu.srm.supplier.repository.po.SupplierGreyListPO;
import com.weifu.srm.supplier.request.SupplierGradeAdjustReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeHistoryReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeReqDTO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.service.SupplierGradeService;
import com.weifu.srm.supplier.service.biz.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierGradeServiceImpl implements SupplierGradeService {

    private final SupplierGradeConvert supplierGradeConvert;
    private final DictDataApi dictDataApi;
    private final SupplierBasicInfoMapperService supplierBasicMsgMapperService;
    private final SupplierGradeSubmitGradeAdjustBiz supplierGradeSubmitGradeAdjustBiz;
    private final SupplierGradeQueryWorkDetailBiz supplierGradeQueryWorkDetailBiz;
    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;
    private final SupplierGradeImportGradeAdjustBiz supplierGradeImportGradeAdjustBiz;
    private final SupplierGradeExportHistoryListBiz supplierGradeExportHistoryListBiz;
    private final SupplierGradeHandleAuditBiz supplierGradeHandleAuditBiz;
    private final SupplierBlackListMapperService supplierBlackListMapperService;
    private final SupplierGreyListMapperService supplierGreyListMapperService;

    @Override
    public PageResponse<SupplierGradeRespDTO> queryList(SupplierGradeReqDTO supplierGradeReqDTO) {
        Page<SupplierBasicInfoPO> page = new Page<>(supplierGradeReqDTO.getPageNum(), supplierGradeReqDTO.getPageSize());
        SupplierBasicInfoPO supplierBasicMsgPO = supplierGradeConvert.toPo(supplierGradeReqDTO);
        Page<SupplierBasicInfoPO> supplierBasicMsgS = supplierBasicMsgMapperService.queryList(page, supplierBasicMsgPO);
        List<SupplierGradeRespDTO> result = supplierGradeConvert.toResult(supplierBasicMsgS.getRecords());
        // 分级类型
        ApiResponse<Map<String, List<DictDataShowResDTO>>> gradeTypeDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_GRADE_TYPE, LocaleContextHolder.getLocale().toString());
        // 供应商状态
        ApiResponse<Map<String, List<DictDataShowResDTO>>> gradeStatusDictResp = dictDataApi.getDictDataList(ServiceConstants.SUPPLIER_BASIC_STATUS, LocaleContextHolder.getLocale().toString());

        if (Boolean.TRUE.equals(gradeTypeDictResp.getSucc()) && Boolean.TRUE.equals(gradeStatusDictResp.getSucc())){
            Map<String, List<DictDataShowResDTO>> gradeTypeDicDataMap = gradeTypeDictResp.getData();
            List<DictDataShowResDTO> gradeTypeDictList = gradeTypeDicDataMap.get(ServiceConstants.SUPPLIER_GRADE_TYPE);
            Map<String, List<DictDataShowResDTO>> gradeStatusDicDataMap = gradeStatusDictResp.getData();
            List<DictDataShowResDTO> gradeStatusDictList = gradeStatusDicDataMap.get(ServiceConstants.SUPPLIER_BASIC_STATUS);
            result.forEach(r-> gradeTypeDictList.stream()
                    .filter(a -> Objects.equals(a.getDataValue(), r.getSupplierClassification()))
                    .findFirst().ifPresent(k->r.setSupplierClassification(k.getDataShowName())));
            result.forEach(r-> gradeStatusDictList.stream()
                    .filter(a -> Objects.equals(a.getDataValue(), r.getStatus()))
                    .findFirst().ifPresent(k->r.setStatus(k.getDataShowName())));
        }
        return PageResponse.toResult(supplierGradeReqDTO.getPageNum(), supplierGradeReqDTO.getPageSize(), supplierBasicMsgS.getTotal(), result);
    }

    @Override
    public List<SupplierGradeCheckCertificateRespDTO> checkCertificate(List<String> supplierCodes) {
        // 查询出供应商 code 对应的供应商信息
        List<SupplierBasicInfoPO> supplierBasicMsgPOS = supplierBasicMsgMapperService.queryCertificate(supplierCodes);
        List<SupplierGradeCheckCertificateRespDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(supplierBasicMsgPOS)) {
            return result;
        }
        List<SupplierGreyListPO> greyList = supplierGreyListMapperService.lambdaQuery().in(SupplierGreyListPO::getSupplierCode, supplierCodes).list();
        List<String> greyListCode = CollectionUtils.isEmpty(greyList)?Collections.emptyList():greyList.stream().map(SupplierGreyListPO::getSupplierCode).distinct().collect(Collectors.toList());
        List<SupplierBlackListPO> blackList = supplierBlackListMapperService.lambdaQuery().in(SupplierBlackListPO::getSupplierCode, supplierCodes).list();
        List<String> blackListCode = CollectionUtils.isEmpty(blackList)?Collections.emptyList():blackList.stream().map(SupplierBlackListPO::getSupplierCode).distinct().collect(Collectors.toList());
        return supplierBasicMsgPOS.stream().map(basicInfo -> {
            SupplierGradeCheckCertificateRespDTO checkResp = new SupplierGradeCheckCertificateRespDTO();
            checkResp.setSupplierCode(basicInfo.getSapSupplierCode());
            checkResp.setSupplierGradeBefore(basicInfo.getSupplierClassification());
            checkResp.setSupplierName(basicInfo.getSupplierName());
            checkResp.setSupplierStatus(basicInfo.getStatus());
            if (!basicInfo.getStatus().equals(SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode())) {
                checkResp.setSupplierCheckResult(YesOrNoEnum.NO.getCode());
                checkResp.setSupplierCheckResultDesc("供应商状态不是合格供应商");
                return checkResp;
            }
            if (greyListCode.contains(basicInfo.getSapSupplierCode())){
                checkResp.setSupplierCheckResult(YesOrNoEnum.NO.getCode());
                checkResp.setSupplierCheckResultDesc("供应商在灰名单内");
                return checkResp;
            }
            if (blackListCode.contains(basicInfo.getSapSupplierCode())){
                checkResp.setSupplierCheckResult(YesOrNoEnum.NO.getCode());
                checkResp.setSupplierCheckResultDesc("供应商在黑名单内");
                return checkResp;
            }
            checkResp.setSupplierCheckResult(YesOrNoEnum.YES.getCode());
            return checkResp;
        }).collect(Collectors.toList());
    }

    @Override
    public String submitGradeAdjust(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO) {
        return supplierGradeSubmitGradeAdjustBiz.submitGradeAdjust(supplierGradeAdjustReqDTO);
    }

    @Override
    public SupplierGradeWorkRespDTO queryWorkDetail(String applyNo) {
        return supplierGradeQueryWorkDetailBiz.queryWorkDetail(applyNo);
    }

    @Override
    public PageResponse<SupplierGradeHistoryRespDTO> queryHistoryList(SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO) {
        Page<SupplierGradeHistoryReqDTO> page = new Page<>(supplierGradeHistoryReqDTO.getPageNum(), supplierGradeHistoryReqDTO.getPageSize());
        Page<SupplierGradeHistoryRespDTO> supplierGradeHistoryRespDTOPage = supplierGradeAdjustApplyItemMapperService.queryHistoryList(page, supplierGradeHistoryReqDTO);
        supplierGradeHistoryRespDTOPage.getRecords().forEach(supplierGradeHistoryRespDTO -> {
            if (supplierGradeHistoryRespDTO.getSupplierGradeBefore().equals(supplierGradeHistoryRespDTO.getSupplierGradeAfter())) {
                supplierGradeHistoryRespDTO.setIsChange(YesOrNoEnum.NO.getCode());
            } else {
                supplierGradeHistoryRespDTO.setIsChange(YesOrNoEnum.YES.getCode());
            }
        });
        return PageResponse.toResult(supplierGradeHistoryReqDTO.getPageNum(), supplierGradeHistoryReqDTO.getPageSize(), supplierGradeHistoryRespDTOPage.getTotal(), supplierGradeHistoryRespDTOPage.getRecords());
    }

    @Override
    public SupplierGradeImportRespDTO importGradeAdjust(MultipartFile file) {
        return supplierGradeImportGradeAdjustBiz.importGradeAdjust(file);
    }

    @Override
    public void exportHistoryList(HttpServletResponse response, SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO)  {
        supplierGradeExportHistoryListBiz.exportHistoryList(response, supplierGradeHistoryReqDTO);
    }

    @Override
    public void supplierGradeHandleAudit(TicketStatusChangedMQ mq) {
        supplierGradeHandleAuditBiz.handleAudit(mq);
    }

}
