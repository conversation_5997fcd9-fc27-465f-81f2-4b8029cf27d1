package com.weifu.srm.supplier.service.biz;


import com.weifu.srm.supplier.manager.remote.user.SysUserManager;
import com.weifu.srm.supplier.repository.atomicservice.UserSupplierRelationshipInfoMapperService;
import com.weifu.srm.supplier.repository.constants.SupplierUserRoleConstants;
import com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CheckUserWithSupplierRelationshipBiz {

    private final SysUserManager sysUserManager;
    private final UserSupplierRelationshipInfoMapperService supplierRelationshipInfoMapperService;

    public boolean checkUserWithSupplierRelationship(String supplierName, String phone, String email){
        BaseSysUserRespDTO userByPhone = sysUserManager.getSupplierUserByPhoneAndEmail(phone,email);
        if (userByPhone == null) {
            // 该手机号未在威孚供应商系统进行注册
            return true;
        }
        List<UserSupplierRelationshipInfoPO> list = supplierRelationshipInfoMapperService.lambdaQuery()
                .eq(UserSupplierRelationshipInfoPO::getSysUserId, userByPhone.getId())
                .eq(UserSupplierRelationshipInfoPO::getRoleId, SupplierUserRoleConstants.SUPPLIER_REGISTER_CONTACTS)
                .list();
        if (CollectionUtils.isEmpty(list)){
            // 该手机号已注册但并不是某个供应商的注册联系人
            return true;
        }
        List<String> supplierNames = list.stream().map(UserSupplierRelationshipInfoPO::getSupplierName).collect(Collectors.toList());
        if (!supplierNames.contains(supplierName)) {
            return false;
        }
        return true;
    }
}
