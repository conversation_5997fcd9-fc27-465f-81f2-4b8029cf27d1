package com.weifu.srm.supplier.service.biz.admission;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.masterdata.response.CategoryLevelRespDTO;
import com.weifu.srm.supplier.manager.SupplierAdmissionQueryByCodeManager;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionCategoryRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionRecordMapperService;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionCategoryRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionRecordPO;
import com.weifu.srm.supplier.response.AdmissionQuestionnaireQueryByCodeRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/18 12:48
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class SupplierAdmissionQueryByInvitationNoBiz {

    private final SupplierAdmissionQueryByCodeManager supplierAdmissionQueryByCodeManager;
    private final SupplierAdmissionRecordMapperService supplierAdmissionRecordMapperService;
    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierAdmissionCategoryRecordMapperService categoryRecordMapperService;
    private final CategoryManager categoryManager;


    public AdmissionQuestionnaireQueryByCodeRespDTO queryAdmissionByInvitationNo(String invitationNo) {
        SupplierAdmissionRecordPO admissionRecordPO = supplierAdmissionRecordMapperService.lambdaQuery()
                .eq(SupplierAdmissionRecordPO::getInvitationNo, invitationNo)
                .eq(SupplierAdmissionRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (!ObjectUtil.isNull(admissionRecordPO)) {
            List<SupplierAdmissionCategoryRecordPO> categories = categoryRecordMapperService.listByInvitationNo(admissionRecordPO.getInvitationNo());
            List<CategoryLevelRespDTO> categoryLevelRespDTOS = categoryManager.parentCategory(categories.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList()));
            AdmissionQuestionnaireQueryByCodeRespDTO respDTO = supplierAdmissionQueryByCodeManager.queryAdmissionByCode(admissionRecordPO);
            respDTO.setAdmissionCategoryNames(categoryLevelRespDTOS.stream().map(s ->  s.getLevelOneName().concat("-").concat(s.getLevelTwoName()).concat("-").concat(s.getLevelThirdName())).collect(Collectors.toList()));
            return respDTO;
        }
        SupplierAdmissionInvitationRecordPO invitationRecordPO = invitationInfoMapperService.getByInvitationNo(invitationNo);
        if (ObjectUtil.isNull(invitationRecordPO)) {
            return new AdmissionQuestionnaireQueryByCodeRespDTO();
        }
        List<SupplierAdmissionCategoryRecordPO> categories = categoryRecordMapperService.listByInvitationNo(invitationNo);
        List<CategoryLevelRespDTO> categoryLevelRespDTOS = categoryManager.parentCategory(categories.stream().map(SupplierAdmissionCategoryRecordPO::getCategoryCode).collect(Collectors.toList()));
        AdmissionQuestionnaireQueryByCodeRespDTO respDTO = getRespDTO(invitationNo, invitationRecordPO);
        respDTO.setAdmissionCategoryNames(categoryLevelRespDTOS.stream().map(s ->  s.getLevelOneName().concat("-").concat(s.getLevelTwoName()).concat("-").concat(s.getLevelThirdName())).collect(Collectors.toList()));
        return respDTO;
    }

    private static AdmissionQuestionnaireQueryByCodeRespDTO getRespDTO(String invitationNo,
                                                                       SupplierAdmissionInvitationRecordPO invitationRecordPO) {
        AdmissionQuestionnaireQueryByCodeRespDTO respDTO = new AdmissionQuestionnaireQueryByCodeRespDTO();
        respDTO.setInvitationNo(invitationNo);
        respDTO.setQuestionnaireDesc(invitationRecordPO.getQuestionnaireDesc());
        respDTO.setSupplierName(invitationRecordPO.getSupplierName());
        respDTO.setQuestionnaireTemplateCode(invitationRecordPO.getQuestionnaireTemplateCode());
        respDTO.setAdmissionType(invitationRecordPO.getAdmissionType());
        respDTO.setSupplierType(invitationRecordPO.getSupplierType());
        respDTO.setAdmissionInvitationBy(String.valueOf(invitationRecordPO.getCreateBy()));
        respDTO.setAdmissionInvitationName(invitationRecordPO.getCreateName());
        respDTO.setSupplierBasicMsgId(invitationRecordPO.getSupplierBasicMsgId());
        return respDTO;
    }
}
