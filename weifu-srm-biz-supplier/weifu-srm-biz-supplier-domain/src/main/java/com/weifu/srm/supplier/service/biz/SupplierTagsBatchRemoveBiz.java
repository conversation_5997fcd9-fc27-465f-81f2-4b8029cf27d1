package com.weifu.srm.supplier.service.biz;

import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierTagsMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoListSearchPO;
import com.weifu.srm.supplier.request.SupplierTagsReqDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SupplierTagsBatchRemoveBiz {

    private final SupplierTagsMapperService supplierTagsMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final LocaleMessage localeMessage;

    public void batchRemoveSupplierTags(SupplierTagsReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getTagCodes())) {
            log.error("the request params can not be null ");
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        List<SupplierBasicInfoListSearchPO> listSupplier = supplierBasicInfoMapperService.queryList(reqDTO);
        if (CollectionUtils.isEmpty(listSupplier)) {
            log.info("can not match supplier to add Tags");
            return;
        }
        List<String> codes = listSupplier.stream().map(SupplierBasicInfoListSearchPO::getSapSupplierCode).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        supplierTagsMapperService.removeTags(codes,reqDTO.getTagCodes());
    }
}
