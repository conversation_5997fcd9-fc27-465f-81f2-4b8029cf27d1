package com.weifu.srm.supplier.service;


import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.SupplierContactQueryReqDTO;
import com.weifu.srm.supplier.request.SupplierFinanceContactQueryReqDTO;
import com.weifu.srm.supplier.response.SupplierContactAllRespDTO;
import com.weifu.srm.supplier.response.SupplierContactSupplierCodeRespDTO;
import com.weifu.srm.supplier.response.SupplierFinanceContactRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description SupplierContactInfoService 服务interface
 * @Version 1.0
 */
public interface SupplierContactInfoService {
    /**
     * 查询供应商联系人列表
     */
    List<SupplierContactAllRespDTO> listByQuery(SupplierContactQueryReqDTO reqDTO);

    /**
     * 根据供应商id和联系人类型查询供应商联系人
     */
    SupplierContactAllRespDTO getSupplierContactInfo(Long supplierBasicMsgId, String type);

    List<SupplierContactSupplierCodeRespDTO> listSupplierContactInfoBySupplierCodes(List<String> supplierCodes, String roleType);

    PageResponse<SupplierFinanceContactRespDTO> pageFinancialContact(SupplierFinanceContactQueryReqDTO param);
}
