package com.weifu.srm.supplier.service.impl;

import com.weifu.srm.supplier.request.SupplierTagsReqDTO;
import com.weifu.srm.supplier.service.SupplierTagService;
import com.weifu.srm.supplier.service.biz.SupplierTagsBatchRemoveBiz;
import com.weifu.srm.supplier.service.biz.SupplierTagsBatchSaveBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierTagServiceImpl implements SupplierTagService {

    private final SupplierTagsBatchSaveBiz supplierTagsBatchSaveBiz;
    private final SupplierTagsBatchRemoveBiz supplierTagsBatchRemoveBiz;

    @Override
    public void batchSaveSupplierTags(SupplierTagsReqDTO req) {
        supplierTagsBatchSaveBiz.batchSaveSupplierTags(req);
    }

    @Override
    public void batchRemoveSupplierTags(SupplierTagsReqDTO req) {
        supplierTagsBatchRemoveBiz.batchRemoveSupplierTags(req);
    }
}
