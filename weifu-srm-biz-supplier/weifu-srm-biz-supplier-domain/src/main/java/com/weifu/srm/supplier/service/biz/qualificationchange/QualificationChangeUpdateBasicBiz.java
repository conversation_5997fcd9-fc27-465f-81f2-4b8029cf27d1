package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.DateUtils;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.common.util.StringUtil;
import com.weifu.srm.segment.service.IdService;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.request.QualificationChangeUpdateBasicReqDTO;
import com.weifu.srm.supplier.util.BusinessNoGenerateUtil;
import com.weifu.srm.user.api.SysUserApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static com.weifu.srm.supplier.repository.constants.SupplierCommonConstants.DATE_FORMAT;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeUpdateBasicBiz {

    private static final String QUALIFICATION_CHANGE = "QC";

    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final SupplierBasicInfoMapperService basicInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final LocaleMessage localeMessage;
    private final IdService idService;
    private final TransactionTemplate transactionTemplate;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final QualificationChangeBasicItemMapperService qualificationChangeBasicItemMapperService;
    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final MQServiceManager mqService;

    public void updateBasic(QualificationChangeUpdateBasicReqDTO reqDTO, String submitSource) {
        // 查询基础表信息
        SupplierBasicInfoPO basicInfoPO = basicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getId, reqDTO.getSupplierId())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (basicInfoPO == null) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.QUERY_DATA_FAIL));
        }
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getChangeType, QualificationChangeTypeEnum.BASIC_UPDATE.getCode())
                .eq(QualificationChangeApplyPO::getSupplierCode, basicInfoPO.getSapSupplierCode())
                .eq(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.SUBMIT.getCode())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNotEmpty(changeApplyPO)) {
            throw new BizFailException("已经有基本信息变更的修改申请，不允许再次提交");
        }
        if (ObjectUtil.isNull(reqDTO)) {
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        QualificationChangeBasicItemPO updateBasicPO = qualificationChangeConvert.toUpdateBasicPO(reqDTO);
        updateBasicPO.setCreateBy(reqDTO.getOperationUserId());
        updateBasicPO.setCreateName(reqDTO.getOperationUser());
        updateBasicPO.setCreateTime(new Date());
        updateBasicPO.setUpdateBy(reqDTO.getOperationUserId());
        updateBasicPO.setUpdateName(reqDTO.getOperationUser());
        updateBasicPO.setUpdateTime(new Date());
        updateBasicPO.setIsDelete(YesOrNoEnum.NO.getCode());
        // 供应商基础信息
        checkUpdateBasicMessage(updateBasicPO, basicInfoPO);
        // 联系人信息
        checkUpdateContactMessage(updateBasicPO, basicInfoPO);
        // 邓白氏编码
        checkDunAndBradstreetCode(updateBasicPO,basicInfoPO);
        // 营业起始期限
        checkBusinessDate(updateBasicPO,basicInfoPO);
        // 校验附件是否修改
        String fileName = reqDTO.getBusinessLicenseAttachmentList().get(0).getFileName();
        AttachmentRecordPO attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, basicInfoPO.getRegisterNo())
                .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.BUSINESS_LICENSE)
                .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (ObjectUtil.isNull(attachmentRecordPO) || !attachmentRecordPO.getFileName().equals(fileName)) {
            updateBasicPO.setBusinessLicenseAttachmentBefore(basicInfoPO.getRegisterNo());
        }
        // 生成资质变更编号
        String qualificationChangeNo = BusinessNoGenerateUtil.getNextBusinessNo(SupplierBizEnum.QUALIFICATION_CHANGE.getBizTypeCodee(), idService);
        updateBasicPO.setQualificationChangeNo(qualificationChangeNo);
        // 还需要存入资质变更申请表
        QualificationChangeApplyPO qualificationChangeApplyPO = new QualificationChangeApplyPO();
        qualificationChangeApplyPO.setQualificationChangeNo(qualificationChangeNo);
        qualificationChangeApplyPO.setSupplierCode(basicInfoPO.getSapSupplierCode());
        qualificationChangeApplyPO.setSupplierName(basicInfoPO.getSupplierName());
        qualificationChangeApplyPO.setChangeType(QualificationChangeTypeEnum.BASIC_UPDATE.getCode());
        qualificationChangeApplyPO.setChangeStatus(QualificationChangeStatusEnum.SUBMIT.getCode());
        qualificationChangeApplyPO.setSubmitSource(submitSource);
        qualificationChangeApplyPO.setCreateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setCreateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setCreateTime(new Date());
        qualificationChangeApplyPO.setUpdateBy(reqDTO.getOperationUserId());
        qualificationChangeApplyPO.setUpdateName(reqDTO.getOperationUser());
        qualificationChangeApplyPO.setUpdateTime(new Date());
        qualificationChangeApplyPO.setIsDelete(YesOrNoEnum.NO.getCode());
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            // 存入数据到资质变更
            qualificationChangeBasicItemMapperService.save(updateBasicPO);
            // 存入数据到附件中
            if (ObjectUtil.isNull(attachmentRecordPO) || !attachmentRecordPO.getFileName().equals(fileName)) {
                AttachmentRecordPO attachmentRecordInsertPO = attachmentMessageConvert.toPO(reqDTO.getBusinessLicenseAttachmentList().get(0));
                attachmentRecordInsertPO.setBusinessNo(updateBasicPO.getId() + "");
                attachmentRecordInsertPO.setBusinessType(AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_BASIC);
                attachmentRecordInsertPO.setCreateBy(reqDTO.getOperationUserId());
                attachmentRecordInsertPO.setCreateName(reqDTO.getOperationUser());
                attachmentRecordInsertPO.setCreateTime(new Date());
                attachmentRecordInsertPO.setUpdateBy(reqDTO.getOperationUserId());
                attachmentRecordInsertPO.setUpdateName(reqDTO.getOperationUser());
                attachmentRecordInsertPO.setUpdateTime(new Date());
                attachmentRecordInsertPO.setIsDelete(YesOrNoEnum.NO.getCode());
                attachmentRecordMapperService.save(attachmentRecordInsertPO);
            }
            // 发送MQ给工单系统生成审批工单
            CreateTicketMQ createTicketMQ = boxCreateTicketMQ(reqDTO, qualificationChangeNo);
            mqService.sendMQ(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
            // 存入数据到资质变更申请表中
            qualificationChangeApplyMapperService.save(qualificationChangeApplyPO);
        });
    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(QualificationChangeUpdateBasicReqDTO reqDTO, String qualificationChangeNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(qualificationChangeNo);
        mq.setTicketType(TicketTypeEnum.BASIC_INFO_ADJUSTMENT.getCode());
        mq.setSupplierId(reqDTO.getSupplierId());
        mq.setSubmitBy(reqDTO.getOperationUserId());
        mq.setSubmitName(reqDTO.getOperationUser());
        return mq;
    }

    private void checkUpdateBasicMessage(QualificationChangeBasicItemPO updateBasicPO, SupplierBasicInfoPO basicInfoPO) {
        updateBasicPO.setSupplierNameChange(0);
        // 校验值是否修改过(供应商名称)
        if (!basicInfoPO.getSupplierName().equals(updateBasicPO.getSupplierName())) {
            updateBasicPO.setSupplierNameBefore(basicInfoPO.getSupplierName());
            updateBasicPO.setSupplierNameChange(1);
        }
        // 设置默认值
        updateBasicPO.setSupplierShortNameChange(0);
        // 校验值是否修改过(供应商简称)
        if (!basicInfoPO.getSupplierShortName().equals(updateBasicPO.getSupplierShortName())) {
            updateBasicPO.setSupplierShortNameBefore(basicInfoPO.getSupplierShortName());
            updateBasicPO.setSupplierShortNameChange(1);
        }
        // 设置默认值
        updateBasicPO.setOrganizationNatureChange(0);
        // 校验值是否修改过(机构性质)
        if (!StringUtils.equals(basicInfoPO.getOrganizationNature(),(updateBasicPO.getOrganizationNature()))) {
            updateBasicPO.setOrganizationNatureBefore(basicInfoPO.getOrganizationNature());
            updateBasicPO.setOrganizationNatureChange(1);
        }
        // 设置默认值
        updateBasicPO.setEnterpriseTypeChange(0);
        // 校验值是否修改过(企业类型)
        if (!StringUtils.equals(basicInfoPO.getEnterpriseType(), updateBasicPO.getEnterpriseType())) {
            updateBasicPO.setEnterpriseTypeBefore(basicInfoPO.getEnterpriseType());
            updateBasicPO.setEnterpriseTypeChange(1);
        }
        // 设置默认值
        updateBasicPO.setRegisteredAmtChange(0);
        // 校验值是否修改过(注册资金（万）)
        if (Objects.nonNull(basicInfoPO.getRegisteredAmt()) || Objects.nonNull(updateBasicPO.getRegisteredAmt())) {
            if(!NumberUtil.equals(basicInfoPO.getRegisteredAmt(),updateBasicPO.getRegisteredAmt())) {
                updateBasicPO.setRegisteredAmtBefore(basicInfoPO.getRegisteredAmt());
                updateBasicPO.setRegisteredAmtChange(1);
            }
        }
        // 设置默认值
        updateBasicPO.setRegisteredCurrencyChange(0);
        // 校验值是否修改过(注册币种)
        if (!basicInfoPO.getRegisteredCurrency().equals(updateBasicPO.getRegisteredCurrency())) {
            updateBasicPO.setRegisteredCurrencyBefore(basicInfoPO.getRegisteredCurrency());
            updateBasicPO.setRegisteredCurrencyChange(1);
        }
        // 设置默认值
        updateBasicPO.setEstablishmentDateChange(0);
        // 校验值是否修改过(成立日期)
        if (!DateUtils.format(basicInfoPO.getEstablishmentDate(), DATE_FORMAT).equals(DateUtils.format(updateBasicPO.getEstablishmentDate(), DATE_FORMAT))) {
            updateBasicPO.setEstablishmentDateBefore(basicInfoPO.getEstablishmentDate());
            updateBasicPO.setEstablishmentDateChange(1);
        }
        // 设置默认值
        updateBasicPO.setRegisteredAddressChange(0);
        // 校验值是否修改过(注册地址)
        if (!basicInfoPO.getRegisteredAddress().equals(updateBasicPO.getRegisteredAddress())) {
            updateBasicPO.setRegisteredAddressBefore(basicInfoPO.getRegisteredAddress());
            updateBasicPO.setRegisteredAddressChange(1);
        }
        // 设置默认值
        updateBasicPO.setCompanyZipCodeChange(0);
        // 校验值是否修改过(公司邮编)
        if (!basicInfoPO.getCompanyZipCode().equals(updateBasicPO.getCompanyZipCode())) {
            updateBasicPO.setCompanyZipCodeBefore(basicInfoPO.getCompanyZipCode());
            updateBasicPO.setCompanyZipCodeChange(1);
        }
        // 设置默认值
        updateBasicPO.setProductionAddressChange(0);
        // 校验值是否修改过(生产地址)
        if (!basicInfoPO.getProductionAddress().equals(updateBasicPO.getProductionAddress())) {
            updateBasicPO.setProductionAddressBefore(basicInfoPO.getProductionAddress());
            updateBasicPO.setProductionAddressChange(1);
        }
        // 设置默认值
        updateBasicPO.setBusinessScopeChange(0);
        // 校验值是否修改过(经营范围)
        if (StringUtil.isNullOrEmpty(basicInfoPO.getBusinessScope()) || !basicInfoPO.getBusinessScope().equals(updateBasicPO.getBusinessScope())) {
            updateBasicPO.setBusinessScopeBefore(basicInfoPO.getBusinessScope());
            updateBasicPO.setBusinessScopeChange(1);
        }
        // 设置默认值
        updateBasicPO.setIsOverseasChange(0);
        // 校验值是否修改过(是否境外(0,1))
        if (!Objects.equals(basicInfoPO.getIsOverseas(), updateBasicPO.getIsOverseas())) {
            updateBasicPO.setIsOverseasBefore(basicInfoPO.getIsOverseas());
            updateBasicPO.setIsOverseasChange(1);
        }

    }
    private void checkBusinessDate(QualificationChangeBasicItemPO updateBasicPO, SupplierBasicInfoPO basicInfoPO){
        // 设置默认值
        updateBasicPO.setBusinessPeriodStartChange(0);
        updateBasicPO.setBusinessPeriodEndChange(0);
        // 校验值是否修改过(营业期限起始)
        if (ObjectUtil.isNull(basicInfoPO.getBusinessPeriodStart())
                || !DateUtils.format(basicInfoPO.getBusinessPeriodStart(), DATE_FORMAT).equals(DateUtils.format(updateBasicPO.getBusinessPeriodStart(), DATE_FORMAT))
                || ObjectUtil.isNull(basicInfoPO.getBusinessPeriodEnd())
                || !DateUtils.format(basicInfoPO.getBusinessPeriodEnd(), DATE_FORMAT).equals(DateUtils.format(updateBasicPO.getBusinessPeriodEnd(), DATE_FORMAT))) {
            updateBasicPO.setBusinessPeriodStartBefore(basicInfoPO.getBusinessPeriodStart());
            updateBasicPO.setBusinessPeriodStartChange(1);
            updateBasicPO.setBusinessPeriodEndBefore(basicInfoPO.getBusinessPeriodEnd());
            updateBasicPO.setBusinessPeriodEndChange(1);
        }
    }

    private void checkDunAndBradstreetCode(QualificationChangeBasicItemPO updateBasicPO, SupplierBasicInfoPO basicInfoPO){
        // 设置默认值
        updateBasicPO.setDunAndBradstreetCodeChange(0);
        // 校验值是否修改过(邓白氏编码D-U-N-S)
        if (StringUtil.isNullOrEmpty(basicInfoPO.getDunAndBradstreetCode())) {
            if (!StringUtil.isNullOrEmpty(updateBasicPO.getDunAndBradstreetCode())) {
                updateBasicPO.setDunAndBradstreetCodeBefore(basicInfoPO.getDunAndBradstreetCode());
                updateBasicPO.setDunAndBradstreetCodeChange(1);
            }
        } else if (StringUtil.isNullOrEmpty(updateBasicPO.getDunAndBradstreetCode())) {
            if (!StringUtil.isNullOrEmpty(basicInfoPO.getDunAndBradstreetCode())) {
                updateBasicPO.setDunAndBradstreetCodeBefore(basicInfoPO.getDunAndBradstreetCode());
                updateBasicPO.setDunAndBradstreetCodeChange(1);
            }
        } else {
            if (!basicInfoPO.getDunAndBradstreetCode().equals(updateBasicPO.getDunAndBradstreetCode())) {
                updateBasicPO.setDunAndBradstreetCodeBefore(basicInfoPO.getDunAndBradstreetCode());
                updateBasicPO.setDunAndBradstreetCodeChange(1);
            }
        }
    }

    private void checkUpdateContactMessage(QualificationChangeBasicItemPO updateBasicPO, SupplierBasicInfoPO basicInfoPO) {
        // 查询供应商联系信息表信息
        SupplierContactInfoPO supplierContactInfoPO = supplierContactInfoMapperService.lambdaQuery()
                .eq(SupplierContactInfoPO::getSupplierBasicMsgId, basicInfoPO.getId())
                .eq(SupplierContactInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 设置默认值
        updateBasicPO.setCompanyPhoneChange(0);
        updateBasicPO.setCompanyFaxChange(0);
        if (ObjectUtil.isNull(supplierContactInfoPO)) {
            return;
        }
        // 校验值是否修改过(公司固定电话)
        if (StringUtil.isNullOrEmpty(supplierContactInfoPO.getCompanyPhone()) || !supplierContactInfoPO.getCompanyPhone().equals(updateBasicPO.getCompanyPhone())) {
            updateBasicPO.setCompanyPhoneBefore(supplierContactInfoPO.getCompanyPhone());
            updateBasicPO.setCompanyPhoneChange(1);
        }
        // 校验值是否修改过(公司传真号码)
        if (StringUtil.isNullOrEmpty(supplierContactInfoPO.getCompanyFax())) {
            if (!StringUtil.isNullOrEmpty(updateBasicPO.getCompanyFax())) {
                updateBasicPO.setCompanyFaxBefore(supplierContactInfoPO.getCompanyFax());
                updateBasicPO.setCompanyFaxChange(1);
            }
        } else if (StringUtil.isNullOrEmpty(updateBasicPO.getCompanyFax())) {
            if (!StringUtil.isNullOrEmpty(supplierContactInfoPO.getCompanyFax())) {
                updateBasicPO.setCompanyFaxBefore(supplierContactInfoPO.getCompanyFax());
                updateBasicPO.setCompanyFaxChange(1);
            }
        } else {
            if (!supplierContactInfoPO.getCompanyFax().equals(updateBasicPO.getCompanyFax())) {
                updateBasicPO.setCompanyFaxBefore(supplierContactInfoPO.getCompanyFax());
                updateBasicPO.setCompanyFaxChange(1);
            }
        }

    }

}
