package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.request.SupplierGradeAdjustReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeHistoryReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeReqDTO;
import com.weifu.srm.supplier.response.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 供应商分级服务
 */
public interface SupplierGradeService {

    PageResponse<SupplierGradeRespDTO> queryList(SupplierGradeReqDTO supplierGradeReqDTO);

    List<SupplierGradeCheckCertificateRespDTO> checkCertificate(List<String> supplierCodes);

    String submitGradeAdjust(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO);

    SupplierGradeWorkRespDTO queryWorkDetail(String applyNo);

    PageResponse<SupplierGradeHistoryRespDTO> queryHistoryList(SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO);

    SupplierGradeImportRespDTO importGradeAdjust(MultipartFile file);

    void exportHistoryList(HttpServletResponse response, SupplierGradeHistoryReqDTO supplierGradeHistoryReqDTO);

    void supplierGradeHandleAudit(TicketStatusChangedMQ mq);

}
