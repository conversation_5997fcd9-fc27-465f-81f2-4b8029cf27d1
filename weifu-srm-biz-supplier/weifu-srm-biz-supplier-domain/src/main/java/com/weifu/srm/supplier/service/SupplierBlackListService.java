package com.weifu.srm.supplier.service;

import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.supplier.mq.SupplierBlackListMQ;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyCancelReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyListReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListApplyReqDTO;
import com.weifu.srm.supplier.request.black.SupplierBlackListSearchReqDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListApplyDetailRespDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListApplyListRespDTO;
import com.weifu.srm.supplier.response.black.SupplierBlackListItemRespDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SupplierBlackListService {


    PageResponse<SupplierBlackListApplyListRespDTO> queryListPage(@RequestBody SupplierBlackListApplyListReqDTO req);

    String apply(@RequestBody SupplierBlackListApplyReqDTO req);

    String applyCancel(@RequestBody SupplierBlackListApplyCancelReqDTO req);

   SupplierBlackListApplyDetailRespDTO queryDetail(@RequestParam("applyNo") String applyNo);

    List<SupplierBlackListItemRespDTO> cancelBlackListCollection(@RequestParam("list")List<Integer> limitIds);

    List<SupplierBlackListApplyListRespDTO> exportBlackListApplyList(SupplierBlackListApplyListReqDTO req);

    void handleBlackListApply(TicketStatusChangedMQ ticketInfo);

    void handleBlackListApplyCancel(TicketStatusChangedMQ ticketInfo);

    void scheduleCheckBlackList();

    void scheduleCheckBlackListSuggestOutTime();

    void handleSupplierBlackListForSap(SupplierBlackListMQ mq);

    List<String> queryBlackListBySupplierCodes(SupplierBlackListSearchReqDTO req);

}
