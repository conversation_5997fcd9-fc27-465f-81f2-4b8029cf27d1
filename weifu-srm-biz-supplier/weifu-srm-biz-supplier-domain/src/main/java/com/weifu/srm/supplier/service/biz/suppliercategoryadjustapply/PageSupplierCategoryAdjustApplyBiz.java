package com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyTypeEnum;
import com.weifu.srm.supplier.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.manager.remote.user.DataPermissionManager;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.mapper.SupplierCategoryAdjustApplyMapper;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryAdjustApplyPO;
import com.weifu.srm.supplier.request.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageReqDTO;
import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryAdjustApplyPageRespDTO;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.constants.InternalUserRoleConstants;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO;
import com.weifu.srm.user.response.SysUserDetailRespDTO.SysUserDetailRoleRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分页查询供应商品类关系申请
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PageSupplierCategoryAdjustApplyBiz {

    private final SupplierCategoryAdjustApplyMapper supplierCategoryAdjustApplyMapper;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SysUserApi sysUserApi;
    private final CategoryManager categoryManager;
    private final DataPermissionManager dataPermissionManager;

    public PageResponse<SupplierCategoryAdjustApplyPageRespDTO> execute(SupplierCategoryAdjustApplyPageReqDTO req) {
        DataPermissionRespDTO dataPermission = dataPermissionManager.queryUserDataPermission(req.getUserId(), "PC_INTERNAL_PAGE_SUPPLIER_CATEGORY_QUALIFY_ADJUST");
        if (dataPermission.isNo()) {
            log.info("分页查询供应商品类关系申请，无数据权限，userId={}", req.getUserId());
            return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, new ArrayList<>());
        }

        List<String> categoryCodes = null;
        if (req.getUserId() != null) {
            categoryCodes = queryCategorys(req.getUserId());
            log.info("分页查询供应商品类关系申请，用户可查看的品类列表，userId={}，categoryCodes={}", req.getUserId(), categoryCodes);

            // 没有任何可查看的品类
            if (categoryCodes != null && categoryCodes.size() ==0) {
                return PageResponse.toResult(req.getPageNum(), req.getPageSize(), 0L, new ArrayList<>());
            }
        }

        Page<SupplierCategoryAdjustApplyPO> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<SupplierCategoryAdjustApplyPageRespDTO> iPage = supplierCategoryAdjustApplyMapper.page(page, dataPermission.getKeys(), req);
        PageResponse<SupplierCategoryAdjustApplyPageRespDTO> result = PageResponse.toResult(
                req.getPageNum(), req.getPageSize(), iPage.getTotal(), iPage.getRecords());
        if (CollectionUtils.isEmpty(iPage.getRecords())) {
            return result;
        }

        List<String> businessNos = iPage.getRecords().stream().map(SupplierCategoryAdjustApplyPageRespDTO::getApplyNo)
                .collect(Collectors.toList());
        List<AttachmentRecordPO> attachmentRecords = attachmentRecordMapperService.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessNo, businessNos)
                .in(AttachmentRecordPO::getBusinessType, List.of(
                        AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_COMMON,
                        AttachmentBizTypeConstants.SUPPLIER_CATEGORY_ADJUST_MEETING_RESOLUTION
                    ))
                .list();
        Map<String, List<AttachmentRecordPO>> attachmentMap = attachmentRecords.stream().collect(
                Collectors.groupingBy(AttachmentRecordPO::getBusinessNo));
        for (SupplierCategoryAdjustApplyPageRespDTO apply:result.getList()) {
            apply.setApplyStatusName(SupplierCategoryAdjustApplyStatusEnum.getName(apply.getApplyStatus(), LocaleContextHolder.getLocale()));
            apply.setApplyTypeName(SupplierCategoryAdjustApplyTypeEnum.getName(apply.getApplyType(), LocaleContextHolder.getLocale()));
            List<AttachmentRecordPO> subAttachmentRecords = attachmentMap.get(apply.getApplyNo());
            if (CollectionUtils.isEmpty(subAttachmentRecords)) {
                continue;
            }
            apply.setAttachments(BeanUtil.copyToList(subAttachmentRecords, AttachmentMessageRespDTO.class));
        }
        return result;
    }

    /**
     * 数据权限-可查看的品类
     * 1.null表示所有品类
     * 2.size=0表示没有可查看品类
     * 3.size!=0表示有可查看品类
     */
    private List<String> queryCategorys(Long userId) {
        ApiResponse<SysUserDetailRespDTO> userRes = sysUserApi.findUserDetails(userId);
        if (!userRes.getSucc()) {
            log.error("分页查询供应商品类关系申请时，查询用户角色失败，userRes={}", JacksonUtil.bean2Json(userRes));
            throw new BizFailException("查询用户角色失败");
        }

        SysUserDetailRespDTO user = userRes.getData();
        List<SysUserDetailRoleRespDTO> roleList = ObjectUtil.defaultIfNull(user.getRoleList(), new ArrayList<>());
        Set<String> roleIds = roleList.stream().map(SysUserDetailRoleRespDTO::getRoleId).collect(Collectors.toSet());
        // 采购部长拥有所有品类权限
        if (roleIds.contains(InternalUserRoleConstants.PURCHASE_MINISTER)) {
            return null;
        }
        // 品类工程师、品类组长，可以查看自己负责的品类
        else if (roleIds.contains(InternalUserRoleConstants.CPE)
                || roleIds.contains(InternalUserRoleConstants.CPE_MASTER)) {
            List<CategoryEngineerResultDTO> categoryEngineers = categoryManager.queryCategoryEngineerByUserId(userId);
            if (CollectionUtils.isEmpty(categoryEngineers)) {
                return new ArrayList<>();
            }
            return categoryEngineers.stream().map(CategoryEngineerResultDTO::getCategoryCode).collect(Collectors.toList());
        }

        // 其他角色无权限
        return new ArrayList<>();
    }

}
