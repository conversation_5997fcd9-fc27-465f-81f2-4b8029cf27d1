package com.weifu.srm.supplier.service.biz.qualificationchange;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.supplier.repository.atomicservice.SupplierContactInfoMapperService;
import com.weifu.srm.supplier.repository.enums.QualificationChangeUserTypeEnum;
import com.weifu.srm.supplier.repository.po.SupplierContactInfoPO;
import com.weifu.srm.supplier.request.QualificationChangeUpdateUserReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class QualificationChangeUpdateUserBiz {

    private final SupplierContactInfoMapperService supplierContactInfoMapperService;

    public void updateUser(QualificationChangeUpdateUserReqDTO qualificationChangeUpdateUserReqDTO) {
        List<QualificationChangeUpdateUserReqDTO.QualificationChangeUpdateUserItemReqDTO> reqDTOS = qualificationChangeUpdateUserReqDTO.getQualificationChangeUpdateUserItemReqDTOList();
        checkContactRestrict(reqDTOS);
        LambdaUpdateWrapper<SupplierContactInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SupplierContactInfoPO::getSupplierBasicMsgId, qualificationChangeUpdateUserReqDTO.getSupplierId());
        reqDTOS.forEach(reqDTO -> {
            if (reqDTO.getType().equals(QualificationChangeUserTypeEnum.FINANCE_CONTACT.getCode())) {
                updateWrapper
                        .set(SupplierContactInfoPO::getFinanceContactName, reqDTO.getName())
                        .set(SupplierContactInfoPO::getFinanceContactPhone, reqDTO.getPhone())
                        .set(SupplierContactInfoPO::getFinanceContactEmail, reqDTO.getEmail())
                        .set(SupplierContactInfoPO::getFinanceContactPosition, reqDTO.getPosition());
            }
            if (reqDTO.getType().equals(QualificationChangeUserTypeEnum.LEGAL_PERSON.getCode())) {
                updateWrapper
                        .set(SupplierContactInfoPO::getLegalPersonName, reqDTO.getName())
                        .set(SupplierContactInfoPO::getLegalPersonPhone, reqDTO.getPhone())
                        .set(SupplierContactInfoPO::getLegalPersonEmail, reqDTO.getEmail());
            }
            if (reqDTO.getType().equals(QualificationChangeUserTypeEnum.REGISTER_CONTACT.getCode())) {
                updateWrapper
                        .set(SupplierContactInfoPO::getRegisterContactName, reqDTO.getName())
                        .set(SupplierContactInfoPO::getRegisterContactPhone, reqDTO.getPhone())
                        .set(SupplierContactInfoPO::getRegisterContactEmail, reqDTO.getEmail())
                        .set(SupplierContactInfoPO::getRegisterContactPosition, reqDTO.getPosition());
            }
            if (reqDTO.getType().equals(QualificationChangeUserTypeEnum.BUSINESS_CONTACT.getCode())) {
                updateWrapper
                        .set(SupplierContactInfoPO::getBusinessContactName, reqDTO.getName())
                        .set(SupplierContactInfoPO::getBusinessContactPhone, reqDTO.getPhone())
                        .set(SupplierContactInfoPO::getBusinessContactEmail, reqDTO.getEmail())
                        .set(SupplierContactInfoPO::getBusinessContactPosition, reqDTO.getPosition());
            }
            if (reqDTO.getType().equals(QualificationChangeUserTypeEnum.QUALITY_CONTACT.getCode())) {
                updateWrapper
                        .set(SupplierContactInfoPO::getQualityContactName, reqDTO.getName())
                        .set(SupplierContactInfoPO::getQualityContactPhone, reqDTO.getPhone())
                        .set(SupplierContactInfoPO::getQualityContactEmail, reqDTO.getEmail())
                        .set(SupplierContactInfoPO::getQualityContactPosition, reqDTO.getPosition());
            }
        });
        updateWrapper
                .set(SupplierContactInfoPO::getUpdateBy, qualificationChangeUpdateUserReqDTO.getOperationUserId())
                .set(SupplierContactInfoPO::getUpdateName, qualificationChangeUpdateUserReqDTO.getOperationUser())
                .set(SupplierContactInfoPO::getUpdateTime, new Date());
        supplierContactInfoMapperService.update(updateWrapper);
    }

    private void checkContactRestrict(List<QualificationChangeUpdateUserReqDTO.QualificationChangeUpdateUserItemReqDTO> contactList) {
        if (CollectionUtils.isEmpty(contactList)) {
            throw new BizFailException("联系人信息不能为空");
        }
        List<String> financeList = contactList.stream()
                .filter(reqDTO -> QualificationChangeUserTypeEnum.FINANCE_CONTACT.getCode().equals(reqDTO.getType()))
                .map(QualificationChangeUpdateUserReqDTO.QualificationChangeUpdateUserItemReqDTO::getPhone)
                .collect(Collectors.toList());
        for (QualificationChangeUpdateUserReqDTO.QualificationChangeUpdateUserItemReqDTO qualificationChangeUpdateUserItemReqDTO : contactList) {
            if (QualificationChangeUserTypeEnum.FINANCE_CONTACT.getCode().equals(qualificationChangeUpdateUserItemReqDTO.getType())) {
                continue;
            }
            if (financeList.contains(qualificationChangeUpdateUserItemReqDTO.getPhone())) {
                throw new BizFailException("财务联系人不能与商务联系人、质量联系人、注册联系人重复");
            }
        }
    }

}
