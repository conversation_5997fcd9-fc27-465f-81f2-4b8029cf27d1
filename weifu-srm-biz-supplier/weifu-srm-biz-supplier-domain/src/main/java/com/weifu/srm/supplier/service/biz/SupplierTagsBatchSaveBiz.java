package com.weifu.srm.supplier.service.biz;

import com.google.common.collect.Lists;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierTagsMapperService;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoListSearchPO;
import com.weifu.srm.supplier.repository.po.SupplierTagsPO;
import com.weifu.srm.supplier.request.SupplierTagsReqDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SupplierTagsBatchSaveBiz {

    private final SupplierTagsMapperService supplierTagsMapperService;
    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;

    @Transactional
    public void batchSaveSupplierTags(SupplierTagsReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getTagCodes())) {
            log.error("the request params can not be null ");
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.PARAMS_ERROR));
        }
        List<SupplierBasicInfoListSearchPO> listSupplier = supplierBasicInfoMapperService.queryList(reqDTO);
        if (CollectionUtils.isEmpty(listSupplier)) {
            log.info("can not match supplier to add Tags");
            return;
        }
        List<String> codes = listSupplier.stream().map(SupplierBasicInfoListSearchPO::getSapSupplierCode).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<SupplierTagsPO> list = boxSupplierTagsPOs(codes, reqDTO);
        // 查询数据库已有的标签
        List<SupplierTagsPO> exists = supplierTagsMapperService.lambdaQuery()
                .in(SupplierTagsPO::getSupplierCode, codes)
                .in(SupplierTagsPO::getTagCode, reqDTO.getTagCodes())
                .list();
        // 挪出已有的标签
        list = list.stream().filter(supplierTagsPO -> !exists.contains(supplierTagsPO)).collect(Collectors.toList());
        int batchNumber = 50;
        List<List<SupplierTagsPO>> partition = Lists.partition(list, batchNumber);
        for (List<SupplierTagsPO> supplierTagsPOS : partition) {
            supplierTagsMapperService.saveBatch(supplierTagsPOS);
        }
    }

    private List<SupplierTagsPO> boxSupplierTagsPOs(List<String> codes, SupplierTagsReqDTO reqDTO) {
        List<SupplierTagsPO> supplierTags = new ArrayList<>();
        codes.parallelStream().forEach(supplierCode -> reqDTO.getTagCodes().parallelStream().forEach(tagCode -> {
            SupplierTagsPO supplierTag = new SupplierTagsPO();
            supplierTag.setSupplierCode(supplierCode);
            supplierTag.setTagCode(tagCode);
            BaseEntityUtil.setCommon(supplierTag, reqDTO.getUserId(), reqDTO.getUserName(), null);
            supplierTags.add(supplierTag);
        }));
        return supplierTags;
    }
}
