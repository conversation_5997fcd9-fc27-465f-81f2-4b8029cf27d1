package com.weifu.srm.supplier.service.biz.suppliercategoryadjustapply;

import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.supplier.constants.ErrorCodeConstants;
import org.apache.commons.lang3.StringUtils;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryWithAllLevelDTO;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.po.SupplierCategoryRelationshipPO;
import com.weifu.srm.supplier.response.suppliercategoryadjustapply.SupplierCategoryTreeRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询与供应商关联的品类树（为了删除）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryCategoryTreeForDelBiz {

    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final CategoryApi categoryApi;
    private final LocaleMessage localeMessage;

    public List<SupplierCategoryTreeRespDTO> execute(String sapSupplierCode) {
        List<SupplierCategoryRelationshipPO> relationships = supplierCategoryRelationshipMapperService.queryBySapSupplierCodeList(Arrays.asList(sapSupplierCode));
        if (CollectionUtils.isEmpty(relationships)) {
            return new ArrayList<>();
        }

        List<String> categoryCodes = relationships.stream().map(SupplierCategoryRelationshipPO::getCategoryCode)
                .collect(Collectors.toList());
        ApiResponse<List<CategoryWithAllLevelDTO>> apiResponse = categoryApi.listCategory(categoryCodes);
        if (!apiResponse.getSucc()) {
            throw new BizFailException(localeMessage.getMessage(ErrorCodeConstants.QUERY_CATEGORY_INFO_EXCEPTION));
        }

        Map<String, SupplierCategoryTreeRespDTO> resultMap = new HashMap<>();
        for (CategoryWithAllLevelDTO category:apiResponse.getData()) {
            SupplierCategoryTreeRespDTO oneLevelTreeNode = resultMap.get(category.getOneLevelCategoryCode());
            if (oneLevelTreeNode == null) {
                oneLevelTreeNode = new SupplierCategoryTreeRespDTO();
                oneLevelTreeNode.setCategoryCode(category.getOneLevelCategoryCode());
                oneLevelTreeNode.setCategoryName(category.getOneLevelCategoryName());
                oneLevelTreeNode.setCategoryNameEn(category.getOneLevelCategoryNameEn());
                oneLevelTreeNode.setCategoryLevel("1");
                oneLevelTreeNode.setStatus("H");
                oneLevelTreeNode.setChildren(new ArrayList<>());
                resultMap.put(category.getOneLevelCategoryCode(), oneLevelTreeNode);
            }

            SupplierCategoryTreeRespDTO twoLevelTreeNode = getTreeNode(category.getTwoLevelCategoryCode(), oneLevelTreeNode.getChildren());
            if (twoLevelTreeNode == null) {
                twoLevelTreeNode = new SupplierCategoryTreeRespDTO();
                twoLevelTreeNode.setCategoryCode(category.getTwoLevelCategoryCode());
                twoLevelTreeNode.setCategoryName(category.getTwoLevelCategoryName());
                twoLevelTreeNode.setCategoryNameEn(category.getTwoLevelCategoryNameEn());
                twoLevelTreeNode.setCategoryLevel("2");
                twoLevelTreeNode.setStatus("H");
                twoLevelTreeNode.setChildren(new ArrayList<>());
                oneLevelTreeNode.getChildren().add(twoLevelTreeNode);
            }

            SupplierCategoryTreeRespDTO threeLevelTreeNode = new SupplierCategoryTreeRespDTO();
            threeLevelTreeNode.setCategoryCode(category.getThreeLevelCategoryCode());
            threeLevelTreeNode.setCategoryName(category.getThreeLevelCategoryName());
            threeLevelTreeNode.setCategoryNameEn(category.getThreeLevelCategoryNameEn());
            threeLevelTreeNode.setCategoryLevel("3");
            threeLevelTreeNode.setStatus("H");
            twoLevelTreeNode.getChildren().add(threeLevelTreeNode);
        }
        return new ArrayList<>(resultMap.values());
    }

    private SupplierCategoryTreeRespDTO getTreeNode(String categoryCode, List<SupplierCategoryTreeRespDTO> categorys) {
        for (SupplierCategoryTreeRespDTO category:categorys) {
            if (StringUtils.equals(categoryCode, category.getCategoryCode())) {
                return category;
            }
        }
        return null;
    }

}
