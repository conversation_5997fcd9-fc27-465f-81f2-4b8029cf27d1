package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.collection.CollUtil;
import com.weifu.srm.audit.constants.AuditTopicConstants;
import com.weifu.srm.audit.enums.TicketTypeEnum;
import com.weifu.srm.audit.mq.CreateTicketMQ;
import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.SupplierGradeConvert;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.constants.ErrorMessageKeyConstants;
import com.weifu.srm.supplier.repository.enums.*;
import com.weifu.srm.supplier.repository.mapper.SupplierGradeAdjustApplyMapper;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.supplier.repository.utils.BizNoGenerateUtil;
import com.weifu.srm.supplier.request.SupplierGradeAdjustDetailReqDTO;
import com.weifu.srm.supplier.request.SupplierGradeAdjustReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGradeSubmitGradeAdjustBiz {

    private final SupplierGradeAdjustApplyMapperService supplierGradeAdjustApplyMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierGradeConvert supplierGradeConvert;
    private final SupplierGradeAdjustApplyMapper supplierGradeAdjustApplyMapper;
    private final KafkaTemplate<String,String> kafkaTemplate;
    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;
    private final RedisUtil redisUtil;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final SupplierGreyListMapperService greyListMapperService;
    private final SupplierBlackListMapperService blackListMapperService;
    private final LocaleMessage localeMessage;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierKeyInfoChangeRecordMapperService keyInfoChangeRecordMapperService;
    @Value("${environment.params.grade.limitYear:1}")
    private Integer yearLimit;
    @Value("${environment.params.grade.limitDay:0}")
    private Integer dayLimit;
    @Value("${environment.params.grade.limitHour:0}")
    private Integer hourLimit;

    public String submitGradeAdjust(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO) {
        // 检查是否存在重复申请
        checkSupplierDuplication(supplierGradeAdjustReqDTO);
        // 检查供应商状态
        checkSupplierStatus(supplierGradeAdjustReqDTO);
        // 查询出所有审批中的供应商
        List<String> examineCodes = supplierGradeAdjustApplyMapper.querySupplierGradeExamineCode();
        List<String> applierSupplierCodes = supplierGradeAdjustReqDTO.getSupplierGradeAdjustDetailReqDTOList().stream().map(SupplierGradeAdjustDetailReqDTO::getSupplierCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(examineCodes)){
            examineCodes.retainAll(applierSupplierCodes);
            if (CollectionUtils.isNotEmpty(examineCodes)){
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_IS_PROCESSING,new String[]{JacksonUtil.bean2Json(examineCodes)}));
            }
        }
        // 仅有转合格供应商后1年的数据(如果没有记录潜在转合格时间则不做限制)才支持调整为 核心供应商与战略供应商
        checkSupplierDays(supplierGradeAdjustReqDTO);
        String applyNo = BizNoGenerateUtil.generateBizNo(redisUtil, SupplierBizEnum.SUPPLIER_GRADE_CODE);
        // 将工单信息存入供应商分级调整申请表
        SupplierGradeAdjustApplyPO supplierGradeAdjustApplyPO = new SupplierGradeAdjustApplyPO();
        // 生成供应商申请单主表数据
        supplierGradeAdjustApplyPO.setApplyNo(applyNo);
        supplierGradeAdjustApplyPO.setApplyName(supplierGradeAdjustReqDTO.getOperationUser());
        supplierGradeAdjustApplyPO.setApplyBy(supplierGradeAdjustReqDTO.getOperationUserId());
        supplierGradeAdjustApplyPO.setApplyTime(new Date());
        supplierGradeAdjustApplyPO.setApplyDesc(supplierGradeAdjustReqDTO.getApplyDesc());
        supplierGradeAdjustApplyPO.setApplyRemark(supplierGradeAdjustReqDTO.getApplyRemark());
        supplierGradeAdjustApplyPO.setCreateTime(new Date());
        supplierGradeAdjustApplyPO.setUpdateTime(new Date());
        supplierGradeAdjustApplyPO.setApplyAttachments(applyNo);
        // 对附件进行保存处理
        ArrayList<AttachmentRecordPO> attachmentRecordPOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(supplierGradeAdjustReqDTO.getAttachmentMessageReqDTOS())) {
            supplierGradeAdjustReqDTO.getAttachmentMessageReqDTOS().forEach(attachmentMessageRespDTO -> {
                if (StringUtils.isNotEmpty(attachmentMessageRespDTO.getFileName())) {
                    AttachmentRecordPO attachmentRecordPO = attachmentMessageConvert.toPO(attachmentMessageRespDTO);
                    attachmentRecordPO.setBusinessNo(applyNo);
                    attachmentRecordPO.setBusinessType(AttachmentBizTypeConstants.POTENTIAL_TO_QUALIFIED);
                    BaseEntityUtil.setCommon(attachmentRecordPO, supplierGradeAdjustReqDTO.getOperationUserId(), supplierGradeAdjustReqDTO.getOperationUser(), new Date());
                    attachmentRecordPOS.add(attachmentRecordPO);
                }
            });
        }
        // 暂定默认审批中
        supplierGradeAdjustApplyPO.setApplyStatus(SupplierGradeWorkStatusEnum.INVITATION_AUDIT_STATUS.getCode());
        supplierGradeAdjustApplyPO.setIsDelete(YesOrNoEnum.NO.getCode());
        transactionTemplate.execute(transactionStatus -> {
            supplierGradeAdjustApplyMapperService.save(supplierGradeAdjustApplyPO);
            attachmentRecordMapperService.saveBatch(attachmentRecordPOS);
            // 发送MQ给工单系统生成准入邀请审批工单
            CreateTicketMQ createTicketMQ = boxCreateTicketMQ(supplierGradeAdjustReqDTO, applyNo);
            log.info("start send MQ to ticket service create ticket ={}",createTicketMQ);
            kafkaTemplate.send(AuditTopicConstants.CREATE_TICKET, JacksonUtil.bean2Json(createTicketMQ));
            log.info("end send MQ to ticket service create ticket ={}",createTicketMQ);
            // 遍历分级调整详情，存入供应商分级调整申请明细表
            List<SupplierGradeAdjustApplyItemPO> supplierGradeAdjustApplyItemPOS = new ArrayList<>();
            supplierGradeAdjustReqDTO.getSupplierGradeAdjustDetailReqDTOList().forEach(supplierGradeAdjustDetailReqDTO -> {
                SupplierGradeAdjustApplyItemPO applyItemPO = supplierGradeConvert.toApplyPo(supplierGradeAdjustDetailReqDTO);
                // 暂定默认申请单
                applyItemPO.setApplyNo(applyNo);
                applyItemPO.setCreateTime(new Date());
                applyItemPO.setUpdateTime(new Date());
                applyItemPO.setIsDelete(YesOrNoEnum.NO.getCode());
                supplierGradeAdjustApplyItemPOS.add(applyItemPO);
            });
            supplierGradeAdjustApplyItemMapperService.saveBatch(supplierGradeAdjustApplyItemPOS);
            return null;
        });
        return applyNo;
    }

    private void checkSupplierDays(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO) {
        List<String> codeOrStrategyCodes = supplierGradeAdjustReqDTO.getSupplierGradeAdjustDetailReqDTOList().stream()
                .filter(gradeDetail -> SupplierGradeTypeEnum.CORE_SUPPLIER.getCode().equals(gradeDetail.getSupplierGradeAfter())
                        || SupplierGradeTypeEnum.STRATEGY_SUPPLIER.getCode().equals(gradeDetail.getSupplierGradeAfter()))
                .map(SupplierGradeAdjustDetailReqDTO::getSupplierCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeOrStrategyCodes)){
            return;
        }
        List<SupplierKeyInfoChangeRecordPO> list = keyInfoChangeRecordMapperService.lambdaQuery()
                .in(SupplierKeyInfoChangeRecordPO::getSupplierCode, codeOrStrategyCodes)
                .eq(SupplierKeyInfoChangeRecordPO::getChangeType, SupplierLifeCycleTypeEnum.POTENTIAL_TO_QUALIFIED.getCode())
                .list();
        // 获取比较时间
        LocalDateTime compareTime = LocalDateTime.now();
        if (yearLimit != 0) {
            compareTime = compareTime.plusYears(-yearLimit);
        }
        if (dayLimit != 0) {
            compareTime = compareTime.plusDays(-dayLimit);
        }
        if (hourLimit != 0) {
            compareTime = compareTime.plusHours(-hourLimit);
        }
        Instant instant = compareTime.atZone(ZoneId.systemDefault()).toInstant();
        Date lastDate = Date.from(instant);
        List<String> canNotGradeSuppliers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)){
            for (SupplierKeyInfoChangeRecordPO supplierKeyInfoChangeRecordPO : list) {
                if (lastDate.compareTo(supplierKeyInfoChangeRecordPO.getCreateTime()) < 0) {
                    canNotGradeSuppliers.add(supplierKeyInfoChangeRecordPO.getSupplierCode());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(canNotGradeSuppliers)){
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_GRADE_NOT_ENOUGH_LANG, new String[]{JacksonUtil.bean2Json(canNotGradeSuppliers)}));
        }
    }

    private void checkSupplierDuplication(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO) {
        Set<String> supplierCodes = new HashSet<>();
        for (SupplierGradeAdjustDetailReqDTO adjustDetail:supplierGradeAdjustReqDTO.getSupplierGradeAdjustDetailReqDTOList()) {
            if (supplierCodes.contains(adjustDetail.getSupplierCode())) {
                throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_DUPLICATE, new String[]{adjustDetail.getSupplierCode()}));
            }
            supplierCodes.add(adjustDetail.getSupplierCode());
        }
    }

    private void checkSupplierStatus(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO) {
        List<SupplierGradeAdjustDetailReqDTO> list = supplierGradeAdjustReqDTO.getSupplierGradeAdjustDetailReqDTOList();
        List<String> supplierCodes = list.stream().map(SupplierGradeAdjustDetailReqDTO::getSupplierCode).collect(Collectors.toList());
        List<SupplierBasicInfoPO> supplierList = supplierBasicInfoMapperService.lambdaQuery().in(SupplierBasicInfoPO::getSapSupplierCode, supplierCodes).list();
        List<SupplierBasicInfoPO> notQualifiedSuppliers = supplierList.stream().filter(supplier -> !SupplierBasicStatusEnum.QUALIFIED_SUPPLIER.getCode().equals(supplier.getStatus())).collect(Collectors.toList());
        // 合格供应商
        if (CollectionUtils.isNotEmpty(notQualifiedSuppliers)){
            List<String> codes = notQualifiedSuppliers.stream().map(SupplierBasicInfoPO::getSapSupplierCode).collect(Collectors.toList());
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.SUPPLIER_GRADE_NOT_QUALIFIED_SUPPLIER,new String[]{JacksonUtil.bean2Json(codes)}));
        }
        // 没有灰/黑名单数据
        List<SupplierGreyListPO> greyList = greyListMapperService.lambdaQuery()
                .in(SupplierGreyListPO::getSupplierCode, supplierCodes)
                .list();
        List<SupplierBlackListPO> blackList = blackListMapperService.lambdaQuery()
                .in(SupplierBlackListPO::getSupplierCode, supplierCodes)
                .list();
        if (CollectionUtils.isNotEmpty(greyList) || CollectionUtils.isNotEmpty(blackList)){
            log.error("supplier grade adjust list exist greyList or blackList greyList={} blackList={}",greyList,blackList);
            throw new BizFailException(localeMessage.getMessage(ErrorMessageKeyConstants.GREY_BLACK_LIST_EXIST));
        }

    }

    /**
     * 发送工单创建MQ
     */
    private CreateTicketMQ boxCreateTicketMQ(SupplierGradeAdjustReqDTO supplierGradeAdjustReqDTO, String applyNo) {
        CreateTicketMQ mq = new CreateTicketMQ();
        mq.setBusinessNo(applyNo);
        mq.setTicketType(TicketTypeEnum.SUPPLIER_GRADE_ADJUST.getCode());
        mq.setSubmitBy(supplierGradeAdjustReqDTO.getOperationUserId());
        mq.setSubmitName(supplierGradeAdjustReqDTO.getOperationUser());
        mq.setSubmitDesc(supplierGradeAdjustReqDTO.getApplyDesc());
        mq.setSubmitRemark(supplierGradeAdjustReqDTO.getApplyRemark());
        return mq;
    }

}
