package com.weifu.srm.supplier.service.biz.qualificationchange;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.supplier.manager.MQServiceManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QualificationChangeAssociationItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryRelationshipMapperService;
import com.weifu.srm.supplier.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.repository.enums.NoticeTemplateEnum;
import com.weifu.srm.supplier.repository.enums.QualificationChangeStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.repository.constants.SupplierCommonConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandleQualificationChangeAssociationRstBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeAssociationItemMapperService qualificationChangeAssociationItemMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final MQServiceManager mqService;
    private final CategoryApi categoryApi;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;


    public void handle(TicketStatusChangedMQ mq) {
        // 查询资质变更申请表
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 查询供应商基础表信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (TicketStatusEnum.APPROVING.equalsCode(mq.getStatus())) {
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getTicketNo, mq.getTicketNo())
                    .update();
        }
        // 审批通过
        if (TicketStatusEnum.APPROVED.equalsCode(mq.getStatus())) {
            // 查询工单最后一次审批详情
            LambdaUpdateWrapper<QualificationChangeApplyPO> applyPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            applyPOLambdaUpdateWrapper
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.AGREE_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime());
            // 查询资质变更关联方修改信息
            QualificationChangeAssociationItemPO associationItemPO = qualificationChangeAssociationItemMapperService.lambdaQuery()
                    .eq(QualificationChangeAssociationItemPO::getQualificationChangeNo, mq.getBusinessNo())
                    .eq(QualificationChangeAssociationItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .one();
            transactionTemplate.execute(transactionStatus -> {
                // 更新申请单审核状态
                qualificationChangeApplyMapperService.update(applyPOLambdaUpdateWrapper);
                supplierBasicInfoMapperService.lambdaUpdate()
                        .eq(SupplierBasicInfoPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                        .set(SupplierBasicInfoPO::getIsWeifuRelatedParty, associationItemPO.getIsWeifuRelatedParty())
                        .set(SupplierBasicInfoPO::getDirectRelatedPartyTypeDesc, associationItemPO.getDirectRelatedPartyTypeDesc())
                        .update();
                // 通知对应的人(将供应商基本信息发生变化通知到该供应商相关的品类采购工程师、品类采购工程师组长、供应商质量工程师、供应商质量工程师组长、采购工程处处长、事业部/子公司供应商质量负责人)
                mqService.sendMQ(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(changeApplyPO, mq)));
                return null;
            });
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailSuccess(changeApplyPO,userInfo)));
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(mq.getStatus())) {
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.REJECT_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .update();
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqService.sendMQ(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailFail(changeApplyPO,userInfo, mq)));
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(mq.getStatus())) {
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.RECALL.getCode())
                    .update();
        }
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(QualificationChangeApplyPO changeApplyPO, TicketStatusChangedMQ ticketInfo) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        LambdaQueryWrapper<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierCategoryRelationshipPOLambdaQueryWrapper
                .eq(SupplierCategoryRelationshipPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierCategoryRelationshipPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.list(supplierCategoryRelationshipPOLambdaQueryWrapper);
        List<String> categoryCodes = supplierCategoryRelationshipPOS.stream()
                .map(SupplierCategoryRelationshipPO::getCategoryCode)
                .collect(Collectors.toList());
        ApiResponse<List<CategoryEngineerResultDTO>> userOne = categoryApi.queryCategoryEngineerByCategoryCodes(categoryCodes);
        // 去重
        HashMap<Long, String> userMap = new HashMap<>();
        userOne.getData().forEach(categoryEngineerResultDTO -> userMap.put(categoryEngineerResultDTO.getUserId(), categoryEngineerResultDTO.getUserName()));
        userMap.forEach((k, v) -> {
            CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
            notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
            notice.setBusinessNo(ticketInfo.getBusinessNo());
            notice.setBusinessType(MessageClsEnum.SUPPLIER_ASSOCIATE_ADJUST_EFFECTIVE.getCode());
            notice.setUserId(k);
            notice.setUserName(v);
            notice.setIconType(IconTypeEnum.GENERAL.getCode());
            notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_ASSOCIATION_COMPLETE.getTitle());
            String content = NoticeTemplateEnum.QUALIFICATION_CHANGE_ASSOCIATION_COMPLETE.getContent();
            content = content
                    .replace("${供应商名称}", changeApplyPO.getSupplierName())
                    .replace("${供应商编码}", changeApplyPO.getSupplierCode())
                    .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT));
            notice.setContent(content);
            result.add(notice);
        });
        return result;
    }


    private CreateSendEmailTaskMQ sendEmailSuccess(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo) {
        CreateSendEmailTaskMQ emailReq  = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_ASSOCIATION_UPDATE_SUCCESS.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_ASSOCIATION_UPDATE_SUCCESS.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private CreateSendEmailTaskMQ sendEmailFail(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo, TicketStatusChangedMQ mq) {
        CreateSendEmailTaskMQ emailReq  = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_ASSOCIATION_UPDATE_FAIL.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_ASSOCIATION_UPDATE_FAIL.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT))
                .replace("${审批意见}", mq.getApprovalComment()));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

}
