package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.supplier.enums.SupplierCategoryAdjustApplyStatusEnum;
import com.weifu.srm.supplier.manager.AttachmentManager;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyNewItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierCategoryBatchAdjustApplyOldItemMapperService;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.enums.SupplierCategoryStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyNewItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyOldItemPO;
import com.weifu.srm.supplier.repository.po.SupplierCategoryBatchAdjustApplyPO;
import com.weifu.srm.supplier.request.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyReqDTO;
import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.suppliercategorybatchadjustapply.SupplierCategoryBatchAdjustApplyDetailRespDTO;
import com.weifu.srm.supplier.response.suppliercategorybatchadjustapply.SupplierCategoryBatchAdjustApplyItemRespDTO;
import com.weifu.srm.supplier.service.SupplierCategoryBatchAdjustApplyService;
import com.weifu.srm.supplier.service.biz.suppliercategorybatchadjustapply.CreateSupplierCategoryBatchAdjustApplyBiz;
import com.weifu.srm.supplier.service.biz.suppliercategorybatchadjustapply.HandleSupplierCategoryBatchAdjustAuditRstBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCategoryBatchAdjustApplyServiceImpl implements SupplierCategoryBatchAdjustApplyService {

    private final CreateSupplierCategoryBatchAdjustApplyBiz createSupplierCategoryBatchAdjustApplyBiz;
    private final SupplierCategoryBatchAdjustApplyMapperService supplierCategoryBatchAdjustApplyMapperService;
    private final SupplierCategoryBatchAdjustApplyOldItemMapperService supplierCategoryBatchAdjustApplyOldItemMapperService;
    private final SupplierCategoryBatchAdjustApplyNewItemMapperService supplierCategoryBatchAdjustApplyNewItemMapperService;
    private final HandleSupplierCategoryBatchAdjustAuditRstBiz handleSupplierCategoryBatchAdjustAuditRstBiz;
    private final AttachmentManager attachmentManager;

    @Override
    public String create(CreateSupplierCategoryBatchAdjustApplyReqDTO req) {
        return createSupplierCategoryBatchAdjustApplyBiz.execute(req);
    }

    @Override
    public SupplierCategoryBatchAdjustApplyDetailRespDTO queryApplyDetail(String applyNo) {
        List<SupplierCategoryBatchAdjustApplyOldItemPO> oldItemPOs = supplierCategoryBatchAdjustApplyOldItemMapperService.lambdaQuery()
                .eq(SupplierCategoryBatchAdjustApplyOldItemPO::getApplyNo, applyNo).list();
        List<SupplierCategoryBatchAdjustApplyItemRespDTO> oldItems = BeanUtil.copyToList(oldItemPOs, SupplierCategoryBatchAdjustApplyItemRespDTO.class);
        for (SupplierCategoryBatchAdjustApplyItemRespDTO item:oldItems) {
            item.setSupplierCategoryStatusName(SupplierCategoryStatusEnum.getName(item.getSupplierCategoryStatus(), LocaleContextHolder.getLocale()));
        }

        List<SupplierCategoryBatchAdjustApplyNewItemPO> newItemPOs = supplierCategoryBatchAdjustApplyNewItemMapperService.lambdaQuery()
                .eq(SupplierCategoryBatchAdjustApplyNewItemPO::getApplyNo, applyNo).list();
        List<SupplierCategoryBatchAdjustApplyItemRespDTO> newItems = BeanUtil.copyToList(newItemPOs, SupplierCategoryBatchAdjustApplyItemRespDTO.class);
        for (SupplierCategoryBatchAdjustApplyItemRespDTO item:newItems) {
            item.setSupplierCategoryStatusName(SupplierCategoryStatusEnum.getName(item.getSupplierCategoryStatus(), LocaleContextHolder.getLocale()));
        }

        List<AttachmentMessageRespDTO> files = attachmentManager.getAttachmentsByBusinessNoAndBusType(applyNo,
                Arrays.asList(AttachmentBizTypeConstants.SUPPLIER_CATEGORY_BATCH_ADJUST));

        SupplierCategoryBatchAdjustApplyPO apply = supplierCategoryBatchAdjustApplyMapperService.lambdaQuery()
                .eq(SupplierCategoryBatchAdjustApplyPO::getApplyNo, applyNo).one();

        SupplierCategoryBatchAdjustApplyDetailRespDTO result = BeanUtil.toBean(apply, SupplierCategoryBatchAdjustApplyDetailRespDTO.class);
        result.setFiles(files);
        result.setOldItems(oldItems);
        result.setNewItems(newItems);
        result.setApplyStatusName(SupplierCategoryAdjustApplyStatusEnum.getName(result.getApplyStatus(), LocaleContextHolder.getLocale()));
        return result;
    }

    @Override
    public void handleSupplierCategoryAdjustApplyRst(TicketStatusChangedMQ mq) {
        handleSupplierCategoryBatchAdjustAuditRstBiz.execute(mq);
    }

}