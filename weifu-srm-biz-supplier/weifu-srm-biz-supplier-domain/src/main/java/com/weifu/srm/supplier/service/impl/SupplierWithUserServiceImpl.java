package com.weifu.srm.supplier.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.UserSupplierRelationshipInfoMapperService;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO;
import com.weifu.srm.supplier.response.*;
import com.weifu.srm.supplier.service.SupplierWithUserService;
import com.weifu.srm.supplier.service.biz.CheckUserWithSupplierRelationshipBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierWithUserServiceImpl implements SupplierWithUserService {

    private final UserSupplierRelationshipInfoMapperService userSupplierRelationshipInfoMapperService;
    private final CheckUserWithSupplierRelationshipBiz checkUserWithSupplierRelationshipBiz;
    private final SupplierBasicInfoMapperService basicService;

    @Override
    public List<SupplierRegistryInfoRespDTO> searchSupplierRegistryListByUserId(Long userId) {
        return userSupplierRelationshipInfoMapperService.searchSupplierRegistryListByUserId(userId);
    }

    @Override
    public boolean checkRegisterUserUsed(String supplierName, String phone, String email) {
        return checkUserWithSupplierRelationshipBiz.checkUserWithSupplierRelationship(supplierName, phone, email);
    }

    @Override
    public List<SupplierRoleRespDTO> querySupplierRoleByUserId(Long userId) {
        List<UserSupplierRelationshipInfoPO> roles = userSupplierRelationshipInfoMapperService.lambdaQuery()
                .eq(UserSupplierRelationshipInfoPO::getSysUserId, userId)
                .list();

        List<SupplierRoleRespDTO> supplierRoles = new ArrayList<>();
        for (UserSupplierRelationshipInfoPO relationship : roles) {
            SupplierRoleRespDTO roleResp = new SupplierRoleRespDTO();
            roleResp.setRoleId(relationship.getRoleId());
            roleResp.setSupplierId(relationship.getSupplierBasicInfoId());
            supplierRoles.add(roleResp);
        }
        return supplierRoles;
    }

    @Override
    public List<UserRelationSupplierRespDTO> listByUserIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) return null;

        LambdaQueryWrapper<UserSupplierRelationshipInfoPO> query = new LambdaQueryWrapper<>();
        query.in(UserSupplierRelationshipInfoPO::getSysUserId, userIds);
        query.eq(UserSupplierRelationshipInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());

        List<UserSupplierRelationshipInfoPO> list = userSupplierRelationshipInfoMapperService.list(query);
        if (CollUtil.isEmpty(list)) return null;

        List<Long> supplierIds = list.stream().map(v -> v.getSupplierBasicInfoId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(supplierIds)) return null;

        LambdaQueryWrapper<SupplierBasicInfoPO> querySupplier = new LambdaQueryWrapper<>();
        querySupplier.in(SupplierBasicInfoPO::getId, supplierIds);
        querySupplier.eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode());

        List<SupplierBasicInfoPO> suppliers = basicService.list(querySupplier);
        if (CollUtil.isEmpty(suppliers)) return null;

        List<UserRelationSupplierRespDTO> result = new ArrayList<>();
        list.stream().collect(Collectors.groupingBy(v -> v.getSysUserId())).forEach((k, v) -> {
            UserRelationSupplierRespDTO rs = new UserRelationSupplierRespDTO(k);
            List<Long> basicIds = v.stream().map(v1 -> v1.getSupplierBasicInfoId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(basicIds)) {
                List<RelationSupplierRespDTO> relationSuppliers = BeanUtil.copyToList(
                        suppliers.stream().filter(v1 -> CollUtil.contains(basicIds, v1.getId())).collect(Collectors.toList()),
                        RelationSupplierRespDTO.class);
                rs.setRelationSuppliers(relationSuppliers);
            }
            result.add(rs);
        });
        return result;
    }

    @Override
    public List<UserSupplierRelationshipInfoRespDTO> listBySupplierBasicInfoIdAndRoleId(Long supplierBasicInfoId, String roleId) {
        List<UserSupplierRelationshipInfoPO> poList = userSupplierRelationshipInfoMapperService.lambdaQuery().eq(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, supplierBasicInfoId).eq(StringUtils.isNotBlank(roleId), UserSupplierRelationshipInfoPO::getRoleId, roleId).list();
        return BeanUtil.copyToList(poList,UserSupplierRelationshipInfoRespDTO.class);
    }
}
