package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ErrorCode;
import com.weifu.srm.supplier.constants.BizErrorCode;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateMapperService;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplatePO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/7/11 13:20
 * @Description 标记模板为默认模板
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class QuestionnaireTemplateBeDefaultBiz {
    private final QuestionnaireTemplateMapperService questionnaireTemplateMapperService;
    public String makeDefaultTemplate(String templateCode , Integer isDefault){
        QuestionnaireTemplatePO questionnaireTemplatePO = questionnaireTemplateMapperService.queryByTemplateCode(templateCode);
        if (ObjectUtil.isNull(questionnaireTemplatePO)) {
            throw new BizFailException(ErrorCode.C_DATA_NOT_EXIST.getMsg());
        }
        Integer defaultTemplateCount = questionnaireTemplateMapperService
                .queryDefaultTemplateCount(questionnaireTemplatePO.getSupplierType(),templateCode);
        if (!YesOrNoEnum.NO.getCode().equals(defaultTemplateCount) && YesOrNoEnum.YES.getCode().equals(isDefault)) {
            throw new BizFailException(BizErrorCode.SAME_TYPE_DEFAULT_TEMPLATE_CAN_NOT_BE_MORE_THAN_ONE.getMsg());
        }
        LambdaUpdateWrapper<QuestionnaireTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(QuestionnaireTemplatePO::getTemplateCode, templateCode);
        updateWrapper.eq(QuestionnaireTemplatePO::getIsDelete, YesOrNoEnum.NO.getCode());
        updateWrapper.set(QuestionnaireTemplatePO::getIsDefault, isDefault);
        Integer count = questionnaireTemplateMapperService.updateTemplate(updateWrapper);
        if (count.equals(YesOrNoEnum.NO.getCode())) {
            throw new BizFailException(ErrorCode.C_DATA_NOT_EXIST.getMsg());
        }
        return templateCode;
    }
}
