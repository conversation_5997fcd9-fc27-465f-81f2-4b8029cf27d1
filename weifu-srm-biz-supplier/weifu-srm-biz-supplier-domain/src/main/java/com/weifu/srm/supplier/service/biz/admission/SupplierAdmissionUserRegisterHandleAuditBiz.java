package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.supplier.manager.SupplierAdmissionTriggerManager;
import com.weifu.srm.supplier.mq.SupplierAdmissionUserRegisterCompletedMQ;
import com.weifu.srm.supplier.repository.atomicservice.SupplierAdmissionInvitationInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.enums.SupplierBasicStatusEnum;
import com.weifu.srm.supplier.repository.po.SupplierAdmissionInvitationRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierAdmissionUserRegisterHandleAuditBiz {

    private final SupplierAdmissionTriggerManager admissionTriggerManager;
    private final SupplierAdmissionInvitationInfoMapperService invitationInfoMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;

    public void handleAudit(SupplierAdmissionUserRegisterCompletedMQ mq) {
        SupplierAdmissionInvitationRecordPO invitationRecordPO = invitationInfoMapperService.lambdaQuery()
                .eq(SupplierAdmissionInvitationRecordPO::getInvitationNo, mq.getBusinessNo())
                .one();
        if (ObjectUtils.isEmpty(invitationRecordPO)){
            log.info("非准入列表邀请供应商用户注册数据 不进行处理 = {}",mq);
            return;
        }
        SupplierBasicInfoPO basicInfoPO = supplierBasicInfoMapperService.lambdaQuery().eq(SupplierBasicInfoPO::getId, invitationRecordPO.getSupplierBasicMsgId()).one();
        if (ObjectUtils.isEmpty(basicInfoPO) || !SupplierBasicStatusEnum.normalStatus.contains(SupplierBasicStatusEnum.getByCode(basicInfoPO.getStatus()))){
            log.info("供应商状态尚未达到准入填写发起要求，暂不进行其它操作={}",basicInfoPO);
            return;
        }
        // 触发准入动作
        admissionTriggerManager.completedAdmissionInvitation(null, invitationRecordPO.getSupplierBasicMsgId());
    }
}
