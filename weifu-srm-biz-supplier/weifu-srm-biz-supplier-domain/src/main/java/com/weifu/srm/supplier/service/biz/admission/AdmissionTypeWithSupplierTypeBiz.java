package com.weifu.srm.supplier.service.biz.admission;

import com.weifu.srm.supplier.repository.enums.AdmissionInvitationTypeEnum;
import com.weifu.srm.supplier.repository.enums.SupplierTypeEnum;
import com.weifu.srm.supplier.response.AdmissionTypeMapSupplierTypeRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
@Slf4j
@Service
public class AdmissionTypeWithSupplierTypeBiz {

    public AdmissionTypeMapSupplierTypeRespDTO getAdmissionTypeMapSupplierType() {
        Locale locale = LocaleContextHolder.getLocale();
        boolean isEnglish = "en".equals(locale.getLanguage());
        // 存储在枚举中的准入类型 + 供应商类型关联进行组装返回给调用方
        List<AdmissionTypeMapSupplierTypeRespDTO.AdmissionType> admissionTypes = Arrays.stream(AdmissionInvitationTypeEnum.values())
                .map(admissionTypeEnum -> {
                    List<SupplierTypeEnum> supplierTypeEnums = AdmissionInvitationTypeEnum.ADMISSION_TYPE_MAP_SUPPLIER_TYPE.get(admissionTypeEnum);
                    List<AdmissionTypeMapSupplierTypeRespDTO.SupplierType> supplierTypes = supplierTypeEnums.stream().map(supplierTypeEnum -> {
                        AdmissionTypeMapSupplierTypeRespDTO.SupplierType supplierType = new AdmissionTypeMapSupplierTypeRespDTO.SupplierType();
                        supplierType.setCode(supplierTypeEnum.getCode());
                        supplierType.setName(isEnglish?supplierTypeEnum.getEnglishName():supplierTypeEnum.getChineseName());
                        return supplierType;
                    }).collect(Collectors.toList());
                    AdmissionTypeMapSupplierTypeRespDTO.AdmissionType admissionType = new AdmissionTypeMapSupplierTypeRespDTO.AdmissionType();
                    admissionType.setCode(admissionTypeEnum.getCode());
                    admissionType.setName(isEnglish?admissionTypeEnum.getEnglishName():admissionTypeEnum.getChineseName());
                    admissionType.setSupplierList(supplierTypes);
                    return admissionType;
                }).collect(Collectors.toList());
        return new AdmissionTypeMapSupplierTypeRespDTO(admissionTypes);
    }
}
