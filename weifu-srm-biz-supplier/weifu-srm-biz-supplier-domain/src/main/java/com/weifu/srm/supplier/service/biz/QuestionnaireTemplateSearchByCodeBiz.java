package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.supplier.convert.QuestionnaireTemplateConvert;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateFieldSampleMapperService;
import com.weifu.srm.supplier.repository.atomicservice.QuestionnaireTemplateMapperService;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplateFieldSamplePO;
import com.weifu.srm.supplier.repository.po.QuestionnaireTemplatePO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateDetailRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateFieldsRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/7/12 9:31
 * @Description
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class QuestionnaireTemplateSearchByCodeBiz {
    private final QuestionnaireTemplateMapperService questionnaireTemplateMapperService;
    private final QuestionnaireTemplateConvert questionnaireTemplateConvert;
    private final QuestionnaireTemplateFieldMapperService questionnaireTemplateFieldMapperService;
    private final QuestionnaireTemplateFieldSampleMapperService queryTemplateFieldSampleService;

    public QuestionnaireTemplateDetailRespDTO queryTemplateFieldsByCode(String templateCode){
        QuestionnaireTemplateDetailRespDTO questionnaireTemplateDetailRespDTO = new QuestionnaireTemplateDetailRespDTO();
        QuestionnaireTemplatePO questionnaireTemplatePO = questionnaireTemplateMapperService.queryByTemplateCode(templateCode);
        if (!ObjectUtil.isNull(questionnaireTemplatePO)) {
            questionnaireTemplateDetailRespDTO.setTemplateCode(questionnaireTemplatePO.getTemplateCode());
            questionnaireTemplateDetailRespDTO.setTemplateDesc(questionnaireTemplatePO.getTemplateDesc());
            questionnaireTemplateDetailRespDTO.setSupplierType(questionnaireTemplatePO.getSupplierType());
            questionnaireTemplateDetailRespDTO.setEnable(questionnaireTemplatePO.getEnable());
            questionnaireTemplateDetailRespDTO.setIsDefault(questionnaireTemplatePO.getIsDefault());
            List<QuestionnaireTemplateFieldsRespDTO> fieldDetails = questionnaireTemplateConvert
                    .toFiledResult(questionnaireTemplateFieldMapperService.queryTemplateFieldListByCode(templateCode));
            List<QuestionnaireTemplateFieldSamplePO> fieldSampleDetails = queryTemplateFieldSampleService.queryTemplateFieldSampList();
            Map<Pair<String, String>, QuestionnaireTemplateFieldSamplePO> aMap = fieldSampleDetails.stream()
                    .collect(Collectors.toMap(
                            a -> new Pair<>(a.getCategorize(), a.getFieldName()),
                            Function.identity(),
                            (a1, a2) -> a1
                    ));
            fieldDetails = fieldDetails.stream()
                    .peek(b -> {
                        Pair<String, String> key = new Pair<>(b.getCategorize(), b.getFieldName());
                        QuestionnaireTemplateFieldSamplePO matched = aMap.get(key);
                        if (matched != null) {
                            b.setFieldNameEn(matched.getFieldNameEn());
                            b.setFieldNameCn(matched.getFieldNameCn());
                        }
                    })
                    .collect(Collectors.toList());
            questionnaireTemplateDetailRespDTO.setFieldDetail(fieldDetails);
        }
        return questionnaireTemplateDetailRespDTO;
    }


    class Pair<K, V> {
        private K key;
        private V value;

        public Pair(K key, V value) {
            this.key = key;
            this.value = value;
        }

        public K getKey() {
            return key;
        }

        public V getValue() {
            return value;
        }

        // 重写 equals 和 hashCode 方法以支持作为 HashMap 的键
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Pair<?, ?> pair = (Pair<?, ?>) o;
            return Objects.equals(key, pair.key) &&
                    Objects.equals(value, pair.value);
        }

        @Override
        public int hashCode() {
            return Objects.hash(key, value);
        }
    }


}
