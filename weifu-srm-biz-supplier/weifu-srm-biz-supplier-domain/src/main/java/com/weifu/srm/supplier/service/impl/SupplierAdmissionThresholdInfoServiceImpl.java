package com.weifu.srm.supplier.service.impl;

import com.weifu.srm.supplier.request.SupplierAdmissionThresholdInfoReqDTO;
import com.weifu.srm.supplier.response.SupplierAdmissionThresholdInfoResponseDTO;
import com.weifu.srm.supplier.service.SupplierAdmissionThresholdInfoService;
import com.weifu.srm.supplier.service.biz.admission.SupplierAdmissionThresholdInfoSearchBiz;
import com.weifu.srm.supplier.service.biz.admission.SupplierAdmissionThresholdInfoUpdateBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierAdmissionThresholdInfoServiceImpl implements SupplierAdmissionThresholdInfoService {

    private final SupplierAdmissionThresholdInfoUpdateBiz supplierAdmissionThresholdInfoUpdateBiz;
    private final SupplierAdmissionThresholdInfoSearchBiz supplierAdmissionThresholdInfoSearchBiz;

    @Override
    public void updateThresholdInfo(SupplierAdmissionThresholdInfoReqDTO req) {
        supplierAdmissionThresholdInfoUpdateBiz.update(req);
    }

    @Override
    public SupplierAdmissionThresholdInfoResponseDTO searchThresholdInfo() {
        return supplierAdmissionThresholdInfoSearchBiz.select();
    }
}
