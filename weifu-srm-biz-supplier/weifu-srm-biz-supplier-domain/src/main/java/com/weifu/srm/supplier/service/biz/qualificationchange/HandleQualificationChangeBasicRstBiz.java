package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import org.apache.commons.lang3.StringUtils;
import com.weifu.srm.communication.constants.CommunicationTopicConstants;
import com.weifu.srm.communication.enums.IconTypeEnum;
import com.weifu.srm.communication.enums.MessageClsEnum;
import com.weifu.srm.communication.enums.MessageTypeEnum;
import com.weifu.srm.communication.mq.CreateSiteMessageMQ;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.manager.QualificationChangeManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.repository.enums.NoticeTemplateEnum;
import com.weifu.srm.supplier.repository.enums.QualificationChangeStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.request.SysUserQueryReqDTO;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.repository.constants.SupplierCommonConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandleQualificationChangeBasicRstBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeBasicItemMapperService qualificationChangeBasicItemMapperService;
    private final SupplierContactInfoMapperService supplierContactInfoMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final TransactionTemplate transactionTemplate;
    private final MqManager mqManager;
    private final SysUserApi sysUserApi;
    private final CategoryApi categoryApi;
    private final SupplierCategoryRelationshipMapperService supplierCategoryRelationshipMapperService;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final UserSupplierRelationshipInfoMapperService userSupplierRelationshipInfoMapperService;
    private final QualificationChangeManager qualificationChangeManager;

    public void handle(TicketStatusChangedMQ mq) {
        // 查询资质变更申请表
        QualificationChangeApplyPO changeApply = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 查询供应商基础表信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, changeApply.getSupplierCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 审批中
        if (TicketStatusEnum.APPROVING.equalsCode(mq.getStatus())) {
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getTicketNo, mq.getTicketNo())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, new Date())
                    .update();
            return;
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(mq.getStatus())) {
            Date current = new Date();
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.REJECT_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, current)
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .update();
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailFail(changeApply, userInfo, mq)));
            return;
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(mq.getStatus())) {
            Date current = new Date();
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.RECALL.getCode())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, current)
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                    .update();
            return;
        }
        // 审批通过
        // 查询资质变更基本信息修改详情表
        QualificationChangeBasicItemPO qualificationChangeBasicItem = qualificationChangeBasicItemMapperService.getByNo(mq.getBusinessNo());
        BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());

        Date current = new Date();
        transactionTemplate.execute(transactionStatus -> {
            // 更新供应商基础信息表
            LambdaUpdateWrapper<SupplierBasicInfoPO> basicInfoUpdateWrapper = buildBasicInfoUpdateWrapper(changeApply, qualificationChangeBasicItem, current);
            supplierBasicInfoMapperService.update(basicInfoUpdateWrapper);

            // 修改联系表 bug修复 初始化导入的数据没有公司电话与传真 -> 变更为有无输入公司电话传真数据
            if (StringUtils.isNotEmpty(qualificationChangeBasicItem.getCompanyPhone()) ||
                    StringUtils.isNotEmpty(qualificationChangeBasicItem.getCompanyFax())) {
                if(Objects.equals(qualificationChangeBasicItem.getCompanyPhoneChange(),1)
                        ||  Objects.equals(qualificationChangeBasicItem.getCompanyFaxChange(),1)) {
                    LambdaUpdateWrapper<SupplierContactInfoPO> contactInfoPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    contactInfoPOLambdaUpdateWrapper.eq(SupplierContactInfoPO::getSupplierBasicMsgId, supplierBasicInfoPO.getId());
                    contactInfoPOLambdaUpdateWrapper.set(qualificationChangeBasicItem.getCompanyPhoneChange() == 1, SupplierContactInfoPO::getCompanyPhone, qualificationChangeBasicItem.getCompanyPhone());
                    contactInfoPOLambdaUpdateWrapper.set(qualificationChangeBasicItem.getCompanyFaxChange() == 1, SupplierContactInfoPO::getCompanyFax, qualificationChangeBasicItem.getCompanyFax());
                    supplierContactInfoMapperService.update(contactInfoPOLambdaUpdateWrapper);
                }
            }

            // 修改供应商和用户关系表中供应商的名称
            if (StringUtils.isNotEmpty(qualificationChangeBasicItem.getSupplierNameBefore())) {
                userSupplierRelationshipInfoMapperService.updateSupplierName(supplierBasicInfoPO.getId(), qualificationChangeBasicItem.getSupplierName());
            }
            if (StringUtils.isNotEmpty(qualificationChangeBasicItem.getBusinessLicenseAttachmentBefore())) {
                attachmentRecordMapperService.lambdaUpdate()
                        .eq(AttachmentRecordPO::getBusinessNo, supplierBasicInfoPO.getRegisterNo())
                        .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.BUSINESS_LICENSE)
                        .set(AttachmentRecordPO::getIsDelete, YesOrNoEnum.YES.getCode())
                        .update();
                AttachmentRecordPO attachmentRecordPO = attachmentRecordMapperService.lambdaQuery()
                        .eq(AttachmentRecordPO::getBusinessNo, qualificationChangeBasicItem.getId())
                        .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_BASIC)
                        .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                        .one();

                AttachmentRecordPO newAttachmentRecord = BeanUtil.toBean(attachmentRecordPO, AttachmentRecordPO.class);
                newAttachmentRecord.setId(null);
                newAttachmentRecord.setBusinessNo(supplierBasicInfoPO.getRegisterNo());
                newAttachmentRecord.setBusinessType(AttachmentBizTypeConstants.BUSINESS_LICENSE);
                BaseEntityUtil.setCommon(newAttachmentRecord, changeApply.getCreateBy(), changeApply.getCreateName(), current);
                attachmentRecordMapperService.save(newAttachmentRecord);
            }

            // 更新申请单审核状态
            LambdaUpdateWrapper<QualificationChangeApplyPO> applyUpdateWrapper = new LambdaUpdateWrapper<>();
            applyUpdateWrapper.eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.AGREE_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, current)
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime());
            qualificationChangeApplyMapperService.update(applyUpdateWrapper);

            // 通知对应的人(将供应商基本信息发生变化通知到该供应商相关的品类采购工程师、品类采购工程师组长、供应商质量工程师、供应商质量工程师组长、采购工程处处长、事业部/子公司供应商质量负责人)
            mqManager.sendTopic(CommunicationTopicConstants.CREATE_SITE_MESSAGE, JacksonUtil.bean2Json(boxMessageMQ(changeApply, mq)));
            // 给注册用户发送通知邮件
            mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailSuccess(changeApply, userInfo)));
            return null;
        });

        // 同步至IAM、SAP、CCMS
        try {
            qualificationChangeManager.basicInfoToIamAndSapAndCcms(qualificationChangeBasicItem, changeApply, changeApply.getCreateBy(), changeApply.getCreateName());
        } catch (Exception e) {
            log.error("同步资质变更信息至SAP等失败，qualificationChangeNo={}", changeApply.getQualificationChangeNo(), e);
        }

    }


    private LambdaUpdateWrapper<SupplierBasicInfoPO> buildBasicInfoUpdateWrapper(QualificationChangeApplyPO changeApply,
                                                                                 QualificationChangeBasicItemPO changeBasicItem,
                                                                                 Date current) {
        LambdaUpdateWrapper<SupplierBasicInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SupplierBasicInfoPO::getSapSupplierCode, changeApply.getSupplierCode());
        updateWrapper.set(changeBasicItem.getSupplierNameChange() == 1, SupplierBasicInfoPO::getSupplierName, changeBasicItem.getSupplierName());
        updateWrapper.set(changeBasicItem.getSupplierShortNameChange() == 1, SupplierBasicInfoPO::getSupplierShortName, changeBasicItem.getSupplierShortName());
        updateWrapper.set(changeBasicItem.getOrganizationNatureChange() == 1, SupplierBasicInfoPO::getOrganizationNature, changeBasicItem.getOrganizationNature());
        updateWrapper.set(changeBasicItem.getEnterpriseTypeChange() == 1, SupplierBasicInfoPO::getEnterpriseType, changeBasicItem.getEnterpriseType());
        updateWrapper.set(changeBasicItem.getRegisteredAmtChange() == 1, SupplierBasicInfoPO::getRegisteredAmt, changeBasicItem.getRegisteredAmt());
        updateWrapper.set(changeBasicItem.getRegisteredCurrencyChange() == 1, SupplierBasicInfoPO::getRegisteredCurrency, changeBasicItem.getRegisteredCurrency());
        updateWrapper.set(changeBasicItem.getEstablishmentDateChange() == 1, SupplierBasicInfoPO::getEstablishmentDate, changeBasicItem.getEstablishmentDate());
        updateWrapper.set(changeBasicItem.getBusinessPeriodStartChange() == 1, SupplierBasicInfoPO::getBusinessPeriodStart, changeBasicItem.getBusinessPeriodStart());
        updateWrapper.set(changeBasicItem.getBusinessPeriodEndChange() == 1, SupplierBasicInfoPO::getBusinessPeriodEnd, changeBasicItem.getBusinessPeriodEnd());
        updateWrapper.set(changeBasicItem.getRegisteredAddressChange() == 1, SupplierBasicInfoPO::getRegisteredAddress, changeBasicItem.getRegisteredAddress());
        updateWrapper.set(changeBasicItem.getCompanyZipCodeChange() == 1, SupplierBasicInfoPO::getCompanyZipCode, changeBasicItem.getCompanyZipCode());
        updateWrapper.set(changeBasicItem.getProductionAddressChange() == 1, SupplierBasicInfoPO::getProductionAddress, changeBasicItem.getProductionAddress());
        updateWrapper.set(changeBasicItem.getBusinessScopeChange() == 1, SupplierBasicInfoPO::getBusinessScope, changeBasicItem.getBusinessScope());
        updateWrapper.set(changeBasicItem.getDunAndBradstreetCodeChange() == 1, SupplierBasicInfoPO::getDunAndBradstreetCode, changeBasicItem.getDunAndBradstreetCode());
        updateWrapper.set(changeBasicItem.getIsOverseasChange() == 1, SupplierBasicInfoPO::getIsOverseas, changeBasicItem.getIsOverseas());
        updateWrapper.set(SupplierBasicInfoPO::getUpdateBy, changeApply.getCreateBy());
        updateWrapper.set(SupplierBasicInfoPO::getUpdateName, changeApply.getCreateName());
        updateWrapper.set(SupplierBasicInfoPO::getUpdateTime, current);
        return updateWrapper;
    }

    private List<CreateSiteMessageMQ> boxMessageMQ(QualificationChangeApplyPO changeApplyPO, TicketStatusChangedMQ ticketInfo) {
        List<CreateSiteMessageMQ> result = new ArrayList<>();
        LambdaQueryWrapper<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        supplierCategoryRelationshipPOLambdaQueryWrapper
                .eq(SupplierCategoryRelationshipPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierCategoryRelationshipPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<SupplierCategoryRelationshipPO> supplierCategoryRelationshipPOS = supplierCategoryRelationshipMapperService.list(supplierCategoryRelationshipPOLambdaQueryWrapper);
        List<String> categoryCodes = supplierCategoryRelationshipPOS.stream()
                .map(SupplierCategoryRelationshipPO::getCategoryCode)
                .collect(Collectors.toList());
        ApiResponse<List<CategoryEngineerResultDTO>> userOne = categoryApi.queryCategoryEngineerByCategoryCodes(categoryCodes);
        // 查询出所有的规划处长，规划工程师
        SysUserQueryReqDTO var1 = new SysUserQueryReqDTO();
        //SRM-1980 授权采购处长不进行通知
        var1.setRoleIds(Arrays.asList("AUTHORIZED_PEM_DIRECTOR", "SQE_MASTER", "SQE"));
        ApiResponse<List<BaseSysUserRespDTO>> userTwo = sysUserApi.findUser(var1);
        // 去重
        HashMap<Long, String> userMap = new HashMap<>();
        //SRM-1980 授权采购处长不进行通知
        userOne.getData().stream().filter(v->!CharSequenceUtil.equalsIgnoreCase(v.getRoleId(),CategoryRoleEnum.AUTHORIZED_PURCHASE_DIRECTOR.getCode()))
                .forEach(categoryEngineerResultDTO -> userMap.put(categoryEngineerResultDTO.getUserId(), categoryEngineerResultDTO.getUserName()));
        userTwo.getData().forEach(baseSysUserRespDTO -> userMap.put(baseSysUserRespDTO.getId(), baseSysUserRespDTO.getUserName()));
        userMap.forEach((k, v) -> {
            CreateSiteMessageMQ notice = new CreateSiteMessageMQ();
            notice.setMessageType(MessageTypeEnum.PRIVATE.getCode());
            notice.setBusinessNo(ticketInfo.getBusinessNo());
            notice.setBusinessType(MessageClsEnum.SUPPLIER_BASIC_ADJUST_EFFECTIVE.getCode());
            notice.setUserId(k);
            notice.setUserName(v);
            notice.setIconType(IconTypeEnum.GENERAL.getCode());
            notice.setTitle(NoticeTemplateEnum.QUALIFICATION_CHANGE_BASIC_COMPLETE.getTitle());
            String content = NoticeTemplateEnum.QUALIFICATION_CHANGE_BASIC_COMPLETE.getContent();
            content = content
                    .replace("${供应商名称}", changeApplyPO.getSupplierName())
                    .replace("${供应商编码}", changeApplyPO.getSupplierCode())
                    .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT));
            notice.setContent(content);
            result.add(notice);
        });
        return result;
    }

    private CreateSendEmailTaskMQ sendEmailSuccess(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo) {
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_BASIC_UPDATE_SUCCESS.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_BASIC_UPDATE_SUCCESS.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private CreateSendEmailTaskMQ sendEmailFail(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo, TicketStatusChangedMQ mq) {
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_BASIC_UPDATE_FAIL.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_BASIC_UPDATE_FAIL.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT))
                .replace("${审批意见}", mq.getApprovalComment()));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

}
