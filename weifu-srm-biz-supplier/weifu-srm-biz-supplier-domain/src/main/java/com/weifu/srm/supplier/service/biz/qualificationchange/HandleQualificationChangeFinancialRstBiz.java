package com.weifu.srm.supplier.service.biz.qualificationchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.weifu.srm.audit.enums.TicketStatusEnum;
import com.weifu.srm.audit.mq.TicketStatusChangedMQ;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.util.DateUtil;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.supplier.convert.QualificationChangeConvert;
import com.weifu.srm.supplier.manager.QualificationChangeManager;
import com.weifu.srm.supplier.manager.UserSupplierRelationshipManager;
import com.weifu.srm.supplier.repository.atomicservice.*;
import com.weifu.srm.supplier.repository.constants.AttachmentBizTypeConstants;
import com.weifu.srm.supplier.repository.enums.EmailTemplateEnum;
import com.weifu.srm.supplier.repository.enums.QualificationChangeStatusEnum;
import com.weifu.srm.supplier.repository.po.*;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.weifu.srm.supplier.repository.constants.SupplierCommonConstants.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandleQualificationChangeFinancialRstBiz {

    private final QualificationChangeApplyMapperService qualificationChangeApplyMapperService;
    private final QualificationChangeFinancialItemMapperService qualificationChangeFinancialItemMapperService;
    private final TransactionTemplate transactionTemplate;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final SupplierFinancialInfoMapperService supplierFinancialInfoMapperService;
    private final QualificationChangeConvert qualificationChangeConvert;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final UserSupplierRelationshipManager userSupplierRelationshipManager;
    private final MqManager mqManager;
    private final QualificationChangeManager qualificationChangeManager;

    public void handle(TicketStatusChangedMQ mq) {
        // 查询资质变更申请表
        QualificationChangeApplyPO changeApplyPO = qualificationChangeApplyMapperService.lambdaQuery()
                .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                .eq(QualificationChangeApplyPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        // 查询供应商基础表信息
        SupplierBasicInfoPO supplierBasicInfoPO = supplierBasicInfoMapperService.lambdaQuery()
                .eq(SupplierBasicInfoPO::getSapSupplierCode, changeApplyPO.getSupplierCode())
                .eq(SupplierBasicInfoPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .one();
        if (TicketStatusEnum.APPROVING.equalsCode(mq.getStatus())) {
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getTicketNo, mq.getTicketNo())
                    .update();
            return;
        }
        // 审批通过
        if (TicketStatusEnum.APPROVED.equalsCode(mq.getStatus())) {
            Date current = new Date();
            // 查询资质变更财务修改信息
            List<QualificationChangeFinancialItemPO> changeFinancialItems = qualificationChangeFinancialItemMapperService.lambdaQuery()
                    .eq(QualificationChangeFinancialItemPO::getQualificationChangeNo, mq.getBusinessNo())
                    .eq(QualificationChangeFinancialItemPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            List<Long> collect = changeFinancialItems.stream().map(QualificationChangeFinancialItemPO::getId).collect(Collectors.toList());
            // 查询修改的相关附件信息
            List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.lambdaQuery()
                    .eq(AttachmentRecordPO::getBusinessType, AttachmentBizTypeConstants.QUALIFICATION_CHANGE_UPDATE_FINANCIAL)
                    .in(AttachmentRecordPO::getBusinessNo, collect)
                    .eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();
            List<SupplierFinancialInfoPO> supplierFinancialInfoPOS = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(changeFinancialItems)) {
                changeFinancialItems.forEach(qualificationChangeFinancialItemPO -> {
                    SupplierFinancialInfoPO financialPO = qualificationChangeConvert.toFinancialPO(qualificationChangeFinancialItemPO);
                    financialPO.setSupplierBasicMsgId(supplierBasicInfoPO.getId());
                    supplierFinancialInfoPOS.add(financialPO);
                });
            }
            // 查询注册用户
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            transactionTemplate.execute(transactionStatus -> {
                // 更新申请单审核状态
                if (ObjectUtil.isNotEmpty(changeFinancialItems)) {
                    supplierFinancialInfoMapperService.lambdaUpdate()
                            .eq(SupplierFinancialInfoPO::getSupplierBasicMsgId, supplierBasicInfoPO.getId())
                            .remove();
                    supplierFinancialInfoMapperService.saveBatch(supplierFinancialInfoPOS);
                    Map<String, Long> bankCodeMapId = new HashMap<>();
                    supplierFinancialInfoPOS.forEach(financialPO -> bankCodeMapId.put(financialPO.getBankAccount(), financialPO.getId()));
                    List<AttachmentRecordPO> attachmentRecords = new ArrayList<>();
                    // 财务信息附件
                    changeFinancialItems.forEach(qualificationChangeFinancialItemPO ->
                            addAttachment(attachmentRecordPOList, attachmentRecords, bankCodeMapId, qualificationChangeFinancialItemPO)
                    );
                    attachmentRecordMapperService.saveBatch(attachmentRecords);
                }

                LambdaUpdateWrapper<QualificationChangeApplyPO> applyUpdateWrapper = new LambdaUpdateWrapper<>();
                applyUpdateWrapper.eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                        .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.AGREE_STATUS.getCode())
                        .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                        .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                        .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                        .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                        .set(QualificationChangeApplyPO::getUpdateTime, current);
                qualificationChangeApplyMapperService.update(applyUpdateWrapper);

                // 发送通知邮件
                mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailSuccess(changeApplyPO, userInfo)));
                return null;
            });

            // 同步至SAP、CCMS
            try {
                qualificationChangeManager.financialInfoToSapAndCcms(changeFinancialItems, changeApplyPO, changeApplyPO.getCreateBy(), changeApplyPO.getCreateName());
            } catch (Exception e) {
                log.error("同步财务信息至SAP等失败，qualificationChangeNo={}", changeApplyPO.getQualificationChangeNo(), e);
            }
        }
        // 审批不通过
        if (TicketStatusEnum.REJECTED.equalsCode(mq.getStatus())) {
            Date current = new Date();
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.REJECT_STATUS.getCode())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, current)
                    .set(QualificationChangeApplyPO::getApproveTime, mq.getUpdateTime())
                    .set(QualificationChangeApplyPO::getApproveOpinion, mq.getApprovalComment())
                    .update();
            // 注册用户查询
            BaseSysUserRespDTO userInfo = userSupplierRelationshipManager.getRegisterUserBySupplierId(supplierBasicInfoPO.getId());
            // 发送通知邮件
            mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(sendEmailFail(changeApplyPO, userInfo, mq)));
        }
        // 审批撤回
        if (TicketStatusEnum.CANCELED.equalsCode(mq.getStatus())) {
            // 更新申请单审核状态
            qualificationChangeApplyMapperService.lambdaUpdate()
                    .eq(QualificationChangeApplyPO::getQualificationChangeNo, mq.getBusinessNo())
                    .set(QualificationChangeApplyPO::getChangeStatus, QualificationChangeStatusEnum.RECALL.getCode())
                    .set(QualificationChangeApplyPO::getUpdateBy, mq.getOperateBy())
                    .set(QualificationChangeApplyPO::getUpdateName, mq.getOperateName())
                    .set(QualificationChangeApplyPO::getUpdateTime, new Date())
                    .update();
        }
    }

    private void addAttachment(List<AttachmentRecordPO> attachmentRecordPOList,
                               List<AttachmentRecordPO> attachmentRecords,
                               Map<String, Long> bankCodeMapId,
                               QualificationChangeFinancialItemPO qualificationChangeFinancialItemPO) {
        attachmentRecordPOList.forEach(attachmentRecordPO -> {
            if (attachmentRecordPO.getBusinessNo().equals(qualificationChangeFinancialItemPO.getId() + "")) {
                AttachmentRecordPO newAttachment = BeanUtil.copyProperties(attachmentRecordPO, AttachmentRecordPO.class);
                newAttachment.setId(null);
                newAttachment.setBusinessNo(bankCodeMapId.get(qualificationChangeFinancialItemPO.getBankAccount()) + "");
                newAttachment.setBusinessType(AttachmentBizTypeConstants.INVOICE_INFORMATION);
                attachmentRecords.add(newAttachment);
            }
        });
    }

    private CreateSendEmailTaskMQ sendEmailSuccess(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo) {
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_FINANCIAL_UPDATE_SUCCESS.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_FINANCIAL_UPDATE_SUCCESS.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

    private CreateSendEmailTaskMQ sendEmailFail(QualificationChangeApplyPO changeApplyPO, BaseSysUserRespDTO userInfo, TicketStatusChangedMQ mq) {
        CreateSendEmailTaskMQ emailReq = new CreateSendEmailTaskMQ();
        emailReq.setTitle(EmailTemplateEnum.SUPPLIER_FINANCIAL_UPDATE_FAIL.getTitle()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT)));
        emailReq.setContent(EmailTemplateEnum.SUPPLIER_FINANCIAL_UPDATE_FAIL.getContent()
                .replace(SUPPLIER_NAME, changeApplyPO.getSupplierName())
                .replace(APPLY_TIME, DateUtil.dateFormat(changeApplyPO.getCreateTime(), TIME_FORMAT))
                .replace("${审批意见}", mq.getApprovalComment()));
        emailReq.setRecipients(userInfo.getEmail());
        emailReq.setBusinessNo(changeApplyPO.getQualificationChangeNo());
        emailReq.setCreateBy(-1L);
        emailReq.setCreateName("system");
        return emailReq;
    }

}
