package com.weifu.srm.supplier.service.impl;

import com.weifu.srm.cache.utils.RedisUtil;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.UserSupplierRelationshipInfoMapperService;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.UserSupplierRelationshipInfoPO;
import com.weifu.srm.supplier.request.SwithSupplierReqDTO;
import com.weifu.srm.supplier.response.SeletedSupplierRespDTO;
import com.weifu.srm.supplier.service.SupplierSwitchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierSwitchServiceImpl implements SupplierSwitchService {

    /** 供应商用户当前选中的供应商Key */
    private final static String SELETED_SUPPLIER_KEY = "SELETED_SUPPLIER_KEY::";

    private final UserSupplierRelationshipInfoMapperService userSupplierRelationshipInfoMapperService;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;
    private final RedisUtil redisUtil;

    @Override
    public void swithSupplier(SwithSupplierReqDTO req) {
        log.info("切换供应商用户当前选中的供应商开始，userId={}，req={}", req.getUserId(), JacksonUtil.bean2Json(req));
        Integer cnt = userSupplierRelationshipInfoMapperService.lambdaQuery()
                .eq(UserSupplierRelationshipInfoPO::getSysUserId, req.getUserId())
                .eq(UserSupplierRelationshipInfoPO::getSupplierBasicInfoId, req.getSupplierId())
                .count();
        if (cnt <= 0) {
            log.error("切换供应商用户当前选中的供应商失败，当前用户与此供应商没有关系，userId={}", req.getUserId());
            throw new BizFailException("当前用户与此供应商没有关系");
        }

        String key = SELETED_SUPPLIER_KEY + req.getUserId();
        redisUtil.set(key, req.getSupplierId() + "", 30 * 24 * 3600L);
        log.info("切换供应商用户当前选中的供应商完成，userId={}", req.getUserId());
    }

    @Override
    public SeletedSupplierRespDTO getSeletedSupplier(Long userId) {
        String key = SELETED_SUPPLIER_KEY + userId;

        String supplierIdStr = (String)redisUtil.get(key);
        if (StringUtils.isEmpty(supplierIdStr)) {
            return null;
        }

        Long supplierId = Long.valueOf(supplierIdStr);
        SupplierBasicInfoPO supplier = supplierBasicInfoMapperService.getById(supplierId);

        SeletedSupplierRespDTO result = null;
        if (supplier != null) {
            result = new SeletedSupplierRespDTO();
            result.setSupplierId(supplier.getId());
            result.setSapSupplierCode(supplier.getSapSupplierCode());
            result.setSapSupplierName(supplier.getSupplierName());
        }
        return result;
    }

}
