package com.weifu.srm.supplier.service.biz;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.convert.AttachmentMessageConvert;
import com.weifu.srm.supplier.convert.SupplierGradeConvert;
import com.weifu.srm.supplier.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierBasicInfoMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyItemMapperService;
import com.weifu.srm.supplier.repository.atomicservice.SupplierGradeAdjustApplyMapperService;
import com.weifu.srm.supplier.repository.po.AttachmentRecordPO;
import com.weifu.srm.supplier.repository.po.SupplierBasicInfoPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyItemPO;
import com.weifu.srm.supplier.repository.po.SupplierGradeAdjustApplyPO;
import com.weifu.srm.supplier.response.SupplierGradeWorkDetailRespDTO;
import com.weifu.srm.supplier.response.SupplierGradeWorkRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierGradeQueryWorkDetailBiz {

    private final SupplierGradeAdjustApplyMapperService supplierGradeAdjustApplyMapperService;
    private final SupplierGradeAdjustApplyItemMapperService supplierGradeAdjustApplyItemMapperService;
    private final SupplierGradeConvert supplierGradeConvert;
    private final TransactionTemplate transactionTemplate;
    private final AttachmentRecordMapperService attachmentRecordMapperService;
    private final AttachmentMessageConvert attachmentMessageConvert;
    private final SupplierBasicInfoMapperService supplierBasicInfoMapperService;

    public SupplierGradeWorkRespDTO queryWorkDetail(String applyNo) {
        LambdaQueryWrapper<SupplierGradeAdjustApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierGradeAdjustApplyPO::getApplyNo, applyNo);
        queryWrapper.eq(SupplierGradeAdjustApplyPO::getIsDelete, YesOrNoEnum.NO.getCode());
        SupplierGradeAdjustApplyPO supplierGradeAdjustApplyPO = supplierGradeAdjustApplyMapperService.getOne(queryWrapper);
        SupplierGradeWorkRespDTO supplierGradeWorkRespDTO = getSupplierGradeWorkRespDTO(supplierGradeAdjustApplyPO);
        if (supplierGradeAdjustApplyPO != null) {
            transactionTemplate.execute(transactionStatus -> {
                // 根据申请单号查询分级调整详情表
                LambdaQueryWrapper<SupplierGradeAdjustApplyItemPO> itemPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                if (supplierGradeAdjustApplyPO.getApplyNo() != null) {
                    itemPOLambdaQueryWrapper.eq(SupplierGradeAdjustApplyItemPO::getApplyNo, supplierGradeAdjustApplyPO.getApplyNo());
                    List<SupplierGradeAdjustApplyItemPO> supplierGradeAdjustApplyItemPO = supplierGradeAdjustApplyItemMapperService.list(itemPOLambdaQueryWrapper);
                    if (supplierGradeAdjustApplyItemPO != null) {
                        List<String> supplierCodes = supplierGradeAdjustApplyItemPO.stream().map(SupplierGradeAdjustApplyItemPO::getSupplierCode).collect(Collectors.toList());
                        // 查询供应商实时状态
                        List<SupplierBasicInfoPO> suppliers = supplierBasicInfoMapperService.lambdaQuery()
                                .in(SupplierBasicInfoPO::getSapSupplierCode, supplierCodes)
                                .list();
                        Map<String, List<SupplierBasicInfoPO>> supplierCodeMapSupplierInfo = suppliers.stream().collect(Collectors.groupingBy(SupplierBasicInfoPO::getSapSupplierCode));
                        List<SupplierGradeWorkDetailRespDTO> supplierGradeWorkDetailRespDTOS = new ArrayList<>();
                        supplierGradeAdjustApplyItemPO.forEach(itemPO -> {
                            SupplierGradeWorkDetailRespDTO applyResult = supplierGradeConvert.toApplyResult(itemPO);
                            applyResult.setSupplierStatus(supplierCodeMapSupplierInfo.get(itemPO.getSupplierCode()).get(0).getStatus());
                            if (applyResult.getSupplierGradeBefore().equals(applyResult.getSupplierGradeAfter())) {
                                applyResult.setIsChange(YesOrNoEnum.NO.getCode());
                            } else {
                                applyResult.setIsChange(YesOrNoEnum.YES.getCode());
                            }
                            supplierGradeWorkDetailRespDTOS.add(applyResult);
                        });
                        supplierGradeWorkRespDTO.setSupplierGradeWorkDetailRespDTOS(supplierGradeWorkDetailRespDTOS);
                    }
                }
                return null;
            });
        }
        return supplierGradeWorkRespDTO;
    }

    private SupplierGradeWorkRespDTO getSupplierGradeWorkRespDTO(SupplierGradeAdjustApplyPO supplierGradeAdjustApplyPO) {
        SupplierGradeWorkRespDTO supplierGradeWorkRespDTO = new SupplierGradeWorkRespDTO();
        supplierGradeWorkRespDTO.setApplyDesc(supplierGradeAdjustApplyPO.getApplyDesc());
        supplierGradeWorkRespDTO.setApplyName(supplierGradeAdjustApplyPO.getApplyName());
        supplierGradeWorkRespDTO.setTicketNo(supplierGradeAdjustApplyPO.getTicketNo());
        supplierGradeWorkRespDTO.setApplyRemark(supplierGradeAdjustApplyPO.getApplyRemark());
        supplierGradeWorkRespDTO.setApplyTime(supplierGradeAdjustApplyPO.getApplyTime());
        // 查询附件相关信息
        LambdaQueryWrapper<AttachmentRecordPO> attachmentRecordPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getBusinessNo, supplierGradeAdjustApplyPO.getApplyAttachments());
        attachmentRecordPOLambdaQueryWrapper.eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode());
        List<AttachmentRecordPO> attachmentRecordPOList = attachmentRecordMapperService.list(attachmentRecordPOLambdaQueryWrapper);
        supplierGradeWorkRespDTO.setSupplierGradeWorkFileRespDTOS(attachmentMessageConvert.toList(attachmentRecordPOList));
        return supplierGradeWorkRespDTO;
    }

}
