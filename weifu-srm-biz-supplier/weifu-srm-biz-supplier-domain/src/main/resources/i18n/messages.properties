## 公共
common.option.result.failure=操作失败
common.option.result.success=操作成功
common.option.result.version.error=数据已被更新，请刷新重试
common.query.result.failure=查询失败
common.query.result.success=查询成功
common.query.result.not.find=查询的数据不存在
common.query.not.value=暂无数据
common.data.already.exists=记录已存在
common.params.cannot.be.empty={0}不能为空
common.template.is.inconformity=模板不匹配
common.data.is.empty=数据为空
common.data.is.duplicate=数据重复
common.params.failure=参数错误
common.update.result.not.find=update data does not exist
common.delete.result.fail=delete data fail
## 信用额度
credit.limit.params.error=信用额度申请单参数错误
credit.limit.not.found=该信用额度申请单不存在
common.delete.result.success=删除成功

## 合作伙伴
new.code.not.exist=新编码不存在，请核实后重试
old.code.not.exist=原编码不存在，请核实后重试
new.code.status.not.effective=新编码的状态不是已生效，不能合并
this.partner.not.exist=当前合作伙伴不存在
partner.cannot.be.disabled=请检查合作伙伴{0}在合同、销售提单、供应商发货指令中的使用情况

## 商品主数据
product.data.null=商品主数据不存在，请确认
product.option.result.part.failure=部分数据操作失败，请刷新重试！
product.chinese.name.exists=中文名称已存在，请重新输入！
product.chinese.name.duplicate=中文名称有重复，请重新输入！
product.display.name.exists=显示名称已存在，请重新输入！
product.display.name.duplicate=显示名称有重复，请检查！
product.import.title=商品主数据
product.import.sheet=商品信息
product.excel.empty=excel内容为空，请填写后再导入!
product.history.not.find=未找到操作历史！
product.customs.multiple=海关编码存在多个，请检查！
product.customs.not.find=海关编码不存在，请检查！
product.group.category.not.find=集团产品分类不存在，请检查！
product.basic.unit.not.find=基本单位不存在，请检查！
product.value.added.taxRate.not.find=进项税不存在，请检查！
product.value.added.salesTaxRate.not.find=销项税不存在，请检查！
merge.query.result.not.find=要合并的商品明细编码不存在或状态不是已生效
product.cannot.be.disabled=请检查商品{0}子类状态及在合同、销售提单、业务库存中的使用情况
product.company.or.department.not.find=数据权限公司或部门不存在，请检查！

## 币种
currency.list.empty=币种列表为空
currency.code.cannot.be.empty=币种代码不能为空
currency.name.cannot.be.empty=币种名称不能为空
currency.code.cannot.be.duplicate=币种代码不能重复
currency.code.already.exists=币种代码{0}已经存在
currency.name.already.exists=币种名称{0}已经存在
currency.not.exist=币种不存在
currency.cannot.be.disabled=币种{0}是某个账套的本位币，不允许禁用

## 会计日历
calendar.list.empty=会计日历列表为空
calendar.code.cannot.be.empty=日历代码不能为空
calendar.type.cannot.be.empty=日历类型不能为空
calendar.desc.cannot.be.empty=日历说明不能为空
calendar.init.open.year=初始打开年度不能为空
calendar.code.cannot.be.duplicate=日历代码不能重复
calendar.code.already.exists=日历代码{0}已经存在
calendar.not.exist=日历不存在

## 账套
ledger.code.cannot.be.empty=分类账代码不能为空
ledger.name.cannot.be.empty=分类账名称不能为空
ledger.chart.cannot.be.empty=会计科目表不能为空
ledger.calendar.cannot.be.empty=会计日历不能为空
ledger.currency.code.cannot.be.empty=本位币不能为空
ledger.must.contain.legal=账套必须包含法人
ledger.code.already.exists=分类账代码{0}已经存在
ledger.not.exist=账套不存在
ledger.default.exchange.type.cannot.be.empty=默认汇率类型不能为空
ledger.init.open.period.cannot.be.empty=初始打开期间不能为空
ledger.legal.code.cannot.be.empty=法人主体代码不能为空
ledger.legal.name.cannot.be.empty=法人主体不能为空
ledger.legal.country.cannot.be.empty=国家(地区)不能为空
ledger.legal.credit.code.cannot.be.empty=统一社会信用代码不能为空
ledger.legal.address.cannot.be.empty=注册地址不能为空
ledger.legal.code.cannot.be.duplicate=法人主体不能重复
ledger.legal.code.already.exists=法人主体{0}已经存在

## 人员主数据
person.sequence.too.long=序列优先级不能大于999
person.id.exists=身份证号码重复，请查看对应人员数据
person.personNo.exists=人员编号已存在
## 导入
excel.import.data.not.found=内容为空，请填写内容后再导入
excel.import.result.success=导入成功
excel.import.template.incorrect=导入的模板不正确
excel.import.analytic.anomaly=解析异常,请使用Excel标准模板
excel.import.result.failure=导入失败

## 权限
account.code.result.save=该员工账号已存在,请重新选择
application.code.result.delete=应用下面含有菜单不能删除
application.code.result.save=应用名称已存在

## 组织主数据
company.code.result.save=当前数据已被其他人修改，请刷新后重试
company.code.result.import.null=请填写数据
company.code.result.order=序列优先级已到最大值，请重新输入
company.code.result.controlAreaCodeExist = 控制范围编号重复\ua
company.code.result.check=公司名称已存在
company.code.result.codeExist=公司编号以存在
nationality.not.find=国籍不存在
department.name.repeat=当前公司下部门名称重复
department.code.repeat=当前公司下部门编号重复
company.prohibit=当前公司下存在人员，不能禁用
storage.code.result.check=仓库名称已存在
department.code.result.check=部门名称已存在
department.code.result.codeExist=部门编号已存在
department.code.result.enable=请先启用父级部门
department.prohibit=当前部门或子部门下存在人员，不能禁用
storage.person.result=该账号绑定的人员异常
control.area.existence.department=所选部门已存在其他控制范围，请重新选择
storage.cannot.be.disabled=请检查仓库{0}在供应商发货指令、物流入库、业务入库、物流出库、采购退换货、销售退换货中的使用情况

## 合作伙伴
partner.bank.not.match=银行信息不匹配
partner.person.not.match=人员信息不匹配

## 汇率
currency.exchange.date.repeat = 日期重复
currency.exchange.database.exists = 数据库已存在相同数据，请修改后重试
currency.exchange.rate.query.not.value=请维护当日汇率
currency.exchange.to.cannot.be.empty=币种至不能为空

## 外部价格
external.price.data.repeat = 相同价格日期，相同产品，相同价格类型，只能存在一条数据

## 供应链主体
supplier.status.exception=草稿、已驳回、已生效状态下才能修改
partner.name.exist=合作伙伴名称已存在
approval.error=审批失败，错误码
supplier.cannot.be.disabled=请检查供应链主体{0}在合同中的使用情况

## MDG
data.send.mdg.error=同步MDG失败:{0}
data.send.mdg.return.error=MDG返回报文异常
data.send.mdg.response.error=MDG响应异常
data.send.mdg.response.success=推送MDG成功

## Supplier
supplier.category.invitation.repeat=供应商准入邀请品类重复
supplier.category.invitation.process=供应商准入审批中的品类
supplier.invitation.no.empty=准入邀请码为必填信息
supplier.invitation.not.exist=不存在有效的准入邀请信息
supplier.register.exist=你所注册的供应商已注册或者存在注册中的流程，请勿重复提交！
supplier.not.be.invited.register=该供应商并未被邀请注册
supplier.admission.create.ticket.error=准入申请发送消息失败
supplier.status.not.support.handle=当前供应商状态不支持该操作，请刷新后再试
supplier.type.not.exists.error=供应商类型错误
supplier.admission.status.not.exists.error=准入状态错误
supplier.admission.type.not.exists.error=准入类型错误
supplier.not.exist.error=供应商不存在
supplier.invitation.already.exist=重复的准入邀请
supplier.invitation.no.empty.error=准入邀请号不能为空
supplier.invitation.name.empty.error=供应商名称不能为空
supplier.invitation.id.empty.error=供应商id不能为空
supplier.invitation.by.empty.error=邀请人不能为空
supplier.admission.type.empty.error=准入类型不能为空
supplier.admission.status.error=存在相同邀请号且准入状态错误的准入申请
supplier.is.black.list=该供应商用户为黑名单用户
supplier.admission.net.gross.compare.error=净利率需要小于毛利率
supplier.admission.start.expire.compare.error=开始资质认证开始日期需早于资质失效日期
invitation.record.status.not.support.update=当前准入邀请的状态，不支持修改内容
supplier.admission.personnel.qty.error=员工总数量不可小于其他人员数量之和
supplier.user.exist.many=您输入的手机号与邮箱已被其他用户使用，请重新输入
supplier.user.phone.exist=您输入的手机号已被其他用户使用，请重新输入
supplier.user.email.exist=您输入的邮箱已被其他用户使用，请重新输入
grey.list.exist=您所选择的灰名单申请列表中存在处理中或已经处理完成的灰名单数据
black.list.exist=您所选择的黑名单申请列表中存在处理中或已经处理完成的黑名单数据
leave.list.exist=您所选择的待退出申请列表中存在处理中或已经处理完成的待退出数据
supplier.had.exited=该供应商已退出，不可被邀请进行准入
invitation.category.duplication=邀请中的品类，已被重新发起
supplier.not.exist=供应商不存在
recover.apply.exist=供应商恢复申请已存在
grey.black.list.exist=存在黑名单或灰名单供应商
not.support.invoice.type=不支持的开票类型
this.assessment.not.support.invoice=该考核单暂不支持开票
exist.grey.or.black.list.apply=该供应商存在待处理的灰/黑名单审批，请处理完成后，再发起恢复流程
supplier.category.status.not.support=无效我的供应商品类资质
request.category.server.fail=查询品类信息失败:{0}
user.can.not.update=当前用户不能修改该数据
supplier.duplicate=供应商重复 {0}
supplier.have.no.grading=供应商分级未设置
some.supplier.is.processing=部分供应商已在流程处理中 {0}
request.todo.server.fail=请求待办服务失败:{0}
request.user.server.fail=请求用户服务失败:{0}
request.purchase.server.fail=请求采购服务失败:{0}
request.user.permission.server.fail=请求用户权限服务失败:{0}
supplier.not.qualified.supplier=这些供应商不是合格供应商不允许进行分级操作: {0}
supplier.qualified.not.enough.long=供应商成为合格供应商不足1年，不能调整为核心供应商或者战略供应商: {0}
supplier.register.not.same.with.others=填写的供应商注册联系人信息与其它准入邀请不一致，请重新填写加载已生效的注册联系人
supplier.admission.factory.area.error=可用厂房面积，不能大于厂房总面积
batch.category.admission.duplicate=本次准入的品类中，存在已生效的潜在或者合格资质品类: {0}
tmp.sample.category.admission.duplicate=本次准入的品类中，存在已生效的临时或样件资质品类: {0}
admission.category.is.process=存在准入进行中的品类: {0}
admission.limit.over=修改值超出准入限额阈值
sample.order.amt.over=当前批次品类已发生的样件订单金额+本次允许的样件订单金额已超过最大交易允许限制: {0}
data.permission.not.exist=目前尚未处理该用户角色的当前列表权限
invitation.division.not.config=邀请人所在事业部配置不完整，无法发起邀请
category.purchase.director.not.exist=供应商品类采购处长未进行配置，无法发起流程
category.cpe.not.exist=当前提交人对应的品类相关的审批人未配置

sync.qualification.change.to.sap.failed=同步资质变更（基本信息）至SAP失败
sync.qualification.change.to.ccms.failed=同步资质变更信息至CCMS失败
sync.qualification.change.basic.to.ccms.failed=同步资质变更（基本信息）至CCMS失败
sync.financial.info.to.sap.failed=同步财务信息至SAP失败
sync.financial.info.to.ccms.failed=同步财务信息至CCMS失败
query.category.info.exception=查询品类信息异常
supplier.category.adj.has.pending.adjustment.application=供应商存在审核中的调整申请
supplier.category.adj.third.level.category.cannot.be.empty=ä¸çº§åç±»å é¤åï¼åä¸äºçº§åç±»[{0}]ä¸è¿éè³å°ä¿çä¸ä¸ªä¸çº§åç±»å³ç³»
find.approver.failed.check.configuration=找寻审批人失败，请检查品类[{0}]上品类组长、采购处长等配置

## 供应商商务政策
supplier.policy.batch.import.file=文件需为Excel文件！
supplier.policy.data.not.exists=数据不存在！
supplier.policy.remove.status=仅支持草稿状态的数据！
supplier.policy.import.fssc.status=只支持审批通过且导入失败的数据！
supplier.policy.not.data=无数据，请新增！
supplier.policy.supplier.not.exists=供应商{0}不存在！
supplier.policy.division.not.exists=事业部{0}不存在！
supplier.policy.subClass.not.exists=业务小类{0}不存在！
supplier.policy.paymentBaseDateType.not.exists=付款基准日期类型{0}不存在或输入数据无效！
supplier.policy.currency.not.exists=币种{0}不存在！
supplier.policy.save.fail=保存失败！
supplier.policy.id.not.null=ID不能为空！
supplier.policy.data.required=存在必填信息未填！
supplier.policy.bill.equals=请确保“票据”+“银企直连”+“其他”+“应收票据-银票”=100！
supplier.policy.division.subClass.one.data=相同的供应商在同一个事业部下的同一个业务小类的商务政策只能存在一条！
supplier.policy.exists.approving.data=提交审批的数据存在审核中的申请！
supplier.policy.file.export.fail=文件导出失败！
supplier.policy.verify.is.number=第{0}行‘{1}’需为数字！
supplier.policy.verify.is.number1=第{0}行数据‘{1}’需为数字！
supplier.policy.verify.data.is.long=第{0}行’{1}‘需为整数！
supplier.policy.verify.data.is.long1=第{0}行数据’{1}‘需为整数！
supplier.policy.apply.save.fail=保存申请明细失败！
supplier.policy.data.not.exist=数据不存在！
supplier.policy.apply.main.save.fail=保存申请单失败！
supplier.policy.apply.not.exist=商务政策申请单不存在！
supplier.policy.file.template.not.matching=导入文件与模板格式不一致！
supplier.policy.not.bank=政策编号不能为空！
supplier.invitation.commit=供应商有同一品类的准入在流程中，无法重新邀请！