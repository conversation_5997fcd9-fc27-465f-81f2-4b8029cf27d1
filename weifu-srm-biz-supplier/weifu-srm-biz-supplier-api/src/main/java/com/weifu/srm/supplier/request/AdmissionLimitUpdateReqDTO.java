package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class AdmissionLimitUpdateReqDTO {

    @ApiModelProperty(value = "供应商准入编号",notes = "")
    @NotNull(message = "供应商准入编号不能为空")
    private String admissionNo ;
    /** 年度采购金额（元） */
    @ApiModelProperty(value = "年度采购金额（元）",notes = "")
    private BigDecimal annualPurchaseAmt ;
    /** 年度采购次数 */
    @ApiModelProperty(value = "年度采购次数",notes = "")
    private Long annualPurchaseCnt ;
    /** 最大单次采购金额（元） */
    @ApiModelProperty(value = "最大单次采购金额（元）",notes = "")
    private BigDecimal mastPurchaseAmt ;
    /** 交易有效开始时间 */
    @ApiModelProperty(value = "交易有效开始时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date admissionStartTime ;
    /** 交易有效终止时间 */
    @ApiModelProperty(value = "交易有效终止时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date admissionEndTime ;
    /** 操作人 */
    @ApiModelProperty(value = "操作人ID",notes = "")
    private Long userId;
    /** 操作人 */
    @ApiModelProperty(value = "操作人名称",notes = "")
    private String userName;
}
