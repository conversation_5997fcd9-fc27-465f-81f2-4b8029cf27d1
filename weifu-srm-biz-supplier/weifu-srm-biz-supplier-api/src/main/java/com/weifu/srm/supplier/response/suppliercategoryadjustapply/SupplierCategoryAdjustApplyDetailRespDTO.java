package com.weifu.srm.supplier.response.suppliercategoryadjustapply;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "供应商品类调整申请详情")
public class SupplierCategoryAdjustApplyDetailRespDTO {

    @ApiModelProperty(name = "SAP（CCRM）供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商名称（英文）")
    private String supplierNameEn;
    @ApiModelProperty(name = "申请类型（调整类型）")
    private String applyType;
    @ApiModelProperty(name = "申请类型名称")
    private String applyTypeName;
    @ApiModelProperty(name = "申请说明")
    private String applyDesc;
    @ApiModelProperty(name = "申请备注")
    private String applyRemark;
    @ApiModelProperty(name = "申请状态")
    private String applyStatus;
    @ApiModelProperty(name = "申请状态名称")
    private String applyStatusName;
    @ApiModelProperty(name = "申请明细")
    private List<SupplierCategoryAdjustApplyItemRespDTO> items;
    @ApiModelProperty(name = "相关附件")
    private List<AttachmentMessageRespDTO> files;

}
