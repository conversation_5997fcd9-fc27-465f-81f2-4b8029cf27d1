package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/7/09 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateDetailRespDTO {
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;
    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String templateDesc;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    private String supplierType;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Integer enable;
    /** 是否默认 */
    @ApiModelProperty(value = "是否默认")
    private Integer isDefault;

    /**
     * 字段明细
     */
    @ApiModelProperty(value = "字段明细")
    private List<QuestionnaireTemplateFieldsRespDTO> fieldDetail;

}
