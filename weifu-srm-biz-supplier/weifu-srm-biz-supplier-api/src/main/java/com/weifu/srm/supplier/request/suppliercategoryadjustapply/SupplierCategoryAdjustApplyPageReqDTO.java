package com.weifu.srm.supplier.request.suppliercategoryadjustapply;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "分页查询供应商品类调整申请请求参数")
public class SupplierCategoryAdjustApplyPageReqDTO extends PageRequest {

    @ApiModelProperty(name = "当前登录用户ID")
    private Long userId;
    @ApiModelProperty(name = "申请编号")
    private String applyNo;
    @ApiModelProperty(name = "工单编号")
    private String ticketNo;
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "申请类型（调整类型）")
    private List<String> applyTypes;
    @ApiModelProperty(name = "申请状态")
    private List<String> applyStatus;
    @ApiModelProperty(name = "申请人姓名")
    private String applyName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "申请时间-开始")
    private Date applyTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "申请时间-结束")
    private Date applyTimeEnd;

}
