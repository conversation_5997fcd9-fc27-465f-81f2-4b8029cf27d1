package com.weifu.srm.supplier.response.suppliercategoryadjustapply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "供应商品类调整申请明细")
public class SupplierCategoryAdjustApplyItemRespDTO {

    @ApiModelProperty(name = "一级品类编码")
    private String oneLevelCategoryCode;
    @ApiModelProperty(name = "一级品类名称")
    private String oneLevelCategoryName;
    @ApiModelProperty(name = "一级品类名称（英文）")
    private String oneLevelCategoryNameEn;
    @ApiModelProperty(name = "二级品类编码")
    private String twoLevelCategoryCode;
    @ApiModelProperty(name = "二级品类名称")
    private String twoLevelCategoryName;
    @ApiModelProperty(name = "二级品类名称（英文）")
    private String twoLevelCategoryNameEn;
    @ApiModelProperty(name = "三级品类编码")
    private String threeLevelCategoryCode;
    @ApiModelProperty(name = "三级品类名称")
    private String threeLevelCategoryName;
    @ApiModelProperty(name = "三级品类名称（英文）")
    private String threeLevelCategoryNameEn;

}
