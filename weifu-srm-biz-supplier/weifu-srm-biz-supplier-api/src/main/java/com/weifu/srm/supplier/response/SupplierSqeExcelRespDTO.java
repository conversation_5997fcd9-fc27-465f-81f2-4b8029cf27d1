package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description SupplierSqeExcelRespDTO
 * @Version 1.0
 */
@Data
@ApiModel(value = "SupplierSqeExcelRespDTO", description = "供应商主责cpe-sqe导出")
@NoArgsConstructor
public class SupplierSqeExcelRespDTO {

    @ExcelProperty("供应商编码")
    @ColumnWidth(20)
    @ApiModelProperty(name = "SAP（CCRM）供应商编码", notes = "")
    private String sapSupplierCode;

    @ExcelProperty("供应商名称")
    @ColumnWidth(50)
    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;

    @ExcelProperty("三级品类")
    @ColumnWidth(50)
    @ApiModelProperty(name = "三级品类", notes = "")
    private String categoryNames;

    @ExcelProperty("供应商状态")
    @ColumnWidth(20)
    @ApiModelProperty(name = "供应商状态", notes = "")
    private String statusName;

    @ExcelProperty("主责SQE")
    @ColumnWidth(20)
    @ApiModelProperty(name = "SQE主责人名称", notes = "")
    private String sqeMainName;
}
