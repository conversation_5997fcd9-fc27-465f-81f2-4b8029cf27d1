package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SupplierAdmissionInvitationSearchListReqDTO extends PageRequest {

    @ApiModelProperty(value = "准入邀请编号",notes = "")
    private String invitationNo ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName ;
    /** 供应商类型 */
    @ApiModelProperty(value = "供应商类型",notes = "")
    private String supplierType ;
    /**准入类型*/
    @ApiModelProperty(value = "准入类型",notes = "")
    private String admissionType;
    /** 准入品类（末级） */
    @ApiModelProperty(value = "准入品类（末级）",notes = "")
    private List<String> admissionCategories ;
    /** 供应商准入邀请状态 */
    @ApiModelProperty(value = "供应商准入邀请状态",notes = "")
    private String supplierAdmissionInvitationStatus ;
    @ApiModelProperty(value = "准入邀请起始查询时间",notes = "")
    private String startTime;
    @ApiModelProperty(value = "准入邀请终止查询时间",notes = "")
    private String endTime;
    @ApiModelProperty(value = "邀请人ID",notes = "")
    private Long createUserId;
    @ApiModelProperty(value = "操作人Id",notes = "")
    private Long operationId;

}
