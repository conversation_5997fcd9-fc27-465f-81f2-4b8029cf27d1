package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:02
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司人员情况",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnairePersonnelRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 员工数量 */
    @ApiModelProperty("员工数量")
    private Integer totalEmployeesQty ;
    /** 管理人员数量 */
    @ApiModelProperty("管理人员数量")
    private Integer managementPersonnelQty ;
    /** 技术人员数量 */
    @ApiModelProperty("技术人员数量")
    private Integer technicalPersonnelQty ;
    /** 质量人员数量 */
    @ApiModelProperty("质量人员数量")
    private Integer qualityPersonnelQty ;
    /** 生产人员数量 */
    @ApiModelProperty("生产人员数量")
    private Integer productionPersonnelQty ;
    /** 生产人员本地占比 */
    @ApiModelProperty("生产人员本地占比")
    private BigDecimal localProductionPersonnelRatioRatio ;
}
