package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:06
 * @Description
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCertificationSaveReqDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {
    @ApiModelProperty("证书序号唯一")
    @NotNull(message = "序号不能为空")
    private Integer index ;
    /** 认证类型 */
    @ApiModelProperty("认证类型")
    @NotNull(message = "认证类型不能为空")
    private String certificationType ;
    /** 认证范围 */
    @ApiModelProperty("认证范围")
    private String certificationScope ;
    /** 认证编号 */
    @ApiModelProperty("认证编号")
    private String certificationNumber ;
    /** 认证机构 */
    @ApiModelProperty("认证机构")
    private String certificationBody ;
    /** 认证起始日期 */
    @ApiModelProperty("认证起始日期")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date certificationStartDate ;
    /** 认证有效期至 */
    @ApiModelProperty("认证有效期至")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date certificationExpiryDate ;
    /** 是否质量资质 */
    @ApiModelProperty("是否质量资质")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasQualityCertification ;
    @ApiModelProperty("备注，当用户选择当证件类型为其它时，必填")
    private String remarks;
    @ApiModelProperty("供应商资质证书")
    private List<AttachmentMessageReqDTO> attachmentLists;
}
