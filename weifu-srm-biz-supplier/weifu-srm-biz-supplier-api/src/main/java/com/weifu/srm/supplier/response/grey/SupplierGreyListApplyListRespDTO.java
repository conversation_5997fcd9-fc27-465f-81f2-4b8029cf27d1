package com.weifu.srm.supplier.response.grey;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SupplierGreyListApplyListRespDTO implements Serializable {
    @ExcelIgnore
    @ApiModelProperty(value = "限制Id",notes = "")
    private Long limitId;
    @ExcelProperty("限制编号")
    @ColumnWidth(20)
    private String limitNo;
    /** 供应商Id */
    @ApiModelProperty(value = "供应商Id",notes = "")
    @ExcelIgnore
    private Long supplierBasicInfoId ;
    @ExcelProperty("供应商编码")
    @ColumnWidth(20)
    private String supplierCode ;
    @ApiModelProperty(value = "供应商名称",notes = "")
    @ExcelProperty("供应商名称")
    @ColumnWidth(20)
    private String supplierName ;
    @ApiModelProperty(value = "供应商状态",notes = "")
    @ExcelProperty("供应商状态")
    @ColumnWidth(20)
    private String supplierStatus ;
    /** 事业部编码 */
    @ApiModelProperty(value = "事业部编码",notes = "")
    @ExcelIgnore
    private String divisionId ;
    @ApiModelProperty(value = "事业部名称",notes = "")
    @ExcelProperty("限制事业部")
    @ColumnWidth(20)
    private String divisionName ;
    /** 二级品类编码 */
    @ApiModelProperty(value = "二级品类编码",notes = "")
    @ExcelIgnore
    private String level2CategoryCode ;
    @ApiModelProperty(value = "二级品类名称",notes = "")
    @ExcelProperty("限制品类")
    @ColumnWidth(20)
    private String level2CategoryName ;
    @ApiModelProperty(value = "灰名单状态",notes = "")
    @ExcelProperty("限制状态")
    @ColumnWidth(20)
    private String status ;
    @ApiModelProperty(value = "申请业务单号",notes = "")
    @ExcelIgnore
    private String validNo ;
    @ApiModelProperty(value = "申请工单号",notes = "")
    @ExcelProperty("灰名单限制工单编号")
    @ColumnWidth(20)
    private String validTicketNo ;
    /** 灰名单申请创建时间 */
    @ApiModelProperty(value = "灰名单申请创建时间",notes = "")
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime ;
    @ApiModelProperty(value = "生效时间",notes = "")
    @ExcelProperty("预计生效时间")
    @ColumnWidth(20)
    private Date validTime ;
    @ApiModelProperty(value = "解除业务单号",notes = "")
    @ExcelIgnore
    private String invalidNo ;
    @ApiModelProperty(value = "解除工单号",notes = "")
    @ExcelProperty("灰名单解除工单编号")
    @ColumnWidth(20)
    private String invalidTicketNo ;
    /** 灰名单解除生效时间 */
    @ApiModelProperty(value = "灰名单解除生效时间",notes = "")
    @ExcelProperty("解除时间")
    @ColumnWidth(20)
    private Date invalidTime ;


}
