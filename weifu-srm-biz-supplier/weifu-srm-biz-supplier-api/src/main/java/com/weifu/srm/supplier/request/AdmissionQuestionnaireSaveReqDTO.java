package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@ApiModel("调查表填写参数")
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireSaveReqDTO {

    /**
     * 1-同意/2-拒绝
     */
    @ApiModelProperty("1-同意/2-拒绝")
    private Integer isAgree;
    /**
     * 调查表模板code
     */
    @NotNull(message = "调查表模板code 不能为null")
    @ApiModelProperty("调查表模板code")
    private String questionnaireTemplateCode;
    /**
     * 准入编号
     */
    @ApiModelProperty("准入编号")
    private String admissionNo;
    /**
     * 准入邀请编号
     */
    @ApiModelProperty("准入邀请编号")
    private String invitationNo;
    /** 供应商ID */
    private Long supplierBasicMsgId ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("准入状态(供应商-品类)")
    private String admissionStatus ;
    /** 邀请人ID */
    @ApiModelProperty("邀请人ID")
    private String admissionInvitationBy ;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人名称")
    private String admissionInvitationName ;
    /** 供应商ID */
    @ApiModelProperty("供应商类型")
    private String supplierType ;
    /** 准入品类类型 */
    @ApiModelProperty("准入类型")
    private String admissionType ;
    /** 供应商ID */
    @ApiModelProperty("供应商编码")
    private String sapSupplierCode ;
    /** 准入品类类型 */
    @ApiModelProperty("供应商名称")
    private String supplierName ;
    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy ;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName ;
    /**
     * 资质认证信息
     */
    @ApiModelProperty("资质认证信息")
    List<AdmissionQuestionnaireCertificationSaveReqDTO> certification;
    /**
     * 客户情况
     */
    @ApiModelProperty("客户情况")
    List<AdmissionQuestionnaireCustomerSaveReqDTO> customer;
    /**
     * 设备情况
     */
    @ApiModelProperty("设备情况")
    List<AdmissionQuestionnaireEquipmentSaveReqDTO> equipment;
    /**
     * 人员情况
     */
    @ApiModelProperty("人员情况")
    List<AdmissionQuestionnairePersonnelSaveReqDTO> personnel;
    /**
     * 表财务状况
     */
    @ApiModelProperty("表财务状况")
    List<AdmissionQuestionnaireFinancialSaveReqDTO> financial;
    /**
     * 新供应商合作意愿
     */
    @ApiModelProperty("新供应商合作意愿")
    List<AdmissionQuestionnaireWillingnessSaveDTO> willingness;
    /**
     * 质量保证能力
     */
    @ApiModelProperty("质量保证能力")
    List<AdmissionQuestionnaireQualityAssuranceSaveReqDTO> qualityAssurance;
    /**
     * 公司规模
     */
    @ApiModelProperty("公司规模")
    List<AdmissionQuestionnaireCompanySizeSaveReqDTO> companySize;
    /**
     * 研发能力
     */
    @ApiModelProperty("研发能力")
    List<AdmissionQuestionnaireRdCapabilitySaveDTO> rdCapability;
    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    List<AdmissionQuestionnaireLogisticsSaveDTO> logistics;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireCompanyInfoSaveReqDTO> companyInfo;
    /**
     * 分供方信息
     */
    @ApiModelProperty("分供方信息")
    List<AdmissionQuestionnaireSubcontractorSaveReqDTO> subcontractor;


}
