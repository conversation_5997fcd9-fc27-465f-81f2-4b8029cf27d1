package com.weifu.srm.supplier.response.leave;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SupplierLeaveApplyListRespDTO implements Serializable {
    @ExcelIgnore
    @ApiModelProperty(value = "限制Id",notes = "")
    private Long limitId;
    @ExcelProperty("编号")
    @ColumnWidth(20)
    private String limitNo;
    /** 供应商Id */
    @ApiModelProperty(value = "供应商Id",notes = "")
    @ExcelIgnore
    private Long supplierBasicInfoId ;
    @ExcelProperty("供应商编码")
    @ColumnWidth(20)
    private String supplierCode ;
    @ApiModelProperty(value = "供应商名称",notes = "")
    @ExcelProperty("供应商名称")
    @ColumnWidth(20)
    private String supplierName ;
    @ApiModelProperty(value = "供应商状态",notes = "")
    @ExcelProperty("供应商状态")
    @ColumnWidth(20)
    private String supplierStatus ;
    @ApiModelProperty(value = "供应商分级",notes = "")
    @ExcelProperty("供应商分级")
    @ColumnWidth(20)
    private String supplierGrade ;
    @ApiModelProperty(value = "待退出业务单号",notes = "")
    @ExcelIgnore
    private String waitExitNo ;
    @ApiModelProperty(value = "待退出工单号",notes = "")
    @ExcelProperty("待退出工单号")
    @ColumnWidth(20)
    private String waitExitTicketNo ;
    /** 待退出申请创建时间 */
    @ApiModelProperty(value = "创建时间",notes = "")
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime ;
    @ApiModelProperty(value = "待退出生效时间",notes = "")
    @ExcelProperty("待退出生效时间")
    @ColumnWidth(20)
    private Date waitExitValidTime ;
    /** 黑名单解除生效时间 */
    @ApiModelProperty(value = "退出时间",notes = "")
    @ExcelProperty("正式退出时间")
    @ColumnWidth(20)
    private Date exitValidTime ;
    @ApiModelProperty(value = "退出业务单号",notes = "")
    @ExcelIgnore
    private String exitNo ;
    @ApiModelProperty(value = "退出工单号",notes = "")
    @ColumnWidth(20)
    @ExcelProperty("正式退出工单号")
    private String exitTicketNo ;
    @ApiModelProperty(value = "退出状态",notes = "")
    @ColumnWidth(20)
    private String status ;



}
