package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QualificationChangeQueryAssociationRespDTO {

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 是否为威孚关联方（0，1）
     */
    @ApiModelProperty("是否为威孚关联方（0，1）")
    @ExcelIgnore()
    private Integer isWeifuRelatedParty;

    @ApiModelProperty("是否为威孚关联方（0，1）")
    @ExcelProperty(value = "是否为威孚关联方")
    private String isWeifuRelatedPartyDesc;
    /**
     * 关联方类型--威孚直接关联方
     */
    @ApiModelProperty("关联方类型")
    @ExcelProperty(value = "关联方类型")
    private String directRelatedPartyTypeDesc;
    /**
     * 是否与威孚供应商存在关联关系（0，1）
     */
    @ApiModelProperty("是否与威孚供应商存在关联关系（0，1）")
    @ExcelIgnore
    private Integer isRelatedToWeifuSupplier;

    @ExcelIgnore
    private List<AssociationRelationItemRespDTO> associationRelationItemRespDTOList;

    @Data
    public static class AssociationRelationItemRespDTO implements Serializable {
        /**
         * 关联供应商名称
         */
        @ApiModelProperty("关联供应商名称")
        private String associationSupplierName;
        /**
         * 关联类型
         */
        @ApiModelProperty("关联类型")
        private String associationType;
    }

}
