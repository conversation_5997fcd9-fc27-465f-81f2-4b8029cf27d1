package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class QualificationChangeQueryBasicRespDTO {
    /** 供应商名称 */
    @ApiModelProperty("供应商名称")
    @ExcelProperty("供应商名称")
    private String supplierName ;
    /** 供应商简称 */
    @ApiModelProperty("供应商简称")
    @ExcelProperty("供应商简称")
    private String supplierShortName ;
    /** 供应商编码 */
    @ApiModelProperty("供应商编码")
    @ExcelProperty("供应商编码")
    private String sapSupplierCode ;
    /** 供应商分级 */
    @ApiModelProperty("供应商分级")
    @ExcelIgnore
    private String supplierClassification ;
    /** 注册时间 */
    @ApiModelProperty("注册时间")
    @ExcelIgnore
    private Date registerTime ;
    /** 首次准入时间 */
    @ApiModelProperty("首次准入时间")
    @ExcelIgnore
    private Date firstAdmissionTime ;
    /** 最新绩效 */
    @ApiModelProperty("最新绩效")
    @ExcelIgnore
    private BigDecimal latestPerformance ;
    /** 供应商状态 */
    @ApiModelProperty("供应商状态")
    @ExcelIgnore
    private String status ;
    /** 统一社会信用代码 */
    @ApiModelProperty("统一社会信用代码")
    @ExcelProperty("统一社会信用代码")
    private String creditCode ;
    /** 机构性质 */
    @ApiModelProperty("机构性质")
    @ExcelProperty("机构性质")
    private String organizationNature ;
    /** 企业类型 */
    @ApiModelProperty("企业类型")
    @ExcelProperty("企业类型")
    private String enterpriseType ;
    /** 注册资金（万）(修改后） */
    @ApiModelProperty("注册资金（万）(修改后）")
    @ExcelProperty("注册资本(万元)")
    private Double registeredAmt ;
    /** 注册币种 */
    @ApiModelProperty("注册币种")
    @ExcelProperty("注册币种")
    private String registeredCurrency ;
    /** 成立日期 */
    @ApiModelProperty("成立日期")
    @ExcelProperty("成立日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date establishmentDate ;
    /** 营业期限起始 */
    @ApiModelProperty("营业期限起始")
    @ExcelProperty("营业期限(开始时间）")
    @DateTimeFormat("yyyy-MM-dd")
    private Date businessPeriodStart ;
    /** 营业期限终止 */
    @ApiModelProperty("营业期限终止")
    @ExcelProperty("营业期限(截止时间）")
    @DateTimeFormat("yyyy-MM-dd")
    private Date businessPeriodEnd ;
    /** 注册地址 */
    @ApiModelProperty("注册地址")
    @ExcelProperty("注册地址")
    private String registeredAddress ;
    /** 公司邮编 */
    @ApiModelProperty("公司邮编")
    @ExcelProperty("邮编")
    private String companyZipCode ;
    /** 生产地址 */
    @ApiModelProperty("生产地址")
    @ExcelProperty("生产地址")
    private String productionAddress ;
    /** 经营范围 */
    @ApiModelProperty("经营范围")
    @ExcelProperty("经营范围")
    private String businessScope ;
    /** 邓白氏编码D-U-N-S */
    @ApiModelProperty("邓白氏编码D-U-N-S")
    @ExcelProperty("邓白氏编码")
    private String dunAndBradstreetCode ;
    /** 法人姓名 */
    @ApiModelProperty("法人姓名")
    @ExcelProperty("法人")
    private String legalPersonName ;
    /** 公司固定电话 */
    @ApiModelProperty("公司固定电话")
    @ExcelProperty("固定电话")
    private String companyPhone ;
    /** 公司传真号码 */
    @ApiModelProperty("公司传真号码")
    @ExcelProperty("传真号码")
    private String companyFax ;
    /** 是否境外(0,1) */
    @ApiModelProperty("是否境外(0,1)")
    @ExcelIgnore
    private Integer isOverseas ;
    /** 是否境外(0,1) */
    @ExcelProperty("境内外关系")
    private String overseasDesc ;
    @ExcelIgnore
    @ApiModelProperty(value = "SRM注册时间", notes = "")
    private Date srmRegisterDate;
    @ExcelIgnore
    @ApiModelProperty(value = "潜在转合格时间", notes = "")
    private Date potentialToQualifiedDate;
    /** 附件 */
    @ExcelIgnore
    private List<AttachmentMessageRespDTO> businessLicenseAttachmentList;
    /** logo附件 */
    @ApiModelProperty("logo附件")
    @ExcelIgnore
    private AttachmentMessageRespDTO logoAttachmentMessage;
    
}
