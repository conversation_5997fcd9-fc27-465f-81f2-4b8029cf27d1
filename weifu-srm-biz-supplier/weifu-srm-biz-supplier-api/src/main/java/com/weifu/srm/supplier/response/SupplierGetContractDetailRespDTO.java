package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SupplierGetContractDetailRespDTO {

    private Integer secrecyContractSign;
    private Integer honestContractSign;
    private Date honestStartDate;
    private Date honestEndDate;
    private List<FrameworkContractDetail> frameworkContractDetailList;

    @Data
    public static class FrameworkContractDetail {
        @ExcelProperty(value = "供应商编码")
        private String supplierCode;
        @ExcelProperty(value = "供应商名称")
        private String supplierName;
        @ExcelProperty(value = "合同编号")
        private String contractNo;
        @ExcelProperty(value = "合同名称")
        private String contractName;
        @ExcelProperty(value = "有效期(开始)")
        private Date startDate;
        @ExcelProperty(value = "有效期(截止)")
        private Date endDate;
    }

}
