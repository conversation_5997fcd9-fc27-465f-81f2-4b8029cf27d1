package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@ApiModel("分页查询供应商品类关系请求入参")
public class SupplierCategoryRelPageReqDTO extends PageRequest {

    @ApiModelProperty(name = "操作人")
    private Long operateBy;
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商状态")
    private List<String> supplierStatus;
    @ApiModelProperty(name = "供应商品类资质")
    private List<String> supplierCategoryStatus;
    @ApiModelProperty(name = "三级品类")
    private List<String> categoryCodes;

}
