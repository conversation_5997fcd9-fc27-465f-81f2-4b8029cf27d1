package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@NoArgsConstructor
public class ChangeLogsRespDTO {
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;
    @ApiModelProperty("变更表名")
    private String changeTable ;
    /** 变更表名索引列 */
    @ApiModelProperty("变更表名索引列")
    private String businessNo ;
    /** 变更字段名 */
    @ApiModelProperty("变更字段名")
    private String changeField ;
    /** 变更前的值 */
    @ApiModelProperty("变更前的值")
    private String oriValue ;
    /** 变更后的值 */
    @ApiModelProperty("变更后的值")
    private String newValue ;
    /** 输出内容 */
    @ApiModelProperty("输出内容")
    private String content ;
    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createBy ;
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime ;
    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private String createName ;
    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateBy ;
    /** 更新人名称 */
    @ApiModelProperty("更新人名称")
    private String updateName ;
    /** 更新时间 */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty("更新时间")
    private Date updateTime ;
    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Integer isDelete ;

}
