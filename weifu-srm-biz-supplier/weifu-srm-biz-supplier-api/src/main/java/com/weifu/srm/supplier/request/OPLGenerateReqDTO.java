package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class OPLGenerateReqDTO implements Serializable {

    @ApiModelProperty("准入编号")
    @NotNull(message = "准入编号不能为空")
    private String admissionNo;
    @ApiModelProperty("准入类型")
    @NotNull(message = "准入类型不能为空")
    private String admissionType;
    private String operationUser;
    private Long operationUserId;
}
