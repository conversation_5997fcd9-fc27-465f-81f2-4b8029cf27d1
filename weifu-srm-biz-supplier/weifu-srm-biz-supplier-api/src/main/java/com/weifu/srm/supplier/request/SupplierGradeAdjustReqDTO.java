package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SupplierGradeAdjustReqDTO {

    @NotNull(message = "申请原因不能为空")
    private String applyDesc;
    private String applyRemark;
    @NotNull(message = "附件信息不能为空")
    @Size(min = 1, message = "附件信息不能为空")
    @Valid
    private List<AttachmentMessageReqDTO> attachmentMessageReqDTOS;

    @NotNull(message = "供应商分级信息不能为空")
    @Size(min = 1, message = "供应商分级信息不能为空")
    @Valid
    private List<SupplierGradeAdjustDetailReqDTO> supplierGradeAdjustDetailReqDTOList;

    /**操作发起人*/
    @ApiModelProperty(value = "发起人",notes = "")
    private String operationUser;
    @ApiModelProperty(value = "发起人Id",notes = "")
    private Long operationUserId;

}
