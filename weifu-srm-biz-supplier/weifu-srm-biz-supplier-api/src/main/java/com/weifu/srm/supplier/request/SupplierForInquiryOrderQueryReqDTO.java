package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplierForInquiryOrderQueryReqDTO extends PageRequest {

    @ApiModelProperty(value = "品类编码", required = true)
    private String categoryCode;
    @ApiModelProperty(value = "是否排除样件供应商 1-是|0-否")
    private Integer isExcludeSampleSupplier;
    @ApiModelProperty(value = "推荐供应商信息 会根据该信息标记供应商是否是推荐供应商")
    private List<SupplierRecommendedReqDTO> recommendedSuppliers;
    @ApiModelProperty(value = "相似物料号")
    private List<String> similarMaterialCodes;
    @ApiModelProperty(value = "相似物料号供应商编码", hidden = true)
    private List<String> similarMaterialCodeSuppliers;
    @ApiModelProperty(hidden = true, value = "是否同步SAP 1-是|0-否")
    private Integer synSapResult;
    @ApiModelProperty(value = "供应商状态", hidden = true)
    private List<String> status;
    @ApiModelProperty(hidden = true, value = "排除的供应商品类资质")
    private List<String> excludeSupplierCategoryStatusAry;
    @ApiModelProperty(value = "事业部编码")
    private List<String> divisionIds;
    @ApiModelProperty(value = "二级品类编码")
    private String categorySecond;
}
