package com.weifu.srm.supplier.response.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PolicyAdjustMainRespDTO extends BaseRespDTO {
    @ApiModelProperty(value = "申请单号")
    private String applyNo;

    @ApiModelProperty(value = "申请类型\n" +
            "新建:CREATE\n" +
            "编辑:UPDATE\n" +
            "调整:ADJUST\n" +
            "批量导入:BATCH_IMPORT")
    private String applyType;

    @ApiModelProperty(value = "申请说明")
    private String applyDesc;

    @ApiModelProperty(value = "申请备注")
    private String applyRemark;

    @ApiModelProperty(value = "是否满足集团规定账期")
    private Boolean isPaymentDays;

    @ApiModelProperty(value = "政策类型\n" +
            "集采:JC\n" +
            "非集采:NON_JC")
    private String policyType;

    @ApiModelProperty("附件数据")
    private List<AttachmentRespDTO> attachments;

    @ApiModelProperty("政策明细数据")
    private List<PolicyAdjustDetailRespDTO> details;
}
