package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:15
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司信息",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireSubcontractorSaveReqDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 分工方名称 */
    @ApiModelProperty("分工方名称")
    private String subcontractorName ;
    /** 供应占比 */
    @ApiModelProperty("供应占比")
    private Double supplyRatio ;
    /** 主要采购商品 */
    @ApiModelProperty("主要采购商品")
    private String mainPurchaseProduct ;
    /** 年采购额 */
    @ApiModelProperty("年采购额")
    private Double annualProcurementAmt ;
}
