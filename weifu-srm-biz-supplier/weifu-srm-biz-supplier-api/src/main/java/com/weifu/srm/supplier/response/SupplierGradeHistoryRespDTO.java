package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class SupplierGradeHistoryRespDTO {

    private String supplierCode;
    private String supplierName;
    private String supplierGradeBefore;
    private String supplierGradeAfter;
    private String ticketNo;
    private String applyNo;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date publishTime;
    private String applyRemark;
    private Integer isChange;

}
