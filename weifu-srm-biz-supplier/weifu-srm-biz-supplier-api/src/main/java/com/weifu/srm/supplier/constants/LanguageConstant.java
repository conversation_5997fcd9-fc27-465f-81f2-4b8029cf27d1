package com.weifu.srm.supplier.constants;

public class LanguageConstant {

    /**
     * 文件需为Excel文件！
     */
    public static final String POLICY_BATCH_IMPORT_FILE = "supplier.policy.batch.import.file";

    /**
     * 数据不存在！
     */
    public static final String POLICY_DATA_NOT_EXISTS = "supplier.policy.data.not.exists";

    /**
     * 仅支持草稿状态的数据！
     */
    public static final String POLICY_REMOVE_STATUS = "supplier.policy.remove.status";

    /**
     * 只支持审批通过且导入失败的数据！
     */
    public static final String POLICY_IMPORT_FSSC_STATUS = "supplier.policy.import.fssc.status";

    /**
     * 无数据，请新增！
     */
    public static final String POLICY_NOT_DATA = "supplier.policy.not.data";

    /**
     * 供应商{0}不存在！
     */
    public static final String POLICY_SUPPLIER_NOT_EXISTS = "supplier.policy.supplier.not.exists";

    /**
     * 事业部{0}不存在！
     */
    public static final String POLICY_DIVISION_NOT_EXISTS = "supplier.policy.division.not.exists";

    /**
     * 业务小类{0}不存在！
     */
    public static final String POLICY_SUBCLASS_NOT_EXISTS = "supplier.policy.subClass.not.exists";

    /**
     *付款基准日期类型{0}不存在或输入数据无效！
     */
    public static final String POLICY_PAYMENTBASEDATETYPE_NOT_EXISTS="supplier.policy.paymentBaseDateType.not.exists";

    /**
     *币种{}不存在！
     */
    public static final String POLICY_CURRENCY_NOT_EXISTS="supplier.policy.currency.not.exists";

    /**
     *保存失败！
     */
    public static final String POLICY_SAVE_FAIL="supplier.policy.save.fail";

    /**
     *ID不能为空！
     */
    public static final String POLICY_ID_NOT_NULL="supplier.policy.id.not.null";

    /**
     *存在必填信息未填！
     */
    public static final String POLICY_DATA_REQUIRED="supplier.policy.data.required";

    /**
     *请确保“票据”+“银企直连”+“其他”+“应收票据-银票”=100！
     */
    public static final String POLICY_BILL_EQUALS="supplier.policy.bill.equals";

    /**
     *相同的供应商在同一个事业部下的同一个业务小类的商务政策只能存在一条！
     */
    public static final String POLICY_DIVISION_SUBCLASS_ONE_DATA="supplier.policy.division.subClass.one.data";

    /**
     *提交审批的数据存在审核中的申请！
     */
    public static final String POLICY_EXISTS_APPROVING_DATA="supplier.policy.exists.approving.data";

    /**
     *文件导出失败！
     */
    public static final String POLICY_FILE_EXPORT_FAIL="supplier.policy.file.export.fail";

    /**
     *第{0}行‘{1}’需为数字！
     */
    public static final String POLICY_VERIFY_IS_NUMBER="supplier.policy.verify.is.number";

    /**
     *第{0}行数据‘{1}’需为数字！
     */
    public static final String POLICY_VERIFY_IS_NUMBER1="supplier.policy.verify.is.number1";

    /**
     *第{0}行’{1}‘需为整数！
     */
    public static final String POLICY_VERIFY_DATA_IS_LONG="supplier.policy.verify.data.is.long";

    /**
     *第{0}行数据’{1}‘需为整数！
     */
    public static final String POLICY_VERIFY_DATA_IS_LONG1="supplier.policy.verify.data.is.long1";

    /**
     *保存申请明细失败！
     */
    public static final String POLICY_APPLY_SAVE_FAIL="supplier.policy.apply.save.fail";

    /**
     *数据不存在！
     */
    public static final String POLICY_DATA_NOT_EXIST="supplier.policy.data.not.exist";

    /**
     *保存申请单失败！
     */
    public static final String POLICY_APPLY_MAIN_SAVE_FAIL="supplier.policy.apply.main.save.fail";

    /**
     *商务政策申请单不存在！
     */
    public static final String POLICY_APPLY_NOT_EXIST="supplier.policy.apply.not.exist";

    /**
     *导入文件与模板格式不一致！
     */
    public static final String POLICY_FILE_TEMPLATE_NOT_MATCHING="supplier.policy.file.template.not.matching";

    /**
     *政策编号不能为空！
     */
    public static final String POLICY_NOT_BANK = "supplier.policy.not.bank";
}
