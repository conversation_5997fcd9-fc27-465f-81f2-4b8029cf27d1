package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireWarehousePageRespDTO implements Serializable {


    /**
     * 准入编号
     */
    @ApiModelProperty("准入编号")
    private String admissionNo;
    /**
     * 调查表编号
     */
    @ApiModelProperty("调查表编号")
    private String questionnaireNo;
    /**
     * 准入邀请编号
     */
    @ApiModelProperty("准入邀请编号")
    private String invitationNo;
    /** 供应商ID */
    @ApiModelProperty("供应商ID")
    private Long supplierBasicMsgId ;
    /** 供应商编码 */
    @ApiModelProperty("供应商编码")
    private String supplierCode ;
    /** 供应商名称 */
    @ApiModelProperty("供应商名称")
    private String supplierName ;
    /** 供应商ID */
    @ApiModelProperty("供应商类型")
    private String supplierType ;
    /** 供应商ID */
    @ApiModelProperty("供应商类型描述")
    private String supplierTypeDesc ;
    /** 准入品类类型 */
    @ApiModelProperty("准入类型")
    private String admissionType ;
    /** 准入品类类型描述 */
    @ApiModelProperty("准入类型描述")
    private String admissionTypeDesc ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商准入状态(供应商-品类)")
    private String admissionStatus ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商准入状态(供应商-品类)")
    private String admissionStatusDesc;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人名称")
    private String admissionInvitationName ;
    /** 准入申请时间 */
    @ApiModelProperty("准入申请时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionApplyTime ;
    /** 准入完成时间 */
    @ApiModelProperty("准入完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionCompleteTime ;
    @ApiModelProperty("创建人")
    private String createBy ;
    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    private String createName ;
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime ;
    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateBy ;
    /** 更新人名 */
    @ApiModelProperty("更新人名")
    private String updateName ;
    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date updateTime ;
    /** 逻辑删除 */
    @ApiModelProperty("逻辑删除")
    private Integer isDelete ;
    @ApiModelProperty("准入品类描述")
    private List<String> admissionCategoryNames;

}
