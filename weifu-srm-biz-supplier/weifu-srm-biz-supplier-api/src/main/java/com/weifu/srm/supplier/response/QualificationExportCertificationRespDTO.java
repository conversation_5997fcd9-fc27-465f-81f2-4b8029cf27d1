package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QualificationExportCertificationRespDTO {

    @ExcelIgnore
    private Long certificationId;
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    /** 认证类型 */
    @ApiModelProperty("认证类型")
    @ExcelProperty(value = "认证类型")
    private String certificationType ;
    /** 认证范围 */
    @ApiModelProperty("认证范围")
    @ExcelProperty(value = "认证范围")
    private String certificationScope ;
    /** 认证编号 */
    @ApiModelProperty("认证编号")
    @ExcelProperty(value = "认证编号")
    private String certificationNumber ;
    /** 认证机构 */
    @ApiModelProperty("认证机构")
    @ExcelProperty(value = "认证机构")
    private String certificationBody ;
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @ExcelProperty(value = "创建时间")
    private Date createTime ;
    /** 认证起始日期 */
    @ApiModelProperty("认证起始日期")
    @ExcelProperty(value = "有效期(开始时间)")
    private Date certificationStartDate ;
    /** 认证有效期至 */
    @ApiModelProperty("认证有效期至")
    @ExcelProperty(value = "有效期(截止时间)")
    private Date certificationExpiryDate ;
    /** 是否质量资质 */
    @ApiModelProperty("是否质量资质")
    @ExcelProperty(value = "是否质量资质")
    private String hasQualityCertification ;
    @ExcelProperty(value = "备注")
    private String remarks;
    @ExcelIgnore
    private List<AttachmentMessageRespDTO> certificationAttachmentList;

}
