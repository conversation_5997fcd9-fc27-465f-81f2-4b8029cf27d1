package com.weifu.srm.supplier.response.suppliercategoryadjustapply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "供应商品类关系树")
public class SupplierCategoryTreeRespDTO {

    @ApiModelProperty(name = "品类编码")
    private String categoryCode;
    @ApiModelProperty(name = "品类名称")
    private String categoryName;
    @ApiModelProperty(name = "品类名称（英文）")
    private String categoryNameEn;
    @ApiModelProperty(name = "采购品类等级")
    private String categoryLevel;
    @ApiModelProperty(name = "状态（标识是否为已存在关系）")
    private String status;
    @ApiModelProperty(name = "子品类")
    private List<SupplierCategoryTreeRespDTO> children;

}
