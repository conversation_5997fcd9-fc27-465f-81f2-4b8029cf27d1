package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdmissionTypeMapSupplierTypeRespDTO implements Serializable {

    @ApiModelProperty(value = "准入类型列表")
    private List<AdmissionType> admissionTypeList;


    @Data
    @NoArgsConstructor
    public static class AdmissionType implements Serializable{
        private String code;
        private String name;
        private List<SupplierType> supplierList;
    }

    @Data
    @NoArgsConstructor
    public static class SupplierType implements Serializable{
        private String code;
        private String name;
    }
}
