package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:11
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司规模",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCompanySizeSaveReqDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 厂房类型（自有租赁） */
    @ApiModelProperty("厂房类型（自有租赁）")
    private String plantType ;
    /** 厂房总面积（m2） */
    @ApiModelProperty("厂房总面积（m2）")
    @Digits(integer = 10, fraction = 2, message = "格式不正确，整数部分最多10位，小数最多2位")
    private BigDecimal totalPlantArea ;
    /** 在用厂房面积 */
    @ApiModelProperty("在用厂房面积")
    @Digits(integer = 10, fraction = 2, message = "格式不正确，整数部分最多10位，小数最多2位")
    private BigDecimal usedPlantArea ;
}
