package com.weifu.srm.supplier.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class SupplierRegistryInfoRespDTO implements Serializable {

    /**供应商在SRM系统中的ID*/
    private Long supplierId;
    /**供应商在SRM系统中的名称*/
    private String supplierName;
    /**供应商当前注册状态*/
    private String status;
    /**供应商编码*/
    private String supplierCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SupplierRegistryInfoRespDTO that = (SupplierRegistryInfoRespDTO) o;
        return Objects.equals(getSupplierId(), that.getSupplierId()) && Objects.equals(getSupplierName(), that.getSupplierName()) && Objects.equals(getStatus(), that.getStatus());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSupplierId(), getSupplierName(), getStatus());
    }
}
