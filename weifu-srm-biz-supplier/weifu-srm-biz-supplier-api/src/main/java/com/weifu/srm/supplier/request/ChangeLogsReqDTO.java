package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class ChangeLogsReqDTO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String bizNo;

}
