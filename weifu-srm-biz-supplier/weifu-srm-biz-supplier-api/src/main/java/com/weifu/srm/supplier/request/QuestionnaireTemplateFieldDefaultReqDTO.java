package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.NumberFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/7/08 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateFieldDefaultReqDTO {
    /**
     * 模板编号
     */
    @ApiModelProperty(value = "模板编号")
    @NotNull
    private String templateCode;
    /**
     * 默认/不默认
     */
    @ApiModelProperty(value = "默认/不默认")
    @NotNull
    @Max(value = 1, message = "默认/不默认只能为0/1")
    @Min(value = 0, message = "默认/不默认只能为0/1")
    @NumberFormat
    private Integer isDefault;
}
