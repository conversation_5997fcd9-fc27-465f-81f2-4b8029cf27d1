package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class SupplierGradeWorkRespDTO {

    private String ticketNo;
    private String applyName;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date applyTime;
    private String applyDesc;
    private String applyRemark;
    private List<AttachmentMessageRespDTO> supplierGradeWorkFileRespDTOS;
    private List<SupplierGradeWorkDetailRespDTO> supplierGradeWorkDetailRespDTOS;
}
