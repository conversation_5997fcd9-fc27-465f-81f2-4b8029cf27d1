package com.weifu.srm.supplier.response.recover;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class SupplierRecoverRecordListRespDTO implements Serializable {

    @ApiModelProperty(value = "申请单号",notes = "")
    private String applyNo ;
    @ApiModelProperty(value = "申请工单号",notes = "")
    private String ticketNo ;
    @ApiModelProperty(value = "供应商编码",notes = "")
    private String supplierCode ;
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName ;
    @ApiModelProperty(value = "创建时间",notes = "")
    private Date createTime ;
    @ApiModelProperty(value = "恢复状态",notes = "")
    private String status ;
    @ApiModelProperty(value = "创建人名称",notes = "")
    private String createName ;



}
