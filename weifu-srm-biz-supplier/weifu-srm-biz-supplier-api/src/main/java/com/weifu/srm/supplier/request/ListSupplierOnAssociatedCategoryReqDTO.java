package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("查询用户负责的品类所关联的供应商入参")
public class ListSupplierOnAssociatedCategoryReqDTO {

    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("查询条件")
    private String searchCriteria;
    @ApiModelProperty("查询条数（默认值10条）")
    private Long limit = 10L;
    @ApiModelProperty(name = "申请类型（调整类型）")
    private String applyType;

}
