package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UserSupplierRelationshipInfoRespDTO {


    @ApiModelProperty(name = "用户表ID",notes = "")
    private Long sysUserId ;

    @ApiModelProperty(name = "角色编码",notes = "")
    private String roleId ;

    @ApiModelProperty(name = "供应商基础表ID",notes = "")
    private Long supplierBasicInfoId ;

    @ApiModelProperty(name = "供应商名称",notes = "")
    private String supplierName ;
}
