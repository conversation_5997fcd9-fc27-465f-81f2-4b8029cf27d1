package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class QualificationChangeUpdateCertificationReqDTO {
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    /** 资质证书ID */
    @ApiModelProperty("资质证书ID")
    private Long certificationId;
    /** 认证类型 */
    @ApiModelProperty("认证类型")
    private String certificationType;
    /** 认证范围 */
    @ApiModelProperty("认证范围")
    private String certificationScope;
    /** 认证编号 */
    @ApiModelProperty("认证编号")
    private String certificationNumber;
    /** 认证机构 */
    @ApiModelProperty("认证机构")
    private String certificationBody;
    /** 认证起始日期 */
    @ApiModelProperty("认证起始日期")
    private Date certificationStartDate;
    /** 认证有效期至 */
    @ApiModelProperty("认证有效期至")
    private Date certificationExpiryDate;
    /** 是否质量资质 */
    @ApiModelProperty("是否质量资质")
    private Integer hasQualityCertification;
    /** 备注 */
    @ApiModelProperty("备注")
    private String remarks;
    /** 操作类型 */
    @ApiModelProperty("操作类型")
    private String operationType;

    private String operationUser;
    private Long operationUserId;
    private List<AttachmentMessageReqDTO> certificationAttachmentList;

    @ApiModelProperty("工单提交说明")
    private String submitDesc;

}
