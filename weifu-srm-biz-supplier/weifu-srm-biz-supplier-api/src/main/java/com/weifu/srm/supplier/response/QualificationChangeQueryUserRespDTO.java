package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class QualificationChangeQueryUserRespDTO {
    @ExcelProperty("供应商编码")
    private String supplierCode;
    @ExcelProperty("供应商名称")
    private String supplierName;
    @ExcelProperty("姓名")
    private String name;
    @ExcelProperty("人员类型")
    private String type;
    @ExcelProperty("电话")
    private String phone;
    @ExcelProperty("邮箱")
    private String email;
    @ExcelIgnore
    private String position;

}
