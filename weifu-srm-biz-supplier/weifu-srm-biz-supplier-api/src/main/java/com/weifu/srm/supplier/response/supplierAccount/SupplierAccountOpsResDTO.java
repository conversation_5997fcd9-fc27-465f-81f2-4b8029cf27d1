package com.weifu.srm.supplier.response.supplierAccount;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierAccountOpsResDTO extends PageRequest implements Serializable {

    @ApiModelProperty(value = "是否退出当前账户，1，退出，0，不退出", required = true)
    private Boolean isLogout;
}
