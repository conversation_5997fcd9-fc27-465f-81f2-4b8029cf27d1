package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RelationSupplierRespDTO {
    @ApiModelProperty("供应商基础表ID")
    private Long id;

    @ApiModelProperty("供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(name = "供应商名称", notes = "")
    private String supplierName;
    /**
     * 供应商名简称
     */
    @ApiModelProperty(name = "供应商名简称", notes = "")
    private String supplierShortName;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称英文", notes = "")
    private String supplierNameEN;
}
