package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商编码所有联系人response
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierContactSupplierCodeRespDTO", description = "供应商编码所有联系人response")
@Data
public class SupplierContactSupplierCodeRespDTO extends SupplierContactAllRespDTO {
    @ApiModelProperty("供应商编码")
    private String supplierCode;
}
