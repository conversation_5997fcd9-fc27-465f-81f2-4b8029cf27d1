package com.weifu.srm.supplier.response.suppliercategorybatchadjustapply;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "供应商品类批量调整申请详情")
public class SupplierCategoryBatchAdjustApplyDetailRespDTO {

    @ApiModelProperty(name = "申请编号")
    private String applyNo;
    @ApiModelProperty(name = "申请说明")
    private String applyDesc;
    @ApiModelProperty(name = "申请状态")
    private String applyStatus;
    @ApiModelProperty(name = "申请状态名称")
    private String applyStatusName;
    @ApiModelProperty(name = "调整前明细")
    private List<SupplierCategoryBatchAdjustApplyItemRespDTO> oldItems;
    @ApiModelProperty(name = "调整后明细")
    private List<SupplierCategoryBatchAdjustApplyItemRespDTO> newItems;
    @ApiModelProperty(name = "相关附件")
    private List<AttachmentMessageRespDTO> files;

}
