package com.weifu.srm.supplier.response.black;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import com.weifu.srm.supplier.response.grey.SupplierGreyListItemRespDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierBlackListApplyDetailRespDTO implements Serializable {

    private String applyNo;

    private String createTime;

    private String createUser;

    private Integer applyType;

    private String applyDesc;

    private String applyRemark;

    private List<AttachmentMessageRespDTO> applyAttachments;

    private List<SupplierBlackListItemRespDTO> blackList;
}
