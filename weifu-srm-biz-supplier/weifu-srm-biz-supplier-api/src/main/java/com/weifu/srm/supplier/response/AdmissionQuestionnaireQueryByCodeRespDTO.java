package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireQueryByCodeRespDTO {
    /**
     * 调查表模板code
     */
  
    private Integer isAgree;
    /**
     * 调查表模板code
     */
    private String questionnaireTemplateCode;
    /**
     * 准入编号
     */
    private String admissionNo;
    /**
     * 准入邀请编号
     */
    private String invitationNo;
    /** 供应商ID */
    private Long supplierBasicMsgId ;
    /** 供应商编码 */
    private String sapSupplierCode ;
    /** 供应商名称 */
    private String supplierName ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商准入状态(供应商-品类)")
    private String admissionStatus ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商准入状态描述")
    private String admissionStatusDesc ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("准入类型")
    private String admissionType ;
    @ApiModelProperty("准入类型描述")
    private String admissionTypeDesc ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商类型")
    private String supplierType ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商类型描述")
    private String supplierTypeDesc ;
    /** 邀请人ID */
    @ApiModelProperty("邀请人ID")
    private String admissionInvitationBy ;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人名称")
    private String admissionInvitationName ;
    @ApiModelProperty("创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime ;
    @ApiModelProperty("准入申请时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionApplyTime ;

    /**
     * 调查表说明
     */
    private String questionnaireDesc;
    /**
     * 资质认证信息
     */
    @ApiModelProperty("资质认证信息")
    List<AdmissionQuestionnaireCertificationRespDTO> certification;
    /**
     * 客户情况
     */
    @ApiModelProperty("客户情况")
    List<AdmissionQuestionnaireCustomerRespDTO> customer;
    /**
     * 设备情况
     */
    @ApiModelProperty("设备情况")
    List<AdmissionQuestionnaireEquipmentRespDTO> equipment;
    /**
     * 人员情况
     */
    @ApiModelProperty("人员情况")
    List<AdmissionQuestionnairePersonnelRespDTO> personnel;
    /**
     * 表财务状况
     */
    @ApiModelProperty("表财务状况")
    List<AdmissionQuestionnaireFinancialRespDTO> financial;
    /**
     * 新供应商合作意愿
     */
    @ApiModelProperty("新供应商合作意愿")
    List<AdmissionQuestionnaireWillingnessRespDTO> willingness;
    /**
     * 质量保证能力
     */
    @ApiModelProperty("质量保证能力")
    List<AdmissionQuestionnaireQualityAssuranceRespDTO> qualityAssurance;
    /**
     * 公司规模
     */
    @ApiModelProperty("公司规模")
    List<AdmissionQuestionnaireCompanySizeRespDTO> companySize;
    /**
     * 研发能力
     */
    @ApiModelProperty("研发能力")
    List<AdmissionQuestionnaireRdCapabilityRespDTO> rdCapability;
    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    List<AdmissionQuestionnaireLogisticsRespDTO> logistics;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireCompanyInfoRespDTO> companyInfo;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireSubcontractorRespDTO> subcontractor;

    @ApiModelProperty("准入品类描述")
    private List<String> admissionCategoryNames;


}
