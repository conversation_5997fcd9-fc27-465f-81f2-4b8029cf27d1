package com.weifu.srm.supplier.response.policy;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.weifu.srm.supplier.request.policy.easyexcel.ImportStatusConvert;
import com.weifu.srm.supplier.request.policy.easyexcel.EnableConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PolicyListExportRespDTO {
    @ExcelProperty(value = "政策编号")
    @ColumnWidth(value = 15)
    private String policyNo;

    @ExcelProperty(value = "导入状态",converter = ImportStatusConvert.class)
    @ColumnWidth(value = 9)
    private String importStatus;

    @ExcelProperty(value = "导入反馈")
    @ColumnWidth(value = 9)
    private String importFeedback;

    @ExcelProperty(value = "供应商编码")
    @ColumnWidth(value = 11)
    private String sapSupplierCode;

    @ExcelProperty(value = "供应商名称")
    @ColumnWidth(value = 11)
    private String supplierName;

    @ExcelProperty(value = "事业部编码")
    @ColumnWidth(value = 11)
    private String divisionCode;

    @ExcelProperty(value = "业务小类代码")
    @ColumnWidth(value = 13)
    private String businessSubclassCode;

    @ExcelProperty(value = "业务小类")
    @ColumnWidth(value = 9)
    private String businessSubclassName;

    @ExcelProperty(value = "业务大类代码")
    @ColumnWidth(value = 13)
    private String businessCategoryCode;

    @ExcelProperty(value = "业务大类")
    @ColumnWidth(value = 9)
    private String businessCategoryName;

    @ExcelProperty(value = "付款周期")
    @ColumnWidth(value = 9)
    private Long paymentCycle;

    @ExcelProperty(value = "付款条款类型")
    @ColumnWidth(value = 13)
    private String paymentTermsType;

    /**
     * 存值使用 不导出 进行字典转换
     * 以paymentBaseDateTypeName 字段导出
     */
    @ExcelIgnore
    private String paymentBaseDateType;

    @ExcelProperty(value = "付款基准日期类型")
    @ColumnWidth(value = 17)
    private String paymentBaseDateTypeName;

    @ExcelProperty(value = "质保等抵押金")
    @ColumnWidth(value = 13)
    private BigDecimal collateralAmount;

    @ExcelProperty(value = "票据（%）")
    @ColumnWidth(value = 11)
    private BigDecimal bill;

    @ExcelProperty(value = "银企直连（%）")
    @ColumnWidth(value = 13)
    private BigDecimal bankEnterpriseLink;

    @ExcelProperty(value = "应收票据-银票（%）")
    @ColumnWidth(value = 17)
    private BigDecimal billReceivable;

    @ExcelProperty(value = "其它（%）")
    @ColumnWidth(value = 11)
    private BigDecimal other;

    @ExcelIgnore
    private String currency;
    @ExcelProperty(value = "币种")
    @ColumnWidth(value = 9)
    private String currencyName;

    @ExcelProperty(value = "是否启用",converter = EnableConvert.class)
    @ColumnWidth(value = 9)
    private Boolean isEnable;

    @ExcelProperty("创建人")
    @ColumnWidth(value = 9)
    private String createName;

    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(value = 21)
    private Date createTime;
}
