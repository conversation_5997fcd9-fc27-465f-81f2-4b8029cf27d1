package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description SupplierBasicInfoSimpleRespDTO
 * @Version 1.0
 */
@Data
@ApiModel(value = "SupplierBasicInfoCategoryRespDTO", description = "供应商主责cpe/sqe列表")
@NoArgsConstructor
public class SupplierBasicInfoCategorySimpleRespDTO {

    @ApiModelProperty(value = "id", notes = "")
    private Long id;

    @ApiModelProperty(name = "SAP（CCRM）供应商编码", notes = "")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;

    @ApiModelProperty(value = "供应商名称-EN", notes = "")
    private String supplierNameEN;

    @ApiModelProperty(name = "供应商状态名称", notes = "")
    private String statusName;
    @ApiModelProperty(name = "供应商状态名称英文", notes = "")
    private String statusNameEn;

    @ApiModelProperty(name = "供应商状态", notes = "")
    private String status;

    @ApiModelProperty(name = "CPE主责人ID", notes = "")
    private Long cpeMainId;

    @ApiModelProperty(name = "CPE主责人名称", notes = "")
    private String cpeMainName;

    @ApiModelProperty(name = "SQE主责人ID", notes = "")
    private Long sqeMainId;

    @ApiModelProperty(name = "SQE主责人名称", notes = "")
    private String sqeMainName;

    @ApiModelProperty(name = "品类id集合", notes = "")
    private List<Long> categoryIdList;

    @ApiModelProperty(name = "三级品类名称集合", notes = "")
    private String categoryNames;

    @ApiModelProperty(name = "三级品类英文名称集合", notes = "")
    private String categoryNameEns;
}
