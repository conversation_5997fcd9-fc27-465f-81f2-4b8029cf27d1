package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:08
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表新供应商合作意愿",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireWillingnessSaveDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 是否愿意签订威孚版本采购框架协议 */
    @ApiModelProperty("是否愿意签订威孚版本采购框架协议")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToSignFrameworkAgreement ;
    /** 是否愿意签订威孚版本质量保证协议 */
    @ApiModelProperty("是否愿意签订威孚版本质量保证协议")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToSignQualityAgreement ;
    /** 是否愿意签订威孚版本廉洁协议 */
    @ApiModelProperty("是否愿意签订威孚版本廉洁协议")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToSignIntegrityAgreement ;
    /** 是否愿意配合提供法人身份证复印件，法人签名，法人授权委托书，备案的公章及合同章印模；上级公司给分公司和挂靠公司开具的担保书或授权书等 */
    @ApiModelProperty("是否愿意配合提供法人身份证复印件，法人签名，法人授权委托书，备案的公章及合同章印模；上级公司给分公司和挂靠公司开具的担保书或授权书等")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToProvideDocuments ;
    /** 是否愿意接受承兑汇票 */
    @ApiModelProperty("是否愿意接受承兑汇票")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToAcceptAcceptanceBill ;
    /** 是否愿意接受账期（3个月） */
    @ApiModelProperty("是否愿意接受账期（3个月）")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToAcceptCreditPeriod ;
    /** 是否愿意接受寄售模式结算 */
    @ApiModelProperty("是否愿意接受寄售模式结算")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToAcceptConsignmentSettlement ;
    /** 是否愿意响应威孚全国范围内需求 */
    @ApiModelProperty("是否愿意响应威孚全国范围内需求")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer willingToRespondToNationalDemand ;
}
