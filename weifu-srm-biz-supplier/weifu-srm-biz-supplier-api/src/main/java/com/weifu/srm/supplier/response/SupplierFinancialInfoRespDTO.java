package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierFinancialInfoRespDTO {

    private Long id;

    @ApiModelProperty(name = "供应商基础表Id")
    private Long supplierBasicMsgId;

    @ApiModelProperty(name = "国家/地区代码")
    private String countryRegionCode;

    @ApiModelProperty(name = "银行代码")
    private String bankCode;

    @ApiModelProperty(name = "银行名称")
    private String bankName;

    @ApiModelProperty(name = "联行号")
    private String bankBranchCode;

    @ApiModelProperty(name = "开户行名称")
    private String accountBankName;

    @ApiModelProperty(name = "银行账号类型")
    private String bankAccountType;

    @ApiModelProperty(name = "账户名称")
    private String accountName ;

    @ApiModelProperty(name = "银行账户")
    private String bankAccount;
    
}
