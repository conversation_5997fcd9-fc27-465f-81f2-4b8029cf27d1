package com.weifu.srm.supplier.response.suppliercategorybatchadjustapply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "供应商品类批量调整申请详情")
public class SupplierCategoryBatchAdjustApplyItemRespDTO {

    @ApiModelProperty(name = "SAP（CCRM）供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商名称（英文）")
    private String supplierNameEn;
    @ApiModelProperty(name = "品类编码")
    private String categoryCode;
    @ApiModelProperty(name = "品类名称")
    private String categoryName;
    @ApiModelProperty(name = "品类名称-英文")
    private String categoryNameEn;
    @ApiModelProperty(name = "供应商品类资质")
    private String supplierCategoryStatus;
    @ApiModelProperty(name = "供应商品类资质名称")
    private String supplierCategoryStatusName;

}
