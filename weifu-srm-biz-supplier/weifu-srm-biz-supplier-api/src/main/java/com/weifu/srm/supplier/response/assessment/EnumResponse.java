package com.weifu.srm.supplier.response.assessment;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/27 10:17
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class EnumResponse {
    private  String code;
    private  String nameCn;
    private  String nameEn;
    public EnumResponse(String code, String nameCn, String nameEn) {
        this.code = code;
        this.nameCn = nameCn;
        this.nameEn = nameEn;
    }
}
