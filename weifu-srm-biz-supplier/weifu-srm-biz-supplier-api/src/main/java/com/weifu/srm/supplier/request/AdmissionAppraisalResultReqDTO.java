package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class AdmissionAppraisalResultReqDTO implements Serializable {

    @ApiModelProperty("工单号")
    @NotNull(message = "工单号不能为空")
    private String ticketNo;
    @NotNull(message = "会议评审结果附件不能为空")
    @ApiModelProperty("会议评审结果附件")
    private List<AttachmentMessageReqDTO> listFiles;
    private String operationUser;
    private Long operationUserId;
}
