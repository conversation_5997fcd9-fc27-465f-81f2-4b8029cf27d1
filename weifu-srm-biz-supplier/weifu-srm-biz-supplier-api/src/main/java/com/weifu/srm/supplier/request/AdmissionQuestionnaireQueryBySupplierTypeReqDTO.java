package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireQueryBySupplierTypeReqDTO implements Serializable {

    /**
     * 准入编号
     */
    @ApiModelProperty("供应商类型")
    @NotNull(message = "供应商类型不能为空")
    private String supplerType;

    /**
     * 准入编号
     */
    @ApiModelProperty("供应商编码")
    @NotNull(message = "供应商编码不能为空")
    private String supplerCode;


}
