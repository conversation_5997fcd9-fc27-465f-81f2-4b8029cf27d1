package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** id */
    @ApiModelProperty(name = "id",notes = "")
    private Long id ;
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String templateDesc;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型描述")
    private String supplierTypeDesc;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    private String supplierType;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Integer enable;
    /**
     * 是否默认
     */
    @ApiModelProperty(value = "是否默认")
    private Integer isDefault;

    @ApiModelProperty(name = "创建人",notes = "")
    private String createBy ;
    /** 创建人名称 */
    @ApiModelProperty(name = "创建人名称",notes = "")
    private String createName ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updateBy ;
    /** 更新人名 */
    @ApiModelProperty(name = "更新人名",notes = "")
    private String updateName ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date updateTime ;
    /** 逻辑删除 */
    @ApiModelProperty(name = "逻辑删除",notes = "")
    private Integer isDelete ;
}
