package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QualificationChangeQueryFinancialUpdateRespDTO {

    /** 国家/地区代码 */
    @ApiModelProperty(name = "国家/地区代码",notes = "")
    private String countryRegionCode ;
    /** 银行代码 */
    @ApiModelProperty(name = "银行代码",notes = "")
    private String bankCode ;
    /** 银行名称 */
    @ApiModelProperty(name = "银行名称",notes = "")
    private String bankName ;
    /** 联行号 */
    @ApiModelProperty(name = "联行号",notes = "")
    private String bankBranchCode ;
    /** 开户行名称 */
    @ApiModelProperty(name = "开户行名称",notes = "")
    private String accountBankName ;
    /** 账户名称 */
    @ApiModelProperty(name = "账户名称",notes = "")
    private String accountName ;
    /** 银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银 */
    @ApiModelProperty(name = "银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银",notes = "")
    private String bankAccountType ;
    /** 银行账号 */
    @ApiModelProperty(name = "银行账号",notes = "")
    private String bankAccount ;

    private List<AttachmentMessageRespDTO> financialAttachmentList;

}
