package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierContractItemRespDTO {
    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同名称")
    private String contractName;

    @ApiModelProperty("合同性质名称")
    private String contractCreateForName;

    @ApiModelProperty("合同性质")
    private String contractCreateFor;

    @ApiModelProperty("有效期开始")
    private Date validityPeriodStart;

    @ApiModelProperty("有效期结束")
    private Date validityPeriodEnd;

}
