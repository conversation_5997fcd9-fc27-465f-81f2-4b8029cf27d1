package com.weifu.srm.supplier.response.recover;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierRecoverRecordDetailRespDTO implements Serializable {
    /** 申请单号 */
    @ApiModelProperty(value = "申请单号",notes = "")
    private String applyNo ;
    @ApiModelProperty(value = "供应商编码",notes = "")
    private String supplierCode;
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName;
    @ApiModelProperty(value = "恢复时间",notes = "")
    private Date recoverTime ;
    /** 说明 */
    @ApiModelProperty(value = "说明",notes = "")
    private String applyDesc ;
    /** 审批状态 */
    @ApiModelProperty(value = "审批状态",notes = "")
    private String status;
    @ApiModelProperty(value = "附件",notes = "")
    private List<AttachmentMessageRespDTO> applyAttachments;

}
