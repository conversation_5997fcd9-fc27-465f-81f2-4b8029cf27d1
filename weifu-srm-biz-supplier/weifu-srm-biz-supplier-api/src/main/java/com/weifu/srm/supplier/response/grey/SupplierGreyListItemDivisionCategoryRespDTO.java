package com.weifu.srm.supplier.response.grey;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class SupplierGreyListItemDivisionCategoryRespDTO implements Serializable {

    private String supplierCode;

    private List<DivisionCategory> divisionCategory;

    private List<CustomMap> divisions;

    private List<CustomMap> categories;

    @Data
    @NoArgsConstructor
    public static class DivisionCategory implements Serializable{

        private String divisionId;
        private String divisionName;
        private List<String> categories;
    }

    @Data
    @NoArgsConstructor
    public static class CustomMap implements Serializable{

        private String id;
        private String name;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SupplierGreyListItemDivisionCategoryRespDTO.CustomMap customMap = (SupplierGreyListItemDivisionCategoryRespDTO.CustomMap) o;
            return Objects.equals(getId(), customMap.getId()) && Objects.equals(getName(), customMap.getName());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getId(), getName());
        }
    }
}
