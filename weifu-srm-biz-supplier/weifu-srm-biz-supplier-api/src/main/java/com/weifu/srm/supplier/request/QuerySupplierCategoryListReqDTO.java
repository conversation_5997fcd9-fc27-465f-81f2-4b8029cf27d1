package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@NoArgsConstructor
public class QuerySupplierCategoryListReqDTO {

    @ApiModelProperty(value = "供应商编码")
    @NotNull
    @Size(min = 1,message = "供应商编码不能为空")
    private List<String> supplierCodes;
    @ApiModelProperty(value = "品类编码")
    private List<String> categoryCodes;
}
