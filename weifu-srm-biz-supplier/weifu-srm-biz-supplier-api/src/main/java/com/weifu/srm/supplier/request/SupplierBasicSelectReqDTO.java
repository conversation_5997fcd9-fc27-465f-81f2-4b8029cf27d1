package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description 供应商下拉列表
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "SupplierBasicSelectReqDTO", description = "根据供应商名称查询有效的供应商")
public class SupplierBasicSelectReqDTO extends PageRequest {

    @ApiModelProperty(value = "供应商编码和简称")
    private String supplierCodeAndShortName;

    @ApiModelProperty(value = "供应商编码和全称")
    private String supplierCodeAndSupplierName;

    @ApiModelProperty(value = "供应商状态")
    private List<String> status;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商名称英文")
    private String supplierNameEn;

    @ApiModelProperty(value = "是否为黑名单 0:否 1:是")
    private Integer isBlackList;

    @ApiModelProperty(value = "是否为灰名单 0:否 1:是")
    private Integer isGreyList;

    @ApiModelProperty(value = "是否过滤供应商编码为空的数据 0:否 1:是")
    private Integer supplierCodeMustExist = 1;

}
