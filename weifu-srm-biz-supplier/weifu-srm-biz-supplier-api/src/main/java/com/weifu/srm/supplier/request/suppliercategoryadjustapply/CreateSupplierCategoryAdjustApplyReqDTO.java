package com.weifu.srm.supplier.request.suppliercategoryadjustapply;

import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "创建供应商品类调整申请请求参数")
public class CreateSupplierCategoryAdjustApplyReqDTO {

    @NotEmpty(message = "供应商编码不能为空")
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;
    @NotEmpty(message = "申请类型（调整类型）不能为空")
    @ApiModelProperty(name = "申请类型（调整类型）")
    private String applyType;
    @ApiModelProperty(name = "申请人")
    private Long applyBy;
    @ApiModelProperty(name = "申请人姓名")
    private String applyName;
    @NotEmpty(message = "申请说明不能为空")
    @ApiModelProperty(name = "申请说明")
    private String applyDesc;
    @ApiModelProperty(name = "申请备注")
    private String applyRemark;
    @NotNull(message = "申请明细不能为空")
    @ApiModelProperty(name = "申请明细")
    private List<CreateSupplierCategoryAdjustApplyItemReqDTO> items;
    @ApiModelProperty(name = "普通附件")
    private List<AttachmentMessageReqDTO> files;

    @Data
    public static class CreateSupplierCategoryAdjustApplyItemReqDTO {

        @ApiModelProperty(name = "品类编码")
        private String categoryCode;
        @ApiModelProperty(name = "采购品类等级")
        private String categoryLevel;

    }

}
