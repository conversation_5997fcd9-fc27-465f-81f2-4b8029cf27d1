package com.weifu.srm.supplier.response;

import com.weifu.srm.common.enums.YesOrNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class QualificationChangeQueryApplyRespDTO {

    /**
     * 资质变更编号
     */
    @ApiModelProperty("资质变更编号")
    private String qualificationChangeNo;
    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商编码")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）
     */
    @ApiModelProperty("变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）")
    private String changeType;
    /**
     * 变更状态（已提交；审批通过；审批拒绝；已撤回）
     */
    @ApiModelProperty("变更状态（已提交；审批通过；审批拒绝；已撤回）")
    private String changeStatus;
    /**
     * 工单编号
     */
    @ApiModelProperty("工单编号")
    private String ticketNo;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    private Date approveTime;
    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String approveOpinion;
    /**
     * 导入sap是否成功
     */
    @ApiModelProperty("导入sap是否成功")
    private Integer sapImport;
    /**
     * 导入SAP失败时，记录的错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;
    /**
     * 是否能够导入sap
     */
    @ApiModelProperty("是否能够导入sap")
    private Integer canSapImport = YesOrNoEnum.NO.getCode();

}
