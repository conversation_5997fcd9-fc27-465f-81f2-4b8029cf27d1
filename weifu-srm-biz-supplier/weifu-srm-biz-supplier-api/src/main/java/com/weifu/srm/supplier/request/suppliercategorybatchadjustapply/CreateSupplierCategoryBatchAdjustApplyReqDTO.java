package com.weifu.srm.supplier.request.suppliercategorybatchadjustapply;

import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "创建供应商品类批量调整申请请求参数")
public class CreateSupplierCategoryBatchAdjustApplyReqDTO {

    @NotNull(message = "申请人不能为空")
    @ApiModelProperty(name = "申请人")
    private Long applyBy;
    @NotEmpty(message = "申请人姓名")
    @ApiModelProperty(name = "申请人姓名")
    private String applyName;
    @NotEmpty(message = "申请说明不能为空")
    @ApiModelProperty(name = "申请说明")
    private String applyDesc;
    @NotNull(message = "申请明细-调整前")
    @ApiModelProperty(name = "申请明细-调整前")
    private List<BeforeAdjustItemReqDTO> oldItems;
    @NotNull(message = "申请明细-调整后")
    @ApiModelProperty(name = "申请明细-调整后")
    private List<AfterAdjustItemReqDTO> newItems;
    @ApiModelProperty(name = "普通附件")
    private List<AttachmentMessageReqDTO> files;

    /**
     * 调整前的信息
     */
    @Data
    public static class BeforeAdjustItemReqDTO {

        @ApiModelProperty(name = "供应商编码")
        private String sapSupplierCode;
        @ApiModelProperty(name = "调整前品类编码")
        private String categoryCode;
        @ApiModelProperty(name = "调整前品类名称")
        private String categoryName;
        @ApiModelProperty(name = "调整前品类名称-英文")
        private String categoryNameEn;
        @ApiModelProperty(name = "调整前供应商品类资质")
        private String supplierCategoryStatus;

    }

    /**
     * 调整后的信息
     */
    @Data
    public static class AfterAdjustItemReqDTO {

        @ApiModelProperty(name = "供应商编码")
        private String sapSupplierCode;
        @ApiModelProperty(name = "调整后品类编码")
        private String categoryCode;
        @ApiModelProperty(name = "调整后品类名称")
        private String categoryName;
        @ApiModelProperty(name = "调整后品类名称-英文")
        private String categoryNameEn;
        @ApiModelProperty(name = "调整后供应商品类资质")
        private String supplierCategoryStatus;

    }

}
