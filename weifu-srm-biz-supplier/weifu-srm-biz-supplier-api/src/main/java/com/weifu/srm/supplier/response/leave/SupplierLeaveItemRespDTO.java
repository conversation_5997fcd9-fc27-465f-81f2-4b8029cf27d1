package com.weifu.srm.supplier.response.leave;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class SupplierLeaveItemRespDTO implements Serializable {

    private Integer limitId;

    private String supplierCode;

    private String supplierName;

    private String supplierStatus;

    private Date waitExitValidTime;

    private String grade;
}
