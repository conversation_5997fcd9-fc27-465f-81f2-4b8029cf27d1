package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:10
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表质量保证能力",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireQualityAssuranceSaveReqDTO  extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 是否入厂检验 */
    @ApiModelProperty("是否入厂检验")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasIncomingInspection ;
    /** 入厂检验人数 */
    @ApiModelProperty("入厂检验人数")
    private Integer incomingInspectionPersonnelQty ;
    /** 是否出厂检验 */
    @ApiModelProperty("是否出厂检验")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasOutgoingInspection ;
    /** 出厂检验人数 */
    @ApiModelProperty("出厂检验人数")
    private Integer outgoingInspectionPersonnelQty ;
    /** 是否生产过程检验 */
    @ApiModelProperty("是否生产过程检验")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasInProcessInspection ;
    /** 生产过程检验人数 */
    @ApiModelProperty("生产过程检验人数")
    private Integer inProcessInspectionPersonnelQty ;
}
