package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:06
 * @Description
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCertificationRespDTO implements Serializable {
    @ApiModelProperty("主键ID")
    private Long id;
    /** 调查表编号 */
    @ApiModelProperty("准入编号")
    private String admissionNo ;
    /** 认证类型 */
    @ApiModelProperty("认证类型")
    private String certificationType ;
    /** 认证类型 */
    @ApiModelProperty("认证类型描述")
    private String certificationTypeDesc ;
    /** 认证范围 */
    @ApiModelProperty("认证范围")
    private String certificationScope ;
    /** 认证编号 */
    @ApiModelProperty("认证编号")
    private String certificationNumber ;
    /** 认证机构 */
    @ApiModelProperty("认证机构")
    private String certificationBody ;
    /** 认证起始日期 */
    @ApiModelProperty("认证起始日期")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date certificationStartDate ;
    /** 认证有效期至 */
    @ApiModelProperty("认证有效期至")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date certificationExpiryDate ;
    /** 是否质量资质 */
    @ApiModelProperty("是否质量资质")
    private Integer hasQualityCertification ;
    @ApiModelProperty("备注，当用户选择当证件类型为其它时，必填")
    private String remarks;
    @ApiModelProperty("资质证书")
    private List<AttachmentMessageRespDTO> attachmentLists;
}
