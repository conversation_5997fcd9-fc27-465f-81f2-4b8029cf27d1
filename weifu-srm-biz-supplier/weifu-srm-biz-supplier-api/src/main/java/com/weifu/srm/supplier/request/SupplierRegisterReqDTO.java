package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
@Data
public class SupplierRegisterReqDTO {

    /** 供应商ID*/
    @ApiModelProperty(value = "供应商Id", notes = "")
    private Long supplierBasicInfoId;
    /** 注册业务单号*/
    @ApiModelProperty(value = "供应商注册单号", notes = "")
    private String registerNo;
    /** 供应商名称*/
    @NotNull(message = "供应商名称不能为空")
    @ApiModelProperty(value = "供应商名称", notes = "")
    @Length(max = 70, message = "供应商名称不能超过70个字符")
    private String supplierName;
    /** 供应商名称*/
    @ApiModelProperty(value = "供应商名称_EN", notes = "")
    private String supplierNameEN;
    /** 统一社会信用代码*/
    @ApiModelProperty(value = "统一社会信用代码", notes = "")
    @Length(max = 32, message = "统一社会信用代码不能超过32个字符")
    private String creditCode;
    /** 机构性质*/
    @ApiModelProperty(value = "机构性质", notes = "")
    private String organizationNature;
    /** 是否境外(0,1)*/
    @ApiModelProperty(value = "是否境外(0,1)", notes = "")
    private Integer isOverseas;
    /** 企业类型*/
    @ApiModelProperty(value = "企业类型", notes = "")
    private String enterpriseType;
    /** 注册资金（万）*/
    @NotNull(message = "注册资金不能为空")
    @ApiModelProperty(value = "注册资金（万）", notes = "")
    private Double registeredAmt;
    /*** 注册币种*/
    @ApiModelProperty(value = "注册币种", notes = "")
    @NotNull(message = "注册币种不能为空")
    private String registeredCurrency;
    /** 成立日期*/
    @NotNull(message = "成立日期不能为空")
    @ApiModelProperty(value = "成立日期", notes = "")
    private Date establishmentDate;
    /*** 营业期限类型*/
    @ApiModelProperty(value = "营业期限类型", notes = "")
    private String businessPeriodType;
    /*** 营业期限起始*/
    @ApiModelProperty(value = "营业期限起始", notes = "")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessPeriodStart;
    /** 营业期限终止*/
    @ApiModelProperty(value = "营业期限终止", notes = "")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessPeriodEnd;
    /*** 注册地址*/
    @NotNull(message = "注册地址不能为空")
    @ApiModelProperty(value = "注册地址", notes = "")
    @Length(max = 1000, message = "注册地址不能超过35个字符")
    private String registeredAddress;
    /*** 公司邮编*/
    @NotNull(message = "公司邮编不能为空")
    @ApiModelProperty(value = "公司邮编", notes = "")
    @Length(max = 10, message = "公司邮编不能超过10个字符")
    private String companyZipCode;
    /*** 生产地址*/
    @NotNull(message = "生产地址不能为空")
    @ApiModelProperty(value = "生产地址", notes = "")
    private String productionAddress;
    /*** 经营范围*/
    @ApiModelProperty(value = "经营范围", notes = "")
    private String businessScope;
    /** 营业执照附件*/
    @ApiModelProperty(value = "营业执照附件", notes = "")
    private AttachmentMessageReqDTO businessLicenseAttachmentFile;
    /** 邓白氏编码D-U-N-S*/
    @ApiModelProperty(value = "邓白氏编码D-U-N-S", notes = "")
    private String dunAndBradstreetCode;
    /*** 法人姓名*/
    @NotNull(message = "法人姓名不能为空")
    @ApiModelProperty(value = "法人姓名", notes = "")
    private String legalPersonName;
    /** 法人邮箱*/
    @ApiModelProperty(value = "法人邮箱", notes = "")
    private String legalPersonEmail;
    /*** 法人电话*/
    @ApiModelProperty(value = "法人电话", notes = "")
    @Length(max = 16, message = "法人电话不能超过16个字符")
    private String legalPersonPhone;
    /** 是否为威孚关联方（0，1*/
    @NotNull(message = "是否为威孚关联方不能为空")
    @ApiModelProperty(value = "是否为威孚关联方（0，1）", notes = "")
    private Integer isWeifuRelatedParty;
    /*** 关联方类型--威孚直接关联方*/
    @ApiModelProperty(value = "关联方类型--威孚直接关联方", notes = "")
    private String directRelatedPartyTypeDesc;
    /** 是否与威孚供应商存在关联关系（0，1）*/
    @NotNull(message = "是否与威孚供应商存在关联关系不能为空")
    @ApiModelProperty(value = "是否与威孚供应商存在关联关系（0，1）", notes = "")
    private Integer isRelatedToWeifuSupplier;
    /** 存在关联关系的威孚供应商名称*/
    @ApiModelProperty(value = "是否与威孚供应商存在关联关系的供应商列表", notes = "")
    private List<QualificationChangeUpdateAssociationReqDTO.QualificationChangeUpdateAssociationItemReqDTO> associationRelationItemRespDTOList;
    /** 公司固定电话 */
    @ApiModelProperty(value = "公司固定电话",notes = "")
    private String companyPhone ;
    /** 公司传真号码 */
    @ApiModelProperty(value = "公司传真号码",notes = "")
    @Length(max = 30, message = "公司传真号码不能超过30个字符")
    private String companyFax ;
    /** 公司网址 */
    @ApiModelProperty(value = "公司网址",notes = "")
    private String companyWebUrl ;
    /** 商务联系人姓名 */
    @NotNull(message = "商务联系人姓名不能为空")
    @ApiModelProperty(value = "商务联系人姓名",notes = "")
    private String businessContactName ;
    /** 商务联系人邮箱 */
    @NotNull(message = "商务联系人邮箱不能为空")
    @ApiModelProperty(value = "商务联系人邮箱",notes = "")
    private String businessContactEmail ;
    /** 商务联系人电话 */
    @NotNull(message = "商务联系人电话不能为空")
    @ApiModelProperty(value = "商务联系人电话",notes = "")
    private String businessContactPhone ;
    /** 商务联系人职位 */
    @ApiModelProperty(value = "商务联系人职位",notes = "")
    private String businessContactPosition ;
    /** 质量联系人姓名 */
    @NotNull(message = "质量联系人姓名不能为空")
    @ApiModelProperty(value = "质量联系人姓名",notes = "")
    private String qualityContactName ;
    /** 质量联系人邮箱 */
    @NotNull(message = "质量联系人邮箱不能为空")
    @ApiModelProperty(value = "质量联系人邮箱",notes = "")
    private String qualityContactEmail ;
    /** 质量联系人电话 */
    @NotNull(message = "质量联系人电话不能为空")
    @ApiModelProperty(value = "质量联系人电话",notes = "")
    private String qualityContactPhone ;
    /** 质量联系人职位 */
    @ApiModelProperty(value = "质量联系人职位",notes = "")
    private String qualityContactPosition ;
    /** 财务联系人姓名 */
    @NotNull(message = "财务联系人姓名不能为空")
    @ApiModelProperty(value = "财务联系人姓名",notes = "")
    private String financeContactName ;
    /** 财务联系人邮箱 */
    @NotNull(message = "财务联系人邮箱不能为空")
    @ApiModelProperty(value = "财务联系人邮箱",notes = "")
    private String financeContactEmail ;
    /** 财务联系人电话 */
    @NotNull(message = "财务联系人电话不能为空")
    @ApiModelProperty(value = "财务联系人电话",notes = "")
    private String financeContactPhone ;
    /** 财务联系人职位 */
    @ApiModelProperty(value = "财务联系人职位",notes = "")
    private String financeContactPosition ;
    /** 财务账号信息 */
    @ApiModelProperty(value = "财务账号信息",notes = "")
    @NotNull(message = "财务账号信息不能为空")
    @Valid
    private List<FinancialInfoReqDTO> financialMsgList;
    /**操作人*/
    private String operationUser;
    private Long operationUserId;

    @Data
    public static class FinancialInfoReqDTO implements Serializable {
        /** 国家/地区代码 */
        @ApiModelProperty(value = "国家/地区代码",notes = "")
        @NotNull(message = "地区代码不能为空")
        private String countryRegionCode ;
        /** 银行代码 */
        @NotNull(message = "银行代码不能为空")
        @ApiModelProperty(value = "银行代码",notes = "")
        private String bankCode ;
        /** 银行名称 */
        @NotNull(message = "银行名称不能为空")
        @ApiModelProperty(value = "银行名称",notes = "")
        private String bankName ;
        /** 联行号 */
        @NotNull(message = "联行号不能为空")
        @ApiModelProperty(value = "联行号",notes = "")
        @Length(max = 15, message = "联行号不能超过15个字符")
        private String bankBranchCode ;
        /** 境外银行代码 */
        @ApiModelProperty(value = "境外银行代码",notes = "")
        private String foreignBankCode ;
        /** 开户行名称 */
        @NotNull(message = "开户行名称不能为空")
        @ApiModelProperty(value = "开户行名称",notes = "")
        private String accountBankName ;
        /** 账户名称 */
        @NotNull(message = "账户名称不能为空")
        @ApiModelProperty(value = "账户名称",notes = "")
        @Length(max = 60, message = "账户名称不能超过60个字符")
        private String accountName ;
        /** 银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银 */
        @ApiModelProperty(value = "银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银",notes = "")
        @NotNull(message = "银行账号类型不能为空")
        private String bankAccountType ;
        /** 银行账号 */
        @NotNull(message = "银行账号不能为空")
        @ApiModelProperty(value = "银行账号",notes = "")
        private String bankAccount ;
        /** 开票资料附件 */
        @ApiModelProperty(value = "开票资料附件",notes = "")
        private AttachmentMessageReqDTO invoiceDataAttachmentFile ;
    }
}
