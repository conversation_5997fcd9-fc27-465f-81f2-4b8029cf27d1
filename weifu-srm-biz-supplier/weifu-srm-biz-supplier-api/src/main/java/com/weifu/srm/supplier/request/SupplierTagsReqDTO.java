package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "SupplierTagsSaveReqDTO", description = "供应商设置Tags")
public class SupplierTagsReqDTO  extends SupplierBasicInfoListReqDTO{

    @ApiModelProperty(value = "供应商编码列表", notes = "")
    private List<String> sapSupplierCodes;
    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;
    @ApiModelProperty(value = "供应商状态", notes = "")
    private String status;
    @ApiModelProperty(value = "供应商分级", notes = "")
    private String supplierClassification;
    @ApiModelProperty(value = "供应商Tags", notes = "")
    private List<String> tags;
    /**0:否 1:是*/
    @ApiModelProperty(value = "灰名单状态1:是 0:否", notes = "")
    private Integer greyListStatus;
    /**0:否 1:是*/
    @ApiModelProperty(value = "黑名单状态1:是 0:否", notes = "")
    private Integer blackListStatus;
    @ApiModelProperty(value = "标签codes", notes = "")
    @NotNull(message = "标签codes不能为空")
    private List<String> tagCodes;
    @ApiModelProperty(value = "用户Id", notes = "")
    private Long userId;
    @ApiModelProperty(value = "用户名", notes = "")
    private String userName;
}
