package com.weifu.srm.supplier.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Data
public class SupplierGradeImportRespDTO {

    private Integer supplierSuccessCount;
    private Integer supplierFailCount;
    private List<SupplierGradeImportCheckCertificateRespDTO> supplierGradeImportCheckCertificateRespDTOList;

}
