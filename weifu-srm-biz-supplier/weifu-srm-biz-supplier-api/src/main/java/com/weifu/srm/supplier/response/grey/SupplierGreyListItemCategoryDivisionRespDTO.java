package com.weifu.srm.supplier.response.grey;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class SupplierGreyListItemCategoryDivisionRespDTO implements Serializable {

    private String supplierCode;

    private List<CategoryDivision> categoryDivisions;

    private List<CustomMap> divisions;

    private List<CustomMap> categories;

    @Data
    @NoArgsConstructor
    public static class CategoryDivision implements Serializable{

        private String categoryCode;
        private String categoryName;
        private List<String> divisionIds;
    }

    @Data
    @NoArgsConstructor
    public static class CustomMap implements Serializable{

        private String id;
        private String name;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CustomMap customMap = (CustomMap) o;
            return Objects.equals(getId(), customMap.getId()) && Objects.equals(getName(), customMap.getName());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getId(), getName());
        }
    }

}
