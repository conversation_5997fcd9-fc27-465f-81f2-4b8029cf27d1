package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QualificationChangeUpdateAssociationReqDTO {

    private Long supplierId;
    /** 是否为威孚关联方（0，1） */
    @ApiModelProperty(name = "是否为威孚关联方（0，1）",notes = "")
    private Integer isWeifuRelatedParty ;
    /** 关联方类型--威孚直接关联方 */
    @ApiModelProperty(name = "关联方类型--威孚直接关联方",notes = "")
    private String directRelatedPartyTypeDesc ;
    /** 是否与威孚供应商存在关联关系（0，1） */
    @ApiModelProperty(name = "是否与威孚供应商存在关联关系（0，1）",notes = "")
    private Integer isRelatedToWeifuSupplier ;
    private List<QualificationChangeUpdateAssociationItemReqDTO> qualificationChangeUpdateAssociationItemReqDTOS;
    private String operationUser;
    private Long operationUserId;

    @Data
    public static class QualificationChangeUpdateAssociationItemReqDTO implements Serializable {
        /** 关联供应商名称 */
        @ApiModelProperty(name = "关联供应商名称",notes = "")
        private String associationSupplierName ;
        /** 关联类型 */
        @ApiModelProperty(name = "关联类型",notes = "")
        private String associationType ;
    }

}
