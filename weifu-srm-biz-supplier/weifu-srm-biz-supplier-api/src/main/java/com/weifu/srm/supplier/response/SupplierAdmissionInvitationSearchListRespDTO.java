package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@Data
@NoArgsConstructor
public class SupplierAdmissionInvitationSearchListRespDTO implements Serializable {

    @ApiModelProperty(value = "准入邀请编号",notes = "")
    private String invitationNo ;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    private String supplierName ;
    /** 供应商类型 */
    @ApiModelProperty(value = "供应商类型",notes = "")
    private String supplierType ;
    /**准入类型*/
    @ApiModelProperty(value = "准入类型名称",notes = "")
    private String admissionType;
    /**准入类型*/
    @ApiModelProperty(value = "准入类型编码",notes = "")
    private String admissionTypeCode;
    /** 准入品类（末级） */
    @ApiModelProperty(value = "准入品类（末级）",notes = "")
    private List<String> admissionCategories ;
    /** 供应商准入邀请状态 */
    @ApiModelProperty(value = "供应商准入邀请状态",notes = "")
    private String supplierAdmissionInvitationStatus ;
    @ApiModelProperty(value = "供应商准入邀请状态",notes = "")
    private String supplierAdmissionInvitationStatusCode ;
    /**yyyy-MM-dd HH:mm:ss*/
    @ApiModelProperty(value = "供应商准入邀请时间",notes = "")
    private String invitationTime;
    @ApiModelProperty(value = "准入邀请人",notes = "")
    private String createUser;
    @ApiModelProperty(value = "0:非集采 1:集采",notes = "")
    private Integer isCenPurchase;
}
