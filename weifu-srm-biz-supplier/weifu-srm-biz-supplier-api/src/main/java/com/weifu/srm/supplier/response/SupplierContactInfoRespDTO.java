package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierContactInfoRespDTO {
    private Long id;

    @ApiModelProperty(name = "供应商基础表Id",notes = "")
    private Long supplierBasicMsgId ;

    @ApiModelProperty(value = "法人姓名", notes = "")
    private String legalPersonName;
    /** 法人邮箱*/
    @ApiModelProperty(value = "法人邮箱", notes = "")
    private String legalPersonEmail;
    /*** 法人电话*/
    @ApiModelProperty(value = "法人电话", notes = "")
    private String legalPersonPhone;

    @ApiModelProperty(name = "公司传真号码",notes = "")
    private String companyFax ;

}
