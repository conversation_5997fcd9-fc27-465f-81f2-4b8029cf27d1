package com.weifu.srm.supplier.response.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PolicyAdjustDetailRespDTO extends BaseRespDTO{

    @ApiModelProperty("商务政策编号")
    private String policyNo;

    @ApiModelProperty("变更类型 NEW_CREATE:新增 ADJUST:调整")
    private String type;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("申请工单号")
    private String ticketNo;

    @ApiModelProperty("申请业务单号")
    private String applyNo;

    @ApiModelProperty(value="商务政策列表ID")
    private String policyListId;

    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "事业部编码")
    private String divisionCode;

    @ApiModelProperty(value = "事业部名称")
    private String divisionName;

    @ApiModelProperty(value = "业务小类编码")
    private String businessSubclassCode;

    @ApiModelProperty(value = "业务小类名称")
    private String businessSubclassName;

    @ApiModelProperty(value = "业务大类编码")
    private String businessCategoryCode;

    @ApiModelProperty(value = "业务大类名称")
    private String businessCategoryName;

    @ApiModelProperty(value = "付款周期")
    private Long paymentCycle;

    @ApiModelProperty(value = "付款条款类型")
    private String paymentTermsType;

    @ApiModelProperty(value = "付款基准日期类型")
    private String paymentBaseDateType;

    @ApiModelProperty(value = "质保等抵押金")
    private BigDecimal collateralAmount;

    @ApiModelProperty(value = "票据（%）")
    private BigDecimal bill;

    @ApiModelProperty(value = "银企直连（%）")
    private BigDecimal bankEnterpriseLink;

    @ApiModelProperty(value = "应收票据-银票（%）")
    private BigDecimal billReceivable;

    @ApiModelProperty(value = "其它（%）")
    private BigDecimal other;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "付款周期(调整后)")
    private Long paymentCycleAfter;

    @ApiModelProperty(value = "付款基准日期类型(调整后)")
    private String paymentBaseDateTypeAfter;

    @ApiModelProperty(value = "质保等抵押金(调整后)")
    private BigDecimal collateralAmountAfter;

    @ApiModelProperty(value = "票据（%）(调整后)")
    private BigDecimal billAfter;

    @ApiModelProperty(value = "银企直连（%）(调整后)")
    private BigDecimal bankEnterpriseLinkAfter;

    @ApiModelProperty(value = "应收票据-银票（%）(调整后)")
    private BigDecimal billReceivableAfter;

    @ApiModelProperty(value = "其它（%）(调整后)")
    private BigDecimal otherAfter;

    @ApiModelProperty(value = "币种(调整后)")
    private String currencyAfter;

    @ApiModelProperty(value = "是否启用(调整后)")
    private Boolean isEnableAfter;

}
