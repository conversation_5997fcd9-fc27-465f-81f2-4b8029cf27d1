package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierFinanceContactRespDTO {
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(name = "供应商名称")
    private String supplierName;

    @ApiModelProperty(name = "供应商名简称")
    private String supplierShortName;

    @ApiModelProperty(name = "供应商状态")
    private String status;

    @ApiModelProperty(name = "财务联系人姓名")
    private String financeContactName;

    @ApiModelProperty(name = "财务联系人邮箱")
    private String financeContactEmail;

    @ApiModelProperty(name = "财务联系人电话")
    private String financeContactPhone;
    
    @ApiModelProperty(name = "财务联系人职位")
    private String financeContactPosition;
}
