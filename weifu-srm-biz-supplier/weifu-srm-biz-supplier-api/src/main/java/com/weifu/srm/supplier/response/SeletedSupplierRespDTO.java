package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取供应商用户当前选中的供应商响应结果
 */
@Data
public class SeletedSupplierRespDTO {

    @ApiModelProperty(value = "供应商Id")
    private Long supplierId;

    @ApiModelProperty(value = "Sap供应商编码（在供应商未走完注册流程时，此字段无值）")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String sapSupplierName;
}
