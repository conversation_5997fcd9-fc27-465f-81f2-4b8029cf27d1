package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:11
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司规模",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCompanySizeRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 厂房类型（自有租赁） */
    @ApiModelProperty("厂房类型（自有租赁）")
    private String plantType ;
    @ApiModelProperty("厂房类型描述")
    private String plantTypeDesc ;
    /** 厂房总面积（m2） */
    @ApiModelProperty("厂房总面积（m2）")
    private BigDecimal totalPlantArea ;
    /** 在用厂房面积 */
    @ApiModelProperty("在用厂房面积")
    private BigDecimal usedPlantArea ;
}
