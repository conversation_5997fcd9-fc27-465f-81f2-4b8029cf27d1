package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * <AUTHOR>
 * @Date 2024/7/08 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateSaveReqDTO {
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;
    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    @NotNull(message = "模板描述不能为空")
    private String templateDesc;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    @NotNull(message = "供应商类型不能为空")
    private String supplierType;

    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Integer enable;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "是否默认")
    private Integer isDefault;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private Long operationBy;
    /**
     * 操作人名
     */
    @ApiModelProperty(value = "操作人名")
    private String operationByName;



    @ApiModelProperty(value = "模板字段明细")
    private List<QuestionnaireTemplateFieldDTO> templateFields;

}
