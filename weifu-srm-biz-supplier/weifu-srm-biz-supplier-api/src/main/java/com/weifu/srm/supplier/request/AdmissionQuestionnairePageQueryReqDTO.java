package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnairePageQueryReqDTO extends PageRequest implements Serializable {

    /**
     * 准入编号
     */
    @ApiModelProperty("准入编号")
    private String admissionNo;
    /**
     * 调查表编号
     */
    @ApiModelProperty("调查表编号")
    private String questionnaireNo;
    /**
     * 准入邀请编号
     */
    @ApiModelProperty("准入邀请编号")
    private String invitationNo;
    /** 供应商ID */
    @ApiModelProperty("供应商Id")
    private Long supplierId ;
    /** 供应商编码 */
    @ApiModelProperty("供应商编码")
    private String supplierCode ;
    /** 供应商ID */
    @ApiModelProperty("供应商类型")
    private String supplierType ;

    /** 供应商名称 */
    @ApiModelProperty("供应商名称")
    private String supplierName ;
    /** 供应商名称 */
    @ApiModelProperty("准入品类类型")
    private String admissionType ;
    /** 供应商名称 */
    @ApiModelProperty("准入品类名称")
    private String admissionCategory ;
    /** 供应商准入状态(供应商-品类) */
    @ApiModelProperty("供应商准入状态(供应商-品类)")
    private String admissionStatus ;
    /** 供应商状态 */
    @ApiModelProperty("供应商状态")
    private String supplierStatus ;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人名称")
    private String admissionInvitationName ;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人")
    private Long admissionInvitationBy ;
    /** 准入申请开始时间 */
    @ApiModelProperty("准入申请开始时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionApplyTimeStart ;
    /** 准入申请结束时间 */
    @ApiModelProperty("准入申请结束时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionApplyTimeEnd ;
    /** 准入完成时间 */
    @ApiModelProperty("准入完成时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionCompleteTimeStart ;

    /** 准入完成时间 */
    @ApiModelProperty("准入完成结束时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date admissionCompleteTimeEnd ;

    /** 操作人名称 */
    @ApiModelProperty("操作人")
    private Long operationBy ;
    /** 操作人名称 */
    @ApiModelProperty("操作人名称")
    private String operationByName ;

    /** 操作人名称 */
    @ApiModelProperty("操作人角色")
    private String operationByRole ;


}
