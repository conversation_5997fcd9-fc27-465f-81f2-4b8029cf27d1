package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:14
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表物流能力",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireLogisticsRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 是否有仓库 */
    @ApiModelProperty("是否有仓库")
    private Integer hasWarehouse ;
    /** 仓库面积 */
    @ApiModelProperty("仓库面积")
    private BigDecimal warehouseArea ;
}
