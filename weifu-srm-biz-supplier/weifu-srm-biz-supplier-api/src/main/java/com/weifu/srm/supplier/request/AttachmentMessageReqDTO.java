package com.weifu.srm.supplier.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class AttachmentMessageReqDTO implements Serializable {

    /**
     * 附件真实名称
     */
    @NotNull(message = "附件真实名称不能为空")
    private String fileOriginalName;

    /**
     * 附件URL
     */
    @NotNull(message = "附件URL不能为空")
    private String fileUrl;

    /**
     * 附件Name
     */
    @NotNull(message = "附件Name不能为空")
    private String fileName;
}
