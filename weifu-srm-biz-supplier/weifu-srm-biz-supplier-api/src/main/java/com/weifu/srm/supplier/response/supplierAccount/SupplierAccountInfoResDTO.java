package com.weifu.srm.supplier.response.supplierAccount;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierAccountInfoResDTO extends PageRequest implements Serializable {

    @ApiModelProperty("自增序号")
    private Long id;
    @ApiModelProperty("自增序号")
    private Long sysUserId;
    @ApiModelProperty("姓名：显示账号姓名信息")
    private String realName;
    @ApiModelProperty("电话：显示电话")
    private String phone;
    @ApiModelProperty("邮箱：显示邮箱")
    private String email;
    @ApiModelProperty("账号名：显示账户名")
    private String accountName;
    @ApiModelProperty("角色：显示供应商角色，如果存在多个用逗号隔开")
    private List<String> roleIdList;
    @ApiModelProperty("账号状态：已启用，已禁用")
    private Integer status;
}
