package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

@Data
public class QualificationChangeUpdateFinancialReqDTO {

    private Long supplierId;
    private String operationUser;
    private Long operationUserId;
    private List<QualificationChangeUpdateFinancialItemReqDTO> qualificationChangeUpdateFinancialItemReqDTOS;

    @Data
    public static class QualificationChangeUpdateFinancialItemReqDTO implements Serializable {

        /** 国家/地区代码 */
        @ApiModelProperty("国家/地区代码")
        private String countryRegionCode ;
        /** 银行代码 */
        @ApiModelProperty("银行代码")
        private String bankCode ;
        /** 银行名称 */
        @ApiModelProperty("银行名称")
        private String bankName ;
        /** 联行号 */
        @Length(max = 15,message = "供应商简称长度不能超过15")
        @ApiModelProperty("联行号")
        private String bankBranchCode ;
        /** 开户行名称 */
        @ApiModelProperty("开户行名称")
        private String accountBankName ;
        /** 账户名称 */
        @Length(max = 60,message = "供应商简称长度不能超过60")
        @ApiModelProperty("账户名称")
        private String accountName ;
        /** 银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银 */
        @ApiModelProperty("银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银")
        private String bankAccountType ;
        /** 银行账号 */
        @ApiModelProperty("银行账号")
        private String bankAccount ;
        private List<AttachmentMessageReqDTO> financialAttachmentList;
    }

}
