package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:15
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司信息",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCompanyInfoSaveReqDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 董事长 */
    @ApiModelProperty("董事长")
    private String chairman ;
    /** 总经理 */
    @ApiModelProperty("总经理")
    private String generalManager ;
    /** 是否上市 */
    @ApiModelProperty("是否上市")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer isListed ;
    @ApiModelProperty(value = "公司架构附件",notes = "")
    private List<AttachmentMessageReqDTO> organizationAttachment ;
    @ApiModelProperty(value = "股权架构附件",notes = "")
    private List<AttachmentMessageReqDTO> equityAttachment ;
}
