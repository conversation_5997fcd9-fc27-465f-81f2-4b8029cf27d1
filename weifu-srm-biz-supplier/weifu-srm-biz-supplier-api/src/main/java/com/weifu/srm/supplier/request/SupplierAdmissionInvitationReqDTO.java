package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierAdmissionInvitationReqDTO implements Serializable {

    @ApiModelProperty(value = "供应商准入邀请编号",notes = "")
    private String invitationNo;
    @ApiModelProperty(value = "供应商社会统一信用代码",notes = "")
    @NotNull(message = "供应商社会统一信用代码")
    @Length(max = 32,message = "供应商社会统一信用代码长度不能超过32")
    private String creditCode;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称",notes = "")
    @NotNull(message = "供应商名称不能为空")
    @Length(max = 70,message = "供应商名称长度不能超过70")
    private String supplierName;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称",notes = "")
    @NotNull(message = "供应商简称不能为空")
    @Length(max = 20,message = "供应商简称长度不能超过20")
    private String supplierShortName ;
    /** 供应商类型 */
    @ApiModelProperty(value = "供应商类型",notes = "")
    @NotNull(message = "供应商类型不能为空")
    private String supplierType ;
    /**准入类型*/
    @ApiModelProperty(value = "准入类型",notes = "")
    @NotNull(message = "准入类型不能为空")
    private String admissionType;
    /** 境内外关系 */
    @ApiModelProperty(value = "境内外关系",notes = "")
    @NotNull(message = "境内外关系不能为空")
    private Integer domesticForeignRelationship ;
    /** 客户指定证明附件*/
    @ApiModelProperty(value = "客户指定证明附件",notes = "")
    private List<AttachmentMessageReqDTO> customerDesignationProofAttachments ;
    /** 供应商切换前原名称 */
    @ApiModelProperty(value = "供应商切换前原名称",notes = "")
    private String previousSupplierName ;
    /** 供应商切换前原SAP编码 */
    @ApiModelProperty(value = "供应商切换前原SAP编码",notes = "")
    private String previousSupplierSapCode ;
    /** 准入邀请补充信息附件 */
    @ApiModelProperty(value = "准入邀请补充信息附件",notes = "")
    private List<AttachmentMessageReqDTO> suppAttachments ;
    /** 供应商注册联系人姓名 */
    @ApiModelProperty(value = "供应商注册联系人姓名",notes = "")
    @NotNull(message = "供应商注册联系人姓名不能为空")
    private String supplierRegistrationContactName ;
    /** 供应商注册联系人邮箱 */
    @ApiModelProperty(value = "供应商注册联系人邮箱",notes = "")
    @NotNull(message = "供应商注册联系人邮箱不能为空")
    private String supplierRegistrationContactEmail ;
    /** 电话号码 */
    @ApiModelProperty(value = "电话号码",notes = "")
    @NotNull(message = "电话号码不能为空")
    private String phone ;
    /** 采购类型 */
    @ApiModelProperty(value = "采购类型",notes = "")
    @NotNull(message = "采购类型不能为空")
    private String purchaseType ;
    /** 准入品类（末级） */
    @ApiModelProperty(value = "准入品类（末级）",notes = "")
    @NotNull(message = "准入品类不能为空")
    private List<AdmissionCategoryReqDTO> admissionCategories ;
    /** 准入背景和目的 */
    @NotNull(message = "准入背景和目的不能为空")
    @ApiModelProperty(value = "准入背景和目的",notes = "")
    private String introductionBackgroundAndPurpose ;
    /** 调查表模版编码 */
    @ApiModelProperty(value = "调查表模版编码",notes = "")
    private String questionnaireTemplateCode ;
    /** 调查表说明 */
    @ApiModelProperty(value = "调查表说明",notes = "")
    private String questionnaireDesc ;
    /** 年度采购金额（元） */
    @ApiModelProperty(value = "年度采购金额（元）",notes = "")
    private BigDecimal annualPurchaseAmt ;
    /** 年度采购次数 */
    @ApiModelProperty(value = "年度采购次数",notes = "")
    private Long annualPurchaseCnt ;
    /** 最大单次采购金额（元） */
    @ApiModelProperty(value = "最大单次采购金额（元）",notes = "")
    private BigDecimal mastPurchaseAmt ;
    /** 交易有效开始时间 */
    @ApiModelProperty(value = "交易有效开始时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date admissionStartTime ;
    /** 交易有效终止时间 */
    @ApiModelProperty(value = "交易有效终止时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date admissionEndTime ;
    /** 临时采购组织 */
    @ApiModelProperty(value = "临时采购组织",notes = "")
    private String tmpPurchaseOrg;
    /**操作发起人*/
    @ApiModelProperty(value = "供应商准入邀请发起人",notes = "")
    private String operationUser;
    @ApiModelProperty(value = "供应商准入邀请发起人Id",notes = "")
    private Long operationUserId;
    @ApiModelProperty(value = "当前登陆人所属于事业部编码",notes = "")
    private String divisionCode;
    /**1: 集采 0: 非集采*/
    private Integer isCenPurchase;

}
