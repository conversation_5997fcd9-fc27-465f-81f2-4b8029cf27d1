package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 10:57
 * @Description
 * @Version 1.0
 */

@ApiModel(value = "准入调查表客户情况",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCustomerSaveReqDTO extends AdmissionQuestionnaireBasicReqDTO  implements Serializable {

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String customerName ;
    /** 销售额（每年，万元） */
    @ApiModelProperty("销售额（每年，万元）")
    @Digits(integer = 10, fraction = 2, message = "金额格式不正确，整数部分最多10位，小数最多2位")
    private BigDecimal annualSalesAmt ;
    /** 主要供应商产品 */
    @ApiModelProperty("主要供应商产品")
    private String mainSupplierProducts ;
    /** 是否有汽车行业合作经验 */
    @ApiModelProperty("是否有汽车行业合作经验")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasAutomotiveIndustryExperience ;
}
