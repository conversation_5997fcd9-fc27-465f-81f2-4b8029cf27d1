package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SupplierBasicDetailRespDTO implements Serializable {

    /** 供应商注册流程编号*/
    @NotNull
    @ApiModelProperty(value = "供应商注册流程编号", notes = "")
    private String registerNo;
    /** 供应商名称*/
    @NotNull
    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;
    /** 供应商简称*/
    @NotNull
    @ApiModelProperty(value = "供应商简称", notes = "")
    private String supplierShortName;
    @ApiModelProperty(value = "供应商名称-EN", notes = "")
    private String supplierNameEN;
    /** 统一社会信用代码*/
    @ApiModelProperty(value = "统一社会信用代码", notes = "")
    private String creditCode;
    /** 机构性质*/
    @ApiModelProperty(value = "机构性质", notes = "")
    private String organizationNature;
    /** 是否境外(0,1)*/
    @ApiModelProperty(value = "是否境外(0,1)", notes = "")
    private Integer isOverseas;
    /** 企业类型*/
    @ApiModelProperty(value = "企业类型", notes = "")
    private String enterpriseType;
    /** 注册资金（万）*/
    @NotNull
    @ApiModelProperty(value = "注册资金（万）", notes = "")
    private BigDecimal registeredAmt;
    /*** 注册币种*/
    @NotNull
    @ApiModelProperty(value = "注册币种", notes = "")
    private String registeredCurrency;
    /** 成立日期*/
    @NotNull
    @ApiModelProperty(value = "成立日期", notes = "")
    private String establishmentDate;
    /*** 营业期限类型*/
    @ApiModelProperty(value = "营业期限类型", notes = "")
    private String businessPeriodType;
    /*** 营业期限起始*/
    @NotNull
    @ApiModelProperty(value = "营业期限起始", notes = "")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String businessPeriodStart;
    /** 营业期限终止*/
    @NotNull
    @ApiModelProperty(value = "营业期限终止", notes = "")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String businessPeriodEnd;
    /*** 注册地址*/
    @NotNull
    @ApiModelProperty(value = "注册地址", notes = "")
    private String registeredAddress;
    /*** 公司邮编*/
    @NotNull
    @ApiModelProperty(value = "公司邮编", notes = "")
    private String companyZipCode;
    /*** 生产地址*/
    @NotNull
    @ApiModelProperty(value = "生产地址", notes = "")
    private String productionAddress;
    /*** 经营范围*/
    @ApiModelProperty(value = "经营范围", notes = "")
    private String businessScope;
    /** 营业执照附件*/
    @ApiModelProperty(value = "营业执照附件", notes = "")
    private AttachmentMessageRespDTO businessLicenseAttachmentFile;
    /** 邓白氏编码D-U-N-S*/
    @ApiModelProperty(value = "邓白氏编码D-U-N-S", notes = "")
    private String dunAndBradstreetCode;
    /*** 法人姓名*/
    @NotNull
    @ApiModelProperty(value = "法人姓名", notes = "")
    private String legalPersonName;
    /** 法人邮箱*/
    @ApiModelProperty(value = "法人邮箱", notes = "")
    private String legalPersonEmail;
    /*** 法人电话*/
    @ApiModelProperty(value = "法人电话", notes = "")
    private String legalPersonPhone;
    /** 是否为威孚关联方（0，1*/
    @NotNull
    @ApiModelProperty(value = "是否为威孚关联方（0，1）", notes = "")
    private Integer isWeifuRelatedParty;
    /*** 关联方类型--威孚直接关联方*/
    @ApiModelProperty(value = "关联方类型--威孚直接关联方", notes = "")
    private String directRelatedPartyTypeDesc;
    /** 是否与威孚供应商存在关联关系（0，1）*/
    @NotNull
    @ApiModelProperty(value = "是否与威孚供应商存在关联关系（0，1）", notes = "")
    private Integer isRelatedToWeifuSupplier;
    /**存在关联关系的威孚供应商*/
    @ApiModelProperty(value = "存在关联关系的威孚供应商", notes = "")
    private List<QualificationChangeQueryAssociationRespDTO.AssociationRelationItemRespDTO> associationRelationItemRespDTOList;
    /** 公司固定电话 */
    @ApiModelProperty(value = "公司固定电话",notes = "")
    private String companyPhone ;
    /** 公司传真号码 */
    @ApiModelProperty(value = "公司传真号码",notes = "")
    private String companyFax ;
    /** 公司网址 */
    @ApiModelProperty(value = "公司网址",notes = "")
    private String companyWebUrl ;
    /** 商务联系人姓名 */
    @NotNull
    @ApiModelProperty(value = "商务联系人姓名",notes = "")
    private String businessContactName ;
    /** 商务联系人邮箱 */
    @NotNull
    @ApiModelProperty(value = "商务联系人邮箱",notes = "")
    private String businessContactEmail ;
    /** 商务联系人电话 */
    @NotNull
    @ApiModelProperty(value = "商务联系人电话",notes = "")
    private String businessContactPhone ;
    /** 商务联系人职位 */
    @ApiModelProperty(value = "商务联系人职位",notes = "")
    private String businessContactPosition ;
    /** 质量联系人姓名 */
    @NotNull
    @ApiModelProperty(value = "质量联系人姓名",notes = "")
    private String qualityContactName ;
    /** 质量联系人邮箱 */
    @NotNull
    @ApiModelProperty(value = "质量联系人邮箱",notes = "")
    private String qualityContactEmail ;
    /** 质量联系人电话 */
    @NotNull
    @ApiModelProperty(value = "质量联系人电话",notes = "")
    private String qualityContactPhone ;
    /** 质量联系人职位 */
    @ApiModelProperty(value = "质量联系人职位",notes = "")
    private String qualityContactPosition ;
    /** 财务联系人姓名 */
    @NotNull
    @ApiModelProperty(value = "财务联系人姓名",notes = "")
    private String financeContactName ;
    /** 财务联系人邮箱 */
    @NotNull
    @ApiModelProperty(value = "财务联系人邮箱",notes = "")
    private String financeContactEmail ;
    /** 财务联系人电话 */
    @NotNull
    @ApiModelProperty(value = "财务联系人电话",notes = "")
    private String financeContactPhone ;
    /** 财务联系人职位 */
    @ApiModelProperty(value = "财务联系人职位",notes = "")
    private String financeContactPosition ;
    /** 财务账号信息 */
    @ApiModelProperty(value = "财务账号信息",notes = "")
    @NotNull
    private List<FinancialInfoRespDTO> financialMsgList;

    /** 商业模式 */
    @ApiModelProperty(value = "商业模式", notes = "")
    private String businessModel;
    /** 行业类型 */
    @ApiModelProperty(value = "行业类型", notes = "")
    private String industryType;
    /** 代理品牌 */
    @ApiModelProperty(value = "代理品牌", notes = "")
    private String actingBrand;
    /**供应商状态*/
    @ApiModelProperty(value = "供应商状态", notes = "")
    private String status;
    /**操作人*/
    @NotNull
    private String operationUser;
    @NotNull
    private Long operationUserId;

    @Data
    public static class FinancialInfoRespDTO implements Serializable {
        /** 国家/地区代码 */
        @ApiModelProperty(value = "国家/地区代码",notes = "")
        @NotNull
        private String countryRegionCode ;
        /** 银行代码 */
        @NotNull
        @ApiModelProperty(value = "银行代码",notes = "")
        private String bankCode ;
        /** 银行名称 */
        @NotNull
        @ApiModelProperty(value = "银行名称",notes = "")
        private String bankName ;
        /** 联行号 */
        @NotNull
        @ApiModelProperty(value = "联行号",notes = "")
        private String bankBranchCode ;
        /** 境外银行代码 */
        @ApiModelProperty(value = "境外银行代码",notes = "")
        private String foreignBankCode ;
        /** 开户行名称 */
        @NotNull
        @ApiModelProperty(value = "开户行名称",notes = "")
        private String accountBankName ;
        /** 账户名称 */
        @NotNull
        @ApiModelProperty(value = "账户名称",notes = "")
        private String accountName ;
        /** 银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银 */
        @ApiModelProperty(value = "银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银",notes = "")
        @NotNull
        private String bankAccountType ;
        /** 银行账号 */
        @NotNull
        @ApiModelProperty(value = "银行账号",notes = "")
        private String bankAccount ;
        /** 开票资料附件 */
        @ApiModelProperty(value = "开票资料附件",notes = "")
        private AttachmentMessageRespDTO invoiceDataAttachmentFile ;
    }
}
