package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class AdmissionGBMResultReqDTO implements Serializable {

    @ApiModelProperty("准入编号")
    @NotNull(message = "准入编号不能为空")
    private String admissionNo;
    @ApiModelProperty("工单号")
    @NotNull(message = "工单号不能为空")
    private String ticketNo;
    @NotNull(message = "经营管理能力不能为空")
    @ApiModelProperty("经营管理能力 1:绿灯 2: 黄灯 3: 红灯")
    private String operatingManagementAbility;
    @NotNull(message = "研发能力不能为空")
    @ApiModelProperty("研发能力 1:绿灯 2: 黄灯 3: 红灯")
    private String researchDevelopmentAbility;
    @NotNull(message = "制造能力不能为空")
    @ApiModelProperty("制造能力 1:绿灯 2: 黄灯 3: 红灯")
    private String manufacturingAbility;
    @NotNull(message = "交付能力不能为空")
    @ApiModelProperty("交付能力 1:绿灯 2: 黄灯 3: 红灯")
    private String deliverAbility;
    @NotNull(message = "质量能力不能为空")
    @ApiModelProperty("质量能力 1:绿灯 2: 黄灯 3: 红灯")
    private String qualityAbility;
    @NotNull(message = "五大类评审结果不能为空")
    @ApiModelProperty("五大类评审结果 0:完全准入 1: 条件准入 2: 拒绝准入")
    private String appraisalResult;
    @ApiModelProperty("五大类评审意见")
    @Length(max = 200,message = "五大类评审意见长度不能超过200")
    private String appraisalDesc;
    @ApiModelProperty("五大类评审附件")
    private List<AttachmentMessageReqDTO> gbmFiles;
    private String operationUser;
    private Long operationUserId;

}
