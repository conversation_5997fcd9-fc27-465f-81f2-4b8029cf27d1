package com.weifu.srm.supplier.response;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SupplierBasicInfoInquiryOrderRespDTO {

    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;
    @ApiModelProperty(value = "供应商联系人ID")
    private Long supplierUserId;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商名称英文")
    private String supplierNameEN;
    @ApiModelProperty(name = "供应商品类资质（REGISTERED_SUPPLIER-已注册供应商，TMP_SUPPLIER-临时供应商，SAMPLE_SUPPLIER-样件供应商，POTENTIAL_SUPPLIER-潜在供应商，合格供应商-合格供应商）")
    private String supplierCategoryStatus;
    @ApiModelProperty(name = "准入完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date admissionCompleteTime;
    @ApiModelProperty(name = "供应商分级 BASE_SUPPLIER-基本供应商，CORE_SUPPLIER-核心供应商，STRATEGY_SUPPLIER-战略供应商，NON_GRADE-无分级")
    private String supplierClassification;
    @ApiModelProperty(value = "保密协议 1-有|0-无")
    private Integer isConfidentialityAgreement = 0;
    @ApiModelProperty(value = "廉洁协议 1-已生效|0-未签署")
    private Integer isIntegrityAgreement = 0;
    @ApiModelProperty(value = "框架协议 1-已生效|0-未签署")
    private Integer isFrameworkAgreement = 0;
    @ApiModelProperty(value = "供应商类型")
    private String supplierType;
    @ApiModelProperty(value = "是否为推荐供应商 1-是|0-否")
    private Integer isRecommendedSupplier;
    @ApiModelProperty(value = "推荐属性")
    private String recommendedAttribute;
    @ApiModelProperty(value = "相似零件号供应商 1-是|0-否")
    private Integer isSimilarMaterialCodeSupplier;
    @ApiModelProperty(value = "该供应商匹配上的相识物料号")
    private List<String> similarMaterialCodes;
}
