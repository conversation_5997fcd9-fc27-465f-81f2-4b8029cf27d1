package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description sqe/cpe分页查询参数
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierCpeOrSqePageReqDTO", description = "sqe/cpe分页查询参数")
public class SupplierCpeOrSqePageReqDTO extends PageRequest implements Serializable {

    @ApiModelProperty(value = "SAP（CCRM）供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(value = "SAP（CCRM）供应商编码集合")
    private List<String> supplierCodeList;

    @ApiModelProperty(value = "供应商名称对应的编码集合")
    private List<String> supplierNameCodeList;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "CPE主责人名称")
    private String cpeMainName;

    @ApiModelProperty(value = "SQE主责人名称")
    private String sqeMainName;

    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    @ApiModelProperty(value = "未配置sqe")
    private Boolean nullSqe = false;

    @ApiModelProperty(value = "未配置cpe")
    private Boolean nullCpe = false;
}
