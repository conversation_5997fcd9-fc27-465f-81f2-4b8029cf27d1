package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel("分页查询供应商品类关系响应参数")
public class SupplierCategoryRelPageRespDTO {

    @ExcelProperty(value = "供应商编码", index = 0)
    @ColumnWidth(20)
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;
    @ExcelProperty(value = "供应商名称", index = 1)
    @ColumnWidth(20)
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ExcelIgnore
    @ApiModelProperty(name = "供应商名称-英文")
    private String supplierNameEn;
    @ExcelProperty(value = "供应商简称", index = 2)
    @ColumnWidth(20)
    @ApiModelProperty(name = "供应商简称")
    private String supplierShortName;
    @ExcelProperty(value = "一级品类编码", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty(name = "一级品类编码")
    private String oneLevelCategoryCode;
    @ExcelProperty(value = "一级品类名称", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(name = "一级品类名称")
    private String oneLevelCategoryName;
    @ExcelIgnore
    @ApiModelProperty(name = "一级品类名称（英文）")
    private String oneLevelCategoryNameEn;
    @ExcelProperty(value = "二级品类编码", index = 5)
    @ColumnWidth(20)
    @ApiModelProperty(name = "二级品类编码")
    private String twoLevelCategoryCode;
    @ExcelProperty(value = "二级品类名称", index = 6)
    @ColumnWidth(20)
    @ApiModelProperty(name = "二级品类名称")
    private String twoLevelCategoryName;
    @ExcelIgnore
    @ApiModelProperty(name = "二级品类名称（英文）")
    private String twoLevelCategoryNameEn;
    @ExcelProperty(value = "三级品类编码", index = 7)
    @ColumnWidth(20)
    @ApiModelProperty(name = "三级品类编码")
    private String threeLevelCategoryCode;
    @ExcelProperty(value = "三级品类名称", index = 8)
    @ColumnWidth(20)
    @ApiModelProperty(name = "三级品类名称")
    private String threeLevelCategoryName;
    @ExcelIgnore
    @ApiModelProperty(name = "三级品类名称（英文）")
    private String threeLevelCategoryNameEn;
    @ExcelIgnore
    @ApiModelProperty(name = "供应商品类资质")
    private String supplierCategoryStatus;
    @ExcelProperty(value = "品类资质", index = 9)
    @ColumnWidth(20)
    @ApiModelProperty(name = "供应商品类资质名称")
    private String supplierCategoryStatusName;

}
