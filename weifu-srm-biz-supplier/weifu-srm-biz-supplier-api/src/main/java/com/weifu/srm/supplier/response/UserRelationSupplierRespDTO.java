package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRelationSupplierRespDTO {
    @ApiModelProperty("用户表ID")
    private Long sysUserId;

    @ApiModelProperty("关联供应商信息")
    private List<RelationSupplierRespDTO> relationSuppliers;

    public UserRelationSupplierRespDTO(Long sysUserId){
        this.sysUserId = sysUserId;
    }
}
