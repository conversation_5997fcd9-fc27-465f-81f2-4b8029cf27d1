package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierAndContractRespDTO {
    @ApiModelProperty("供应商简单信息")
    private SupplierSimpleInfoRespDTO supplierInfo;

    @ApiModelProperty("事业部编码")
    private String divisionCode;

    @ApiModelProperty("是否存在有效合同")
    private Boolean existsContract;

    @ApiModelProperty("合同信息 有效期结束时间降序排列")
    private List<SupplierContractItemRespDTO> contract;

    public SupplierAndContractRespDTO(SupplierSimpleInfoRespDTO supplierInfo){
        this.supplierInfo=supplierInfo;
    }
}
