package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SupplierGetLifeCycleRespDTO {

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    @ExcelProperty(value = "时间")
    private Date lifeCycleDate;
    @ExcelProperty(value = "说明")
    private String lifeCycleType;
    @ExcelIgnore
    private String remark;

}
