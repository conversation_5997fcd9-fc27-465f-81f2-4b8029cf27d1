package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireDetailRespDTO {
    /**
     * 调查表模板code
     */
    @NotNull
    private String questionnaireTemplateCode;
    /**
     * 准入编号
     */
    private String admissionNo;
    /**
     * 准入邀请编号
     */
    @NotNull
    private String invitationNo;
    /** 供应商ID */
    @NotNull
    private String supplierBasicMsgId ;
    /** 供应商准入状态(供应商-品类) */
    @NotNull
    @ApiModelProperty("供应商准入状态(供应商-品类)")
    private String admissionStatus ;
    /** 邀请人ID */
    @NotNull
    @ApiModelProperty("邀请人ID")
    private String admissionInvitationBy ;
    /** 邀请人名称 */
    @ApiModelProperty("邀请人名称")
    private String admissionInvitationName ;
    /**
     * 资质认证信息
     */
    @ApiModelProperty("资质认证信息")
    List<AdmissionQuestionnaireCertificationRespDTO> certification;
    /**
     * 客户情况
     */
    @ApiModelProperty("客户情况")
    List<AdmissionQuestionnaireCustomerRespDTO> customer;
    /**
     * 设备情况
     */
    @ApiModelProperty("设备情况")
    List<AdmissionQuestionnaireEquipmentRespDTO> equipment;
    /**
     * 人员情况
     */
    @ApiModelProperty("人员情况")
    List<AdmissionQuestionnairePersonnelRespDTO> personnel;
    /**
     * 表财务状况
     */
    @ApiModelProperty("表财务状况")
    List<AdmissionQuestionnaireFinancialRespDTO> financial;
    /**
     * 新供应商合作意愿
     */
    @ApiModelProperty("新供应商合作意愿")
    List<AdmissionQuestionnaireWillingnessRespDTO> willingness;
    /**
     * 质量保证能力
     */
    @ApiModelProperty("质量保证能力")
    List<AdmissionQuestionnaireQualityAssuranceRespDTO> qualityAssurance;
    /**
     * 公司规模
     */
    @ApiModelProperty("公司规模")
    List<AdmissionQuestionnaireCompanySizeRespDTO> companySize;
    /**
     * 研发能力
     */
    @ApiModelProperty("研发能力")
    List<AdmissionQuestionnaireRdCapabilityRespDTO> rdCapability;
    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    List<AdmissionQuestionnaireLogisticsRespDTO> logistics;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireCompanyInfoRespDTO> companyInfo;


}
