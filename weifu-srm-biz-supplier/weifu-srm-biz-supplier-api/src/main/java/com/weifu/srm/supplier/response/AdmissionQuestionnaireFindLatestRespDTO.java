package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireFindLatestRespDTO {
    /**
     * 资质认证信息
     */
    @ApiModelProperty("资质认证信息")
    List<AdmissionQuestionnaireCertificationRespDTO> certification;
    /**
     * 客户情况
     */
    @ApiModelProperty("客户情况")
    List<AdmissionQuestionnaireCustomerRespDTO> customer;
    /**
     * 设备情况
     */
    @ApiModelProperty("设备情况")
    List<AdmissionQuestionnaireEquipmentRespDTO> equipment;
    /**
     * 人员情况
     */
    @ApiModelProperty("人员情况")
    List<AdmissionQuestionnairePersonnelRespDTO> personnel;
    /**
     * 表财务状况
     */
    @ApiModelProperty("表财务状况")
    List<AdmissionQuestionnaireFinancialRespDTO> financial;
    /**
     * 新供应商合作意愿
     */
    @ApiModelProperty("新供应商合作意愿")
    List<AdmissionQuestionnaireWillingnessRespDTO> willingness;
    /**
     * 质量保证能力
     */
    @ApiModelProperty("质量保证能力")
    List<AdmissionQuestionnaireQualityAssuranceRespDTO> qualityAssurance;
    /**
     * 公司规模
     */
    @ApiModelProperty("公司规模")
    List<AdmissionQuestionnaireCompanySizeRespDTO> companySize;
    /**
     * 研发能力
     */
    @ApiModelProperty("研发能力")
    List<AdmissionQuestionnaireRdCapabilityRespDTO> rdCapability;
    /**
     * 物流信息
     */
    @ApiModelProperty("物流信息")
    List<AdmissionQuestionnaireLogisticsRespDTO> logistics;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireCompanyInfoRespDTO> companyInfo;
    /**
     * 公司信息
     */
    @ApiModelProperty("公司信息")
    List<AdmissionQuestionnaireSubcontractorRespDTO> subcontractor;

}
