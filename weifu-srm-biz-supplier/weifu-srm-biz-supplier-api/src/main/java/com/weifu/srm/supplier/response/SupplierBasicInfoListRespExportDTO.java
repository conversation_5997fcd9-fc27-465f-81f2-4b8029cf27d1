package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SupplierBasicInfoListRespExportDTO implements Serializable {
    @ExcelIgnore
    @ApiModelProperty(value = "供应商id", notes = "")
    private Long id;
    @ExcelProperty("供应商编码")
    @ApiModelProperty(value = "供应商编码", notes = "")
    private String sapSupplierCode;
    @ExcelProperty("供应商名称")
    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;
    @ExcelProperty("供应商状态")
    @ApiModelProperty(value = "供应商状态key", notes = "")
    private String status;
    @ExcelProperty("供应商分级")
    @ApiModelProperty(value = "供应商分级类型key", notes = "")
    private String supplierClassification;
    @ExcelIgnore
    @ApiModelProperty(value = "供应商类型Key", notes = "")
    private String supplierType;

    @ApiModelProperty(value = "黑名单状态Key", notes = "")
    @ExcelIgnore
    private Integer blackListStatus;

    @ExcelProperty("黑名单限制")
    @ApiModelProperty(value = "黑名单状态", notes = "")
    private String blackListStatusDesc;

    @ApiModelProperty(value = "灰名单状态Key", notes = "")
    @ExcelIgnore
    private Integer greyListStatus;

    @ApiModelProperty(value = "灰名单状态Name", notes = "")
    @ExcelProperty("灰名单限制")
    private String greyListStatusDesc;

    @ExcelProperty("供应商标签")
    @ApiModelProperty(value = "供应商标签", notes = "")
    private String tags;
    @ExcelProperty("供应商简称")
    @ApiModelProperty(value = "供应商简称", notes = "")
    private String supplierShortName;
    @ExcelProperty("统一社会信用代码")
    @ApiModelProperty(value = "统一社会信用代码", notes = "")
    private String creditCode;
    @ExcelProperty("成立日期")
    @ApiModelProperty(value = "成立日期", notes = "")
    @DateTimeFormat("yyyy-MM-dd")
    private Date establishmentDate;
    @ExcelProperty("注册地址")
    @ApiModelProperty(value = "注册地址", notes = "")
    private String registeredAddress;
    @ExcelProperty("SRM注册时间")
    @ApiModelProperty(value = "SRM注册时间", notes = "")
    private Date registerDate;
    @ExcelProperty("潜在转合格时间")
    @ApiModelProperty(value = "潜在转合格时间", notes = "")
    private Date potentialToQualifiedDate;
    @ExcelProperty("最新年度绩效")
    @ApiModelProperty(value = "最新年度绩效得分", notes = "")
    private BigDecimal latestPerformance;
    @ExcelProperty("主责CPE")
    @ApiModelProperty(value = "主责CPE", notes = "")
    private String cpeMainName;
    @ExcelProperty("主责SQE")
    @ApiModelProperty(value = "主责SQE", notes = "")
    private String sqeMainName;

}
