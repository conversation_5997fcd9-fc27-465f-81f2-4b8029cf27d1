package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QualificationChangeQueryAssociationUpdateRespDTO {

    /**
     * 是否为威孚关联方（0，1）
     */
    @ApiModelProperty("是否为威孚关联方（0，1）")
    private Integer isWeifuRelatedParty;
    /**
     * 关联方类型--威孚直接关联方
     */
    @ApiModelProperty("关联方类型")
    private String directRelatedPartyTypeDesc;
    /**
     * 是否与威孚供应商存在关联关系（0，1）
     */
    @ApiModelProperty("是否与威孚供应商存在关联关系（0，1）")
    private Integer isRelatedToWeifuSupplier;
    /** 董办校验结果 */
    @ApiModelProperty("董办校验结果")
    private Integer boardDirectosResult;

    private List<AssociationRelationItemRespDTO> associationRelationItemRespDTOList;

    @Data
    public static class AssociationRelationItemRespDTO implements Serializable {
        /**
         * 关联供应商名称
         */
        @ApiModelProperty("关联供应商名称")
        private String associationSupplierName;
        /**
         * 关联类型
         */
        @ApiModelProperty("关联类型")
        private String associationType;
    }

}
