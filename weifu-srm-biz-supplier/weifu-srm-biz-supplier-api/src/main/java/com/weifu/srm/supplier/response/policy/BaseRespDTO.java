package com.weifu.srm.supplier.response.policy;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.supplier.constants.StringConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 基础响应出参
 */
@Data
public class BaseRespDTO {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /** 创建人ID */
    @ApiModelProperty("创建人ID")
    private Long createBy;

    /** 创建人名称 */
    @ApiModelProperty("创建人名称")
    private String createName;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = StringConstant.ZH_TIMEZONE)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date createTime;

    /** 更新人ID */
    @ApiModelProperty("更新人ID")
    private Long updateBy;

    /** 更新人名称 */
    @ApiModelProperty("更新人名称")
    private String updateName;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = StringConstant.ZH_TIMEZONE)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date updateTime;
}
