package com.weifu.srm.supplier.response.supplierAccount;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class SupplierByUserResDTO implements Serializable {

    @ApiModelProperty(name = "用户表ID",notes = "")
    private Long sysUserId;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商机构id",notes = "")
    private Long supplierBasicInfoId;
    @ApiModelProperty(name = "绑定时间",notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date bindTime;
}
