package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SupplierGetAllCategoryRespDTO {

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    @ExcelProperty(value = "一级品类")
    private String oneLevelCategoryName;
    @ExcelProperty(value = "二级品类")
    private String twoLevelCategoryName;
    @ExcelProperty(value = "三级品类")
    private String threeLevelCategoryName;
    @ExcelIgnore
    private String categoryStatus;
    @ExcelProperty(value = "品类资质")
    private String categoryStatusDesc;

}
