package com.weifu.srm.supplier.response.assessment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/9/23 16:16
 * @Description 考核单历史记录
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentLogRespDTO implements Serializable {
    /** 考核单历史记录Id */
    @ApiModelProperty(value = "考核单历史记录Id",notes = "")
    private Long id;
    /** 考核单编号 */
    @ApiModelProperty(value = "考核单编号",notes = "")
    private String assessmentNo ;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date deadlineTime ;
}
