package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class QualificationChangeUpdateBasicReqDTO {

    private Long supplierId;
    /** 供应商名称（修改后） */
    @Length(max = 70,message = "供应商名称长度不能超过70")
    @ApiModelProperty(name = "供应商名称（修改后）",notes = "")
    private String supplierName ;
    /** 供应商简称（修改后） */
    @Length(max = 20,message = "供应商简称长度不能超过20")
    @ApiModelProperty(name = "供应商简称（修改后）",notes = "")
    private String supplierShortName ;
    /** 供应商编码 */
    @ApiModelProperty(name = "供应商编码",notes = "")
    private String sapSupplierCode ;
    /** 供应商分级 */
    @ApiModelProperty(name = "供应商分级",notes = "")
    private String supplierClassification ;
    /** 注册时间 */
    @ApiModelProperty(name = "注册时间",notes = "")
    private Date registerTime ;
    /** 首次准入时间 */
    @ApiModelProperty(name = "首次准入时间",notes = "")
    private Date firstAdmissionTime ;
    /** 最新绩效 */
    @ApiModelProperty(name = "最新绩效",notes = "")
    private String latestPerformance ;
    /** 供应商状态 */
    @ApiModelProperty(name = "供应商状态",notes = "")
    private String status ;
    /** 统一社会信用代码 */
    @ApiModelProperty(name = "统一社会信用代码",notes = "")
    private String creditCode ;
    /** 机构性质（修改后） */
    @ApiModelProperty(name = "机构性质（修改后）",notes = "")
    private String organizationNature ;
    /** 企业类型（修改后） */
    @ApiModelProperty(name = "企业类型（修改后）",notes = "")
    private String enterpriseType ;
    /** 注册资金（万）(修改后） */
    @ApiModelProperty(name = "注册资金（万）(修改后）",notes = "")
    private Double registeredAmt ;
    /** 注册币种（修改后） */
    @ApiModelProperty(name = "注册币种（修改后）",notes = "")
    private String registeredCurrency ;
    /** 成立日期（修改后） */
    @ApiModelProperty(name = "成立日期（修改后）",notes = "")
    private Date establishmentDate ;
    /** 营业期限起始（修改后） */
    @ApiModelProperty(name = "营业期限起始（修改后）",notes = "")
    private Date businessPeriodStart ;
    /** 营业期限终止（修改后） */
    @ApiModelProperty(name = "营业期限终止（修改后）",notes = "")
    private Date businessPeriodEnd ;
    /** 注册地址（修改后） */
    @Length(max = 35,message = "供应商简称长度不能超过35")
    @ApiModelProperty(name = "注册地址（修改后）",notes = "")
    private String registeredAddress ;
    /** 公司邮编（修改后） */
    @Length(max = 10,message = "供应商简称长度不能超过10")
    @ApiModelProperty(name = "公司邮编（修改后）",notes = "")
    private String companyZipCode ;
    /** 生产地址（修改后） */
    @ApiModelProperty(name = "生产地址（修改后）",notes = "")
    private String productionAddress ;
    /** 经营范围（修改后） */
    @ApiModelProperty(name = "经营范围（修改后）",notes = "")
    private String businessScope ;
    /** 邓白氏编码D-U-N-S（修改后） */
    @ApiModelProperty(name = "邓白氏编码D-U-N-S（修改后）",notes = "")
    private String dunAndBradstreetCode ;
    /** 法人姓名 */
    @ApiModelProperty(name = "法人姓名",notes = "")
    private String legalPersonName ;
    /** 公司固定电话（修改后） */
    @ApiModelProperty(name = "公司固定电话（修改后）",notes = "")
    private String companyPhone ;
    /** 公司传真号码（修改后） */
    @Length(max = 30,message = "供应商简称长度不能超过30")
    @ApiModelProperty(name = "公司传真号码（修改后）",notes = "")
    private String companyFax ;
    /** 是否境外(0,1)（修改后） */
    @ApiModelProperty(name = "是否境外(0,1)（修改后）",notes = "")
    private Integer isOverseas ;
    private String operationUser;
    private Long operationUserId;
    private List<AttachmentMessageReqDTO> businessLicenseAttachmentList;

}
