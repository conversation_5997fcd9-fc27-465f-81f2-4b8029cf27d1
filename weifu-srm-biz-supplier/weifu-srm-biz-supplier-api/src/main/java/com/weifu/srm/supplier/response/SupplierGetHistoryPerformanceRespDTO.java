package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class SupplierGetHistoryPerformanceRespDTO {
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    @ExcelProperty(value = "绩效主题")
    private String themes;
    @ExcelProperty(value = "绩效类型")
    private String performanceType;
    @ExcelProperty(value = "发布时间")
    private Date publishTime;
    @ExcelProperty(value = "实际得分")
    private BigDecimal supplierScore;
    @ExcelProperty(value = "满分")
    private BigDecimal fullScore;
    @ExcelProperty(value = "得分率(%)")
    private String scoringRate;

}
