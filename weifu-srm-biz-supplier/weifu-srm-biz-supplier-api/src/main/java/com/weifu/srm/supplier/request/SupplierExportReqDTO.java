package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplierExportReqDTO extends SupplierBasicInfoListReqDTO {

    @ApiModelProperty(value = "是否导出列表信息")
    private Integer isQueryResult;
    @ApiModelProperty(value = "是否导出基本信息")
    private Integer isBasic;
    @ApiModelProperty(value = "是否导出用户信息")
    private Integer isUser;
    @ApiModelProperty(value = "是否导出财务信息")
    private Integer isFinancial;
    @ApiModelProperty(value = "是否导出关联方信息")
    private Integer isAssociation;
    @ApiModelProperty(value = "是否导出资质证书信息")
    private Integer isCertification;
    @ApiModelProperty(value = "是否导出品类信息")
    private Integer isCategory;
    @ApiModelProperty(value = "是否导出绩效信息")
    private Integer isPerformance;
    @ApiModelProperty(value = "是否导出历史分级信息")
    private Integer isHistoryGrade;
    @ApiModelProperty(value = "是否导出协议信息")
    private Integer isFramework;
    @ApiModelProperty(value = "是否导出生命周期信息")
    private Integer isLifeCycle;
    @ApiModelProperty(value = "需要导出的供应商编码集合")
    private List<String> supplierCodes;
    @ApiModelProperty(value = "需要导出的供应商id集合")
    private List<Long> supplierIds;
    private Long operationUserId;
    private String operationUser;

}
