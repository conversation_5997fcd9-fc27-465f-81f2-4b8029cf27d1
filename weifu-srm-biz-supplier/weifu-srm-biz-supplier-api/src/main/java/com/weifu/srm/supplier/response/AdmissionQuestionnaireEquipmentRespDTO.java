package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:00
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表设备情况",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireEquipmentRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 设备类型 */
    @ApiModelProperty("设备类型")
    private String equipmentType ;
    @ApiModelProperty("设备类型")
    private String equipmentTypeDesc ;
    /** 设备名称 */
    @ApiModelProperty("设备名称")
    private String equipmentName ;
    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String specifications ;
    /** 数量 */
    @ApiModelProperty("数量")
    private Integer quantity ;
    /** 品牌 */
    @ApiModelProperty("品牌")
    private String brand ;
    /** 生产日期 */
    @ApiModelProperty("生产日期")
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date productionDate ;
    /** 主要技术指标及能力优势 */
    @ApiModelProperty("主要技术指标及能力优势")
    private String mainTechnicalIndicatorsAndCapabilities ;
}
