package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description SupplierBasicSelectRespDTO
 * @Version 1.0
 */
@Data
@ApiModel(value = "SupplierBasicSelectRespDTO", description = "供应商简单列表")
@NoArgsConstructor
public class SupplierBasicSelectRespDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "SAP（CCRM）供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(name = "供应商名称英文")
    private String supplierNameEN;

    @ApiModelProperty(name = "供应商名简称")
    private String supplierShortName;

    @ApiModelProperty(value = "供应商状态key")
    private String status;

    @ApiModelProperty(value = "供应商状态")
    private String statusName;

    @ApiModelProperty(value = "供应商分级")
    private String grade;
}
