package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class SupplierAdmissionThresholdInfoReqDTO {

    @ApiModelProperty(value = "临时供应商周期",notes = "")
    @NotNull(message = "临时供应商周期不能为空")
    private Long transactionPeriod;
    @ApiModelProperty(value = "样件供应商年采购次数限制",notes = "")
    @NotNull(message = "样件供应商年采购次数限制不能为空")
    private Long annualPurchaseCnt;
    @ApiModelProperty(value = "样件供应商年采购金额限制(单位/元)",notes = "")
    @NotNull(message = "样件供应商年采购金额限制不能为空")
    private BigDecimal annualPurchaseAmt;
    @ApiModelProperty(value = "样件供应商单笔最大采购金额限制(单位/元)",notes = "")
    @NotNull(message = "样件供应商单笔最大采购金额限制不能为空")
    private BigDecimal mastPurchaseAmt;
    @ApiModelProperty(value = "阈值修改人",notes = "")
    @NotNull
    private String operationUser;
    @ApiModelProperty(value = "操作人Id",notes = "")
    @NotNull
    private Long operationUserId;

}
