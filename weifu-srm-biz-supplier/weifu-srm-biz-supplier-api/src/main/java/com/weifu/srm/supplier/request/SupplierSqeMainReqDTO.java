package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商设置cpe
 * @Version 1.0
 */
@Data
@ApiModel(value = "SupplierSqeMainReqDTO", description = "供应商设置sqe")
public class SupplierSqeMainReqDTO {

    @NotNull(message = "id is required.")
    @ApiModelProperty( "供应商id")
    private Long id;

    @NotNull(message = "sqeMainId is required.")
    @ApiModelProperty("CPE主责人ID")
    private Long sqeMainId;

    @ApiModelProperty("CPE主责人名称")
    private String sqeMainName;

    @ApiModelProperty("操作人")
    private Long operationBy;

    @ApiModelProperty("操作人名称")
    private String operationByName;
}
