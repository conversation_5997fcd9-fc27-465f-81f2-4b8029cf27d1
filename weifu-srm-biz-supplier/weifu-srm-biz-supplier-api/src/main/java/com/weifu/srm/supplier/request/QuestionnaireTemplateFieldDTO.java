package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @Date 2024/7/08 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateFieldDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "Tab页")
    private String categorize;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldNameCn;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "字段英文名称")
    private String fieldNameEn;
    /**
     * 排序索引
     */
    @ApiModelProperty(value = "排序索引")
    private Integer orderIndex;
    /**
     * 字段要求设置 必填/选填/不适用
     */
    @ApiModelProperty(value = "字段要求设置 必填/选填/不适用")
    private String fieldSetting;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String fieldRemark;
    /**
     * 是否可添加多组
     */
    @ApiModelProperty(value = "是否可添加多组")
    private Integer isMultigroup;
}
