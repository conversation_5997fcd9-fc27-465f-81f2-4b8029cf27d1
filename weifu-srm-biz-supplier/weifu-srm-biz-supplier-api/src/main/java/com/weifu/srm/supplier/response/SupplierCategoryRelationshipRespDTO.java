package com.weifu.srm.supplier.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
public class SupplierCategoryRelationshipRespDTO implements Serializable {

    /** 供应商编码 */
    private String sapSupplierCode;
    /** 供应商名称 */
    private String supplierName;
    /** 品类编码 */
    private String categoryCode;
    /** 供应商品类资质 */
    private String supplierCategoryStatus;
    /** 资质生效时间 */
    private Date qualificationEffectiveTime;
    /** 有效期开始时间（临时） */
    private Date validityStartTime;
    /** 有效期结束时间（临时） */
    private Date validityEndTime;
    /** 最大单次采购金额（元）-样件准入 */
    private BigDecimal mastPurchaseAmt;
    /** 年度采购次数-样件准入 */
    private Long annualPurchaseCnt;
    /** 年度采购金额（元）-样件准入 */
    private BigDecimal annualPurchaseAmt;
    /** 数据来源 */
    private String source;
    /** 资质状态 除临时供应商外 其它的品类状态只能是已启用*/
    private String status;
}
