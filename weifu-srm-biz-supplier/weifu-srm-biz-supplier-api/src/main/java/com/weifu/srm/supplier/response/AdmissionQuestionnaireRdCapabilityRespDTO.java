package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:13
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表研发能力",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireRdCapabilityRespDTO {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 是否有实验室 */
    @ApiModelProperty("是否有实验室")
    private Integer hasLab ;
    /** 实验室资质水平 */
    @ApiModelProperty("实验室资质水平")
    private String labQualificationLevel ;
    /** 专利获得数量 */
    @ApiModelProperty("专利获得数量")
    private Integer numberOfPatentsQty ;
    /** 主要专利名称 */
    @ApiModelProperty("主要专利名称")
    private String mainPatentNames ;
    /** 研发人员数量 */
    @ApiModelProperty("研发人员数量")
    private Integer numberOfRDPersonnelQty ;
}
