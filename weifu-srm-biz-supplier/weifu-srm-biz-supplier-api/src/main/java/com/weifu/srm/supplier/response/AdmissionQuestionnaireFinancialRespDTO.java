package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:04
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表财务状况",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireFinancialRespDTO implements Serializable  {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 年份 */
    @ApiModelProperty("年份")
    private Integer reportYear ;
    /** 企业总资产（万元） */
    @ApiModelProperty("企业总资产（万元）")
    private BigDecimal totalAssetsAmt ;
    /** 总负债额（万元） */
    @ApiModelProperty("总负债额（万元）")
    private BigDecimal totalLiabilitiesAmt ;
    /** 营业收入（万元） */
    @ApiModelProperty("营业收入（万元）")
    private BigDecimal revenueAmt ;
    /** 应收账款（万元） */
    @ApiModelProperty("应收账款（万元）")
    private BigDecimal accountsReceivableAmt ;
    /** 预收账款（万元） */
    @ApiModelProperty("预收账款（万元）")
    private BigDecimal advancePaymentsAmt ;
    /** 净利率(百分比) */
    @ApiModelProperty("净利率(百分比)")
    private BigDecimal netProfitMarginRatio ;
    /** 毛利率(百分比) */
    @ApiModelProperty("毛利率(百分比)")
    private BigDecimal grossProfitMarginRatio ;
    /** 负债率(百分比) */
    @ApiModelProperty("负债率(百分比)")
    private BigDecimal debtRatio ;
}
