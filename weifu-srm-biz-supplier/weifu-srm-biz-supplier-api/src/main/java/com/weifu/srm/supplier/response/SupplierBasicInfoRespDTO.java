package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplierBasicInfoRespDTO {
    private Long id;

    @ApiModelProperty(name = "SAP（CCRM）供应商编码", notes = "")
    private String sapSupplierCode;

    @ApiModelProperty(name = "供应商名称", notes = "")
    private String supplierName;

    @ApiModelProperty(name = "供应商名简称", notes = "")
    private String supplierShortName;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称英文", notes = "")
    private String supplierNameEN;

    @ApiModelProperty(name = "统一社会信用代码", notes = "")
    private String creditCode;

    @ApiModelProperty(name = "注册地址", notes = "")
    private String registeredAddress;

    @ApiModelProperty(name = "注册币种", notes = "")
    private String registeredCurrency;

    @ApiModelProperty(name = "公司邮编", notes = "")
    private String companyZipCode;

    @ApiModelProperty("银行信息")
    private List<SupplierFinancialInfoRespDTO> financials;

    @ApiModelProperty("联系人信息")
    private List<SupplierContactInfoRespDTO> contacts;
}
