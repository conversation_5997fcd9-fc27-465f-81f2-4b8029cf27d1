package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:13
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表研发能力",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireRdCapabilitySaveDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 是否有实验室 */
    @ApiModelProperty("是否有实验室")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasLab ;
    /** 实验室资质水平 */
    @ApiModelProperty("实验室资质水平")
    private String labQualificationLevel ;
    /** 专利获得数量 */
    @ApiModelProperty("专利获得数量")
    private Integer numberOfPatentsQty ;
    /** 主要专利名称 */
    @ApiModelProperty("主要专利名称")
    private String mainPatentNames ;
    /** 研发人员数量 */
    @ApiModelProperty("研发人员数量")
    private Integer numberOfRDPersonnelQty ;
}
