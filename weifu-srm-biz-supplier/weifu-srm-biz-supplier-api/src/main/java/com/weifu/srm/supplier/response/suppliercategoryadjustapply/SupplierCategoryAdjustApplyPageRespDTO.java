package com.weifu.srm.supplier.response.suppliercategoryadjustapply;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "分页查询供应商品类调整申请响应结果")
public class SupplierCategoryAdjustApplyPageRespDTO {

    @ApiModelProperty(name = "申请编号")
    private String applyNo;
    @ApiModelProperty(name = "申请类型（调整类型）")
    private String applyType;
    @ApiModelProperty(name = "申请类型名称")
    private String applyTypeName;
    @ApiModelProperty(name = "工单编号")
    private String ticketNo;
    @ApiModelProperty(name = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(name = "供应商名称")
    private String supplierName;
    @ApiModelProperty(name = "供应商名称（英文）")
    private String supplierNameEn;
    @ApiModelProperty(name = "申请状态")
    private String applyStatus;
    @ApiModelProperty(name = "申请状态名称")
    private String applyStatusName;
    @ApiModelProperty(name = "申请人")
    private Long applyBy;
    @ApiModelProperty(name = "申请人姓名")
    private String applyName;
    @ApiModelProperty(name = "申请时间")
    private Date applyTime;
    @ApiModelProperty(name = "附件列表")
    private List<AttachmentMessageRespDTO> attachments;

}
