package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QualificationChangeQueryCertificationUpdateRespDTO {

    /** 认证类型 */
    @ApiModelProperty("认证类型")
    private String certificationType ;
    /** 认证范围 */
    @ApiModelProperty("认证范围")
    private String certificationScope ;
    /** 认证编号 */
    @ApiModelProperty("认证编号")
    private String certificationNumber ;
    /** 认证机构 */
    @ApiModelProperty("认证机构")
    private String certificationBody ;
    /** 创建时间 */
    @ApiModelProperty("创建时间")
    private Date createTime ;
    /** 认证起始日期 */
    @ApiModelProperty("认证起始日期")
    private Date certificationStartDate ;
    /** 认证有效期至 */
    @ApiModelProperty("认证有效期至")
    private Date certificationExpiryDate ;
    /** 是否质量资质 */
    @ApiModelProperty("是否质量资质")
    private Integer hasQualityCertification ;
    /** 备注 */
    @ApiModelProperty("备注")
    private String remarks ;
    /** 操作类型 */
    @ApiModelProperty("操作类型")
    private String operationType ;

    private List<AttachmentMessageRespDTO> certificationAttachmentList;

}
