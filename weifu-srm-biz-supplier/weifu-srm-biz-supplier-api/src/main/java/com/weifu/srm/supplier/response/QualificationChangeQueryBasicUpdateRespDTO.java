package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QualificationChangeQueryBasicUpdateRespDTO {

    /** 供应商名称（修改前） */
    @ApiModelProperty("供应商名称（修改前）")
    private String supplierNameBefore ;
    /** 供应商名称（修改后） */
    @ApiModelProperty("供应商名称（修改后）")
    private String supplierName ;
    /** 供应商简称（修改前） */
    @ApiModelProperty("供应商简称（修改前）")
    private String supplierShortNameBefore ;
    /** 供应商简称（修改后） */
    @ApiModelProperty("供应商简称（修改后）")
    private String supplierShortName ;
    /** 供应商编码 */
    @ApiModelProperty("供应商编码")
    private String sapSupplierCode ;
    /** 供应商分级 */
    @ApiModelProperty("供应商分级")
    private String supplierClassification ;
    /** 注册时间 */
    @ApiModelProperty("注册时间")
    private Date registerTime ;
    /** 首次准入时间 */
    @ApiModelProperty("首次准入时间")
    private Date firstAdmissionTime ;
    /** 最新绩效 */
    @ApiModelProperty("最新绩效")
    private String latestPerformance ;
    /** 供应商状态 */
    @ApiModelProperty("供应商状态")
    private String status ;
    /** 统一社会信用代码 */
    @ApiModelProperty("统一社会信用代码")
    private String creditCode ;
    /** 机构性质（修改前） */
    @ApiModelProperty("机构性质（修改前）")
    private String organizationNatureBefore ;
    /** 机构性质（修改后） */
    @ApiModelProperty("机构性质（修改后）")
    private String organizationNature ;
    /** 企业类型（修改前） */
    @ApiModelProperty("企业类型（修改前）")
    private String enterpriseTypeBefore ;
    /** 企业类型（修改后） */
    @ApiModelProperty("企业类型（修改后）")
    private String enterpriseType ;
    /** 注册资金（万）(修改前） */
    @ApiModelProperty("注册资金（万）(修改前）")
    private Double registeredAmtBefore ;
    /** 注册资金（万）(修改后） */
    @ApiModelProperty("注册资金（万）(修改后）")
    private Double registeredAmt ;
    /** 注册币种（修改前） */
    @ApiModelProperty("注册币种（修改前）")
    private String registeredCurrencyBefore ;
    /** 注册币种（修改后） */
    @ApiModelProperty("注册币种（修改后）")
    private String registeredCurrency ;
    /** 成立日期（修改前） */
    @ApiModelProperty("成立日期（修改前）")
    private Date establishmentDateBefore ;
    /** 成立日期（修改后） */
    @ApiModelProperty("成立日期（修改后）")
    private Date establishmentDate ;
    /** 营业期限起始（修改前） */
    @ApiModelProperty("营业期限起始（修改前）")
    private Date businessPeriodStartBefore ;
    /** 营业期限终止（修改前） */
    @ApiModelProperty("营业期限终止（修改前）")
    private Date businessPeriodEndBefore ;
    /** 营业期限起始（修改后） */
    @ApiModelProperty("营业期限起始（修改后）")
    private Date businessPeriodStart ;
    /** 营业期限终止（修改后） */
    @ApiModelProperty("营业期限终止（修改后）")
    private Date businessPeriodEnd ;
    /** 注册地址（修改前） */
    @ApiModelProperty("注册地址（修改前）")
    private String registeredAddressBefore ;
    /** 注册地址（修改后） */
    @ApiModelProperty("注册地址（修改后）")
    private String registeredAddress ;
    /** 公司邮编（修改前） */
    @ApiModelProperty("公司邮编（修改前）")
    private String companyZipCodeBefore ;
    /** 公司邮编（修改后） */
    @ApiModelProperty("公司邮编（修改后）")
    private String companyZipCode ;
    /** 生产地址（修改前） */
    @ApiModelProperty("生产地址（修改前）")
    private String productionAddressBefore ;
    /** 生产地址（修改后） */
    @ApiModelProperty("生产地址（修改后）")
    private String productionAddress ;
    /** 经营范围（修改前） */
    @ApiModelProperty("经营范围（修改前）")
    private String businessScopeBefore ;
    /** 经营范围（修改后） */
    @ApiModelProperty("经营范围（修改后）")
    private String businessScope ;
    /** 营业执照附件-数据值与attachment_record.business_type保持一致（修改前） */
    @ApiModelProperty("营业执照附件-数据值与attachment_record.business_type保持一致（修改前）")
    private String businessLicenseAttachmentBefore ;
    /** 邓白氏编码D-U-N-S（修改前） */
    @ApiModelProperty("邓白氏编码D-U-N-S（修改前）")
    private String dunAndBradstreetCodeBefore ;
    /** 邓白氏编码D-U-N-S（修改后） */
    @ApiModelProperty("邓白氏编码D-U-N-S（修改后）")
    private String dunAndBradstreetCode ;
    /** 法人姓名 */
    @ApiModelProperty("法人姓名")
    private String legalPersonName ;
    /** 公司固定电话（修改前） */
    @ApiModelProperty("公司固定电话（修改前）")
    private String companyPhoneBefore ;
    /** 公司固定电话（修改后） */
    @ApiModelProperty("公司固定电话（修改后）")
    private String companyPhone ;
    /** 公司传真号码（修改前） */
    @ApiModelProperty("公司传真号码（修改前）")
    private String companyFaxBefore ;
    /** 公司传真号码（修改后） */
    @ApiModelProperty("公司传真号码（修改后）")
    private String companyFax ;
    /** 是否境外(0,1)（修改前） */
    @ApiModelProperty("是否境外(0,1)（修改前）")
    private Integer isOverseasBefore ;
    /** 是否境外(0,1)（修改后） */
    @ApiModelProperty("是否境外(0,1)（修改后）")
    private Integer isOverseas ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("供应商名称是否修改")
    private Integer supplierNameChange ;
    /** 供应商简称是否修改 */
    @ApiModelProperty("供应商简称是否修改")
    private Integer supplierShortNameChange ;
    /** 机构性质是否修改 */
    @ApiModelProperty("机构性质是否修改")
    private Integer organizationNatureChange ;
    /** 企业类型是否修改 */
    @ApiModelProperty("企业类型是否修改")
    private Integer enterpriseTypeChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("注册资金是否修改")
    private Integer registeredAmtChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("注册币种是否修改")
    private Integer registeredCurrencyChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("成立日期是否修改")
    private Integer establishmentDateChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("营业期限终止是否修改")
    private Integer businessPeriodEndChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("营业期限起始是否修改")
    private Integer businessPeriodStartChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("注册地址是否修改")
    private Integer registeredAddressChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("公司邮编是否修改")
    private Integer companyZipCodeChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("生产地址是否修改")
    private Integer productionAddressChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("经营范围是否修改")
    private Integer businessScopeChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("邓白氏编码D-U-N-S是否修改")
    private Integer dunAndBradstreetCodeChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("公司固定电话是否修改")
    private Integer companyPhoneChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("公司传真号码是否修改")
    private Integer companyFaxChange ;
    /** 供应商名称是否修改 */
    @ApiModelProperty("是否境外(0,1)是否修改")
    private Integer isOverseasChange ;

    private List<AttachmentMessageRespDTO> businessLicenseAttachmentList;

}
