package com.weifu.srm.supplier.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QualificationChangeUpdateUserReqDTO {

    private Long supplierId;
    private List<QualificationChangeUpdateUserItemReqDTO> qualificationChangeUpdateUserItemReqDTOList;
    private String operationUser;
    private Long operationUserId;

    @Data
    public static class QualificationChangeUpdateUserItemReqDTO implements Serializable {

        private String name;
        private String type;
        private String phone;
        private String email;
        private String position;

    }

}
