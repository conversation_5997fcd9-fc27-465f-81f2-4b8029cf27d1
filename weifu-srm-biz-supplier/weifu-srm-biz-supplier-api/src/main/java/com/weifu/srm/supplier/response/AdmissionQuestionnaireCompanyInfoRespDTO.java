package com.weifu.srm.supplier.response;

import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:15
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表公司信息",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCompanyInfoRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 董事长 */
    @ApiModelProperty("董事长")
    private String chairman ;
    /** 总经理 */
    @ApiModelProperty("总经理")
    private String generalManager ;
    /** 是否上市 */
    @ApiModelProperty("是否上市")
    private Integer isListed ;
    @ApiModelProperty("公司架构附件")
    private List<AttachmentMessageRespDTO> organizationAttachment ;
    @ApiModelProperty("股权架构附件")
    private List<AttachmentMessageRespDTO> equityAttachment ;
}
