package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SupplierGradeRespDTO {

    @ApiModelProperty(value = "供应商编码", notes = "")
    private String sapSupplierCode;
    @ApiModelProperty(value = "供应商名称", notes = "")
    private String supplierName;
    @ApiModelProperty(value = "供应商状态key", notes = "")
    private String status;
    @ApiModelProperty(value = "供应商分级类型key", notes = "")
    private String supplierClassification;
    @ApiModelProperty(value = "供应商类型Key", notes = "")
    private String supplierType;
    @ApiModelProperty(value = "黑名单状态Key", notes = "")
    private String blackRestrictions;
    @ApiModelProperty(value = "灰名单状态Key", notes = "")
    private String greyRestrictions;
    @ApiModelProperty(value = "供应商标签", notes = "")
    private List<String> tags;
}
