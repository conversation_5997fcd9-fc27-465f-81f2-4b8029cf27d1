package com.weifu.srm.supplier.request.suppliercategoryadjustapply;

import com.weifu.srm.supplier.request.AttachmentMessageReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "供应商品类调整申请上传会议决议附件入参")
public class SupplierCategoryAdjustApplyUploadFileReqDTO {

    @ApiModelProperty(name = "申请编号")
    private String applyNo;
    @ApiModelProperty(name = "操作人")
    private Long operatorBy;
    @ApiModelProperty(name = "操作人姓名")
    private String operatorName;
    @ApiModelProperty(name = "会议决议附件")
    private AttachmentMessageReqDTO file;

}
