package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/7 10:57
 * @Description CategoryRespDTO
 * @Version 1.0
 */
@Data
@ApiModel(value = "CategoryRespDTO", description = "品类dto")
@NoArgsConstructor
public class CategoryRespDTO {
    @ApiModelProperty(name = "品类ID")
    private Long id;
    @ApiModelProperty(name = "采购品类编号")
    private String categoryCode;
    @ApiModelProperty(name = "采购品类中文名称")
    private String categoryName;
    @ApiModelProperty(name = "采购品类英文名称")
    private String categoryNameEn;
}
