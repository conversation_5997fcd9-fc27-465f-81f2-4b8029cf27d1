package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierBasicSimpleInfoRespDTO {
    @ApiModelProperty("ID")
    private Long id;
    /**
     * SAP（CCRM）供应商编码
     */
    @ApiModelProperty(name = "SAP（CCRM）供应商编码", notes = "")
    private String sapSupplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称", notes = "")
    private String supplierName;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称英文", notes = "")
    private String supplierNameEN;

    @ApiModelProperty("供应商状态")
    private String status;

    @ApiModelProperty(name = "供应商名简称", notes = "")
    private String supplierShortName;
}
