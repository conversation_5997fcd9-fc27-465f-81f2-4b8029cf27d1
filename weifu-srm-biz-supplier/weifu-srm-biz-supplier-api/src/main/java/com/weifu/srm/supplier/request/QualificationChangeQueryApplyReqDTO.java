package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QualificationChangeQueryApplyReqDTO extends PageRequest implements Serializable {

    /**
     * 资质变更编号
     */
    @ApiModelProperty(name = "资质变更编号", notes = "")
    private String qualificationChangeNo;
    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "供应商编码", notes = "")
    private String supplierCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(name = "供应商名称", notes = "")
    private String supplierName;
    /**
     * 变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）
     */
    @ApiModelProperty(name = "变更类型（基本信息变更; 财务信息变更; 质量资质证书添加; 非质量资质证书添加; 质量资质证书删除; 非质量资质证书删除; 关联方信息变更）", notes = "")
    private String changeType;
    /**
     * 变更状态（已提交；审批通过；审批拒绝；已撤回）
     */
    @ApiModelProperty(name = "变更状态（已提交；审批通过；审批拒绝；已撤回）", notes = "")
    private String changeStatus;
    /**
     * 工单编号
     */
    @ApiModelProperty(name = "工单编号", notes = "")
    private String ticketNo;
    /**
     * 创建开始时间
     */
    @ApiModelProperty(name = "创建开始时间", notes = "")
    private String createTimeStart;
    /**
     * 创建结束时间
     */
    @ApiModelProperty(name = "创建结束时间", notes = "")
    private String createTimeEnd;
    /**
     * 审批开始时间
     */
    @ApiModelProperty(name = "审批开始时间", notes = "")
    private String approveTimeStart;
    /**
     * 审批结束时间
     */
    @ApiModelProperty(name = "审批结束时间", notes = "")
    private String approveTimeEnd;

    private String operationUser;
    private Long operationUserId;
    private Long supplierId;

}
