package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SupplierAdmissionInvitationDetailRespDTO implements Serializable {
    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;
    @ApiModelProperty(value = "准入邀请编号")
    private String invitationNo ;
    /** 供应商ID*/
    @ApiModelProperty(value = "供应商表ID")
    private Long supplierBasicMsgId;
    /** 企业社会信用编码*/
    @ApiModelProperty(value = "企业社会信用编码")
    private String creditCode;
    /** 供应商名称 */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName ;
    /** 供应商简称 */
    @ApiModelProperty(value = "供应商简称")
    private String supplierShortName ;
    /** 供应商类型 */
    @ApiModelProperty(value = "供应商类型")
    private String supplierType ;
    /** 准入类型*/
    @ApiModelProperty(value = "准入类型")
    private String admissionType;
    /** 境内外关系 */
    @ApiModelProperty(value = "境内外关系")
    private Integer domesticForeignRelationship ;
    /** 客户指定证明附件*/
    @ApiModelProperty(value = "客户指定证明附件")
    private List<AttachmentMessageRespDTO> customerDesignationProofAttachments ;
    /** 供应商切换前原名称 */
    @ApiModelProperty(value = "供应商切换前原名称")
    private String previousSupplierName ;
    /** 供应商切换前原SAP编码 */
    @ApiModelProperty(value = "供应商切换前原SAP编码")
    private String previousSupplierSapCode ;
    /** 准入邀请补充信息附件 */
    @ApiModelProperty(name = "准入邀请补充信息附件")
    private List<AttachmentMessageRespDTO> suppAttachments ;
    /** 供应商注册联系人姓名 */
    @ApiModelProperty(value = "供应商注册联系人姓名")
    private String supplierRegistrationContactName ;
    /** 供应商注册联系人邮箱 */
    @ApiModelProperty(value = "供应商注册联系人邮箱")
    private String supplierRegistrationContactEmail ;
    /** 电话号码 */
    @ApiModelProperty(value = "电话号码")
    private String phone ;
    /** 采购类型 */
    @ApiModelProperty(value = "采购类型")
    private String purchaseType ;
    /** 准入品类（末级） */
    @ApiModelProperty(value = "准入品类（末级）")
    private List<AdmissionCategoryRespDTO> admissionCategories ;
    /** 准入背景和目的 */
    @ApiModelProperty(value = "准入背景和目的")
    private String introductionBackgroundAndPurpose ;
    /** 调查表模版编码 */
    @ApiModelProperty(value = "调查表模版编码")
    private String questionnaireTemplateCode ;
    /** 调查表模版名称 */
    @ApiModelProperty(value = "调查表模版名称")
    private String questionnaireTemplateName ;
    /** 调查表说明 */
    @ApiModelProperty(value = "调查表说明")
    private String questionnaireDesc ;
    /** 年度采购金额（元） */
    @ApiModelProperty(value = "年度采购金额（元）")
    private BigDecimal annualPurchaseAmt ;
    /** 年度采购次数 */
    @ApiModelProperty(value = "年度采购次数")
    private Long annualPurchaseCnt ;
    /** 最大单次采购金额（元） */
    @ApiModelProperty(value = "最大单次采购金额（元）")
    private BigDecimal mastPurchaseAmt ;
    /** 交易有效开始时间 */
    @ApiModelProperty(value = "交易有效开始时间")
    private String admissionStartTime ;
    /** 交易有效终止时间 */
    @ApiModelProperty(value = "交易有效终止时间")
    private String admissionEndTime ;
    /** 临时采购组织 */
    @ApiModelProperty(value = "临时采购组织")
    private String tmpPurchaseOrg;
    /** 供应商准入邀请状态 */
    @ApiModelProperty(value = "供应商准入邀请状态")
    private String supplierAdmissionInvitationStatus ;
    /** 邀请发起人Id */
    @ApiModelProperty(value = "邀请发起人Id")
    private Long admissionInvitationBy;
    /** 邀请发起人*/
    @ApiModelProperty(value = "邀请发起人")
    private String admissionInvitationName;
    /** 邀请创建时间*/
    @ApiModelProperty(value = "邀请创建时间")
    private String invitationTime;
    /**1: 集采 0: 非集采*/
    private Integer isCenPurchase;
    @ApiModelProperty(value = "邀请人所在事业部")
    private String divisionCode;
}
