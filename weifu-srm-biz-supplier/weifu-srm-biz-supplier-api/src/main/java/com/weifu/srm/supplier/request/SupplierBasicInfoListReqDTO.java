package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SupplierBasicInfoListReqDTO extends PageRequest implements Serializable {

    private List<String> sapSupplierCodes;

    private String supplierName;

    private String status;

    private List<String> statusS;

    private String supplierClassification;
    private List<String> supplierClassifications;

    private List<String> tags;
    /**0:否 1:是*/
    private Integer greyListStatus;
    /**0:否 1:是*/
    private Integer blackListStatus;

    private List<String> cpeMainNames;

    private List<String> sqeMainNames;

    private String registerDateStart;

    private String registerDateEnd;

    private String potentialToQualifiedDateStart;

    private String potentialToQualifiedDateEnd;

    private Long operationUserId;
    private String operationUser;
}
