package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdmissionInvitationCategoryRespDTO implements Serializable {
    @ApiModelProperty(value = "采购品类编号",notes = "")
    private String categoryCode ;
    /** 采购品类中文名称 */
    @ApiModelProperty(value = "采购品类中文名称",notes = "")
    private String categoryName ;
    /** 采购品类英文名称 */
    @ApiModelProperty(value = "采购品类英文名称",notes = "")
    private String categoryNameEn ;
    /** 采购品类等级 */
    @ApiModelProperty(value = "采购品类等级",notes = "")
    private String categoryLevel ;
    /** 子类 */
    @ApiModelProperty(value = "子类",notes = "")
    private List<AdmissionInvitationCategoryRespDTO> children;
}
