package com.weifu.srm.supplier.request.black;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class SupplierBlackListSearchReqDTO implements Serializable {

    @NotNull(message = "供应商编码不能为空")
    @Size(min = 1, message = "供应商编码不能为空")
    @ApiModelProperty(value = "供应商编码集合")
    private List<String> supplierCodes;
}
