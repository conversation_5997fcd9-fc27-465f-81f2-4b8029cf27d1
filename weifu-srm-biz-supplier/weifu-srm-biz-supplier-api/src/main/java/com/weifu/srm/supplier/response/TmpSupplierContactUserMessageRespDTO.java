package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class TmpSupplierContactUserMessageRespDTO implements Serializable {

    @ApiModelProperty(value = "联系人名称")
    private String supplierRegistrationContactName;
    @ApiModelProperty(value = "联系人电话")
    private String phone;
    @ApiModelProperty(value = "联系人邮箱")
    private String supplierRegistrationContactEmail;
}
