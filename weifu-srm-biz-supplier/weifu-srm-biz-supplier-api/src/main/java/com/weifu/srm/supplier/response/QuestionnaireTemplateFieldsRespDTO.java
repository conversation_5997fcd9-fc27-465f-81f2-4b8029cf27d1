package com.weifu.srm.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import nonapi.io.github.classgraph.json.Id;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateFieldsRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @Id
    private Long id;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "Tab页")
    private String categorize;
    /**
     * 排序索引
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;
    /**
     * 排序索引
     */
    @ApiModelProperty(value = "字段英文名称")
    private String fieldNameEn;
    /**
     * 排序索引
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldNameCn;
    /**
     * 排序索引
     */
    @ApiModelProperty(value = "排序索引")
    private Integer orderIndex;
    /**
     * 字段要求设置
     */
    @ApiModelProperty(value = "字段要求设置 必填/选填/不适用")
    private String fieldSetting;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String fieldRemark;
    /**
     * 是否可添加多组
     */
    @ApiModelProperty(value = "是否可添加多组")
    private Integer isMultigroup;
    @ApiModelProperty(name = "创建人",notes = "")
    private String createBy ;
    /** 创建人名称 */
    @ApiModelProperty(name = "创建人名称",notes = "")
    private String createName ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updateBy ;
    /** 更新人名 */
    @ApiModelProperty(name = "更新人名",notes = "")
    private String updateName ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date updateTime ;
    /** 逻辑删除 */
    @ApiModelProperty(name = "逻辑删除",notes = "")
    private Integer isDelete ;

}
