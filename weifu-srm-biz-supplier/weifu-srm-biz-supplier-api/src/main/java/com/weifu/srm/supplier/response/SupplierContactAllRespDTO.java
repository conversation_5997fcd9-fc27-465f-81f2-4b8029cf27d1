package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 供应商所有联系人response
 * @Version 1.0
 */
@ApiModel(value = "SupplierContactAllRespDTO", description = "供应商所有联系人response")
@Data
public class SupplierContactAllRespDTO {


    @ApiModelProperty("供应商基础表Id")
    private Long supplierBasicMsgId ;

    @ApiModelProperty("联系人类型")
    private String type;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("职位")
    private String position;


}
