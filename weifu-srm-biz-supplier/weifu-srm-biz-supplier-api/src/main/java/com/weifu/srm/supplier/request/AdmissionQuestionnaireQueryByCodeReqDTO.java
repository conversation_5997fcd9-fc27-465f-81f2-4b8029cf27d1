package com.weifu.srm.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:22
 * @Description 准入调查表填写DTO
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class AdmissionQuestionnaireQueryByCodeReqDTO implements Serializable {

    /**
     * 准入编号
     */
    @ApiModelProperty("准入编号")
    @NotNull(message = "准入编号不能为空")
    private String admissionNo;


}
