package com.weifu.srm.supplier.response.policy;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class AttachmentRespDTO implements Serializable {

    /**
     * 附件真实名称
     */
    private String fileOriginalName;

    /**
     * 附件URL
     */
    private String fileUrl;

    /**
     * 附件Name
     */
    private String fileName;

    private Long createBy;
    private String createName;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;
}
