package com.weifu.srm.supplier.response.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PolicyListRespDTO extends BaseRespDTO {

    @ApiModelProperty("商务政策编号")
    private String policyNo;

    @ApiModelProperty(value = "导入状态\n" +
            "未导入:NO_IMPORT\n" +
            "导入成功:IMPORT_SUCCESS\n" +
            "导入失败:IMPORT_FAIL")
    private String importStatus;

    @ApiModelProperty(value = "导入反馈")
    private String importFeedback;

    @ApiModelProperty(value = "供应商编码")
    private String sapSupplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "事业部编码")
    private String divisionCode;

    @ApiModelProperty(value = "事业部名称")
    private String divisionName;

    @ApiModelProperty(value = "业务小类编码")
    private String businessSubclassCode;

    @ApiModelProperty(value = "业务小类名称")
    private String businessSubclassName;

    @ApiModelProperty(value = "业务大类编码")
    private String businessCategoryCode;

    @ApiModelProperty(value = "业务大类名称")
    private String businessCategoryName;

    @ApiModelProperty(value = "付款周期")
    private Long paymentCycle;

    @ApiModelProperty(value = "付款条款类型")
    private String paymentTermsType;

    @ApiModelProperty(value = "付款基准日期类型")
    private String paymentBaseDateType;

    @ApiModelProperty(value = "质保等抵押金")
    private BigDecimal collateralAmount;

    @ApiModelProperty(value = "票据（%）")
    private BigDecimal bill;

    @ApiModelProperty(value = "银企直连（%）")
    private BigDecimal bankEnterpriseLink;

    @ApiModelProperty(value = "应收票据-银票（%）")
    private BigDecimal billReceivable;

    @ApiModelProperty(value = "其它（%）")
    private BigDecimal other;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "CPE主责人ID")
    private Long cpeMainId;

    @ApiModelProperty(value = "CPE主责人名称", notes = "")
    private String cpeMainName;
}
