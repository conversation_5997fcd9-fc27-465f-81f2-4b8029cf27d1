package com.weifu.srm.supplier.request;

import com.weifu.srm.common.restful.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @Date 2024/7/05 14:38
 * @Description
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class QuestionnaireTemplateReqDTO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;
    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    private String supplierType;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createName;
    /**
     * 启用状态
     */
    @ApiModelProperty(value = "启用状态")
    private Integer enable;

}
