package com.weifu.srm.supplier.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 10:57
 * @Description
 * @Version 1.0
 */

@ApiModel(value = "准入调查表客户情况",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireCustomerRespDTO implements Serializable {
    /**
     * 准入编号
     */
    private String admissionNo;
    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String customerName ;
    /** 销售额（每年，万元） */
    @ApiModelProperty("销售额（每年，万元）")
    private Double annualSalesAmt ;
    /** 主要供应商产品 */
    @ApiModelProperty("主要供应商产品")
    private String mainSupplierProducts ;
    /** 是否有汽车行业合作经验 */
    @ApiModelProperty("是否有汽车行业合作经验")
    private Integer hasAutomotiveIndustryExperience ;
}
