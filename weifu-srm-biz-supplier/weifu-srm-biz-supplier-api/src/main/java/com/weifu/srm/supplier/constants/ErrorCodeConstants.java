package com.weifu.srm.supplier.constants;

public class ErrorCodeConstants {

    /** 同步资质变更（基本信息）至SAP失败 */
    public static final String SYNC_QUALIFICATION_CHANGE_TO_SAP_FAILED = "sync.qualification.change.to.sap.failed";
    /** 同步资质变更信息至CCMS失败 */
    public static final String SYNC_QUALIFICATION_CHANGE_TO_CCMS_FAILED = "sync.qualification.change.to.ccms.failed";
    /** 同步资质变更（基本信息）至CCMS失败 */
    public static final String SYNC_QUALIFICATION_CHANGE_BASIC_TO_CCMS_FAILED = "sync.qualification.change.basic.to.ccms.failed";
    /** 同步财务信息至SAP失败 */
    public static final String SYNC_FINANCIAL_INFO_TO_SAP_FAILED = "sync.financial.info.to.sap.failed";
    /** 同步财务信息至CCMS失败 */
    public static final String SYNC_FINANCIAL_INFO_TO_CCMS_FAILED = "sync.financial.info.to.ccms.failed";
    /** 查询品类信息异常 */
    public static final String QUERY_CATEGORY_INFO_EXCEPTION = "query.category.info.exception";
    /** 供应商存在审核中的调整申请 */
    public static final String SUPPLIER_HAS_PENDING_ADJUSTMENT_APPLICATION = "supplier.category.adj.has.pending.adjustment.application";
    /** 三级品类删除后，同一二级品类下还需至少保留一个三级品类关系 */
    public static final String SUPPLIER_CATEGORY_ADJ_THIRD_LEVEL_CATEGORY_CANNOT_BE_EMPTY = "supplier.category.adj.third.level.category.cannot.be.empty";
    /** 查找审批人失败，请检查品类[{0}]上品类组长、采购处长等配置 */
    public static final String FIND_APPROVER_FAILED_CHECK_CONFIGURATION = "find.approver.failed.check.configuration";

}
