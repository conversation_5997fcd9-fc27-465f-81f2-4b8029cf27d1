package com.weifu.srm.supplier.response.grey;

import com.weifu.srm.supplier.response.AttachmentMessageRespDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierGreyListApplyDetailRespDTO implements Serializable {

    private String applyNo;

    private String createTime;

    private String createUser;

    private Integer applyType;

    private String applyDesc;

    private String applyRemark;

    private List<AttachmentMessageRespDTO> applyAttachments;

    private List<SupplierGreyListItemRespDTO> greyList;
}
