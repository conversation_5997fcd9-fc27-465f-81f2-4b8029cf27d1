package com.weifu.srm.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/7/16 11:14
 * @Description
 * @Version 1.0
 */
@ApiModel(value = "准入调查表物流能力",description = "")
@NoArgsConstructor
@Data
public class AdmissionQuestionnaireLogisticsSaveDTO extends AdmissionQuestionnaireBasicReqDTO implements Serializable {

    /** 是否有仓库 */
    @ApiModelProperty("是否有仓库")
    @Max(value = 1, message = "只能为0/1")
    @Min(value = 0, message = "只能为0/1")
    private Integer hasWarehouse ;
    /** 仓库面积 */
    @ApiModelProperty("仓库面积")
    @Digits(integer = 10, fraction = 2, message = "格式不正确，整数部分最多10位，小数最多2位")
    private BigDecimal warehouseArea ;
}
