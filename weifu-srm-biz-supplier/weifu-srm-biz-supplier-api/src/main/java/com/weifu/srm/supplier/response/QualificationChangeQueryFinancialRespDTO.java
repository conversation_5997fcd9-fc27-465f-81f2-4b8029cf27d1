package com.weifu.srm.supplier.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QualificationChangeQueryFinancialRespDTO {

    @ExcelIgnore
    private Long id;
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;
    @ExcelProperty(value = "供应商名称")
    private String supplierName;
    /** 国家/地区代码 */
    @ApiModelProperty("国家/地区代码")
    @ExcelProperty(value = "国家/地区代码")
    private String countryRegionCode ;
    /** 银行代码 */
    @ApiModelProperty("银行代码")
    @ExcelProperty(value = "银行代码")
    private String bankCode ;
    /** 银行名称 */
    @ApiModelProperty("银行名称")
    @ExcelProperty(value = "银行名称")
    private String bankName ;
    /** 联行号 */
    @ApiModelProperty("联行号")
    @ExcelProperty(value = "联行号")
    private String bankBranchCode ;
    /** 开户行名称 */
    @ApiModelProperty("开户行名称")
    @ExcelProperty(value = "开户行名称")
    private String accountBankName ;
    /** 账户名称 */
    @ApiModelProperty("账户名称")
    @ExcelProperty(value = "账户名称")
    private String accountName ;
    /** 银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银 */
    @ApiModelProperty("银行账号类型-承兑汇票-外付网银-承兑汇票-外付网银")
    @ExcelProperty(value = "银行账号类型")
    private String bankAccountType ;
    /** 银行账号  用作附件表中对应开票信息的business_no*/
    @ApiModelProperty("银行账号  用作附件表中对应开票信息的business_no")
    @ExcelProperty(value = "银行账号")
    private String bankAccount ;
    /** 开票资料附件-数据值与attachment_record.business_type保持一致 */
    @ApiModelProperty("开票资料附件-数据值与attachment_record.business_type保持一致")
    @ExcelIgnore
    private String invoiceDataAttachment ;
    /** 附件 */
    @ExcelIgnore
    private List<AttachmentMessageRespDTO> financialAttachmentList;

}
