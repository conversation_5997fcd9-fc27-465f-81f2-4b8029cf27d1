package com.weifu.srm.supplier.request.assessment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/23 13:41
 * @Description 流转至CPE
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class SupplierAssessmentReturnToSQEReqDTO implements Serializable {
    @ApiModelProperty(value = "考核单编号",notes = "")
    @NotNull(message = "assessmentNo can not be null")
    private String assessmentNo ;
    @ApiModelProperty(value = "cpe退回说明",notes = "")
    @NotNull(message = "cpeReturnDesc can not be null")
    private String cpeReturnDesc ;
    @ApiModelProperty(value = "系统上下文用户ID",notes = "")
    private Long userId ;
    @ApiModelProperty(value = "系统上下文用户名称",notes = "")
    private String userName ;
}
