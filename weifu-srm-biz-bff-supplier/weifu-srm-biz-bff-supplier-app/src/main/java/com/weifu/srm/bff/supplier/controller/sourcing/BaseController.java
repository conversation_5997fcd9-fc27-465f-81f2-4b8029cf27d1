package com.weifu.srm.bff.supplier.controller.sourcing;

import com.weifu.srm.bff.supplier.service.SupplierSwitchService;
import com.weifu.srm.common.exception.BizFailException;
import org.apache.commons.lang3.StringUtils;

public abstract class BaseController {

    protected static final String NONE_EXISTS_SUPPLIER_CODE = "-1";
    protected static final String NONE_EXISTS_SUPPLIER_ERROR_MSG = "获取当前登录用户选中的供应商编码失败";

    protected String extractSupplierCode() {
        String supplierCode = getSupplierSwitchService().getSapSupplierCodeByUserId();
        if (StringUtils.isBlank(supplierCode)) {
            throw new BizFailException(NONE_EXISTS_SUPPLIER_ERROR_MSG);
        }
        return supplierCode;
    }

    protected String extractSupplierCodeWithDefVal() {
        String supplierCode = getSupplierSwitchService().getSapSupplierCodeByUserId();
        if (StringUtils.isNotBlank(supplierCode)) {
            return supplierCode;
        }
        return NONE_EXISTS_SUPPLIER_CODE;
    }

    protected abstract SupplierSwitchService getSupplierSwitchService();
}
