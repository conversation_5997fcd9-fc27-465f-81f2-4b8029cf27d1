package com.weifu.srm.bff.supplier.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.composite.api.AnnouncementInfoApi;
import com.weifu.srm.composite.enums.AnnouncementScopeEnum;
import com.weifu.srm.composite.response.announcement.AnnouncementDetailRespDTO;
import com.weifu.srm.composite.response.announcement.ListAnnouncementRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "公告管理")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class AnnouncementInfoBFFController {

    private final AnnouncementInfoApi announcementInfoApi;

    @ApiOperation("查询公告详情")
    @GetMapping("/base/announcement/detail")
    public ApiResponse<AnnouncementDetailRespDTO> detail(@RequestParam("id") Long id) {
        return announcementInfoApi.detail(id);
    }

    @ApiOperation("查询已发布状态公告")
    @GetMapping("/base/announcement/list")
    public ApiResponse<List<ListAnnouncementRespDTO>> list() {
        return announcementInfoApi.list(List.of(AnnouncementScopeEnum.ALL.getCode(), AnnouncementScopeEnum.SUPPLIER.getCode()));
    }

}
