package com.weifu.srm.bff.supplier.controller;

import com.weifu.srm.bff.supplier.service.SupplierSwitchService;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.communication.api.TodoListApi;
import com.weifu.srm.communication.request.todolist.ListOnTodoReqDTO;
import com.weifu.srm.communication.request.todolist.TodoListPageReqDTO;
import com.weifu.srm.communication.response.todolist.TodoListListRespDTO;
import com.weifu.srm.communication.response.todolist.TodoListPageRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "待办事项管理")
@Slf4j
@RestController
@RequiredArgsConstructor
public class TodoListBFFController {

    private final TodoListApi todoListApi;
    private final SupplierSwitchService supplierSwitchService;

    @ApiOperation("分页查询待办事项")
    @PostMapping({"/base/todo-list/page"})
    public ApiResponse<PageResponse<TodoListPageRespDTO>> page(@RequestBody TodoListPageReqDTO req) {
        req.setUserId(SecurityContextHolder.getUserId());
        req.setSupplierId(supplierSwitchService.getSupplierIdByUserId());
        return todoListApi.page(req);
    }

    @ApiOperation("查询待处理待办事项数量")
    @GetMapping({"/base/todo-list/todo-cnt"})
    public ApiResponse<Long> getTodoCnt() {
        Long supplierId = supplierSwitchService.getSupplierIdByUserId();
        return todoListApi.getTodoCnt(SecurityContextHolder.getUserId(), supplierId, null);
    }

    @Operation(summary = "查询待办事项待处理列表")
    @GetMapping("/base/todo-list/todo-list")
    ApiResponse<List<TodoListListRespDTO>> listOnTodo(@SpringQueryMap ListOnTodoReqDTO req) {
        Long supplierId = supplierSwitchService.getSupplierIdByUserId();
        req.setUserId(SecurityContextHolder.getUserId());
        req.setSupplierId(supplierId);
        return todoListApi.listOnTodo(req);
    }

}
