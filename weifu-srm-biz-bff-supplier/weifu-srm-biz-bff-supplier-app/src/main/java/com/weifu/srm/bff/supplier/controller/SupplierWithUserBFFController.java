package com.weifu.srm.bff.supplier.controller;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.supplier.api.SupplierWithUserApi;
import com.weifu.srm.supplier.response.SupplierRegistryInfoRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "供应商与用户管理服务")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class SupplierWithUserBFFController {
    private final SupplierWithUserApi supplierWithUserApi;

    /**
     * 根据用户Id查询用户所属的供应商注册状态
     * @return
     */
    @ApiOperation("查询当前登陆用户所属供应商")
    @GetMapping("/supplier/registryInfo/list")
    public ApiResponse<List<SupplierRegistryInfoRespDTO>> querySupplierRegistryListByUserId(@RequestParam(value = "userId",required = false) Long userId){
        Long currentId = SecurityContextHolder.getUserId();
        return supplierWithUserApi.querySupplierRegistryListByUserId(currentId);
    }
}
