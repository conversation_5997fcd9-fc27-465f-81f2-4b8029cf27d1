<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>weifu-srm-biz-bff-supplier</artifactId>
        <groupId>com.weifu</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>weifu-srm-biz-bff-supplier-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-swagger</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <!-- SpringCloud Openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-supplier-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>