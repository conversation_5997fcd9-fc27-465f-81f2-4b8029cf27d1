package com.weifu.srm.internal.controller.performance;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.internal.controller.cio.ExportController;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.log.enums.ActionTypeEnum;
import com.weifu.srm.supplier.performance.api.PerformanceExamineApi;
import com.weifu.srm.supplier.performance.request.*;
import com.weifu.srm.supplier.performance.request.examine.*;
import com.weifu.srm.supplier.performance.response.examine.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效评价方案 前端控制器
 * @Date 2024-09-20
 */
@Slf4j
@Validated
@Api(tags = "绩效列表")
@RequiredArgsConstructor
@RestController
public class PerformanceExamineBffController extends ExportController {
    private final PerformanceExamineApi performanceExamineApi;

    @ApiOperation("绩效考核分页查询")
    @PostMapping("/performance/examine/page")
    ApiResponse<PageResponse<PerformanceExamineRespDTO>> page(@RequestBody @Valid PerformanceExaminePageReqDTO reqDTO) {
        return performanceExamineApi.page(reqDTO);
    }

    @ApiOperation("绩效考核详情")
    @GetMapping("/performance/examine/detail")
    ApiResponse<PerformanceExamineRespDTO> detail(@RequestParam("performanceNo") String performanceNo) {
        return performanceExamineApi.detail(performanceNo);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_PERFORMANCE_EVALUATION, actionType = ActionTypeEnum.EXPORT)
    @ApiOperation("导出绩效考核")
    @PostMapping("/performance/examine/export")
    ApiResponse<Void> export(@RequestBody @Valid PerformanceExaminePageReqDTO reqDTO) {
        HttpServletResponse response = getHttpServletResponse();
        reqDTO.setPageSize(NO_PAGE_SIZE);
        ApiResponse<PageResponse<PerformanceExamineRespDTO>> apiResponse = performanceExamineApi.page(reqDTO);
        export("绩效考核", apiResponse.getData(), PerformanceExamineRespDTO.class, response);
        return null;
    }

    @OperationLog(behaviorCode = BehaviorConstants.CREATE_PERFORMANCE_EVALUATION_DRAFT, businessNo = "#result.data")
    @ApiOperation("创建绩效考核草稿")
    @PostMapping("/performance/examine/draft")
    ApiResponse<String> draft(@RequestBody @Valid PerformanceExamineAddReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.draft(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.SUBMIT_PERFORMANCE_EVALUATION, businessNo = "#reqDTO.id")
    @ApiOperation("提交绩效考核")
    @PostMapping("/performance/examine/submit")
    ApiResponse<Void> submit(@RequestBody @Valid PerformanceExamineSubmitReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.submit(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.DELETE_PERFORMANCE_EVALUATION_DRAFT, businessNo = "#reqDTO.id")
    @ApiOperation("删除绩效考核草稿")
    @PostMapping("/performance/examine/delete")
    ApiResponse<Void> delete(@RequestBody @Valid PerformanceIdReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.delete(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.SAVE_PERFORMANCE_EVALUATION_SUPPLIERS_WITHOUT_SECOND_LEVEL_CATEGORY, businessNo = "#reqDTO.performanceNo")
    @ApiOperation("保存绩效考核未设定绩效考核二级品类的供应商")
    @PostMapping("/performance/examine/supplier/save")
    ApiResponse<Void> saveExamineSupplier(@RequestBody @Valid PerformanceExamineSupplierSaveReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.saveExamineSupplier(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.DELETE_SUPPLIERS_WITHOUT_SETTING_UP_PERFORMANCE_APPRAISAL_PRODUCT_CATEGORIES, businessNo = "#reqDTO.id")
    @ApiOperation("删除未设定绩效考核二级品类的供应商")
    @PostMapping("/performance/examine/supplier/delete")
    ApiResponse<Void> deleteExamineSupplier(@RequestBody @Valid PerformanceIdReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.deleteExamineSupplier(reqDTO);
    }

    @ApiOperation("未设定绩效考核二级品类的供应商分页查询")
    @PostMapping("/performance/examine/examineSupplierPage")
    ApiResponse<PageResponse<PerformanceExamineSupplierRespDTO>> examineSupplierPage(@RequestBody @Valid PerformanceExamineSupplierPageReqDTO reqDTO) {
        return performanceExamineApi.examineSupplierPage(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_SUPPLIER_SCOPE_FOR_PERFORMANCE_APPRAISAL, actionType = ActionTypeEnum.EXPORT)
    @ApiOperation("导出绩效考核供应商范围")
    @PostMapping("/performance/examine/examineSupplierPage/export")
    ApiResponse<Void> exportExamineSupplierPage(@RequestBody @Valid PerformanceExamineSupplierPageReqDTO reqDTO) {
        HttpServletResponse response = getHttpServletResponse();
        reqDTO.setPageSize(NO_PAGE_SIZE);
        ApiResponse<PageResponse<PerformanceExamineSupplierRespDTO>> apiResponse = performanceExamineApi.examineSupplierPage(reqDTO);
        export("供应商范围", apiResponse.getData(), PerformanceExamineSupplierRespDTO.class, response);
        return null;
    }

    @OperationLog(behaviorCode = BehaviorConstants.IMPORT_PERFORMANCE_EVALUATION_SUPPLIERS_WITHOUT_SECOND_LEVEL_CATEGORY, actionType = ActionTypeEnum.IMPORT, businessNo = "#performanceNo")
    @ApiOperation("导入绩效考核未设定绩效考核二级品类的供应商")
    @PostMapping(value = "/performance/examine/category/supplier/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<PerformanceExamineSupplierImportRespDTO> importSupplier(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "performanceNo") String performanceNo) {
        log.info("importSupplier:{}", performanceNo);
        return performanceExamineApi.importSupplier(file, performanceNo, SecurityContextHolder.getUserId(), SecurityContextHolder.getRealName());
    }

    @ApiOperation("绩效考核人员设定列表查询")
    @GetMapping("/performance/examine/person/list")
    ApiResponse<List<PerformanceExaminePersonRespDTO>> listPersonSetting(@RequestParam("performanceNo") String performanceNo) {
        return performanceExamineApi.listPersonSetting(performanceNo);
    }

    @OperationLog(behaviorCode = BehaviorConstants.SAVE_PERFORMANCE_EVALUATION_PERSONNEL_SETTINGS, businessNo = "#result.data")
    @ApiOperation("保存绩效考核人员设定")
    @PostMapping("/performance/examine/person/save")
    ApiResponse<Long> savePersonSetting(@RequestBody @Valid PerformanceExaminePersonSaveReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.savePersonSetting(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.CALCULATE_THE_CALCULATION_AMOUNT_FOR_PERFORMANCE_APPRAISAL, businessNo = "#reqDTO.performanceNo")
    @ApiOperation("计算绩效考核计算额度")
    @PostMapping("/performance/examine/person/calculate")
    ApiResponse<Void> calculate(@RequestBody @Valid PerformanceExamineCalculateReqDTO reqDTO) {
        reqDTO.setOperationBy(SecurityContextHolder.getUserId());
        reqDTO.setOperationByName(SecurityContextHolder.getRealName());
        return performanceExamineApi.calculate(reqDTO);
    }

    @ApiOperation("交付指标负责人设定")
    @GetMapping("/performance/examine/division/listDeliveryUser")
    ApiResponse<List<PerformanceExamineDivisionRespDTO>> listDeliveryUser(@RequestParam(value = "performanceNo") String performanceNo) {
        return performanceExamineApi.listDeliveryUser(performanceNo);
    }


    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_PERFORMANCE_EVALUATION_PERSON_SETTING, actionType = ActionTypeEnum.IMPORT, businessNo = "#performanceNo")
    @ApiOperation("导入绩效考核人员设定")
    @PostMapping(value = "/performance/examine/person/save/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<List<String>> importPersonSetting(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "performanceNo") String performanceNo) {
        log.info("importPersonSetting:{}", performanceNo);
        return performanceExamineApi.importPersonSetting(file, performanceNo, SecurityContextHolder.getUserId(), SecurityContextHolder.getRealName());
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_PERFORMANCE_EVALUATION_PERSON_SETTING, actionType = ActionTypeEnum.EXPORT, businessNo = "#performanceNo")
    @ApiOperation("导出绩效考核人员设定")
    @GetMapping("/performance/examine/person/export")
    ApiResponse<Void> exportPersonSetting(@RequestParam("performanceNo") String performanceNo) {
        ApiResponse<List<PerformanceExaminePersonRespDTO>> apiResponse = performanceExamineApi.listPersonSetting(performanceNo);
        List<PerformanceExamineCpePersonRespDTO> cpeList = Lists.newArrayList();
        List<PerformanceExamineSqePersonRespDTO> sqeList = Lists.newArrayList();
        if (apiResponse != null && CollectionUtils.isNotEmpty(apiResponse.getData())) {
            List<PerformanceExaminePersonRespDTO> dataList = apiResponse.getData();
            buildExcelData(dataList, cpeList, sqeList);
        }
        HttpServletResponse response = getHttpServletResponse();
        try (OutputStream outputStream = response.getOutputStream();
             ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build()) {
            excelWriter.write(cpeList, EasyExcelFactory.writerSheet(0, "CPE设定").head(PerformanceExamineCpePersonRespDTO.class ).build());
            excelWriter.write(sqeList, EasyExcelFactory.writerSheet(1, "SQE设定").head(PerformanceExamineSqePersonRespDTO.class).build());
            excelWriter.finish();
            String fullName = "人员设定导出" + System.currentTimeMillis() + FILE_SUFFIX;
            setFileName(response, fullName);
        } catch (IOException e) {
            log.error("Error exporting file: {}", e.getMessage(), e);
        }
        return null;
    }


    private void buildExcelData(List<PerformanceExaminePersonRespDTO> dataList,
                                List<PerformanceExamineCpePersonRespDTO> cpeList,
                                List<PerformanceExamineSqePersonRespDTO> sqeList) {
        for (PerformanceExaminePersonRespDTO performanceExaminePerson : dataList) {
            PerformanceExamineCpePersonRespDTO cpe = new PerformanceExamineCpePersonRespDTO();
            cpe.setCategoryCode(performanceExaminePerson.getCategoryCode());
            cpe.setCategoryName(performanceExaminePerson.getCategoryName());
            cpe.setCpeName(getDomain(performanceExaminePerson.getCpeName()));
            cpe.setCpeMasterName(getDomain(performanceExaminePerson.getCpeMasterName()));
            cpeList.add(cpe);
            if(Boolean.TRUE.equals(performanceExaminePerson.getIsSelect())){
                PerformanceExamineSqePersonRespDTO sqe = new PerformanceExamineSqePersonRespDTO();
                sqe.setCategoryCode(performanceExaminePerson.getCategoryCode());
                sqe.setCategoryName(performanceExaminePerson.getCategoryName());
                sqe.setSqeName(getDomain(performanceExaminePerson.getSqeName()));
                sqe.setSqeMasterName(getDomain(performanceExaminePerson.getSqeMasterName()));
                sqeList.add(sqe);
            }
        }
    }

    public static String getDomain(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        int index = input.indexOf("_");
        return input.substring(index + 1);
    }
}
