package com.weifu.srm.internal.controller.requirement;

import com.alibaba.excel.EasyExcelFactory;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.internal.utils.BaseUtils;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.log.enums.ActionTypeEnum;
import com.weifu.srm.requirement.api.PartsPurchasePlanApi;
import com.weifu.srm.requirement.request.PartsPurchasePlanChangeRecordReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanNodeCompleteSaveReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanPostponeReqDTO;
import com.weifu.srm.requirement.request.PartsPurchasePlanSubmitReqDTO;
import com.weifu.srm.requirement.request.project.ProjectBaseReqDTO;
import com.weifu.srm.requirement.response.*;
import com.weifu.srm.requirement.response.project.ProjectFileResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Api(tags = "零件采购计划")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class PartsPurchasePlanBFFController {
    public static final String CHANGE_LOG = "change-log";

    private final PartsPurchasePlanApi partsPurchasePlanApi;
    private final LocaleMessage localeMessage;


    public void setUserInfo(ProjectBaseReqDTO reqDTO){
        reqDTO.setUserId(SecurityContextHolder.getUserId());
        reqDTO.setUserName(SecurityContextHolder.getRealName());
        reqDTO.setLocale(LocaleContextHolder.getLocale());
    }

    /**
     * 保存零件采购计划信息
     * @param reqDTO PartsPurchasePlanSubmitReqDTO
     * @return PartsPurchasePlanDetailRespDTO
     */
    @OperationLog(behaviorCode = BehaviorConstants.SAVE_PART_PURCHASE_PLAN, businessNo = "#result.data.planNo")
    @ApiOperation(value = "保存零件采购计划信息")
    @PostMapping("/requirement/parts/purchasePlan/save")
    ApiResponse<PartsPurchaseSaveRespDTO> savePurchasePlan(@Valid @RequestBody PartsPurchasePlanSubmitReqDTO reqDTO){
        setUserInfo(reqDTO);
        return partsPurchasePlanApi.savePurchasePlan(reqDTO);
    }

    /**
     * 零件采购计划延期原因保存
     * @param reqDTO
     * @return
     */
    @OperationLog(behaviorCode = BehaviorConstants.SAVE_PART_PURCHASE_PLAN_DELAY_REASON, businessNo = "#reqDTO.planNo")
    @ApiOperation(value = "保存零件采购计划延期原因")
    @PostMapping("/requirement/parts/purchasePlan/postponeSave")
    ApiResponse<Void> savePostpone(@Valid @RequestBody PartsPurchasePlanPostponeReqDTO reqDTO){
        setUserInfo(reqDTO);
        return partsPurchasePlanApi.savePostpone(reqDTO);
    }

    /**
     * 零件采购计划延期原因查询
     * @param planNO
     * @param planNode
     * @return
     */
    @ApiOperation(value = "零件采购计划延期原因查询")
    @GetMapping("/requirement/parts/purchasePlan/postponeList")
    ApiResponse<List<PartsPurchasePlanPostponeSearchRespDTO>> searchPostPoneList(@RequestParam("planNo") String planNO, @RequestParam("planNode") String planNode){
        return partsPurchasePlanApi.searchPostPoneList(planNO, planNode);
    }

    /**
     * 修改采购计划节点完成登记
     * @param reqDTO
     * @return
     */
    @OperationLog(behaviorCode = BehaviorConstants.COMPLETE_PURCHASE_PLAN_NODE_REGISTRATION, businessNo = "#reqDTO.planNo")
    @ApiOperation(value = "完成采购计划节点登记")
    @PostMapping("/requirement/parts/purchasePlan/nodeComplete")
    ApiResponse<Void> registerPlanNodeComplete(@Valid @RequestBody PartsPurchasePlanNodeCompleteSaveReqDTO reqDTO){
        setUserInfo(reqDTO);
        return partsPurchasePlanApi.registerPlanNodeComplete(reqDTO);

    }

    /**
     * 采购计划节点情况请查询
     * @param planNo
     * @return
     */
    @ApiOperation(value = "采购计划节点情况请查询")
    @GetMapping("/requirement/parts/purchasePlan/query")
    ApiResponse<PartsPurchasePlanDetailRespDTO> searchPlanNode(@RequestParam("planNo") String planNo){
        return partsPurchasePlanApi.searchPlanNode(null,planNo);
    }

    @ApiOperation(value = "根据零件需求编号查询采购计划需要的信息")
    @GetMapping("/requirement/parts/purchasePlan/requirementNo")
    ApiResponse<PartsPurchaseMessageWithRequirementRespDTO> searchPurchasePlanMessage(@RequestParam("requirementPartsNo") String requirementPartsNo){
        return partsPurchasePlanApi.searchPurchasePlanMessage(requirementPartsNo);
    }

    @OperationLog(behaviorCode = BehaviorConstants.EXPORT_PART_PURCHASE_PLAN_MODIFICATION_RECORD,actionType= ActionTypeEnum.EXPORT )
    @ApiOperation("导出零件采购计划修改记录")
    @PostMapping("/requirement/parts/purchasePlan/change/export")
    ApiResponse<Void> exportChangeLog(@Valid @RequestBody PartsPurchasePlanChangeRecordReqDTO reqDTO){
        ApiResponse<List<PartsPurchaseChangeLogRespDTO>> result = partsPurchasePlanApi.exportChangeLog(reqDTO);
        HttpServletResponse response = BaseUtils.getHttpServletResponse();
        try {
            setFileName(response, reqDTO.getRequirementPartsNo() + "-" + LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) +".xlsx");
            EasyExcelFactory.write(response.getOutputStream(), PartsPurchaseChangeLogRespDTO.class).sheet(CHANGE_LOG).doWrite(result.getData());
            return null;
        } catch (Exception e) {
            log.error("export parts purchase change log error:",e);
            throw new BizFailException(localeMessage.getMessage("export.parts.purchase.change.log.fail"));
        }
    }

    private void setFileName(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String encodeFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodeFileName);
    }

    /**
     * 根据 计采购计划编号 + 节点key查询附件
     * @param planNo
     * @param planNode
     * @return
     */
    @ApiOperation(value = "根据采购计划编号与节点key查询附件")
    @GetMapping("/requirement/parts/purchasePlan/attachmentList")
    ApiResponse<List<ProjectFileResDTO>> searchPlanNodeAttachmentList(@RequestParam("planNo") String planNo, @RequestParam("planNode") String planNode){
        return partsPurchasePlanApi.searchPlanNodeAttachmentList(planNo,planNode);
    }

    /**
     * 查看采购计划修改记录
     */
    @ApiOperation(value = "查询采购计划修改记录")
    @PostMapping("/requirement/parts/purchasePlan/changeRecord")
    ApiResponse<PageResponse<PartsPurchasePlanChangeLogResDTO>> queryRecordList(@Valid @RequestBody PartsPurchasePlanChangeRecordReqDTO reqDTO) {
        return partsPurchasePlanApi.queryRecordList(reqDTO);
    }

}