package com.weifu.srm.internal.controller.supplier;

import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.log.annotation.OperationLog;
import com.weifu.srm.log.constants.BehaviorConstants;
import com.weifu.srm.supplier.api.QuestionnaireTemplateApi;
import com.weifu.srm.supplier.request.*;
import com.weifu.srm.supplier.response.ChangeLogsRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateDetailRespDTO;
import com.weifu.srm.supplier.response.QuestionnaireTemplateRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/7/15 15:52
 * @Description
 * @Version 1.0
 */
@Api(tags = "调查表模板接口")
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class QuestionnaireTemplateBFFController {
    private final QuestionnaireTemplateApi questionnaireTemplateApi;
    /**
     * 模板分页查询
     * @param questionnaireTemplateReqDTO questionnaireTemplateReqDTO
     * @return QuestionnaireTemplateRespDTO
     */
    @ApiOperation("模板分页查询")
    @PostMapping("/supplier/questionnaire/template/list")
    public ApiResponse<PageResponse<QuestionnaireTemplateRespDTO>> queryList(@RequestBody QuestionnaireTemplateReqDTO questionnaireTemplateReqDTO) {
        return questionnaireTemplateApi.queryList(questionnaireTemplateReqDTO);
    }

    /**
     * 新建时获取模板明细字段
     * @return 模板明细字段
     */
    @ApiOperation("新建时获取模板明细字段")
    @GetMapping("/supplier/questionnaire/template/fields/default")
    public ApiResponse<QuestionnaireTemplateDetailRespDTO> templateSampleFields() {
        return questionnaireTemplateApi.templateSampleFields();
    }

    /**
     * 根据模板编码查询模板明细字段
     * @param reqDTO reqDTO
     * @return 模板明细字段
     */
    @ApiOperation("根据模板编码查询模板明细字段")
    @PostMapping("/supplier/questionnaire/template/fields/templateCode")
    public ApiResponse<QuestionnaireTemplateDetailRespDTO> queryTemplateFieldsByCode(@RequestBody QuestionnaireTemplateFieldReqDTO reqDTO) {
        return questionnaireTemplateApi.queryTemplateFieldsByCode(reqDTO);
    }

    /**
     * 保存调查表模板
     * @param  req
     * @return 模板编码
     */
    @OperationLog(behaviorCode = BehaviorConstants.SAVE_SURVEY_TEMPLATE, businessNo = "#result.data")
    @ApiOperation("保存调查表模板")
    @PostMapping("/supplier/questionnaire/template/save")
    public ApiResponse<String> saveTemplate(@RequestBody QuestionnaireTemplateSaveReqDTO req) {
        Long userId = SecurityContextHolder.getUserId();
        String realName = SecurityContextHolder.getRealName();
        req.setOperationBy(userId);
        req.setOperationByName(realName);
        return questionnaireTemplateApi.saveTemplate(req);
    }

    /**
     * 更新轨迹分页查询
     * @param reqDTO reqDTO
     * @return 轨迹分页查询
     */
    @ApiOperation("更新轨迹分页查询")
    @PostMapping("/supplier/questionnaire/template/changeLogs/templateCode")
    public ApiResponse<PageResponse<ChangeLogsRespDTO>> queryChangeLogsByTemplateCode(@RequestBody ChangeLogsReqDTO reqDTO) {
        return questionnaireTemplateApi.queryChangeLogsByTemplateCode(reqDTO);
    }

    @OperationLog(behaviorCode = BehaviorConstants.DELETE_SURVEY_TEMPLATE, businessNo = "#req.templateCode")
    @ApiOperation("删除调查表模板")
    @PostMapping("/supplier/questionnaire/template/delete")
    public ApiResponse<Void> deleteTemplate(@RequestBody QuestionnaireTemplateFieldDeleteReqDTO req) {
        return questionnaireTemplateApi.deleteTemplate(req);
    }

    @OperationLog(behaviorCodeIsSpel = true, behaviorCode = "#req.isEnable==1?'ENABLE_SURVEY_TEMPLATE':'DISABLE_SURVEY_TEMPLATE'", businessNo = "#req.templateCode")
    @ApiOperation("启用或禁用调查表模板")
    @PostMapping("/supplier/questionnaire/template/toEnable")
    public ApiResponse<String> enableTemplate(@RequestBody QuestionnaireTemplateFieldEnableReqDTO req) {
        return questionnaireTemplateApi.enableTemplate(req);
    }

    /**
     * 标记模板默认
     * @param req req
     * @return 标记模板默认
     */
    @OperationLog(behaviorCode = BehaviorConstants.MARK_DEFAULT_SURVEY_TEMPLATE, businessNo = "#req.templateCode")
    @ApiOperation("标记默认调查表模板")
    @PostMapping("/supplier/questionnaire/template/toDefault")
    public ApiResponse<String> beDefaultTemplate(@RequestBody QuestionnaireTemplateFieldDefaultReqDTO req) {
        return questionnaireTemplateApi.beDefaultTemplate(req);
    }

}
