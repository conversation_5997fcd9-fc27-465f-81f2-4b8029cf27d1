package com.weifu.srm.common.controller;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.api.MaterialApi;
import com.weifu.srm.masterdata.api.PurchaseGroupApi;
import com.weifu.srm.masterdata.request.MaterialQueryDTO;
import com.weifu.srm.masterdata.response.MaterialFactoryResultDTO;
import com.weifu.srm.masterdata.response.MaterialResultDTO;
import com.weifu.srm.requirement.api.RequirementApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/25 14:51
 * @Description
 * @Version 1.0
 */
@Api(tags = "物料Controller")
@Slf4j
@RestController
@RequiredArgsConstructor
public class MaterialController {
    private final MaterialApi materialApi;
    private final PurchaseGroupApi purchaseGroupApi;
    private final CategoryApi categoryApi;
    private final RequirementApi requirementApi;

    @ApiOperation("物料列表接口")
    @PostMapping(value = "/master/material/list")
        ApiResponse<PageResponse<MaterialResultDTO>> getMaterialList(@Valid @RequestBody MaterialQueryDTO materialQueryDTO){
        //返回调整中标记
        return materialApi.list(materialQueryDTO);
    }

    @ApiOperation("根据工厂查询物料")
    @GetMapping("/master/material/queryByFactory")
    ApiResponse<List<MaterialFactoryResultDTO>> queryByFactory(@RequestParam(value = "factory" ,required = false) String factory,
                                                               @RequestParam(value = "materialCode",required = false) String materialCode,
                                                               @RequestParam(value = "categoryId",required = false) Long categoryId,
                                                               @RequestParam(value = "categoryCode",required = false) String categoryCode,
                                                               @RequestParam(value = "limit",required = false) Integer limit) {
        return materialApi.queryByFactory(factory, materialCode, categoryId, categoryCode, limit);
    }

}
