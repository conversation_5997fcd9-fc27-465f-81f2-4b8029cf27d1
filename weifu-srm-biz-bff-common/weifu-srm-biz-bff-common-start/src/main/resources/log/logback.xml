<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- 应用名称 -->
	<property name="APP_NAME" value="weifu-srm-biz-bff-common" />
	<!-- 日志输出目录 -->
	<property name="LOG_PATH" value="logs" />
	<!-- 日志输出格式 -->
	<property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{20} - [%tid] - [%method,%line] - %msg%n"/>
	<!-- 日志文件字符集 -->
	<property name="LOG_CHARSET" value="UTF-8" />
	<property name="FILE_LOG_LEVEL" value="${LOG_LEVEL:-INFO}" />

	<!--输出到控制台-->
	<appender name="CONSOLE_LOG" class="ch.qos.logback.core.ConsoleAppender">
		<!--日志输出格式 -->
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
				<pattern>${LOG_PATTERN}</pattern>
			</layout>
			<charset>${LOG_CHARSET}</charset> <!-- 此处设置字符集 -->
		</encoder>

		<!-- 级别过滤 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>${FILE_LOG_LEVEL}</level>
		</filter>
	</appender>

	<!--输出到文件-->
	<appender name="FILE_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 -->
		<file>${LOG_PATH}/${APP_NAME}.log</file>
		<!--日志输出格式 -->
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
				<pattern>${LOG_PATTERN}</pattern>
			</layout>
			<charset>${LOG_CHARSET}</charset> <!-- 此处设置字符集 -->
		</encoder>

		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
			<!-- 除按日志记录之外，还配置了日志文件不能超过100M，若超过100M，日志文件会以索引0开始， 命名日志文件，例如xxx-2024-08-05-0.log -->
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!-- 日志保存周期 天数-->
			<maxHistory>30</maxHistory>
		</rollingPolicy>

		<!-- 级别过滤 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>${FILE_LOG_LEVEL}</level>
		</filter>
	</appender>

	<appender name="skywalking_log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
		<encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
			<layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
				<pattern>${LOG_PATTERN}</pattern>
			</layout>
		</encoder>
	</appender>

	<root level="INFO">
		<appender-ref ref="CONSOLE_LOG"/>
		<appender-ref ref="FILE_LOG"/>
		<appender-ref ref="skywalking_log"/>
	</root>

</configuration>