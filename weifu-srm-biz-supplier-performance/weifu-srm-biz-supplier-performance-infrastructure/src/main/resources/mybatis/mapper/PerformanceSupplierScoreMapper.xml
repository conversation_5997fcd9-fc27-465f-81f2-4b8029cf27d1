<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceSupplierScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO">
        <id column="id" property="id"/>
        <result column="score_no" property="scoreNo"/>
        <result column="first_level" property="firstLevel"/>
        <result column="quota_id" property="quotaId"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="score_type" property="scoreType"/>
        <result column="score" property="score"/>
        <result column="happen_time" property="happenTime"/>
        <result column="event_desc" property="eventDesc"/>
        <result column="is_bring_into" property="isBringInto"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_supplier_score`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`score_no`
        ,`first_level`
        ,`quota_id`
        ,`supplier_code`
        ,`supplier_name`
        ,`supplier_name_en`
        ,`score_type`
        ,`score`
        ,`happen_time`
        ,`event_desc`
        ,`is_bring_into`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>


    <sql id="whereSql">
        <where>
            t1.is_delete = 0
            <if test="dto.firstLevel != null and dto.firstLevel != ''">
                AND t1.`first_level` = #{dto.firstLevel}
            </if>
            <if test="dto.secondLevelLike != null and dto.secondLevelLike != ''">
                AND t2.`second_level` like concat('%',#{dto.secondLevelLike},'%')
            </if>
            <if test="dto.secondLevel != null and dto.secondLevel != ''">
                AND t2.`second_level` = #{dto.secondLevel}
            </if>
            <if test="dto.quotaId != null">
                AND t1.`quota_id` = #{dto.quotaId}
            </if>
            <if test="dto.supplierCode != null and dto.supplierCode != ''">
                AND t1.`supplier_code` like concat('%',#{dto.supplierCode},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                AND t1.`supplier_name` like concat('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.supplierNameEn != null and dto.supplierNameEn != ''">
                AND t1.`supplier_name_en` like concat('%',#{dto.supplierNameEn},'%')
            </if>
            <if test="dto.scoreType != null and dto.scoreType != ''">
                AND t2.`score_type` = #{dto.scoreType}
            </if>
            <if test="dto.happenTimeStart != null">
                AND t1.`happen_time`&gt;= #{dto.happenTimeStart}
            </if>
            <if test="dto.happenTimeEnd != null">
                AND t1.`happen_time`&lt;= #{dto.happenTimeEnd}
            </if>
            <if test="dto.createTimeStart != null">
                AND t1.create_time &gt;= #{dto.createTimeStart}
            </if>
            <if test="dto.createTimeEnd != null">
                AND t1.create_time &lt;= #{dto.createTimeEnd}
            </if>
            <if test="dto.createBy != null">
                AND t1.`create_by` = #{dto.createBy}
            </if>
            <if test="dto.createName != null and dto.createName != ''">
                AND t1.`create_name` like concat('%',#{dto.createName},'%')
            </if>
            <if test="dto.firstLevelList != null and dto.firstLevelList.size() > 0">
                and t1.first_level in
                <foreach collection="inquiryNos" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="listPageByQuery"
            resultType="com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO">
        SELECT
        t1.`id`,
        t1.`score_no`,
        t1.`first_level`,
        t1.`quota_id`,
        t1.`supplier_code`,
        t1.`supplier_name`,
        t1.`supplier_name_en`,
        t1.`happen_time`,
        t1.`event_desc`,
        t1.`is_bring_into`,
        t1.`create_by`,
        t1.`create_time`,
        t1.`create_name`,
        t1.`update_by`,
        t1.`update_time`,
        t1.`update_name`,
        t2.second_level,
        t2.`score_type`,
        t2.`score`
        FROM
        performance_supplier_score t1
        LEFT JOIN performance_quota_score t2 ON t1.quota_id = t2.id
        AND t2.is_delete = 0
        <include refid="whereSql"/>
        ORDER BY `id` DESC
    </select>


</mapper>
