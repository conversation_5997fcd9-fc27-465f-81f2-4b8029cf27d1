<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderCostReductionSupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionSupplierPO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_name_en" property="categoryNameEn"/>
        <result column="is_upload_material" property="isUploadMaterial"/>
        <result column="is_upload_reduction_rate" property="isUploadReductionRate"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_order_cost_reduction_supplier`</sql>
    <select id="countUploadInconsistent" resultType="java.lang.Integer">
        SELECT
        count( id ) AS result
        FROM
        performance_order_cost_reduction_supplier t
        WHERE
        is_delete = 0
        AND order_no = #{orderNo}
        and is_upload_material != is_upload_reduction_rate
    </select>


    <update id="initData">
        INSERT INTO `performance_order_cost_reduction_supplier` (
        `order_no`,
        `supplier_code`,
        `supplier_name`,
        `supplier_name_en`,
        `supplier_short_name`,
        `category_code`,
        `category_name`,
        `category_name_en`,
        `is_upload_material`,
        `is_upload_reduction_rate`,
        `create_by`,
        `create_time`,
        `create_name`,
        `update_by`,
        `update_time`,
        `update_name`
        ) SELECT
        o.order_no,
        s.supplier_code,
        s.`supplier_name`,
        s.`supplier_name_en`,
        s.`supplier_short_name`,
        s.examine_category_code,
        s.examine_category_name,
        s.examine_category_name_en,
        0 AS is_upload_material,
        0 AS is_upload_reduction_rate,
        s.`create_by`,
        s.`create_time`,
        s.`create_name`,
        s.`update_by`,
        s.`update_time`,
        s.`update_name`
        FROM
        performance_examine_supplier s
        INNER JOIN performance_order o ON s.performance_no = o.performance_no
        AND s.examine_category_code = o.two_category_code
        WHERE
        s.is_delete = 0
        AND o.is_delete = 0
        AND o.performance_no = #{performanceNo}
        AND o.performance_type = "YEAR"
        AND o.order_type = "BUSINESS_PROJECT_DEVELOPMENT_INDICATOR"
        AND NOT EXISTS ( SELECT 1 FROM performance_order_cost_reduction_supplier rs WHERE rs.order_no = o.order_no )
    </update>



</mapper>
