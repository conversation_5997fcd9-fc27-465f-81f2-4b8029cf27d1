<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.biz.BizMaterialInspectionDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.biz.BizMaterialInspectionDataPO">
        <id column="id" property="id"/>
        <result column="external_id" property="externalId"/>
        <result column="data_source" property="dataSource"/>
        <result column="division_id" property="divisionId"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="sap_material_code" property="sapMaterialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="sap_supplier_code" property="sapSupplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="arrive_num" property="arriveNum"/>
        <result column="test_results" property="testResults"/>
        <result column="solve_mode" property="solveMode"/>
        <result column="unqualified_description" property="unqualifiedDescription"/>
        <result column="puTest_results" property="puTestResults"/>
        <result column="pu_test_results_modify_reason" property="puTestResultsModifyReason"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <select id="selectListDay" resultMap="BaseResultMap">
        SELECT
            bmid.*
        FROM
            biz_material_inspection_data bmid
        WHERE
            bmid.is_delete = 0
        and NOT EXISTS (
            SELECT 1
            FROM
                biz_material_inspection_psw_data bmipd
            WHERE
                bmid.external_id = bmipd.external_id);
    </select>


</mapper>
