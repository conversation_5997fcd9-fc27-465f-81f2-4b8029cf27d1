<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="performance_no" property="performanceNo"/>
        <result column="examine_no" property="examineNo"/>
        <result column="performance_type" property="performanceType"/>
        <result column="order_type" property="orderType"/>
        <result column="examine_cycle_year" property="examineCycleYear"/>
        <result column="examine_cycle_quarter" property="examineCycleQuarter"/>
        <result column="status" property="status"/>
        <result column="start_date" property="startDate"/>
        <result column="deadline_date" property="deadlineDate"/>
        <result column="submit_date" property="submitDate"/>
        <result column="is_overdue_submit" property="isOverdueSubmit"/>
        <result column="division_id" property="divisionId"/>
        <result column="principal_id" property="principalId"/>
        <result column="principal_name" property="principalName"/>
        <result column="verification_id" property="verificationId"/>
        <result column="verification_name" property="verificationName"/>
        <result column="verification_passed_date" property="verificationPassedDate"/>
        <result column="verification_result" property="verificationResult"/>
        <result column="verification_opinion" property="verificationOpinion"/>
        <result column="two_category_code" property="twoCategoryCode"/>
        <result column="two_category_name" property="twoCategoryName"/>
        <result column="two_category_name_en" property="twoCategoryNameEn"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_order`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`order_no`
        ,`performance_no`
        ,`examine_no`
        ,`performance_type`
        ,`order_type`
        ,`examine_cycle_year`
        ,`examine_cycle_quarter`
        ,`status`
        ,`deadline_date`
        ,`division_id`
        ,`is_overdue_submit`
        ,`submit_date`
        ,`principal_id`
        ,`principal_name`
        ,`verification_id`
        ,`verification_name`
        ,`verification_passed_date`
        ,`verification_result`
        ,`verification_opinion`
        ,`two_category_code`
        ,`two_category_name`
        ,`two_category_name_en`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>

    <select id="statisticsByPerformanceNo" resultType="com.weifu.srm.supplier.performance.response.order.PerformanceOrderStatisticsRespDTO">
        SELECT
        COUNT( id ) AS totalNum,
        COUNT(
        IF
        ( `status` = "SUBMITTED" || `status` = "VERIFIED", id, NULL )) submitNum,
        COUNT(
        IF
        ( `status` = "VERIFIED", id, NULL )) verifiedNum
        FROM
        performance_order t
        WHERE
        t.is_delete = 0
        AND performance_no = #{performanceNo}
    </select>
    
    <select id="statisticsTodoOrder" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        from
        performance_order o
        where
        (o.principal_id = #{operationBy}
        and o.status in("IN_PROGRESS", "VERIFICATION_FAILED") )
        or(
        o.verification_id = #{operationBy}
        and o.status in("SUBMITTED"))
    </select>


</mapper>
