<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderOtsMaterialDeliveryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsMaterialDeliveryPO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="material_code" property="materialCode" />
        <result column="material_description" property="materialDescription" />
        <result column="division_id" property="divisionId" />
        <result column="one_category_code" property="oneCategoryCode" />
        <result column="two_category_code" property="twoCategoryCode" />
        <result column="three_category_code" property="threeCategoryCode" />
        <result column="year_month_date" property="yearMonthDate" />
        <result column="batch_total_quantity" property="batchTotalQuantity" />
        <result column="batch_timely_quantity" property="batchTimelyQuantity" />
        <result column="project_name" property="projectName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_order_ots_material_delivery`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`order_no`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`material_code`
    ,`material_description`
    ,`division_id`
    ,`one_category_code`
    ,`two_category_code`
    ,`three_category_code`
    ,`year_month_date`
    ,`batch_total_quantity`
    ,`batch_timely_quantity`
    ,`project_name`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND `order_no` = #{orderNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND `material_code` = #{materialCode}
        </if>
        <if test="materialDescription != null and materialDescription != ''">
            AND `material_description` = #{materialDescription}
        </if>
        <if test="divisionId != null and divisionId != ''">
            AND `division_id` = #{divisionId}
        </if>
        <if test="oneCategoryCode != null and oneCategoryCode != ''">
            AND `one_category_code` = #{oneCategoryCode}
        </if>
        <if test="twoCategoryCode != null and twoCategoryCode != ''">
            AND `two_category_code` = #{twoCategoryCode}
        </if>
        <if test="threeCategoryCode != null and threeCategoryCode != ''">
            AND `three_category_code` = #{threeCategoryCode}
        </if>
        <if test="yearMonthDate != null">
            AND `year_month_date` = #{yearMonthDate}
        </if>
        <if test="batchTotalQuantity != null">
            AND `batch_total_quantity` = #{batchTotalQuantity}
        </if>
        <if test="batchTimelyQuantity != null">
            AND `batch_timely_quantity` = #{batchTimelyQuantity}
        </if>
        <if test="projectName != null and projectName != ''">
            AND `project_name` = #{projectName}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
