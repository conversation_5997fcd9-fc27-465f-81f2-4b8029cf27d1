<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceQuotaScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceQuotaScorePO">
        <id column="id" property="id" />
        <result column="score_no" property="scoreNo" />
        <result column="first_level" property="firstLevel" />
        <result column="second_level" property="secondLevel" />
        <result column="definition" property="definition" />
        <result column="score_type" property="scoreType" />
        <result column="score" property="score" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_quota_score`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`score_no`
    ,`first_level`
    ,`second_level`
    ,`definition`
    ,`score_type`
    ,`score`
    ,`status`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="scoreNo != null and scoreNo != ''">
            AND `score_no` = #{scoreNo}
        </if>
        <if test="firstLevel != null and firstLevel != ''">
            AND `first_level` = #{firstLevel}
        </if>
        <if test="secondLevel != null and secondLevel != ''">
            AND `second_level` = #{secondLevel}
        </if>
        <if test="definition != null and definition != ''">
            AND `definition` = #{definition}
        </if>
        <if test="scoreType != null and scoreType != ''">
            AND `score_type` = #{scoreType}
        </if>
        <if test="score != null">
            AND `score` = #{score}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
