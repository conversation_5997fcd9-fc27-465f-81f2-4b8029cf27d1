<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePO">
        <id column="id" property="id"/>
        <result column="performance_no" property="performanceNo"/>
        <result column="themes" property="themes"/>
        <result column="performance_type" property="performanceType"/>
        <result column="examine_cycle_year" property="examineCycleYear"/>
        <result column="examine_cycle_quarter" property="examineCycleQuarter"/>
        <result column="status" property="status"/>
        <result column="evaluate_no" property="evaluateNo"/>
        <result column="publish_time" property="publishTime"/>
        <result column="performance_start_date" property="performanceStartDate"/>
        <result column="performance_end_date" property="performanceEndDate"/>
        <result column="calculate_amount" property="calculateAmount"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_examine`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`performance_no`
        ,`themes`
        ,`performance_type`
        ,`examine_cycle_year`
        ,`examine_cycle_quarter`
        ,`status`
        ,`evaluate_no`
        ,`publish_time`
        ,`performance_start_date`
        ,`performance_end_date`
        ,`calculate_amount`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>

    <select id="calculateAmount" resultType="com.weifu.srm.supplier.performance.response.examine.CalculateAmountDTO">
        SELECT
        t.vendor_num as supplierCode,
        t.material_code as materialCode,
        SUM( IFNULL(t.cny_doc_curr_amt_1d ,0)) amount
        FROM
        biz_purchase_invoice_data t
        WHERE
        t.posting_date &gt;=  #{startDate}
        AND t.posting_date &lt;= #{endDate}
        AND t.vendor_num in
        <foreach collection="supplierCodes" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        t.vendor_num,
        t.material_code
    </select>


</mapper>
