<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceEvaluateSchemeSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemeSettingPO">
        <id column="id" property="id" />
        <result column="evaluate_no" property="evaluateNo" />
        <result column="one_level_category_code" property="oneLevelCategoryCode" />
        <result column="one_level_category_name" property="oneLevelCategoryName" />
        <result column="one_level_category_name_en" property="oneLevelCategoryNameEn" />
        <result column="two_level_category_code" property="twoLevelCategoryCode" />
        <result column="two_level_category_name" property="twoLevelCategoryName" />
        <result column="two_level_category_name_en" property="twoLevelCategoryNameEn" />
        <result column="proportion_no" property="proportionNo" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_evaluate_scheme_setting`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`evaluate_no`
    ,`one_level_category_code`
    ,`one_level_category_name`
    ,`one_level_category_name_en`
    ,`two_level_category_code`
    ,`two_level_category_name`
    ,`two_level_category_name_en`
    ,`proportion_no`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>




</mapper>
