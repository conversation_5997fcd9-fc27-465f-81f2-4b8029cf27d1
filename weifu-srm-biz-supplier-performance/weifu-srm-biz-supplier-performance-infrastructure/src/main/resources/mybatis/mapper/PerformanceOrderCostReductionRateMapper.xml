<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderCostReductionRateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionRatePO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="cost_reduction_rate" property="costReductionRate" />
        <result column="rebate_amount" property="rebateAmount" />
        <result column="cost_reduction_amount" property="costReductionAmount" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_order_cost_reduction_rate`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`order_no`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`cost_reduction_rate`
    ,`rebate_amount`
    ,`cost_reduction_amount`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND `order_no` = #{orderNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="costReductionRate != null">
            AND `cost_reduction_rate` = #{costReductionRate}
        </if>
        <if test="rebateAmount != null">
            AND `rebate_amount` = #{rebateAmount}
        </if>
        <if test="costReductionAmount != null">
            AND `cost_reduction_amount` = #{costReductionAmount}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
