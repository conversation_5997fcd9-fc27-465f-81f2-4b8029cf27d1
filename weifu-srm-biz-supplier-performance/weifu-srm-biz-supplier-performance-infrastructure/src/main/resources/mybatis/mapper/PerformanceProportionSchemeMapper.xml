<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceProportionSchemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemePO">
        <id column="id" property="id"/>
        <result column="proportion_no" property="proportionNo"/>
        <result column="themes" property="themes"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="enable_time" property="enableTime"/>
        <result column="disable_time" property="disableTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_proportion_scheme`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`proportion_no`
        ,`themes`
        ,`remark`
        ,`status`
        ,`enable_time`
        ,`disable_time`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="proportionNo != null and proportionNo != ''">
            AND `proportion_no` = #{proportionNo}
        </if>
        <if test="themes != null and themes != ''">
            AND `themes` = #{themes}
        </if>
        <if test="remark != null and remark != ''">
            AND `remark` = #{remark}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="enableTime != null">
            AND `enable_time` = #{enableTime}
        </if>
        <if test="disableTime != null">
            AND `disable_time` = #{disableTime}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>

    <select id="listQuotaByProportionNo"
            resultType="com.weifu.srm.supplier.performance.response.scheme.PerformanceProportionSchemeQuotaRespDTO">
        SELECT
        t1.id first_id,
        t2.id,
        t2.proportion_no,
        t1.first_level,
        t1.first_level_proportion,
        t1.first_level_bonus_ceiling,
        t1.first_level_deduct_ceiling,
        t2.second_level,
        t2.second_level_proportion
        FROM
        performance_proportion_scheme_one t1
        LEFT JOIN performance_proportion_scheme_two t2 ON t1.proportion_no = t2.proportion_no
        AND t1.first_level = t2.first_level
        WHERE
        t1.is_delete = 0
        AND t2.is_delete = 0
        AND t1.proportion_no = #{proportionNo}
        order by t1.id,t2.id
    </select>
    
</mapper>
