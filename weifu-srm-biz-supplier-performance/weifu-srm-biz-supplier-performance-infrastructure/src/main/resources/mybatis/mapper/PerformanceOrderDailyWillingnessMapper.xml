<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderDailyWillingnessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDailyWillingnessPO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="supplier_short_name" property="supplierShortName" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="category_name_en" property="categoryNameEn" />
        <result column="daily_willingness" property="dailyWillingness" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_order_daily_willingness`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`order_no`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`supplier_short_name`
    ,`category_code`
    ,`category_name`
    ,`category_name_en`
    ,`daily_willingness`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND `order_no` = #{orderNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="supplierShortName != null and supplierShortName != ''">
            AND `supplier_short_name` = #{supplierShortName}
        </if>
        <if test="categoryCode != null and categoryCode != ''">
            AND `category_code` = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND `category_name` = #{categoryName}
        </if>
        <if test="categoryNameEn != null and categoryNameEn != ''">
            AND `category_name_en` = #{categoryNameEn}
        </if>
        <if test="dailyWillingness != null and dailyWillingness != ''">
            AND `daily_willingness` = #{dailyWillingness}
        </if>
    </sql>
 
    <update id="initData">
        INSERT INTO `performance_order_daily_willingness` (
        `order_no`,
        `supplier_code`,
        `supplier_name`,
        `supplier_name_en`,
        `supplier_short_name`,
        `category_code`,
        `category_name`,
        `category_name_en`,
        `create_by`,
        `create_time`,
        `create_name`,
        `update_by`,
        `update_time`,
        `update_name`
        ) SELECT
        o.order_no,
        t1.supplier_code,
        t1.supplier_name,
        t1.supplier_name_en,
        t1.supplier_short_name,
        o.two_category_code,
        o.two_category_name,
        o.two_category_name_en,
        o.`create_by`,
        o.`create_time`,
        o.`create_name`,
        o.`update_by`,
        o.`update_time`,
        o.`update_name`
        FROM
        performance_examine_supplier t1
        LEFT JOIN performance_order o ON t1.performance_no = o.performance_no
        AND t1.examine_category_code = o.two_category_code
        WHERE
        t1.is_delete = 0
        AND o.is_delete = 0
        AND o.order_type = "BUSINESS_PROJECT_DEVELOPMENT_INDICATOR"
        AND o.performance_no = #{performanceNo}
        AND NOT EXISTS (
        SELECT
        1
        FROM
        performance_order_daily_willingness w
        WHERE
        w.order_no = o.order_no
        )
    </update>





</mapper>
