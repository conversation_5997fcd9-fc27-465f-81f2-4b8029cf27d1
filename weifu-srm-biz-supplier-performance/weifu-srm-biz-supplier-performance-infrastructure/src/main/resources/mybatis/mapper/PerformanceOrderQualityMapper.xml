<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderQualityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderQualityPO">
        <id column="id" property="id"/>
        <result column="reference_id" property="referenceId"/>
        <result column="order_no" property="orderNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="one_category_code" property="oneCategoryCode"/>
        <result column="one_category_name" property="oneCategoryName"/>
        <result column="one_category_name_en" property="oneCategoryNameEn"/>
        <result column="two_category_code" property="twoCategoryCode"/>
        <result column="two_category_name" property="twoCategoryName"/>
        <result column="two_category_name_en" property="twoCategoryNameEn"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_name_en" property="categoryNameEn"/>
        <result column="division_id" property="divisionId"/>
        <result column="division_name" property="divisionName"/>
        <result column="year_month_str" property="yearMonthStr"/>
        <result column="incoming_quantity" property="incomingQuantity"/>
        <result column="complaint_quantity" property="complaintQuantity"/>
        <result column="problem_location" property="problemLocation"/>
        <result column="problem_description" property="problemDescription"/>
        <result column="feedback_date" property="feedbackDate"/>
        <result column="quantity" property="quantity"/>
        <result column="close_date" property="closeDate"/>
        <result column="cause_analysis" property="causeAnalysis"/>
        <result column="improvement_measures" property="improvementMeasures"/>
        <result column="claimant_progress" property="claimantProgress"/>
        <result column="processing_progress" property="processingProgress"/>
        <result column="material_grade" property="materialGrade"/>
        <result column="heat_number" property="heatNumber"/>
        <result column="procurement_standards" property="procurementStandards"/>
        <result column="problem_nature" property="problemNature"/>
        <result column="is_claimant" property="isClaimant"/>
        <result column="is_need_examine" property="isNeedExamine"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_material_handler_problem`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`reference_id`
        ,`order_no`
        ,`supplier_code`
        ,`supplier_name`
        ,`supplier_name_en`
        ,`material_code`
        ,`material_name`
        ,`category_code`
        ,`category_name`
        ,`category_name_en`
        ,`division_id`
        ,`division_name`
        ,`year_month_str`
        ,`incoming_quantity`
        ,`complaint_quantity`
        ,`problem_location`
        ,`problem_description`
        ,`feedback_date`
        ,`quantity`
        ,`close_date`
        ,`cause_analysis`
        ,`improvement_measures`
        ,`claimant_progress`
        ,`processing_progress`
        ,`material_grade`
        ,`heat_number`
        ,`procurement_standards`
        ,`problem_nature`
        ,`is_need_examine`
        ,`is_claimant`
        ,`remarks`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>

    <update id="mergeIntoData">
        INSERT INTO `performance_order_quality` (
        `order_no`,
        `reference_id`,
        `supplier_code`,
        `supplier_name`,
        `supplier_name_en`,
        `material_code`,
        `material_name`,
        `one_category_code`,
        `one_category_name`,
        `one_category_name_en`,
        `two_category_code`,
        `two_category_name`,
        `two_category_name_en`,
        `category_code`,
        `category_name`,
        `category_name_en`,
        `division_id`,
        `division_name`,
        `year_month_str`,
        `incoming_quantity`,
        `complaint_quantity`,
        `problem_location`,
        `problem_description`,
        `feedback_date`,
        `quantity`,
        `close_date`,
        `cause_analysis`,
        `improvement_measures`,
        `claimant_progress`,
        `processing_progress`,
        `material_grade`,
        `heat_number`,
        `procurement_standards`,
        `problem_nature`,
        `is_need_examine`,
        `is_claimant`,
        `remarks`,
        `create_by`,
        `create_time`,
        `create_name`,
        `update_by`,
        `update_time`,
        `update_name`
        ) SELECT
        o.order_no AS `order_no`,
        p.id,
        p.`supplier_code`,
        p.`supplier_name`,
        p.`supplier_name_en`,
        p.`material_code`,
        p.`material_name`,
        p.`one_category_code`,
        p.`one_category_name`,
        p.`one_category_name_en`,
        p.`two_category_code`,
        p.`two_category_name`,
        p.`two_category_name_en`,
        p.`category_code`,
        p.`category_name`,
        p.`category_name_en`,
        p.`division_id`,
        p.`division_name`,
        p.`year_month_str`,
        p.`incoming_quantity`,
        p.`complaint_quantity`,
        p.`problem_location`,
        p.`problem_description`,
        p.`feedback_date`,
        p.`quantity`,
        p.`close_date`,
        p.`cause_analysis`,
        p.`improvement_measures`,
        p.`claimant_progress`,
        p.`processing_progress`,
        p.`material_grade`,
        p.`heat_number`,
        p.`procurement_standards`,
        p.`problem_nature`,
        p.`is_need_examine`,
        p.`is_claimant`,
        p.`remarks`,
        p.`create_by`,
        p.`create_time`,
        p.`create_name`,
        p.`update_by`,
        p.`update_time`,
        p.`update_name`
        FROM
        performance_material_handler_problem p
        INNER JOIN performance_examine_supplier s ON s.supplier_code = p.supplier_code
        INNER JOIN performance_order o ON o.performance_no = s.performance_no and o.two_category_code =
        s.examine_category_code
        WHERE
        p.is_delete = 0
        AND o.is_delete = 0
        AND s.is_delete = 0
        AND p.is_need_examine = 1
        AND o.performance_no = #{performanceNo}
        and p.feedback_date &gt;= #{startDate}
        and p.feedback_date &lt;= #{endDate}
        AND o.order_type = "QUALITY_INDICATOR_DATA"
        ON DUPLICATE KEY UPDATE `supplier_code` = p.`supplier_code`,
        `supplier_name` = p.`supplier_name`,
        `supplier_name_en` = p.`supplier_name_en`,
        `material_code` = p.`material_code`,
        `material_name` = p.`material_name`,
        `one_category_code` = p.`one_category_code`,
        `one_category_name` = p.`one_category_name`,
        `one_category_name_en` = p.`one_category_name_en`,
        `two_category_code` = p.`two_category_code`,
        `two_category_name` = p.`two_category_name`,
        `two_category_name_en` = p.`two_category_name_en`,
        `category_code` = p.`category_code`,
        `category_name` = p.`category_name`,
        `category_name_en` = p.`category_name_en`,
        `division_id` = p.`division_id`,
        `division_name` = p.`division_name`,
        `year_month_str` = p.`year_month_str`,
        `incoming_quantity` = p.`incoming_quantity`,
        `complaint_quantity` = p.`complaint_quantity`,
        `problem_location` = p.`problem_location`,
        `problem_description` = p.`problem_description`,
        `feedback_date` = p.`feedback_date`,
        `quantity` = p.`quantity`,
        `close_date` = p.`close_date`,
        `cause_analysis` = p.`cause_analysis`,
        `improvement_measures` = p.`improvement_measures`,
        `claimant_progress` = p.`claimant_progress`,
        `processing_progress` = p.`processing_progress`,
        `material_grade` = p.`material_grade`,
        `heat_number` = p.`heat_number`,
        `procurement_standards` = p.`procurement_standards`,
        `problem_nature` = p.`problem_nature`,
        `is_need_examine` = p.`is_need_examine`,
        `is_claimant` = p.`is_claimant`,
        `remarks` = p.`remarks`,
        `update_by` = p.`update_by`,
        `update_time` = p.`update_time`,
        `update_name` = p.`update_name`
    </update>
</mapper>
