<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.biz.BizDefectivePartsDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.biz.BizDefectivePartsDataPO">
        <id column="id" property="id"/>
        <result column="external_id" property="externalId"/>
        <result column="data_source" property="dataSource"/>
        <result column="division_code" property="divisionCode"/>
        <result column="complaint_date" property="complaintDate"/>
        <result column="material_code" property="materialCode"/>
        <result column="part_name" property="partName"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="complaint_failure_situation_resume" property="complaintFailureSituationResume"/>
        <result column="complaint_problem_description" property="complaintProblemDescription"/>
        <result column="part_complaint_qty" property="partComplaintQty"/>
        <result column="complaint_discovery_site" property="complaintDiscoverySite"/>
        <result column="team_and_group" property="teamAndGroup"/>
        <result column="pu_confirms_num" property="puConfirmsNum"/>
        <result column="pu_confirms_desc" property="puConfirmsDesc"/>
        <result column="relation_no" property="relationNo"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT
            bdpd.*
        FROM
            biz_defective_parts_data bdpd
        WHERE
            bdpd.is_delete = 0
        and NOT EXISTS (
            SELECT 1
            FROM
                biz_defective_parts_psw_data bdppd
            WHERE
                bdpd.external_id = bdppd.external_id);
    </select>


</mapper>
