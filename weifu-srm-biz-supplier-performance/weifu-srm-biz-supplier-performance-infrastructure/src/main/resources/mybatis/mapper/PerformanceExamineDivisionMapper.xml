<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineDivisionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceExamineDivisionPO">
        <id column="id" property="id" />
        <result column="performance_no" property="performanceNo" />
        <result column="division_id" property="divisionId" />
        <result column="division_name" property="divisionName" />
        <result column="delivery_indicator_owner_user_id" property="deliveryIndicatorOwnerUserId" />
        <result column="delivery_indicator_checker_user_id" property="deliveryIndicatorCheckerUserId" />
        <result column="delivery_indicator_owner_user_name" property="deliveryIndicatorOwnerUserName" />
        <result column="delivery_indicator_checker_user_name" property="deliveryIndicatorCheckerUserName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_examine_division`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`performance_no`
    ,`division_id`
    ,`division_name`
    ,`delivery_indicator_owner_user_id`
    ,`delivery_indicator_checker_user_id`
    ,`delivery_indicator_owner_user_name`
    ,`delivery_indicator_checker_user_name`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="performanceNo != null and performanceNo != ''">
            AND `performance_no` = #{performanceNo}
        </if>
        <if test="divisionId != null and divisionId != ''">
            AND `division_id` = #{divisionId}
        </if>
        <if test="divisionName != null and divisionName != ''">
            AND `division_name` = #{divisionName}
        </if>
        <if test="deliveryIndicatorOwnerUserId != null">
            AND `delivery_indicator_owner_user_id` = #{deliveryIndicatorOwnerUserId}
        </if>
        <if test="deliveryIndicatorCheckerUserId != null">
            AND `delivery_indicator_checker_user_id` = #{deliveryIndicatorCheckerUserId}
        </if>
        <if test="deliveryIndicatorOwnerUserName != null and deliveryIndicatorOwnerUserName != ''">
            AND `delivery_indicator_owner_user_name` = #{deliveryIndicatorOwnerUserName}
        </if>
        <if test="deliveryIndicatorCheckerUserName != null and deliveryIndicatorCheckerUserName != ''">
            AND `delivery_indicator_checker_user_name` = #{deliveryIndicatorCheckerUserName}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
