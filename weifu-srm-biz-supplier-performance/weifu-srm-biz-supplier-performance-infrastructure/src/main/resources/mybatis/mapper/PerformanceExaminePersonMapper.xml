<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceExaminePersonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePersonPO">
        <id column="id" property="id" />
        <result column="performance_no" property="performanceNo" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="category_name_en" property="categoryNameEn" />
        <result column="sqe_id" property="sqeId" />
        <result column="sqe_name" property="sqeName" />
        <result column="sqe_master_id" property="sqeMasterId" />
        <result column="sqe_master_name" property="sqeMasterName" />
        <result column="cpe_id" property="cpeId" />
        <result column="cpe_name" property="cpeName" />
        <result column="cpe_master_id" property="cpeMasterId" />
        <result column="cpe_master_name" property="cpeMasterName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_examine_person`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`performance_no`
    ,`category_code`
    ,`category_name`
    ,`category_name_en`
    ,`sqe_id`
    ,`sqe_name`
    ,`sqe_master_id`
    ,`sqe_master_name`
    ,`cpe_id`
    ,`cpe_name`
    ,`cpe_master_id`
    ,`cpe_master_name`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="categoryCode != null and categoryCode != ''">
            AND `category_code` = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND `category_name` = #{categoryName}
        </if>
        <if test="categoryNameEn != null and categoryNameEn != ''">
            AND `category_name_en` = #{categoryNameEn}
        </if>
        <if test="sqeId != null">
            AND `sqe_id` = #{sqeId}
        </if>
        <if test="sqeName != null and sqeName != ''">
            AND `sqe_name` = #{sqeName}
        </if>
        <if test="sqeMasterId != null">
            AND `sqe_master_id` = #{sqeMasterId}
        </if>
        <if test="sqeMasterName != null and sqeMasterName != ''">
            AND `sqe_master_name` = #{sqeMasterName}
        </if>
        <if test="cpeId != null">
            AND `cpe_id` = #{cpeId}
        </if>
        <if test="cpeName != null and cpeName != ''">
            AND `cpe_name` = #{cpeName}
        </if>
        <if test="cpeMasterId != null">
            AND `cpe_master_id` = #{cpeMasterId}
        </if>
        <if test="cpeMasterName != null and cpeMasterName != ''">
            AND `cpe_master_name` = #{cpeMasterName}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
