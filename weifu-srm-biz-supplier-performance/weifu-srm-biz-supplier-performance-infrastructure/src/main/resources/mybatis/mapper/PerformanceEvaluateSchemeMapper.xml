<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceEvaluateSchemeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemePO">
        <id column="id" property="id"/>
        <result column="evaluate_no" property="evaluateNo"/>
        <result column="themes" property="themes"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="enable_time" property="enableTime"/>
        <result column="disable_time" property="disableTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_evaluate_scheme`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`evaluate_no`
        ,`themes`
        ,`remark`
        ,`status`
        ,`enable_time`
        ,`disable_time`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>

    <select id="listEvaluateProportion"
            resultType="com.weifu.srm.supplier.performance.response.scheme.PerformanceEvaluateProportionRespDTO">
        SELECT
        t1.evaluate_no,
        t1.proportion_no,
        t1.one_level_category_code,
        t1.one_level_category_name,
        t1.one_level_category_name_en,
        t1.two_level_category_code,
        t1.two_level_category_name,
        t1.two_level_category_name_en,
        t2.first_level,
        t2.first_level_proportion,
        t2.first_level_bonus_ceiling,
        t2.first_level_deduct_ceiling,
        t3.second_level,
        t3.second_level_proportion
        FROM
        performance_evaluate_scheme_setting t1
        LEFT JOIN performance_proportion_scheme_one t2 ON t1.proportion_no = t2.proportion_no
        LEFT JOIN performance_proportion_scheme_two t3 ON t3.proportion_no = t2.proportion_no  AND t2.first_level = t3.first_level
        WHERE t1.is_delete = 0
        AND t2.is_delete = 0
        AND t3.is_delete = 0
        <if test="evaluateNo != null and evaluateNo != ''">
            AND t1.evaluate_no = #{evaluateNo}
        </if>
        ORDER BY
        t1.one_level_category_code,
        t1.two_level_category_code,
        t2.id,
        t3.id
    </select>


</mapper>
