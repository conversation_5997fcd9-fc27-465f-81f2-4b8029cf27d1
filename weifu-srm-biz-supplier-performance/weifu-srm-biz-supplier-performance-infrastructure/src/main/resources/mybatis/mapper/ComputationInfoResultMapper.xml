<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationInfoResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.response.computation.ComputationHistoryScoreResDTO">
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_score" property="newScore" />
        <result column="publish_time" property="publishTime" />
    </resultMap>

    <select id="findNewYearScoreList" resultMap="BaseResultMap">
        SELECT supplier_code, supplier_score, publish_time
        FROM (
        SELECT supplier_code, supplier_score, publish_time,
        ROW_NUMBER() OVER (PARTITION BY supplier_code ORDER BY publish_time DESC) as rn
        FROM performance_computation_info_result
        ) t
        WHERE
        t.rn = 1
        and t.supplier_code in
        <foreach collection="supplierCodeList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>