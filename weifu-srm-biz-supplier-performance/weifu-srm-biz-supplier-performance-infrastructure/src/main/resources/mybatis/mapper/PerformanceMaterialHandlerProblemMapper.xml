<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceMaterialHandlerProblemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceMaterialHandlerProblemPO">
        <id column="id" property="id" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="one_category_code" property="oneCategoryCode" />
        <result column="one_category_name" property="oneCategoryName" />
        <result column="one_category_name_en" property="oneCategoryNameEn" />
        <result column="two_category_code" property="twoCategoryCode" />
        <result column="two_category_name" property="twoCategoryName" />
        <result column="two_category_name_en" property="twoCategoryNameEn" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="category_name_en" property="categoryNameEn" />
        <result column="division_id" property="divisionId" />
        <result column="division_name" property="divisionName" />
        <result column="year_month_str" property="yearMonthStr" />
        <result column="incoming_quantity" property="incomingQuantity" />
        <result column="complaint_quantity" property="complaintQuantity" />
        <result column="is_examined" property="isExamined" />
        <result column="problem_location" property="problemLocation" />
        <result column="problem_description" property="problemDescription" />
        <result column="feedback_date" property="feedbackDate" />
        <result column="quantity" property="quantity" />
        <result column="close_date" property="closeDate" />
        <result column="is_need_examine" property="isNeedExamine" />
        <result column="cause_analysis" property="causeAnalysis" />
        <result column="improvement_measures" property="improvementMeasures" />
        <result column="claimant_progress" property="claimantProgress" />
        <result column="processing_progress" property="processingProgress" />
        <result column="material_grade" property="materialGrade" />
        <result column="heat_number" property="heatNumber" />
        <result column="procurement_standards" property="procurementStandards" />
        <result column="problem_nature" property="problemNature" />
        <result column="is_claimant" property="isClaimant" />
        <result column="remarks" property="remarks" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_material_handler_problem`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`material_code`
    ,`material_name`
    ,`category_code`
    ,`category_name`
    ,`category_name_en`
    ,`division_id`
    ,`division_name`
    ,`year_month_str`
    ,`incoming_quantity`
    ,`complaint_quantity`
    ,`is_examined`
    ,`problem_location`
    ,`problem_description`
    ,`feedback_date`
    ,`quantity`
    ,`close_date`
    ,`is_need_examine`
    ,`cause_analysis`
    ,`improvement_measures`
    ,`claimant_progress`
    ,`processing_progress`
    ,`material_grade`
    ,`heat_number`
    ,`procurement_standards`
    ,`problem_nature`
    ,`is_claimant`
    ,`remarks`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND `material_code` = #{materialCode}
        </if>
        <if test="materialName != null and materialName != ''">
            AND `material_name` = #{materialName}
        </if>
        <if test="categoryCode != null and categoryCode != ''">
            AND `category_code` = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND `category_name` = #{categoryName}
        </if>
        <if test="categoryNameEn != null and categoryNameEn != ''">
            AND `category_name_en` = #{categoryNameEn}
        </if>
        <if test="divisionId != null and divisionId != ''">
            AND `division_id` = #{divisionId}
        </if>
        <if test="divisionName != null and divisionName != ''">
            AND `division_name` = #{divisionName}
        </if>
        <if test="yearMonthStr != null and yearMonthStr != ''">
            AND `year_month_str` = #{yearMonthStr}
        </if>
        <if test="incomingQuantity != null">
            AND `incoming_quantity` = #{incomingQuantity}
        </if>
        <if test="complaintQuantity != null">
            AND `complaint_quantity` = #{complaintQuantity}
        </if>
        <if test="isExamined != null">
            AND `is_examined` = #{isExamined}
        </if>
        <if test="problemLocation != null and problemLocation != ''">
            AND `problem_location` = #{problemLocation}
        </if>
        <if test="problemDescription != null and problemDescription != ''">
            AND `problem_description` = #{problemDescription}
        </if>
        <if test="feedbackDate != null">
            AND `feedback_date` = #{feedbackDate}
        </if>
        <if test="quantity != null">
            AND `quantity` = #{quantity}
        </if>
        <if test="closeDate != null">
            AND `close_date` = #{closeDate}
        </if>
        <if test="isNeedExamine != null">
            AND `is_need_examine` = #{isNeedExamine}
        </if>
        <if test="causeAnalysis != null and causeAnalysis != ''">
            AND `cause_analysis` = #{causeAnalysis}
        </if>
        <if test="improvementMeasures != null and improvementMeasures != ''">
            AND `improvement_measures` = #{improvementMeasures}
        </if>
        <if test="claimantProgress != null and claimantProgress != ''">
            AND `claimant_progress` = #{claimantProgress}
        </if>
        <if test="processingProgress != null and processingProgress != ''">
            AND `processing_progress` = #{processingProgress}
        </if>
        <if test="materialGrade != null and materialGrade != ''">
            AND `material_grade` = #{materialGrade}
        </if>
        <if test="heatNumber != null and heatNumber != ''">
            AND `heat_number` = #{heatNumber}
        </if>
        <if test="procurementStandards != null and procurementStandards != ''">
            AND `procurement_standards` = #{procurementStandards}
        </if>
        <if test="problemNature != null and problemNature != ''">
            AND `problem_nature` = #{problemNature}
        </if>
        <if test="isClaimant != null">
            AND `is_claimant` = #{isClaimant}
        </if>
        <if test="remarks != null and remarks != ''">
            AND `remarks` = #{remarks}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
