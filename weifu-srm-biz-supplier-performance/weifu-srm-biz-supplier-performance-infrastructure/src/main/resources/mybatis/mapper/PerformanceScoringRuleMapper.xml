<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceScoringRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceScoringRulePO">
        <id column="id" property="id" />
        <result column="first_level" property="firstLevel" />
        <result column="second_level" property="secondLevel" />
        <result column="formula" property="formula" />
        <result column="compute_cycle" property="computeCycle" />
        <result column="compute_result" property="computeResult" />
        <result column="score" property="score" />
        <result column="sort_by" property="sortBy" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_scoring_rule`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`first_level`
    ,`second_level`
    ,`formula`
    ,`compute_cycle`
    ,`compute_result`
    ,`score`
    ,`sort_by`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="firstLevel != null and firstLevel != ''">
            AND `first_level` = #{firstLevel}
        </if>
        <if test="secondLevel != null and secondLevel != ''">
            AND `second_level` = #{secondLevel}
        </if>
        <if test="formula != null and formula != ''">
            AND `formula` = #{formula}
        </if>
        <if test="computeCycle != null and computeCycle != ''">
            AND `compute_cycle` = #{computeCycle}
        </if>
        <if test="computeResult != null and computeResult != ''">
            AND `compute_result` = #{computeResult}
        </if>
        <if test="score != null">
            AND `score` = #{score}
        </if>
    </sql>







</mapper>
