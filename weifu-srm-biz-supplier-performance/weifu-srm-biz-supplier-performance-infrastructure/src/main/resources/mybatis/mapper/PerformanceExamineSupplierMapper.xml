<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineSupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceExamineSupplierPO">
        <id column="id" property="id"/>
        <result column="performance_no" property="performanceNo"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="supplier_short_name" property="supplierShortName"/>
        <result column="procurement_category_code" property="procurementCategoryCode"/>
        <result column="procurement_category_name" property="procurementCategoryName"/>
        <result column="procurement_category_name_en" property="procurementCategoryNameEn"/>
        <result column="max_purchase_amount" property="maxPurchaseAmount"/>
        <result column="is_have_purchase" property="isHavePurchase"/>
        <result column="is_calculated" property="isCalculated"/>
        <result column="is_setting" property="isSetting"/>
        <result column="examine_category_code" property="examineCategoryCode"/>
        <result column="examine_category_name" property="examineCategoryName"/>
        <result column="examine_category_name_en" property="examineCategoryNameEn"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`performance_examine_supplier`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`performance_no`
        ,`supplier_code`
        ,`supplier_name`
        ,`supplier_name_en`
        ,`supplier_short_name`
        ,`procurement_category_code`
        ,`procurement_category_name`
        ,`procurement_category_name_en`
        ,`max_purchase_amount`
        ,`is_have_purchase`
        ,`is_calculated`
        ,`is_setting`
        ,`examine_category_code`
        ,`examine_category_name`
        ,`examine_category_name_en`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="performanceNo != null and performanceNo != ''">
            AND `performance_no` = #{performanceNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="supplierShortName != null and supplierShortName != ''">
            AND `supplier_short_name` = #{supplierShortName}
        </if>
        <if test="procurementCategoryCode != null and procurementCategoryCode != ''">
            AND `procurement_category_code` = #{procurementCategoryCode}
        </if>
        <if test="procurementCategoryName != null and procurementCategoryName != ''">
            AND `procurement_category_name` = #{procurementCategoryName}
        </if>
        <if test="procurementCategoryNameEn != null and procurementCategoryNameEn != ''">
            AND `procurement_category_name_en` = #{procurementCategoryNameEn}
        </if>
        <if test="maxPurchaseAmount != null">
            AND `max_purchase_amount` = #{maxPurchaseAmount}
        </if>
        <if test="isHavePurchase != null">
            AND `is_have_purchase` = #{isHavePurchase}
        </if>
        <if test="examineCategoryCode != null and examineCategoryCode != ''">
            AND `examine_category_code` = #{examineCategoryCode}
        </if>
        <if test="examineCategoryName != null and examineCategoryName != ''">
            AND `examine_category_name` = #{examineCategoryName}
        </if>
        <if test="examineCategoryNameEn != null and examineCategoryNameEn != ''">
            AND `examine_category_name_en` = #{examineCategoryNameEn}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>

    <select id="listCreateQualityOrder"
            resultType="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO">
        SELECT
        DISTINCT
        t1.performance_no as performanceNo,
        t1.examine_category_code as twoCategoryCode,
        t1.examine_category_name as twoCategoryName,
        t1.examine_category_name_en as twoCategoryNameEn,
        t2.sqe_id as principalId,
        t2.sqe_name as principalName,
        t2.sqe_master_id as verificationId,
        t2.sqe_master_name as verificationName
        FROM
        performance_examine_supplier t1
        LEFT JOIN performance_examine_person t2 ON t1.performance_no = t2.performance_no
        AND t1.examine_category_code = t2.category_code
        WHERE
        t1.is_delete = 0
        AND t2.is_delete = 0
        AND t2.sqe_id  is not null
        AND t2.sqe_master_id  is not null
        <if test="performanceNo != null and performanceNo != ''">
            AND t1.`performance_no` = #{performanceNo}
        </if>
        <if test="twoCategoryCodeList != null or twoCategoryCodeList.size > 0 ">
            AND
            t1.examine_category_code in
            <foreach collection="twoCategoryCodeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        performance_order o
        WHERE
        o.performance_no = t1.performance_no
        AND o.two_category_code = t1.examine_category_code
        and o.order_type = "QUALITY_INDICATOR_DATA"
        )
    </select>
    <select id="listCreateBusinessAndProjectOrder"
            resultType="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO">
        SELECT
        DISTINCT
        t1.performance_no as performanceNo,
        t1.examine_category_code as twoCategoryCode,
        t1.examine_category_name as twoCategoryName,
        t1.examine_category_name_en as twoCategoryNameEn,
        t2.cpe_id as principalId,
        t2.cpe_name as principalName,
        t2.cpe_master_id as verificationId,
        t2.cpe_master_name as verificationName
        FROM
        performance_examine_supplier t1
        LEFT JOIN performance_examine_person t2 ON t1.performance_no = t2.performance_no
        AND t1.examine_category_code = t2.category_code
        WHERE
        t1.is_delete = 0
        AND t2.is_delete = 0
        AND t2.cpe_id  is not null
        AND t2.cpe_master_id  is not null
        <if test="performanceNo != null and performanceNo != ''">
            AND t1.`performance_no` = #{performanceNo}
        </if>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        performance_order o
        WHERE
        o.performance_no = t1.performance_no
        AND o.two_category_code = t1.examine_category_code
        AND o.order_type = "BUSINESS_PROJECT_DEVELOPMENT_INDICATOR"
        )
    </select>


</mapper>
