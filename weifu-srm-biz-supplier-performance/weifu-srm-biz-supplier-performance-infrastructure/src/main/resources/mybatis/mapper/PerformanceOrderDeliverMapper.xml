<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderDeliverMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDeliverPO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="division_id" property="divisionId" />
        <result column="year_month_str" property="yearMonthStr" />
        <result column="plan_delivery_batch" property="planDeliveryBatch" />
        <result column="timely_delivery_batch" property="timelyDeliveryBatch" />
        <result column="material_code" property="materialCode" />
        <result column="material_description" property="materialDescription" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_order_deliver`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`order_no`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`division_id`
    ,`year_month_str`
    ,`plan_delivery_batch`
    ,`timely_delivery_batch`
    ,`material_code`
    ,`material_description`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND `order_no` = #{orderNo}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="divisionId != null and divisionId != ''">
            AND `division_id` = #{divisionId}
        </if>
        <if test="yearMonthStr != null and yearMonthStr != ''">
            AND `year_month_str` = #{yearMonthStr}
        </if>
        <if test="planDeliveryBatch != null">
            AND `plan_delivery_batch` = #{planDeliveryBatch}
        </if>
        <if test="timelyDeliveryBatch != null">
            AND `timely_delivery_batch` = #{timelyDeliveryBatch}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND `material_code` = #{materialCode}
        </if>
        <if test="materialDescription != null and materialDescription != ''">
            AND `material_description` = #{materialDescription}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
