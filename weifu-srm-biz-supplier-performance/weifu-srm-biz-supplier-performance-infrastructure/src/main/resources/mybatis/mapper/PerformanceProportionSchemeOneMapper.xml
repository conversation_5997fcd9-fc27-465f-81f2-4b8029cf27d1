<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.supplier.performance.repository.mapper.PerformanceProportionSchemeOneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemeOnePO">
        <id column="id" property="id" />
        <result column="proportion_no" property="proportionNo" />
        <result column="first_level" property="firstLevel" />
        <result column="first_level_proportion" property="firstLevelProportion" />
        <result column="first_level_bonus_ceiling" property="firstLevelBonusCeiling" />
        <result column="first_level_deduct_ceiling" property="firstLevelDeductCeiling" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`performance_proportion_scheme_one`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`proportion_no`
    ,`first_level`
    ,`first_level_proportion`
    ,`first_level_bonus_ceiling`
    ,`first_level_deduct_ceiling`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>



</mapper>
