package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderQualityPO;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 绩效工单-质量指标数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderQualityMapperService extends IService<PerformanceOrderQualityPO> {
    IPage<PerformanceOrderQualityPO> listPageByQuery(IPage<PerformanceOrderQualityPO> page, PerformanceOrderNoPageReqDTO reqDTO);
    void mergeIntoData(String performanceNo, Date startDate, Date endDate);

    Integer countByOrderNo(String orderNo);

    List<PerformanceOrderQualityPO> listByOrderNo(String orderNo);
}
