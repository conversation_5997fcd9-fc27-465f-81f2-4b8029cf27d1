package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsSupplierPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderOtsSupplierMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderOtsSupplierMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-ots及时送样率-供应商范围 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceOrderOtsSupplierMapperServiceImpl extends ServiceImpl<PerformanceOrderOtsSupplierMapper, PerformanceOrderOtsSupplierPO> implements PerformanceOrderOtsSupplierMapperService {

    @Override
    public IPage<PerformanceOrderOtsSupplierPO> listPageByQuery(IPage<PerformanceOrderOtsSupplierPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderOtsSupplierPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != null && reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderOtsSupplierPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderOtsSupplierPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public List<PerformanceOrderOtsSupplierPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderOtsSupplierPO::getOrderNo, orderNo).list();
    }

    @Override
    public void markIsUpload(String orderNo, List<String> supplierCodeList) {
        List<List<String>> partitionList = Lists.partition(supplierCodeList, 500);
        for (List<String> partition : partitionList) {
            List<PerformanceOrderOtsSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderOtsSupplierPO::getOrderNo, orderNo)
                    .in(PerformanceOrderOtsSupplierPO::getSupplierCode, partition).list();
            List<Long> ids = poList.stream().map(PerformanceOrderOtsSupplierPO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                updateIsUpload(ids, YesOrNoEnum.YES.getCode());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void clearIsUpload(String orderNo) {
        List<PerformanceOrderOtsSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderOtsSupplierPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderOtsSupplierPO::getId).collect(Collectors.toList());
        List<List<Long>> partitionList = Lists.partition(ids, 500);
        for (List<Long> partition : partitionList) {
            updateIsUpload(partition, YesOrNoEnum.NO.getCode());
        }
    }

    private void updateIsUpload(List<Long> ids, Integer isUpload) {
        this.lambdaUpdate().set(PerformanceOrderOtsSupplierPO::getIsUploadReductionRate, isUpload).in(PerformanceOrderOtsSupplierPO::getId, ids).update();

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderOtsSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderOtsSupplierPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderOtsSupplierPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }
    @Override
    public void initData(String performanceNo) {
        this.getBaseMapper().initData(performanceNo);
    }

    private LambdaQueryWrapper<PerformanceOrderOtsSupplierPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderOtsSupplierPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderOtsSupplierPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderOtsSupplierPO::getId);
        return queryWrapper;
    }
}
