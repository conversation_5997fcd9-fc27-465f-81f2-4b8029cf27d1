package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.SchemeTwoResultMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.SchemeTwoResultMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoResultPO;
import org.springframework.stereotype.Service;

@Service
public class SchemeTwoResultMapperServiceImpl extends ServiceImpl<SchemeTwoResultMapper, ComputationSchemeTwoResultPO> implements SchemeTwoResultMapperService {

}
