package com.weifu.srm.supplier.performance.repository.atomicservice.computation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoLogicResultPO;

/**
 * <p>
 * 绩效核算 - 二级指标计算得分结果
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
public interface SchemeTwoLogicResultMapperService extends IService<ComputationSchemeTwoLogicResultPO> {

}
