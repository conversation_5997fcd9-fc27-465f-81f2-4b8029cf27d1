package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsMaterialDeliveryPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效工单-ots及时送样率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderOtsMaterialDeliveryMapperService extends IService<PerformanceOrderOtsMaterialDeliveryPO> {

    IPage<PerformanceOrderOtsMaterialDeliveryPO> listPageByQuery(IPage<PerformanceOrderOtsMaterialDeliveryPO> page, PerformanceOrderNoPageReqDTO reqDTO);

    List<PerformanceOrderOtsMaterialDeliveryPO> listByOrderNo(String orderNo);

    void removeByOrderNo(String orderNo);
}
