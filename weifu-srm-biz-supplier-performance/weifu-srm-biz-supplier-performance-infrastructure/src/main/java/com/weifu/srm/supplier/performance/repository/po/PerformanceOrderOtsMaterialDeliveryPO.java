package com.weifu.srm.supplier.performance.repository.po;

import com.weifu.srm.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @Description 绩效工单-ots及时送样率
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("performance_order_ots_material_delivery")
public class PerformanceOrderOtsMaterialDeliveryPO extends BaseEntity {

    /**工单编号*/
    private String orderNo;

    /**供应商编码*/
    private String supplierCode;

    /**供应商名称*/
    private String supplierName;

    /**供应商英文名称*/
    private String supplierNameEn;

    /**物料编码*/
    private String materialCode;

    /**物料描述*/
    private String materialDescription;

    /**事业部id*/
    private String divisionId;

    /**一级品类编码*/
    private String oneCategoryCode;

    /**二级品类编码*/
    private String twoCategoryCode;

    /**三级品类编码*/
    private String threeCategoryCode;

    /**年月日*/
    private String yearMonthDate;

    /**ots零件送样总批次数*/
    private Integer batchTotalQuantity;

    /**ots零件及时送样批次数*/
    private Integer batchTimelyQuantity;

    /**项目名称*/
    private String projectName;


}
