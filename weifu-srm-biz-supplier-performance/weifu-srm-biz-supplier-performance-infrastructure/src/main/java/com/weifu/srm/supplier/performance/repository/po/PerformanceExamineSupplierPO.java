package com.weifu.srm.supplier.performance.repository.po;

import com.weifu.srm.mybatis.base.BaseEntity;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @Description 绩效考核-供应商设定
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("performance_examine_supplier")
public class PerformanceExamineSupplierPO extends BaseEntity {

    /**绩效编号*/
    private String performanceNo;

    /**供应商编码*/
    private String supplierCode;

    /**供应商名称*/
    private String supplierName;

    /**供应商英文名称*/
    private String supplierNameEn;

    /**供应商简称*/
    private String supplierShortName;

    /**考核周期内采购最大的二级品类编码*/
    private String procurementCategoryCode;

    /**考核周期内采购最大的二级品类名称*/
    private String procurementCategoryName;

    /**考核周期内采购最大的二级品类英文名称*/
    private String procurementCategoryNameEn;

    /**最大采购额*/
    private BigDecimal maxPurchaseAmount;

    /**是否有采购额*/
    private Integer isHavePurchase;

    /**是否已经计算*/
    private Integer isCalculated;

    /**是否已经设定*/
    private Integer isSetting;

    /**绩效考核二级品类编码*/
    private String examineCategoryCode;

    /**绩效考核二级品类名称*/
    private String examineCategoryName;

    /**绩效考核二级品类英文名称*/
    private String examineCategoryNameEn;


}
