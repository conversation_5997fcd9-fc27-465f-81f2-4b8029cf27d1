package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.SupplierScoreResultMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.SupplierScoreResultMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.SupplierScoreResultPO;
import org.springframework.stereotype.Service;

@Service
public class SupplierScoreResultMapperServiceImpl extends ServiceImpl<SupplierScoreResultMapper, SupplierScoreResultPO> implements SupplierScoreResultMapperService {

}
