package com.weifu.srm.supplier.performance.repository.mapper.computation;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoLogicResultPO;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * @Description 绩效工单-降本目标达成-降本物料清单 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface ComputationSchemeTwoLogicResultMapper extends BaseMapper<ComputationSchemeTwoLogicResultPO> {

}
