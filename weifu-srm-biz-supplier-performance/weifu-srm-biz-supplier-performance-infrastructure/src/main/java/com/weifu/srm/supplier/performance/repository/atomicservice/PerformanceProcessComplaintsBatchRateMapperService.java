package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.weifu.srm.supplier.performance.repository.po.PerformanceProcessComplaintsBatchRatePO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 制程投诉批次率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceProcessComplaintsBatchRateMapperService extends IService<PerformanceProcessComplaintsBatchRatePO> {

    Integer countByCategoryCode(String categoryCode);
}
