package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Sets;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionMaterialPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderCostReductionMaterialMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderCostReductionMaterialMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-降本目标达成-降本物料清单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class OrderCostReductionMaterialMapperServiceImpl extends ServiceImpl<PerformanceOrderCostReductionMaterialMapper, PerformanceOrderCostReductionMaterialPO> implements PerformanceOrderCostReductionMaterialMapperService {

    @Override
    public IPage<PerformanceOrderCostReductionMaterialPO> listPageByQuery(IPage<PerformanceOrderCostReductionMaterialPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionMaterialPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<PerformanceOrderCostReductionMaterialPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderCostReductionMaterialPO::getOrderNo, orderNo).list();
    }

    @Override
    public Set<String> listSupplierAndMaterialCodeByOrderNo(String orderNo) {
        List<PerformanceOrderCostReductionMaterialPO> poList = listByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(poList)) {
            return Sets.newHashSet();
        }
        return poList.stream().map(obj->obj.getSupplierCode()+":"+obj.getMaterialCode()).collect(Collectors.toSet());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderCostReductionMaterialPO> poList = this.lambdaQuery().eq(PerformanceOrderCostReductionMaterialPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderCostReductionMaterialPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    private LambdaQueryWrapper<PerformanceOrderCostReductionMaterialPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionMaterialPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderCostReductionMaterialPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderCostReductionMaterialPO::getId);
        return queryWrapper;
    }
}
