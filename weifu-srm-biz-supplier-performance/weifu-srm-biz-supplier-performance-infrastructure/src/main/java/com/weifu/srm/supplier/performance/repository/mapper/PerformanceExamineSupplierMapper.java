package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceExamineSupplierPO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效考核-供应商设定 Mapper 接口
 * @Date 2024-09-20
 */
@Mapper
public interface PerformanceExamineSupplierMapper extends BaseMapper<PerformanceExamineSupplierPO> {

    List<PerformanceOrderPO> listCreateQualityOrder(@Param("performanceNo") String performanceNo, @Param("twoCategoryCodeList") List<String> twoCategoryCodeList);
    List<PerformanceOrderPO> listCreateBusinessAndProjectOrder(@Param("performanceNo") String performanceNo);
}
