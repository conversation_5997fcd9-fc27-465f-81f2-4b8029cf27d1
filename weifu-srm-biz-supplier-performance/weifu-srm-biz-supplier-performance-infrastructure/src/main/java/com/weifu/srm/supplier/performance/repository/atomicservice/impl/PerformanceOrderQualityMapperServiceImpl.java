package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderQualityMapperService;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderQualityMapper;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderQualityPO;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PerformanceOrderQualityMapperServiceImpl extends ServiceImpl<PerformanceOrderQualityMapper, PerformanceOrderQualityPO> implements PerformanceOrderQualityMapperService {
    @Override
    public IPage<PerformanceOrderQualityPO> listPageByQuery(IPage<PerformanceOrderQualityPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderQualityPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != null && reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderQualityPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderQualityPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public void mergeIntoData(String performanceNo, Date startDate, Date endDate) {
        this.baseMapper.mergeIntoData(performanceNo, startDate, endDate);
    }

    @Override
    public Integer countByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderQualityPO::getOrderNo, orderNo).count();
    }

    @Override
    public List<PerformanceOrderQualityPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderQualityPO::getOrderNo,orderNo).list();
    }

    private LambdaQueryWrapper<PerformanceOrderQualityPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderQualityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderQualityPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderQualityPO::getId);
        return queryWrapper;
    }
}
