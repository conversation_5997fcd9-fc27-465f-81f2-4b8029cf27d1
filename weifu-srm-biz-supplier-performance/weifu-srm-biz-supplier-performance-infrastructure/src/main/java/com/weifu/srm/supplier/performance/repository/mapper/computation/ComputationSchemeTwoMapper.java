package com.weifu.srm.supplier.performance.repository.mapper.computation;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoPO;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * @Description 绩效计算 - 预计算 - 预计算基本详情 Mapper 接口
 * @Date 2024-09-25
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface ComputationSchemeTwoMapper extends BaseMapper<ComputationSchemeTwoPO> {

}
