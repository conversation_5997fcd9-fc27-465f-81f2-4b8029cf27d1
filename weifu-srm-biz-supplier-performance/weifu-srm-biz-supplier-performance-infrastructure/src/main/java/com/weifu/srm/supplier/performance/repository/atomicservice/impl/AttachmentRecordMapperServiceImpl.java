package com.weifu.srm.supplier.performance.repository.atomicservice.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.AttachmentRecordMapper;
import com.weifu.srm.supplier.performance.repository.po.AttachmentRecordPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AttachmentRecordMapperServiceImpl extends ServiceImpl<AttachmentRecordMapper, AttachmentRecordPO> implements AttachmentRecordMapperService {
    @Override
    public List<AttachmentRecordPO> listByBusinessNo(String businessNo) {
        return list(Wrappers.<AttachmentRecordPO>lambdaQuery().
                eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode()).
                eq(AttachmentRecordPO::getBusinessNo, businessNo));
    }

    @Override
    public List<AttachmentRecordPO> listByBusinessNoAndBusinessType(String businessNo, String businessType) {
        return list(Wrappers.<AttachmentRecordPO>lambdaQuery().
                eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode()).
                eq(AttachmentRecordPO::getBusinessType, businessType).
                eq(AttachmentRecordPO::getBusinessNo, businessNo));
    }

    @Override
    public List<AttachmentRecordPO> listByBusinessNoAndBusinessType(List<String> businessNoList, List<String> businessTypeList) {
        return list(Wrappers.<AttachmentRecordPO>lambdaQuery().
                eq(AttachmentRecordPO::getIsDelete, YesOrNoEnum.NO.getCode()).
                in(CollectionUtils.isNotEmpty(businessTypeList), AttachmentRecordPO::getBusinessType, businessTypeList).
                in(AttachmentRecordPO::getBusinessNo, businessNoList));
    }
}
