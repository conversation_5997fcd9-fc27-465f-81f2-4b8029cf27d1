package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.examine.PerformanceExaminePageReqDTO;
import com.weifu.srm.supplier.performance.response.examine.CalculateAmountDTO;

import java.util.List;

/**
 * <p>
 * 绩效考核 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceExamineMapperService extends IService<PerformanceExaminePO> {
    IPage<PerformanceExaminePO> listPageByQuery(IPage<PerformanceExaminePO> page, PerformanceExaminePageReqDTO reqDTO);

    PerformanceExaminePO getPerformanceNo(String performanceNo);

    List<CalculateAmountDTO> calculateAmount(String startDate, String endDate, List<String> supplierCodes);

    void verificationComplete(String performanceNo);
}
