package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDeliverPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderDeliverMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderDeliverMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-交付指标上传 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceOrderDeliverMapperServiceImpl extends ServiceImpl<PerformanceOrderDeliverMapper, PerformanceOrderDeliverPO> implements PerformanceOrderDeliverMapperService {
    @Override
    public IPage<PerformanceOrderDeliverPO> listPageByQuery(IPage<PerformanceOrderDeliverPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderDeliverPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != null && reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderDeliverPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderDeliverPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderDeliverPO> poList = this.lambdaQuery().eq(PerformanceOrderDeliverPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderDeliverPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    @Override
    public Integer countByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderDeliverPO::getOrderNo,orderNo).count();
    }

    private LambdaQueryWrapper<PerformanceOrderDeliverPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderDeliverPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderDeliverPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderDeliverPO::getId);
        return queryWrapper;
    }
}
