package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.response.examine.CalculateAmountDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @Description 绩效考核 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceExamineMapper extends BaseMapper<PerformanceExaminePO> {

    List<CalculateAmountDTO> calculateAmount(@Param("startDate") String startDate,@Param("endDate")String endDate,@Param("supplierCodes") List<String> supplierCodes);
}
