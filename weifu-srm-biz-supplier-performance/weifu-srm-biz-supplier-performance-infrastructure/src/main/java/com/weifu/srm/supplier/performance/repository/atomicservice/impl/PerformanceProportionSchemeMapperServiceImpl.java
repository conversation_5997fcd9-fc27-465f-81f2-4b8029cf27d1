package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceProportionSchemeMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceProportionSchemeMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceProportionSchemePageReqDTO;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceProportionSchemeQuotaRespDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 权重方案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceProportionSchemeMapperServiceImpl extends ServiceImpl<PerformanceProportionSchemeMapper, PerformanceProportionSchemePO> implements PerformanceProportionSchemeMapperService {

    @Override
    public IPage<PerformanceProportionSchemePO> listPageByQuery(IPage<PerformanceProportionSchemePO> page, PerformanceProportionSchemePageReqDTO reqDTO) {
        return this.page(page, buildLambdaQueryWrapper(reqDTO));
    }

    @Override
    public List<PerformanceProportionSchemeQuotaRespDTO> listQuotaByProportionNo(String proportionNo) {
        return this.getBaseMapper().listQuotaByProportionNo(proportionNo);
    }

    @Override
    public List<PerformanceProportionSchemePO> listByProportionNos(List<String> proportionNos) {
        if(CollectionUtils.isEmpty(proportionNos)){
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(PerformanceProportionSchemePO::getProportionNo, proportionNos).list();
    }

    private LambdaQueryWrapper<PerformanceProportionSchemePO> buildLambdaQueryWrapper(PerformanceProportionSchemePageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceProportionSchemePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getThemes()), PerformanceProportionSchemePO::getThemes, reqDTO.getThemes());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PerformanceProportionSchemePO::getStatus, reqDTO.getStatus());
        queryWrapper.eq(reqDTO.getCreateBy() != null, PerformanceProportionSchemePO::getCreateBy, reqDTO.getCreateBy());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCreateName()), PerformanceProportionSchemePO::getCreateName, reqDTO.getCreateName());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, PerformanceProportionSchemePO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, PerformanceProportionSchemePO::getCreateTime, reqDTO.getCreateTimeEnd());
        queryWrapper.orderByDesc(PerformanceProportionSchemePO::getId);
        return queryWrapper;
    }
}
