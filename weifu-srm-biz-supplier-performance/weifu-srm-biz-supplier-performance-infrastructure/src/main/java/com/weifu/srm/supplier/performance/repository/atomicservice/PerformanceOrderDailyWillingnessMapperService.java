package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDailyWillingnessPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;


/**
 * <p>
 * 绩效工单-日常服务意愿 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderDailyWillingnessMapperService extends IService<PerformanceOrderDailyWillingnessPO> {
    IPage<PerformanceOrderDailyWillingnessPO> listPageByQuery(IPage<PerformanceOrderDailyWillingnessPO> page, PerformanceOrderNoPageReqDTO reqDTO);

    List<PerformanceOrderDailyWillingnessPO> listByOrderNo(String orderNo);

    Integer countNullDailyWillingnessByOrderNo(String orderNo);

    void removeByOrderNo(String orderNo);

    void initData(String performanceNo);
}
