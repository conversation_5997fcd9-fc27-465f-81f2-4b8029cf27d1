package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceQuotaScorePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreCreateReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScorePageReqDTO;

/**
 * <p>
 * 供应商绩效指标加减分配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceQuotaScoreMapperService extends IService<PerformanceQuotaScorePO> {
    IPage<PerformanceQuotaScorePO> listPageByQuery(IPage<PerformanceQuotaScorePO> page, PerformanceQuotaScorePageReqDTO reqDTO);

    boolean validRepeatData(Long id, PerformanceQuotaScoreCreateReqDTO reqDTO);
}
