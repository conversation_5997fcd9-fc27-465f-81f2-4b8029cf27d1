package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.weifu.srm.supplier.performance.repository.po.PerformanceProcessComplaintsBatchRatePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceProcessComplaintsBatchRateMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceProcessComplaintsBatchRateMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 制程投诉批次率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class ProcessComplaintsBatchRateMapperServiceImpl extends ServiceImpl<PerformanceProcessComplaintsBatchRateMapper, PerformanceProcessComplaintsBatchRatePO> implements PerformanceProcessComplaintsBatchRateMapperService {

    @Override
    public Integer countByCategoryCode(String categoryCode) {
        return this.lambdaQuery().eq(PerformanceProcessComplaintsBatchRatePO::getCategoryCode,categoryCode).count();
    }
}
