package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemePO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceEvaluateProportionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效评价方案 Mapper 接口
 * @Date 2024-09-20
 */
@Mapper
public interface PerformanceEvaluateSchemeMapper extends BaseMapper<PerformanceEvaluateSchemePO> {
    List<PerformanceEvaluateProportionRespDTO> listEvaluateProportion(@Param("evaluateNo") String evaluateNo);
}
