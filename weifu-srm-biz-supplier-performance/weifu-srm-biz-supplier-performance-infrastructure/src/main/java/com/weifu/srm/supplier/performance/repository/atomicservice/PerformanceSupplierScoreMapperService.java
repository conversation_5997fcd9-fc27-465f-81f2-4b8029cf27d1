package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScorePageReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO;

/**
 * <p>
 * 供应商加减分 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceSupplierScoreMapperService extends IService<PerformanceSupplierScorePO> {

    IPage<PerformanceSupplierScoreRespDTO> listPageByQuery(Page<PerformanceSupplierScoreRespDTO> page, PerformanceSupplierScorePageReqDTO reqDTO);
    boolean validRepeatData(Long id, String firstLevel, Long quotaId, String supplierCode, String happenTime);

}
