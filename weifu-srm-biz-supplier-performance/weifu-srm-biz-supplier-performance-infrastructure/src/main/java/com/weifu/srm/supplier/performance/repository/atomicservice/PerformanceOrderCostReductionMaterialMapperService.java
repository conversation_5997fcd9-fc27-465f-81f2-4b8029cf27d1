package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionMaterialPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 绩效工单-降本目标达成-降本物料清单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderCostReductionMaterialMapperService extends IService<PerformanceOrderCostReductionMaterialPO> {

    IPage<PerformanceOrderCostReductionMaterialPO> listPageByQuery(IPage<PerformanceOrderCostReductionMaterialPO> page, PerformanceOrderNoPageReqDTO reqDTO);
    List<PerformanceOrderCostReductionMaterialPO> listByOrderNo(String orderNo);
    Set<String> listSupplierAndMaterialCodeByOrderNo(String orderNo);
    void removeByOrderNo(String orderNo);
}
