package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionSupplierPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderCostReductionSupplierMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderCostReductionSupplierMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-降本目标达成-供应商范围 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class OrderCostReductionSupplierMapperServiceImpl extends ServiceImpl<PerformanceOrderCostReductionSupplierMapper, PerformanceOrderCostReductionSupplierPO> implements PerformanceOrderCostReductionSupplierMapperService {

    @Override
    public IPage<PerformanceOrderCostReductionSupplierPO> listPageByQuery(IPage<PerformanceOrderCostReductionSupplierPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionSupplierPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != null && reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderCostReductionSupplierPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderCostReductionSupplierPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void markIsUpload(String orderNo, List<String> supplierCodeList, boolean isMaterial) {
        List<List<String>> partitionList = Lists.partition(supplierCodeList, 500);
        for (List<String> partition : partitionList) {
            List<PerformanceOrderCostReductionSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderCostReductionSupplierPO::getOrderNo, orderNo)
                    .in(PerformanceOrderCostReductionSupplierPO::getSupplierCode, partition).list();
            List<Long> ids = poList.stream().map(PerformanceOrderCostReductionSupplierPO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                if (isMaterial) {
                    updateIsUploadMaterial(ids, YesOrNoEnum.YES.getCode());
                } else {
                    updateIsUploadReductionRate(ids, YesOrNoEnum.YES.getCode());
                }

            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void clearIsUpload(String orderNo, boolean isMaterial) {
        List<PerformanceOrderCostReductionSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderCostReductionSupplierPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderCostReductionSupplierPO::getId).collect(Collectors.toList());
        List<List<Long>> partitionList = Lists.partition(ids, 500);
        for (List<Long> partition : partitionList) {
            if (isMaterial) {
                updateIsUploadMaterial(partition, YesOrNoEnum.NO.getCode());
            } else {
                updateIsUploadReductionRate(ids, YesOrNoEnum.NO.getCode());
            }
        }
    }


    private void updateIsUploadMaterial(List<Long> ids, Integer isUploadMaterial) {
        this.lambdaUpdate().set(PerformanceOrderCostReductionSupplierPO::getIsUploadMaterial, isUploadMaterial).in(PerformanceOrderCostReductionSupplierPO::getId, ids).update();
    }

    private void updateIsUploadReductionRate(List<Long> ids, Integer isUploadReductionRate) {
        this.lambdaUpdate().set(PerformanceOrderCostReductionSupplierPO::getIsUploadReductionRate, isUploadReductionRate).in(PerformanceOrderCostReductionSupplierPO::getId, ids).update();
    }

    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderCostReductionSupplierPO> poList = this.lambdaQuery().eq(PerformanceOrderCostReductionSupplierPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderCostReductionSupplierPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    @Override
    public Integer countUploadInconsistent(String orderNo) {
        return this.getBaseMapper().countUploadInconsistent(orderNo);
    }

    @Override
    public void initData(String performanceNo) {
        this.getBaseMapper().initData(performanceNo);
    }

    @Override
    public List<PerformanceOrderCostReductionSupplierPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderCostReductionSupplierPO::getOrderNo, orderNo).list();
    }

    private LambdaQueryWrapper<PerformanceOrderCostReductionSupplierPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionSupplierPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderCostReductionSupplierPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderCostReductionSupplierPO::getId);
        return queryWrapper;
    }
}
