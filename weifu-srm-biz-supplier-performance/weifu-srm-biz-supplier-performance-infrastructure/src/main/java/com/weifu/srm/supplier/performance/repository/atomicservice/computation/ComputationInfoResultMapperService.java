package com.weifu.srm.supplier.performance.repository.atomicservice.computation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationInfoResultPO;
import com.weifu.srm.supplier.performance.request.precomputation.PreComputationPageReqDTO;
import com.weifu.srm.supplier.performance.request.precomputation.PreComputationReqDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationHistoryScoreResDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationInfoResDTO;

import java.util.List;

public interface ComputationInfoResultMapperService extends IService<ComputationInfoResultPO> {

    IPage<ComputationInfoResultPO> listPageByQuery(Page<ComputationInfoResultPO> page, PreComputationPageReqDTO reqDTO);

    List<ComputationInfoResultPO> performanceResultList(PreComputationPageReqDTO reqDTO);

    Page<ComputationInfoResultPO> queryPerformanceResultList(PreComputationPageReqDTO reqDTO);

    List<ComputationHistoryScoreResDTO> findNewYearScoreList(List<String> supplierCode);

    List<ComputationInfoResultPO> findSupplierHistoryScoreList(List<String> supplierCodes);
}
