package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemeOnePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceProportionSchemeOneMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceProportionSchemeOneMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 权重方案-指标权重设定-一级 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceProportionSchemeOneMapperServiceImpl extends ServiceImpl<PerformanceProportionSchemeOneMapper, PerformanceProportionSchemeOnePO> implements PerformanceProportionSchemeOneMapperService {

}
