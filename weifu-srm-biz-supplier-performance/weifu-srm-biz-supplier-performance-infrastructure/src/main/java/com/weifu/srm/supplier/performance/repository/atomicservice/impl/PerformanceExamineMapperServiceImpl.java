package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.enums.ExamineStatusEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceExamineMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.examine.PerformanceExaminePageReqDTO;
import com.weifu.srm.supplier.performance.response.examine.CalculateAmountDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效考核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceExamineMapperServiceImpl extends ServiceImpl<PerformanceExamineMapper, PerformanceExaminePO> implements PerformanceExamineMapperService {

    @Override
    public IPage<PerformanceExaminePO> listPageByQuery(IPage<PerformanceExaminePO> page, PerformanceExaminePageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceExaminePO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceExaminePO> poList = this.list(queryWrapper);
            IPage<PerformanceExaminePO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public PerformanceExaminePO getPerformanceNo(String performanceNo) {
        return this.getOne(Wrappers.<PerformanceExaminePO>lambdaQuery().eq(PerformanceExaminePO::getPerformanceNo,performanceNo),false);
    }

    @Override
    public List<CalculateAmountDTO> calculateAmount(String startDate, String endDate, List<String> supplierCodes) {
        return this.getBaseMapper().calculateAmount(startDate,endDate,supplierCodes);
    }

    @Override
    public void verificationComplete(String performanceNo) {
        PerformanceExaminePO po = this.getPerformanceNo(performanceNo);
        PerformanceExaminePO update = new PerformanceExaminePO();
        update.setId(po.getId());
        update.setStatus(ExamineStatusEnum.VERIFICATION_COMPLETE.getCode());
        this.updateById(update);
    }

    private LambdaQueryWrapper<PerformanceExaminePO> buildLambdaQueryWrapper(PerformanceExaminePageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceExaminePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getPerformanceNo()), PerformanceExaminePO::getPerformanceNo, reqDTO.getPerformanceNo());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getThemes()), PerformanceExaminePO::getThemes, reqDTO.getThemes());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCreateName()), PerformanceExaminePO::getCreateName, reqDTO.getCreateName());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getPerformanceType()), PerformanceExaminePO::getPerformanceType, reqDTO.getPerformanceType());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getExamineCycleYear()), PerformanceExaminePO::getExamineCycleYear, reqDTO.getExamineCycleYear());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getExamineCycleQuarter()), PerformanceExaminePO::getExamineCycleQuarter, reqDTO.getExamineCycleQuarter());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PerformanceExaminePO::getStatus, reqDTO.getStatus());
        queryWrapper.ge(reqDTO.getPublishTimeStart() != null, PerformanceExaminePO::getPublishTime, reqDTO.getPublishTimeStart());
        queryWrapper.le(reqDTO.getPublishTimeEnd() != null, PerformanceExaminePO::getPublishTime, reqDTO.getPublishTimeEnd());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, PerformanceExaminePO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, PerformanceExaminePO::getCreateTime, reqDTO.getCreateTimeEnd());
        queryWrapper.orderByDesc(PerformanceExaminePO::getId);
        return queryWrapper;
    }
}
