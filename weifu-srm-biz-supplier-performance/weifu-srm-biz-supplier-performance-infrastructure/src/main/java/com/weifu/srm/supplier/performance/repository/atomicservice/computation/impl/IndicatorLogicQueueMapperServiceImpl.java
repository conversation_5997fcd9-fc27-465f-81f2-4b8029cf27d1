package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.IndicatorLogicQueueMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationIndicatorLogicQueueMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationIndicatorLogicQueuePO;
import org.springframework.stereotype.Service;

@Service
public class IndicatorLogicQueueMapperServiceImpl extends ServiceImpl<ComputationIndicatorLogicQueueMapper, ComputationIndicatorLogicQueuePO> implements IndicatorLogicQueueMapperService {

}
