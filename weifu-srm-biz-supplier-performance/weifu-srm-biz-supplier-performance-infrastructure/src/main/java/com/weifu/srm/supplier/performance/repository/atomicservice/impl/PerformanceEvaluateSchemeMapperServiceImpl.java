package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceEvaluateSchemeMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceEvaluateSchemeMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceEvaluateSchemePageReqDTO;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceEvaluateProportionRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效评价方案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceEvaluateSchemeMapperServiceImpl extends ServiceImpl<PerformanceEvaluateSchemeMapper, PerformanceEvaluateSchemePO> implements PerformanceEvaluateSchemeMapperService {

    @Override
    public IPage<PerformanceEvaluateSchemePO> listPageByQuery(IPage<PerformanceEvaluateSchemePO> page, PerformanceEvaluateSchemePageReqDTO reqDTO) {
        return this.page(page, buildLambdaQueryWrapper(reqDTO));
    }

    @Override
    public PerformanceEvaluateSchemePO getByEvaluateNo(String evaluateNo) {
        return this.getOne(Wrappers.<PerformanceEvaluateSchemePO>lambdaQuery().eq(PerformanceEvaluateSchemePO::getEvaluateNo, evaluateNo),false);
    }

    @Override
    public List<PerformanceEvaluateProportionRespDTO> listEvaluateProportion(String evaluateNo) {
        return this.getBaseMapper().listEvaluateProportion(evaluateNo);
    }

    private LambdaQueryWrapper<PerformanceEvaluateSchemePO> buildLambdaQueryWrapper(PerformanceEvaluateSchemePageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceEvaluateSchemePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getThemes()), PerformanceEvaluateSchemePO::getThemes, reqDTO.getThemes());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PerformanceEvaluateSchemePO::getStatus, reqDTO.getStatus());
        queryWrapper.eq(reqDTO.getCreateBy() != null, PerformanceEvaluateSchemePO::getCreateBy, reqDTO.getCreateBy());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCreateName()), PerformanceEvaluateSchemePO::getCreateName, reqDTO.getCreateName());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, PerformanceEvaluateSchemePO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, PerformanceEvaluateSchemePO::getCreateTime, reqDTO.getCreateTimeEnd());
        queryWrapper.orderByDesc(PerformanceEvaluateSchemePO::getId);
        return queryWrapper;
    }
}
