package com.weifu.srm.supplier.performance.repository.atomicservice;


import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.AttachmentRecordPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31 10:57
 * @Description 附件
 * @Version 1.0
 */
public interface AttachmentRecordMapperService extends IService<AttachmentRecordPO> {
    List<AttachmentRecordPO> listByBusinessNo(String businessNo);

    List<AttachmentRecordPO> listByBusinessNoAndBusinessType(String businessNo, String businessType);

    List<AttachmentRecordPO> listByBusinessNoAndBusinessType(List<String> businessNoList, List<String> businessTypeList);
}
