package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceExamineDivisionMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineDivisionMapper;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExamineDivisionPO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 交付指标负责人设定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Service
public class PerformanceExamineDivisionMapperServiceImpl extends ServiceImpl<PerformanceExamineDivisionMapper, PerformanceExamineDivisionPO> implements PerformanceExamineDivisionMapperService {

}
