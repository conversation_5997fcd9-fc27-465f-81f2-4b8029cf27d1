package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.PreComputeQueueMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationPreComputeQueueMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationPreComputeQueuePO;
import org.springframework.stereotype.Service;

@Service
public class PreComputeQueueMapperServiceImpl extends ServiceImpl<ComputationPreComputeQueueMapper, ComputationPreComputeQueuePO> implements PreComputeQueueMapperService {

}
