package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceQuotaScorePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceQuotaScoreMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceQuotaScoreMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScoreCreateReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceQuotaScorePageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 供应商绩效指标加减分配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceQuotaScoreMapperServiceImpl extends ServiceImpl<PerformanceQuotaScoreMapper, PerformanceQuotaScorePO> implements PerformanceQuotaScoreMapperService {

    @Override
    public IPage<PerformanceQuotaScorePO> listPageByQuery(IPage<PerformanceQuotaScorePO> page, PerformanceQuotaScorePageReqDTO reqDTO) {
        return this.page(page, buildLambdaQueryWrapper(reqDTO));
    }

    @Override
    public boolean validRepeatData(Long id, PerformanceQuotaScoreCreateReqDTO reqDTO) {
        Integer count = this.lambdaQuery().ne(id != null, PerformanceQuotaScorePO::getId, id)
                .eq(PerformanceQuotaScorePO::getFirstLevel, reqDTO.getFirstLevel())
                .eq(PerformanceQuotaScorePO::getSecondLevel, reqDTO.getSecondLevel())
                .eq(PerformanceQuotaScorePO::getScoreType, reqDTO.getScoreType())
                .eq(PerformanceQuotaScorePO::getDefinition,reqDTO.getDefinition())
                .eq(PerformanceQuotaScorePO::getScore, reqDTO.getScore()).count();
        return count > 0;
    }

    private LambdaQueryWrapper<PerformanceQuotaScorePO> buildLambdaQueryWrapper(PerformanceQuotaScorePageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceQuotaScorePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getFirstLevel()), PerformanceQuotaScorePO::getFirstLevel, reqDTO.getFirstLevel());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getFirstLevelList()), PerformanceQuotaScorePO::getFirstLevel, reqDTO.getFirstLevelList());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getSecondLevel()), PerformanceQuotaScorePO::getSecondLevel, reqDTO.getSecondLevel());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCreateName()), PerformanceQuotaScorePO::getCreateName, reqDTO.getCreateName());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getScoreType()), PerformanceQuotaScorePO::getScoreType, reqDTO.getScoreType());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PerformanceQuotaScorePO::getStatus, reqDTO.getStatus());
        queryWrapper.eq(reqDTO.getCreateBy() != null, PerformanceQuotaScorePO::getCreateBy, reqDTO.getCreateBy());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, PerformanceQuotaScorePO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, PerformanceQuotaScorePO::getCreateTime, reqDTO.getCreateTimeEnd());
        queryWrapper.orderByDesc(PerformanceQuotaScorePO::getId);
        return queryWrapper;
    }
}
