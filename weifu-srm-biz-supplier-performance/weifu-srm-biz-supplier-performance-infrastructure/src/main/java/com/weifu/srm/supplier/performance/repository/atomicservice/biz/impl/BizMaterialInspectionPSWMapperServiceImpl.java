package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizMaterialInspectionPSWMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizMaterialInspectionPSWDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizMaterialInspectionPSWDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BizMaterialInspectionPSWMapperServiceImpl extends ServiceImpl<BizMaterialInspectionPSWDataMapper, BizMaterialInspectionPSWDataPO> implements BizMaterialInspectionPSWMapperService {
}
