package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.enums.OrderStatusEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderPageReqDTO;
import com.weifu.srm.supplier.performance.response.order.PerformanceOrderStatisticsRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效工单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceOrderMapperServiceImpl extends ServiceImpl<PerformanceOrderMapper, PerformanceOrderPO> implements PerformanceOrderMapperService {

    @Override
    public IPage<PerformanceOrderPO> listPageByQuery(IPage<PerformanceOrderPO> page, PerformanceOrderPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public PerformanceOrderPO getByOrderNo(String orderNo) {
        return this.getOne(Wrappers.<PerformanceOrderPO>lambdaQuery().eq(PerformanceOrderPO::getOrderNo, orderNo), false);
    }

    @Override
    public List<PerformanceOrderPO> listByPerformanceNoAndOrderType(String performanceNo, String orderType) {
        return this.lambdaQuery().eq(PerformanceOrderPO::getPerformanceNo, performanceNo).eq(PerformanceOrderPO::getOrderType, orderType).list();
    }

    @Override
    public boolean validVerificationComplete(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceOrderPO::getPerformanceNo, performanceNo).ne(PerformanceOrderPO::getStatus, OrderStatusEnum.VERIFIED.getCode()).count() < 1;
    }

    @Override
    public PerformanceOrderStatisticsRespDTO statisticsByPerformanceNo(String performanceNo) {
        return this.getBaseMapper().statisticsByPerformanceNo(performanceNo);
    }

    @Override
    public void updatePerformanceOrder(PerformanceOrderPO po) {
        this.lambdaUpdate()
                .eq(PerformanceOrderPO::getId, po.getId())
                .set(PerformanceOrderPO::getVerificationPassedDate, po.getVerificationPassedDate())
                .set(PerformanceOrderPO::getStatus, po.getStatus())
                .set(PerformanceOrderPO::getUpdateBy, po.getUpdateBy())
                .set(PerformanceOrderPO::getUpdateName, po.getUpdateName())
                .set(PerformanceOrderPO::getUpdateTime, po.getUpdateTime())
                .update();
    }

    @Override
    public Integer statisticsTodoOrder(Long operationBy) {
        return this.getBaseMapper().statisticsTodoOrder(operationBy);
    }

    private LambdaQueryWrapper<PerformanceOrderPO> buildLambdaQueryWrapper(PerformanceOrderPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getPerformanceNo()), PerformanceOrderPO::getPerformanceNo, reqDTO.getPerformanceNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getPerformanceType()), PerformanceOrderPO::getPerformanceType, reqDTO.getPerformanceType());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getExamineCycleYear()), PerformanceOrderPO::getExamineCycleYear, reqDTO.getExamineCycleYear());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getExamineCycleQuarter()), PerformanceOrderPO::getExamineCycleQuarter, reqDTO.getExamineCycleQuarter());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getOrderType()), PerformanceOrderPO::getOrderType, reqDTO.getOrderType());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PerformanceOrderPO::getStatus, reqDTO.getStatus());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getStatusList()), PerformanceOrderPO::getStatus, reqDTO.getStatusList());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getPrincipalName()), PerformanceOrderPO::getPrincipalName, reqDTO.getPrincipalName());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getVerificationName()), PerformanceOrderPO::getVerificationName, reqDTO.getVerificationName());
        queryWrapper.eq(reqDTO.getPrincipalId() != null, PerformanceOrderPO::getPrincipalId, reqDTO.getPrincipalId());
        queryWrapper.eq(reqDTO.getVerificationId() != null, PerformanceOrderPO::getVerificationId, reqDTO.getVerificationId());
        queryWrapper.ge(reqDTO.getStartDateStart() != null, PerformanceOrderPO::getStartDate, reqDTO.getStartDateStart());
        queryWrapper.le(reqDTO.getStartDateEnd() != null, PerformanceOrderPO::getStartDate, reqDTO.getStartDateEnd());
        queryWrapper.ge(reqDTO.getDeadlineDateStart() != null, PerformanceOrderPO::getDeadlineDate, reqDTO.getDeadlineDateStart());
        queryWrapper.le(reqDTO.getDeadlineDateEnd() != null, PerformanceOrderPO::getDeadlineDate, reqDTO.getDeadlineDateEnd());
        queryWrapper.eq(reqDTO.getIsOverdueSubmit() != null, PerformanceOrderPO::getIsOverdueSubmit, reqDTO.getIsOverdueSubmit());
        if (reqDTO.getOperationBy() != null) {
            queryWrapper.and(wq -> wq.eq(PerformanceOrderPO::getPrincipalId, reqDTO.getOperationBy()).or().eq(PerformanceOrderPO::getVerificationId, reqDTO.getOperationBy()));
        }
        queryWrapper.orderByDesc(PerformanceOrderPO::getId);
        return queryWrapper;
    }

}
