package com.weifu.srm.supplier.performance.repository.po.biz;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购开票金额
 */

@Data
@TableName("biz_purchase_invoice_data")
public class BizPurchaseInvoiceDataPO {

    // businesslinecode
    private String businesslinecode;
    // 采购时间 posting_date
    private Date postingDate;
    //物料编码 material_code
    private String materialCode;
    //事业部 company
    private String company;
    // 供应商编码 vendor_num
    private String vendorNum;
    //order_unit
    private String orderUnit;
    //采购数量 pur_cnt_1d
    @TableField("pur_cnt_1d")
    private BigDecimal purCnt1d;
    // 采购额 cny_doc_curr_amt_1d
    @TableField("cny_doc_curr_amt_1d")
    private BigDecimal cnyDocCurrAmt1d;
    //cny_currency_key_1d
    @TableField("cny_currency_key_1d")
    private String cnyCurrencyKey1d;
    //doc_curr_amt_1d
    @TableField("doc_curr_amt_1d")
    private BigDecimal docCurrAmt1d;
    //currency_key
    private String currencyKey;
}
