package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderPageReqDTO;
import com.weifu.srm.supplier.performance.response.order.PerformanceOrderStatisticsRespDTO;

import java.util.List;

/**
 * <p>
 * 绩效工单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderMapperService extends IService<PerformanceOrderPO> {
    IPage<PerformanceOrderPO> listPageByQuery(IPage<PerformanceOrderPO> page, PerformanceOrderPageReqDTO reqDTO);

    PerformanceOrderPO getByOrderNo(String orderNo);

    List<PerformanceOrderPO> listByPerformanceNoAndOrderType(String performanceNo, String orderType);

    boolean validVerificationComplete(String performanceNo);

    PerformanceOrderStatisticsRespDTO statisticsByPerformanceNo(String performanceNo);

    void updatePerformanceOrder(PerformanceOrderPO po);

    Integer statisticsTodoOrder(Long operationBy);
}
