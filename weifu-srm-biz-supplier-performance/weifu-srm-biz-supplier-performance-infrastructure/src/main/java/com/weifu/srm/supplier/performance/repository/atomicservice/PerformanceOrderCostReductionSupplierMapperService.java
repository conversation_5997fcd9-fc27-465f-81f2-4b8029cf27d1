package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionSupplierPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效工单-降本目标达成-供应商范围 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderCostReductionSupplierMapperService extends IService<PerformanceOrderCostReductionSupplierPO> {
    IPage<PerformanceOrderCostReductionSupplierPO> listPageByQuery(IPage<PerformanceOrderCostReductionSupplierPO> page, PerformanceOrderNoPageReqDTO reqDTO);

    void markIsUpload(String orderNo, List<String> supplierCodeList,boolean isMaterial);

    void clearIsUpload(String orderNo,boolean isMaterial);


    void removeByOrderNo(String orderNo);

    /**
     * 判断上传要求是否一致
     */
    Integer countUploadInconsistent(String orderNo);

    void initData(String performanceNo);

    List<PerformanceOrderCostReductionSupplierPO> listByOrderNo(String orderNo);
}
