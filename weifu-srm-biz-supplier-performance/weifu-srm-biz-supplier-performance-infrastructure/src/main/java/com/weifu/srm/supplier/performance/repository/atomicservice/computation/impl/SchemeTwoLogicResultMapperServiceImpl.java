package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.SchemeTwoLogicResultMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationSchemeTwoLogicResultMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoLogicResultPO;
import org.springframework.stereotype.Service;

@Service
public class SchemeTwoLogicResultMapperServiceImpl extends ServiceImpl<ComputationSchemeTwoLogicResultMapper, ComputationSchemeTwoLogicResultPO> implements SchemeTwoLogicResultMapperService {

}
