package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceProportionSchemePageReqDTO;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceProportionSchemeQuotaRespDTO;

import java.util.List;

/**
 * <p>
 * 权重方案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceProportionSchemeMapperService extends IService<PerformanceProportionSchemePO> {
    IPage<PerformanceProportionSchemePO> listPageByQuery(IPage<PerformanceProportionSchemePO> page, PerformanceProportionSchemePageReqDTO reqDTO);

    List<PerformanceProportionSchemeQuotaRespDTO> listQuotaByProportionNo(String proportionNo);

    List<PerformanceProportionSchemePO> listByProportionNos(List<String> proportionNos);



}
