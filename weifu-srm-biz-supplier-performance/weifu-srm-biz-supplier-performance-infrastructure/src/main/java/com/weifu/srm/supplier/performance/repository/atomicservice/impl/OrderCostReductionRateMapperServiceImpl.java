package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionRatePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderCostReductionRateMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderCostReductionRateMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-降本目标达成-目标降本率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class OrderCostReductionRateMapperServiceImpl extends ServiceImpl<PerformanceOrderCostReductionRateMapper, PerformanceOrderCostReductionRatePO> implements PerformanceOrderCostReductionRateMapperService {

    @Override
    public IPage<PerformanceOrderCostReductionRatePO> listPageByQuery(IPage<PerformanceOrderCostReductionRatePO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionRatePO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<PerformanceOrderCostReductionRatePO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderCostReductionRatePO::getOrderNo, orderNo).list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderCostReductionRatePO> poList = this.lambdaQuery().eq(PerformanceOrderCostReductionRatePO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderCostReductionRatePO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    private LambdaQueryWrapper<PerformanceOrderCostReductionRatePO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderCostReductionRatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderCostReductionRatePO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderCostReductionRatePO::getId);
        return queryWrapper;
    }
}
