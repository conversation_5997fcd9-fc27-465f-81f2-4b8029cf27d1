package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderQualityPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效工单-质量指标数据
 * @Date 2024-09-20
 */
@Mapper
public interface PerformanceOrderQualityMapper extends BaseMapper<PerformanceOrderQualityPO> {
    void mergeIntoData(@Param("performanceNo") String performanceNo, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
