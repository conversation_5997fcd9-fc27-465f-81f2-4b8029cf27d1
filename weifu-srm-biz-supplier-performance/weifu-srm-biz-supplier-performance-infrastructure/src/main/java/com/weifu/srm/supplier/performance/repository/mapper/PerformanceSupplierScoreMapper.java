package com.weifu.srm.supplier.performance.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScorePageReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * @Description 供应商加减分 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceSupplierScoreMapper extends BaseMapper<PerformanceSupplierScorePO> {
    IPage<PerformanceSupplierScoreRespDTO> listPageByQuery(Page<PerformanceSupplierScoreRespDTO> page, @Param("dto") PerformanceSupplierScorePageReqDTO reqDTO);

}
