package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceEvaluateSchemePageReqDTO;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceEvaluateProportionRespDTO;

import java.util.List;

/**
 * <p>
 * 绩效评价方案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceEvaluateSchemeMapperService extends IService<PerformanceEvaluateSchemePO> {
    IPage<PerformanceEvaluateSchemePO> listPageByQuery(IPage<PerformanceEvaluateSchemePO> page, PerformanceEvaluateSchemePageReqDTO reqDTO);

    PerformanceEvaluateSchemePO getByEvaluateNo(String evaluateNo);
    List<PerformanceEvaluateProportionRespDTO> listEvaluateProportion(String evaluateNo);
}
