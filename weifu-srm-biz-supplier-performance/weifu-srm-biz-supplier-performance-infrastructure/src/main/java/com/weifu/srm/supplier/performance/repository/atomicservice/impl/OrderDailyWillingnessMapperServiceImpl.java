package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.enums.OrderDailyWillingnessEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDailyWillingnessPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderDailyWillingnessMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderDailyWillingnessMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-日常服务意愿 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class OrderDailyWillingnessMapperServiceImpl extends ServiceImpl<PerformanceOrderDailyWillingnessMapper, PerformanceOrderDailyWillingnessPO> implements PerformanceOrderDailyWillingnessMapperService {

    @Override
    public IPage<PerformanceOrderDailyWillingnessPO> listPageByQuery(IPage<PerformanceOrderDailyWillingnessPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderDailyWillingnessPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != null && reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceOrderDailyWillingnessPO> poList = this.list(queryWrapper);
            IPage<PerformanceOrderDailyWillingnessPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public List<PerformanceOrderDailyWillingnessPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderDailyWillingnessPO::getOrderNo, orderNo).list();
    }

    @Override
    public Integer countNullDailyWillingnessByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return 0;
        }
        LambdaQueryWrapper<PerformanceOrderDailyWillingnessPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceOrderDailyWillingnessPO::getOrderNo, orderNo);
        queryWrapper.and(
                andWrapper ->
                        andWrapper.notIn(
                                PerformanceOrderDailyWillingnessPO::getDailyWillingness, OrderDailyWillingnessEnum.getCodeList()).or().isNull(PerformanceOrderDailyWillingnessPO::getDailyWillingness)
        );
        return this.count(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderDailyWillingnessPO> poList = this.lambdaQuery().eq(PerformanceOrderDailyWillingnessPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderDailyWillingnessPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    @Override
    public void initData(String performanceNo) {
        this.getBaseMapper().initData(performanceNo);
    }

    private LambdaQueryWrapper<PerformanceOrderDailyWillingnessPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderDailyWillingnessPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderDailyWillingnessPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderDailyWillingnessPO::getId);
        return queryWrapper;
    }
}
