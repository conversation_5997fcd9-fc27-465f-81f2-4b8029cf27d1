package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationSchemeOneMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationSchemeTwoMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationSchemeOneMapper;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationSchemeTwoMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeOnePO;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoPO;
import org.springframework.stereotype.Service;

@Service
public class ComputationSchemeTwoMapperServiceImpl extends ServiceImpl<ComputationSchemeTwoMapper, ComputationSchemeTwoPO> implements ComputationSchemeTwoMapperService {

}
