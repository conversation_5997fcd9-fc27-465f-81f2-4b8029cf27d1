package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationInfoResultMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationInfoResultMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationInfoResultPO;
import com.weifu.srm.supplier.performance.request.precomputation.PreComputationPageReqDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationHistoryScoreResDTO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ComputationInfoResultMapperServiceImpl extends ServiceImpl<ComputationInfoResultMapper, ComputationInfoResultPO> implements ComputationInfoResultMapperService {

    @Override
    public IPage<ComputationInfoResultPO> listPageByQuery(Page<ComputationInfoResultPO> page, PreComputationPageReqDTO reqDTO) {


        LambdaQueryWrapper<ComputationInfoResultPO> lambdaQueryWrapper = getLambdaQueryWrapper(reqDTO);
        return this.page(page,lambdaQueryWrapper);
    }

    @Override
    public List<ComputationInfoResultPO> performanceResultList(PreComputationPageReqDTO reqDTO) {
        LambdaQueryWrapper<ComputationInfoResultPO> lambdaQueryWrapper = getLambdaQueryWrapper(reqDTO);
        return this.list(lambdaQueryWrapper);
    }

    private static LambdaQueryWrapper<ComputationInfoResultPO> getLambdaQueryWrapper(PreComputationPageReqDTO reqDTO) {
        LambdaQueryWrapper<ComputationInfoResultPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotEmpty(reqDTO.getPerformanceNo()), ComputationInfoResultPO::getPerformanceNo, reqDTO.getPerformanceNo());
        Boolean weifu = reqDTO.getWeifu();
        // 威孚端
        if(weifu){
            // 供应商编码
            wrapper.like(ObjectUtil.isNotEmpty(reqDTO.getSupplierCode()),ComputationInfoResultPO::getSupplierCode,reqDTO.getSupplierCode());
        }else {
            String supplierCode = reqDTO.getSupplierCode();
            wrapper.eq(ComputationInfoResultPO::getSupplierCode,supplierCode);
        }
        // 绩效结果编号
        wrapper.like(ObjectUtil.isNotEmpty(reqDTO.getComputationResultNo()),ComputationInfoResultPO::getComputationResultNo,reqDTO.getComputationResultNo())
                // 供应商名称
                .like(ObjectUtil.isNotEmpty(reqDTO.getSupplierName()),ComputationInfoResultPO::getSupplierName,reqDTO.getSupplierName())
                // 绩效考核品类
                .eq(ObjectUtil.isNotEmpty(reqDTO.getCategoryCode()),ComputationInfoResultPO::getTwoLevelCategoryCode,reqDTO.getCategoryCode())
                // 绩效类型
                .eq(ObjectUtil.isNotEmpty(reqDTO.getPerformanceType()),ComputationInfoResultPO::getPerformanceType,
                        reqDTO.getPerformanceType())
                // 考核周期
                .eq(ObjectUtil.isNotEmpty(reqDTO.getExamineCycleYear()),ComputationInfoResultPO::getExamineCycleYear,
                        reqDTO.getExamineCycleYear())
                .eq(ObjectUtil.isNotEmpty(reqDTO.getExamineCycleQuarter()),ComputationInfoResultPO::getExamineCycleQuarter,
                        reqDTO.getExamineCycleQuarter())
                // 发布时间
                .between(ObjectUtil.isNotEmpty(reqDTO.getPublishTimeStart()),ComputationInfoResultPO::getPublishTime,
                        reqDTO.getPublishTimeStart(), reqDTO.getPublishTimeEnd())
                // 实际得分
                .ge(ObjectUtil.isNotEmpty(reqDTO.getScoreMin()),ComputationInfoResultPO::getSupplierScore,reqDTO.getScoreMin())
                .le(ObjectUtil.isNotEmpty(reqDTO.getScoreMax()),ComputationInfoResultPO::getSupplierScore,reqDTO.getScoreMax())
                .orderByDesc(ComputationInfoResultPO::getCreateTime);
        return wrapper;
    }

    public Page<ComputationInfoResultPO> queryPerformanceResultList(PreComputationPageReqDTO reqDTO){
        Page<ComputationInfoResultPO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        return this.page(page, getLambdaQueryWrapper(reqDTO));
    }

    @Override
    public List<ComputationHistoryScoreResDTO> findNewYearScoreList(List<String> supplierCode) {
        return this.baseMapper.findNewYearScoreList(supplierCode);
    }

    @Override
    public List<ComputationInfoResultPO> findSupplierHistoryScoreList(List<String> supplierCodes) {
        LambdaQueryWrapper<ComputationInfoResultPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ComputationInfoResultPO::getSupplierCode,supplierCodes);
        return this.list(wrapper);
    }
}
