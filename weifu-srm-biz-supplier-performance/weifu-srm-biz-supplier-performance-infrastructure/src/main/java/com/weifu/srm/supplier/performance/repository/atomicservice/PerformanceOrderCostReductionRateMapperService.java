package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionRatePO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效工单-降本目标达成-目标降本率 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderCostReductionRateMapperService extends IService<PerformanceOrderCostReductionRatePO> {

    IPage<PerformanceOrderCostReductionRatePO> listPageByQuery(IPage<PerformanceOrderCostReductionRatePO> page, PerformanceOrderNoPageReqDTO reqDTO);

    List<PerformanceOrderCostReductionRatePO> listByOrderNo(String orderNo);

    void removeByOrderNo(String orderNo);

}
