package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderCostReductionSupplierPO;
import com.weifu.srm.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * @Description 绩效工单-降本目标达成-供应商范围 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceOrderCostReductionSupplierMapper extends BaseMapper<PerformanceOrderCostReductionSupplierPO> {
    Integer countUploadInconsistent(String orderNo);
    void initData(@Param("performanceNo") String performanceNo);
}
