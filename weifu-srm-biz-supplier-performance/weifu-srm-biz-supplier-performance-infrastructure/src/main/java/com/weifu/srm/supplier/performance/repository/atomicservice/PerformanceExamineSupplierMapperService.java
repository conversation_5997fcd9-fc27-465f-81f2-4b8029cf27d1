package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExamineSupplierPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import com.weifu.srm.supplier.performance.request.examine.PerformanceExamineSupplierPageReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效考核-供应商设定 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceExamineSupplierMapperService extends IService<PerformanceExamineSupplierPO> {
    IPage<PerformanceExamineSupplierPO> listPageByQuery(IPage<PerformanceExamineSupplierPO> page, PerformanceExamineSupplierPageReqDTO reqDTO);

    List<PerformanceExamineSupplierPO> listByPerformanceNo(String performanceNo);
    List<PerformanceExamineSupplierPO> listByPerformanceNoAndNoSetting(String performanceNo);

    Integer countByPerformanceNo(String performanceNo);

    Integer countNullCategoryByPerformanceNo(String performanceNo);

    Integer countByPerformanceNoAndSupplierCode(String performanceNo, String supplierCode);

    List<PerformanceExamineSupplierPO> listByPerformanceNoAndCategoryCodes(String performanceNo, List<String> categoryCodes);

    List<PerformanceOrderPO> listCreateQualityOrder(String performanceNo, List<String> twoCategoryCodeList);

    List<PerformanceOrderPO> listCreateBusinessAndProjectOrder(String performanceNo);
}
