package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDailyWillingnessPO;
import com.weifu.srm.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效工单-日常服务意愿 Mapper 接口
 * @Date 2024-09-20
 */
@Mapper
public interface PerformanceOrderDailyWillingnessMapper extends BaseMapper<PerformanceOrderDailyWillingnessPO> {
    void initData(@Param("performanceNo") String performanceNo);
}
