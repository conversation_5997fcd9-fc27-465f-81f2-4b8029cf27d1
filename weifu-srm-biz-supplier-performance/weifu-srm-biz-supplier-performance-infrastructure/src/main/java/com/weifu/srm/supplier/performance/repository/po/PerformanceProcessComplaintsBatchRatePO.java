package com.weifu.srm.supplier.performance.repository.po;

import com.weifu.srm.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * @Description 制程投诉批次率
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("performance_process_complaints_batch_rate")
public class PerformanceProcessComplaintsBatchRatePO extends BaseEntity {

    /**一级品类编码*/
    private String categoryCode;

    /**一级品类名称*/
    private String categoryName;

    /**一级品类英文名称*/
    private String categoryNameEn;


}
