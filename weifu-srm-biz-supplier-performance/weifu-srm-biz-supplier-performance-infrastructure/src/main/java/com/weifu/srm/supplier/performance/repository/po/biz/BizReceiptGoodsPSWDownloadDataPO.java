package com.weifu.srm.supplier.performance.repository.po.biz;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 收货数据加psw签署时间
 */
@Data
@TableName("biz_receipt_goods_psw_download_data")
public class BizReceiptGoodsPSWDownloadDataPO extends BaseEntity implements Serializable {
    /** 绩效编号 */
    private String performanceNo;

    /**
     * 采购凭证号
     */
    @ApiModelProperty(name = "采购凭证号", notes = "")
    private String purchaseVoucherNo;
    /**
     * 采购凭证的项目编号
     */
    @ApiModelProperty(name = "采购凭证的项目编号", notes = "")
    private String purchaseVoucherItemNo;
    /**
     * 帐户分配的顺序编号
     */
    @ApiModelProperty(name = "帐户分配的顺序编号", notes = "")
    private String accountAssignedSequenceNo;
    /**
     * 步骤编号
     */
    @ApiModelProperty(name = "步骤编号", notes = "")
    private String stepNo;
    /**
     * 条件计数器
     */
    @ApiModelProperty(name = "条件计数器", notes = "")
    private String conditionalCounter;
    /**
     * 参考凭证编号
     */
    @ApiModelProperty(name = "参考凭证编号", notes = "")
    private String referenceCertificateNo;
    /**
     * 交货单的计量单位数量
     */
    @ApiModelProperty(name = "交货单的计量单位数量", notes = "")
    private String unitQuantityDeliveryOrderNum;
    /**
     * 交货单的计量单位
     */
    @ApiModelProperty(name = "交货单的计量单位", notes = "")
    private String unitQuantityDeliveryOrder;
    /**
     * 业务处理/事件类型,采购订单的历史记录
     */
    @ApiModelProperty(name = "业务处理/事件类型,采购订单的历史记录", notes = "")
    private String businessHistory;
    /**
     * 会计年度
     */
    @ApiModelProperty(name = "会计年度", notes = "")
    private String accountingYear;
    /**
     * 会计凭证编号
     */
    @ApiModelProperty(name = "会计凭证编号", notes = "")
    private String accountingDocumentNo;
    /**
     * 物料凭证中的项目
     */
    @ApiModelProperty(name = "物料凭证中的项目", notes = "")
    private String materialCertificateItem;
    /**
     * 采购订单历史分类
     */
    @ApiModelProperty(name = "采购订单历史分类", notes = "")
    private String purchaseOrderHistoryClassification;
    /**
     * 移动类型（库存管理）
     */
    @ApiModelProperty(name = "移动类型（库存管理）", notes = "")
    private String movementType;
    /**
     * 凭证中的过帐日期
     */
    @ApiModelProperty(name = "凭证中的过帐日期", notes = "")
    private Date voucherPostingDate;
    /**
     * 数量
     */
    @ApiModelProperty(name = "数量", notes = "")
    private Long num;
    /**
     * 按本位币计的金额
     */
    @ApiModelProperty(name = "按本位币计的金额", notes = "")
    private String amountStandardCurrency;
    /**
     * 凭证货币金额
     */
    @ApiModelProperty(name = "凭证货币金额", notes = "")
    private String currencyAmountDocument;
    /**
     * 货币码
     */
    @ApiModelProperty(name = "货币码", notes = "")
    private String currencyCode;
    /**
     * 物料号
     */
    @ApiModelProperty(name = "物料号", notes = "")
    private String materialNo;
    /**
     * 工厂
     */
    @ApiModelProperty(name = "工厂", notes = "")
    private String factory;
    /**
     * 借方/贷方标识
     */
    @ApiModelProperty(name = "借方/贷方标识", notes = "")
    private String identification;
    /**
     * 供应商或债权人的帐号
     */
    @ApiModelProperty(name = "供应商或债权人的帐号", notes = "")
    private String accountNo;
    /**
     * 条件类型
     */
    @ApiModelProperty(name = "条件类型", notes = "")
    private String conditionType;
    /**
     * 是否冲销交货单
     */
    @ApiModelProperty(name = "是否冲销交货单", notes = "")
    private String writtenOffDeliveryOrder;
    /**
     * 参考凭证的凭证号
     */
    @ApiModelProperty(name = "参考凭证的凭证号", notes = "")
    private String referenceCertificateCertificateNo;
    /**
     * 参考凭证项目
     */
    @ApiModelProperty(name = "参考凭证项目", notes = "")
    private String referenceCertificateItem;
    /**
     * 参考凭证会计年度
     */
    @ApiModelProperty(name = "参考凭证会计年度", notes = "")
    private String referenceCertificateAccountingYear;
    /**
     * 贸易合同：贸易合同编号
     */
    @ApiModelProperty(name = "贸易合同：贸易合同编号", notes = "")
    private String tradingContractNo;
    /**
     * 交易合同: 外部凭证编号
     */
    @ApiModelProperty(name = "交易合同: 外部凭证编号", notes = "")
    private String externalCertificateNo;

    /**
     * PSW签署时间
     */
    @ApiModelProperty(name = "PSW签署时间", notes = "")
    private Date pswTime;

    @ApiModelProperty("三级品类编码")
    private String threeCategoryCode;

    @ApiModelProperty("三级品类编码")
    private String twoCategoryCode;

    @ApiModelProperty("一级品类编码")
    private String oneCategoryCode;
}