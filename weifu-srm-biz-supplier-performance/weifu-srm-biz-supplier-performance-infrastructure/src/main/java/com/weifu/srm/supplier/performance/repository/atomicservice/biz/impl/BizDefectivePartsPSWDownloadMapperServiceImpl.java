package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizDefectivePartsPSWDownloadMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizDefectivePartsPSWMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizDefectivePartsPSWDataMapper;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizDefectivePartsPSWDownloadDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizDefectivePartsPSWDataPO;
import com.weifu.srm.supplier.performance.repository.po.biz.BizDefectivePartsPSWDownloadDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BizDefectivePartsPSWDownloadMapperServiceImpl extends ServiceImpl<BizDefectivePartsPSWDownloadDataMapper, BizDefectivePartsPSWDownloadDataPO> implements BizDefectivePartsPSWDownloadMapperService {



}
