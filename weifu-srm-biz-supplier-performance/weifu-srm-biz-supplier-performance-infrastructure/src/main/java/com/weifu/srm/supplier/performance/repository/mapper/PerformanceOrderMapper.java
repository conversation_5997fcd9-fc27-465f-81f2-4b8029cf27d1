package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.response.order.PerformanceOrderStatisticsRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * @Description 绩效工单 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceOrderMapper extends BaseMapper<PerformanceOrderPO> {
    PerformanceOrderStatisticsRespDTO statisticsByPerformanceNo(@Param("performanceNo") String performanceNo);

    Integer statisticsTodoOrder(@Param("operationBy") Long operationBy);
}
