package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizReceiptGoodsPSWDownloadMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizReceiptGoodsPSWDownloadDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizReceiptGoodsPSWDownloadDataPO;
import org.springframework.stereotype.Service;

@Service
public class BizReceiptGoodsDataPSWDownloadMapperServiceImpl extends ServiceImpl<BizReceiptGoodsPSWDownloadDataMapper, BizReceiptGoodsPSWDownloadDataPO> implements BizReceiptGoodsPSWDownloadMapperService {
}
