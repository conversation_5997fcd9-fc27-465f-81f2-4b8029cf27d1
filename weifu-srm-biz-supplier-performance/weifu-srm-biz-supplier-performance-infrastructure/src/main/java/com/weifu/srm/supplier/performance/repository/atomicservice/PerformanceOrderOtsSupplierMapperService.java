package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsSupplierPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效工单-ots及时送样率-供应商范围 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderOtsSupplierMapperService extends IService<PerformanceOrderOtsSupplierPO> {
    IPage<PerformanceOrderOtsSupplierPO> listPageByQuery(IPage<PerformanceOrderOtsSupplierPO> page, PerformanceOrderNoPageReqDTO reqDTO);


    List<PerformanceOrderOtsSupplierPO> listByOrderNo(String orderNo);

    void markIsUpload(String orderNo, List<String> supplierCodeList);

    void clearIsUpload(String orderNo);

    void removeByOrderNo(String orderNo);

    void initData(String performanceNo);
}
