package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationLogicSupplierScoreMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationLogicSupplierScoreMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationLogicSupplierScorePO;
import org.springframework.stereotype.Service;

@Service
public class ComputationLogicSupplierScoreMapperServiceImpl extends ServiceImpl<ComputationLogicSupplierScoreMapper, ComputationLogicSupplierScorePO> implements ComputationLogicSupplierScoreMapperService {

}
