package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceMaterialHandlerProblemPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceMaterialHandlerProblemMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceMaterialHandlerProblemMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.rule.PerformanceMaterialHandlerProblemPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 原材料热处理表面处理质量问题清单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class MaterialHandlerProblemMapperServiceImpl extends ServiceImpl<PerformanceMaterialHandlerProblemMapper, PerformanceMaterialHandlerProblemPO> implements PerformanceMaterialHandlerProblemMapperService {

    @Override
    public IPage<PerformanceMaterialHandlerProblemPO> listPageByQuery(IPage<PerformanceMaterialHandlerProblemPO> page, PerformanceMaterialHandlerProblemPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceMaterialHandlerProblemPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        if (reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceMaterialHandlerProblemPO> poList = this.list(queryWrapper);
            IPage<PerformanceMaterialHandlerProblemPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public void updateIsNeedExamine(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        List<List<Long>> partionList = Lists.partition(ids, 500);
        for (List<Long> partition : partionList) {
            this.lambdaUpdate().in(PerformanceMaterialHandlerProblemPO::getId, partition).set(PerformanceMaterialHandlerProblemPO::getIsExamined, YesOrNoEnum.YES.getCode()).update();
        }
    }

    private LambdaQueryWrapper<PerformanceMaterialHandlerProblemPO> buildLambdaQueryWrapper(PerformanceMaterialHandlerProblemPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceMaterialHandlerProblemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getSupplierCode()), PerformanceMaterialHandlerProblemPO::getSupplierCode, reqDTO.getSupplierCode());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getSupplierName()), PerformanceMaterialHandlerProblemPO::getSupplierName, reqDTO.getSupplierName());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getSupplierNameEn()), PerformanceMaterialHandlerProblemPO::getSupplierNameEn, reqDTO.getSupplierNameEn());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getMaterialCode()), PerformanceMaterialHandlerProblemPO::getMaterialCode, reqDTO.getMaterialCode());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCreateName()), PerformanceMaterialHandlerProblemPO::getCreateName, reqDTO.getCreateName());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getCategoryCodeList()), PerformanceMaterialHandlerProblemPO::getCategoryCode, reqDTO.getCategoryCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(reqDTO.getProblemLocationList()), PerformanceMaterialHandlerProblemPO::getProblemLocation, reqDTO.getProblemLocationList());
        queryWrapper.ge(reqDTO.getCreateTimeStart() != null, PerformanceMaterialHandlerProblemPO::getCreateTime, reqDTO.getCreateTimeStart());
        queryWrapper.le(reqDTO.getCreateTimeEnd() != null, PerformanceMaterialHandlerProblemPO::getCreateTime, reqDTO.getCreateTimeEnd());
        queryWrapper.ge(reqDTO.getFeedbackDateStart() != null, PerformanceMaterialHandlerProblemPO::getFeedbackDate, reqDTO.getFeedbackDateStart());
        queryWrapper.le(reqDTO.getFeedbackDateEnd() != null, PerformanceMaterialHandlerProblemPO::getFeedbackDate, reqDTO.getFeedbackDateEnd());
        queryWrapper.ge(reqDTO.getCloseDateStart() != null, PerformanceMaterialHandlerProblemPO::getCloseDate, reqDTO.getCloseDateStart());
        queryWrapper.le(reqDTO.getCloseDateEnd() != null, PerformanceMaterialHandlerProblemPO::getCloseDate, reqDTO.getCloseDateEnd());
        queryWrapper.orderByDesc(PerformanceMaterialHandlerProblemPO::getId);
        return queryWrapper;
    }


}
