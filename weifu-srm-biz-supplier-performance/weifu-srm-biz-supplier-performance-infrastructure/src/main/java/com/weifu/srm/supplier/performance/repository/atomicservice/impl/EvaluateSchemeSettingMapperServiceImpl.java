package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemeSettingPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceEvaluateSchemeSettingMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceEvaluateSchemeSettingMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceEvaluateSchemeSettingReqDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效评价方案详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class EvaluateSchemeSettingMapperServiceImpl extends ServiceImpl<PerformanceEvaluateSchemeSettingMapper, PerformanceEvaluateSchemeSettingPO> implements PerformanceEvaluateSchemeSettingMapperService {

    @Override
    public List<PerformanceEvaluateSchemeSettingPO> listByEvaluateNo(String evaluateNo) {
        return this.lambdaQuery().eq(PerformanceEvaluateSchemeSettingPO::getEvaluateNo, evaluateNo).list();
    }

    @Override
    public List<PerformanceEvaluateSchemeSettingPO> listSetting(PerformanceEvaluateSchemeSettingReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceEvaluateSchemeSettingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getOneLevelCategoryCode()), PerformanceEvaluateSchemeSettingPO::getOneLevelCategoryCode, reqDTO.getOneLevelCategoryCode());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getTwoLevelCategoryCode()), PerformanceEvaluateSchemeSettingPO::getTwoLevelCategoryCode, reqDTO.getTwoLevelCategoryCode());
        queryWrapper.eq(PerformanceEvaluateSchemeSettingPO::getEvaluateNo, reqDTO.getEvaluateNo());
        queryWrapper.orderByDesc(PerformanceEvaluateSchemeSettingPO::getId);
        return this.list(queryWrapper);
    }
}
