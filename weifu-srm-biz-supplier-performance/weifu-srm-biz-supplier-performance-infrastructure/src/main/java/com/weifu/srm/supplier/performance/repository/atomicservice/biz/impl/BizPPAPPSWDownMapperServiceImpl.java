package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizPPAPPSWDownMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizPPAPPSWDownloadDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizPPAPPSWDownloadDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BizPPAPPSWDownMapperServiceImpl extends ServiceImpl<BizPPAPPSWDownloadDataMapper, BizPPAPPSWDownloadDataPO> implements BizPPAPPSWDownMapperService {
}
