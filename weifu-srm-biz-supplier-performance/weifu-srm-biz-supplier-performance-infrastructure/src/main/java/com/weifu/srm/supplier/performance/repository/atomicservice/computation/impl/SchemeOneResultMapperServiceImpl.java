package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.SchemeOneResultMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.SchemeOneResultMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeOneResultPO;
import org.springframework.stereotype.Service;

@Service
public class SchemeOneResultMapperServiceImpl extends ServiceImpl<SchemeOneResultMapper, ComputationSchemeOneResultPO> implements SchemeOneResultMapperService {

}
