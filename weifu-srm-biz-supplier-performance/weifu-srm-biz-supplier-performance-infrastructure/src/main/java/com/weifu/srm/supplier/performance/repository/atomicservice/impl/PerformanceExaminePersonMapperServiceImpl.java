package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePersonPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceExaminePersonMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceExaminePersonMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效考核-人员设定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceExaminePersonMapperServiceImpl extends ServiceImpl<PerformanceExaminePersonMapper, PerformanceExaminePersonPO> implements PerformanceExaminePersonMapperService {

    @Override
    public List<PerformanceExaminePersonPO> listByPerformanceNo(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExaminePersonPO::getPerformanceNo, performanceNo).list();
    }

    @Override
    public PerformanceExaminePersonPO getByCategoryCodeAndPerformanceNo(String categoryCode, String performanceNo) {
        return this.getOne(Wrappers.<PerformanceExaminePersonPO>lambdaQuery().eq(PerformanceExaminePersonPO::getPerformanceNo, performanceNo).eq(PerformanceExaminePersonPO::getCategoryCode, categoryCode));
    }

    @Override
    public List<PerformanceExaminePersonPO> listByPerformanceNoAndCategoryCodes(String performanceNo, List<String> categoryCodes) {
        return this.lambdaQuery().eq(PerformanceExaminePersonPO::getPerformanceNo, performanceNo).in(PerformanceExaminePersonPO::getCategoryCode, categoryCodes).list();
    }

    @Override
    public Integer countNullPersonByPerformanceNo(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExaminePersonPO::getPerformanceNo, performanceNo)
                .and(wq ->wq.isNull(PerformanceExaminePersonPO::getCpeId).or().isNull(PerformanceExaminePersonPO::getCpeMasterId))
                .count();
    }
}
