package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemeTwoPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceProportionSchemeTwoMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceProportionSchemeTwoMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 权重方案-指标权重设定-二级 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceProportionSchemeTwoMapperServiceImpl extends ServiceImpl<PerformanceProportionSchemeTwoMapper, PerformanceProportionSchemeTwoPO> implements PerformanceProportionSchemeTwoMapperService {

}
