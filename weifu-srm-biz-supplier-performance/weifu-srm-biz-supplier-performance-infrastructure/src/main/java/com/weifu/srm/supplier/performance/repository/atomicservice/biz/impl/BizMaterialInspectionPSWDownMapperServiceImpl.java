package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizMaterialInspectionPSWDownMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizMaterialInspectionPSWMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizMaterialInspectionPSWDataMapper;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizMaterialInspectionPSWDownloadDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizMaterialInspectionPSWDataPO;
import com.weifu.srm.supplier.performance.repository.po.biz.BizMaterialInspectionPSWDownloadDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BizMaterialInspectionPSWDownMapperServiceImpl extends ServiceImpl<BizMaterialInspectionPSWDownloadDataMapper, BizMaterialInspectionPSWDownloadDataPO> implements BizMaterialInspectionPSWDownMapperService {
}
