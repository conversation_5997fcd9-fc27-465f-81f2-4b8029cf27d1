package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizReceiptGoodsDataMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizReceiptGoodsDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizReceiptGoodsDataPO;
import org.springframework.stereotype.Service;

@Service
public class BizReceiptGoodsDataMapperServiceImpl extends ServiceImpl<BizReceiptGoodsDataMapper, BizReceiptGoodsDataPO> implements BizReceiptGoodsDataMapperService {
}
