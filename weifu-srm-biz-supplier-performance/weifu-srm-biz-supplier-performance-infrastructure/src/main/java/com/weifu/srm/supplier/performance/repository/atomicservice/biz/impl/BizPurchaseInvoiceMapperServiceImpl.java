package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizPurchaseInvoiceMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizPurchaseInvoiceDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizPurchaseInvoiceDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 绩效考核 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
@RequiredArgsConstructor
public class BizPurchaseInvoiceMapperServiceImpl extends ServiceImpl<BizPurchaseInvoiceDataMapper, BizPurchaseInvoiceDataPO> implements BizPurchaseInvoiceMapperService {

}
