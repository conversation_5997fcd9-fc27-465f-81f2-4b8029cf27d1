package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceSupplierScoreMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceSupplierScoreMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScorePageReqDTO;
import com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 供应商加减分 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceSupplierScoreMapperServiceImpl extends ServiceImpl<PerformanceSupplierScoreMapper, PerformanceSupplierScorePO> implements PerformanceSupplierScoreMapperService {


    @Override
    public IPage<PerformanceSupplierScoreRespDTO> listPageByQuery(Page<PerformanceSupplierScoreRespDTO> page, PerformanceSupplierScorePageReqDTO reqDTO) {
        return this.getBaseMapper().listPageByQuery(page,reqDTO);
    }

    @Override
    public boolean validRepeatData(Long id, String firstLevel, Long quotaId, String supplierCode, String happenTime) {
        Integer count = this.lambdaQuery().ne(id != null, PerformanceSupplierScorePO::getId, id).eq(PerformanceSupplierScorePO::getFirstLevel, firstLevel)
                .eq(PerformanceSupplierScorePO::getQuotaId, quotaId).eq(PerformanceSupplierScorePO::getSupplierCode, supplierCode)
                .eq(PerformanceSupplierScorePO::getHappenTime, happenTime).count();
        return count > 0;
    }
}
