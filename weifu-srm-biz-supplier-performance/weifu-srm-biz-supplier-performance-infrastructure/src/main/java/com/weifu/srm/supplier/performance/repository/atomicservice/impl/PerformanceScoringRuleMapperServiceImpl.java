package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.weifu.srm.supplier.performance.repository.po.PerformanceScoringRulePO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceScoringRuleMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceScoringRuleMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 绩效指标算分规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceScoringRuleMapperServiceImpl extends ServiceImpl<PerformanceScoringRuleMapper, PerformanceScoringRulePO> implements PerformanceScoringRuleMapperService {

}
