package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizDefectivePartsPSWMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizDefectivePartsPSWDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizDefectivePartsPSWDataPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BizDefectivePartsPSWMapperServiceImpl extends ServiceImpl<BizDefectivePartsPSWDataMapper, BizDefectivePartsPSWDataPO> implements BizDefectivePartsPSWMapperService {



}
