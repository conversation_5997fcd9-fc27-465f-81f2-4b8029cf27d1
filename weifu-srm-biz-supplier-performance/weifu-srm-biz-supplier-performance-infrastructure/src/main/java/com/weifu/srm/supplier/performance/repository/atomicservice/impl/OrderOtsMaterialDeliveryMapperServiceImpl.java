package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsMaterialDeliveryPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceOrderOtsMaterialDeliveryMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceOrderOtsMaterialDeliveryMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 绩效工单-ots及时送样率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class OrderOtsMaterialDeliveryMapperServiceImpl extends ServiceImpl<PerformanceOrderOtsMaterialDeliveryMapper, PerformanceOrderOtsMaterialDeliveryPO> implements PerformanceOrderOtsMaterialDeliveryMapperService {
    @Override
    public IPage<PerformanceOrderOtsMaterialDeliveryPO> listPageByQuery(IPage<PerformanceOrderOtsMaterialDeliveryPO> page, PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderOtsMaterialDeliveryPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<PerformanceOrderOtsMaterialDeliveryPO> listByOrderNo(String orderNo) {
        return this.lambdaQuery().eq(PerformanceOrderOtsMaterialDeliveryPO::getOrderNo, orderNo).list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByOrderNo(String orderNo) {
        List<PerformanceOrderOtsMaterialDeliveryPO> poList = this.lambdaQuery().eq(PerformanceOrderOtsMaterialDeliveryPO::getOrderNo, orderNo).list();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<Long> ids = poList.stream().map(PerformanceOrderOtsMaterialDeliveryPO::getId).collect(Collectors.toList());
        this.removeByIds(ids);
    }

    private LambdaQueryWrapper<PerformanceOrderOtsMaterialDeliveryPO> buildLambdaQueryWrapper(PerformanceOrderNoPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceOrderOtsMaterialDeliveryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getOrderNo()), PerformanceOrderOtsMaterialDeliveryPO::getOrderNo, reqDTO.getOrderNo());
        queryWrapper.orderByAsc(PerformanceOrderOtsMaterialDeliveryPO::getId);
        return queryWrapper;
    }
}
