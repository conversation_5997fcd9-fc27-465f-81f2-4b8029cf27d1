package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationInfoMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationInfoMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationInfoPO;
import org.springframework.stereotype.Service;

@Service
public class ComputationInfoMapperServiceImpl extends ServiceImpl<ComputationInfoMapper, ComputationInfoPO> implements ComputationInfoMapperService {

}
