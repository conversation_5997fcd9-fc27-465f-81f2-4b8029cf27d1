package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.weifu.srm.supplier.performance.repository.po.PerformanceExaminePersonPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 绩效考核-人员设定 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceExaminePersonMapperService extends IService<PerformanceExaminePersonPO> {
    List<PerformanceExaminePersonPO> listByPerformanceNo(String performanceNo);

    PerformanceExaminePersonPO getByCategoryCodeAndPerformanceNo(String categoryCode, String performanceNo);

    List<PerformanceExaminePersonPO> listByPerformanceNoAndCategoryCodes(String performanceNo,List<String> categoryCodes);

    Integer countNullPersonByPerformanceNo(String performanceNo);
}
