package com.weifu.srm.supplier.performance.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.repository.constants.PerformanceCommonConstants;
import com.weifu.srm.supplier.performance.repository.po.PerformanceExamineSupplierPO;
import com.weifu.srm.supplier.performance.repository.mapper.PerformanceExamineSupplierMapper;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceExamineSupplierMapperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderPO;
import com.weifu.srm.supplier.performance.request.examine.PerformanceExamineSupplierPageReqDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 绩效考核-供应商设定 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Service
public class PerformanceExamineSupplierMapperServiceImpl extends ServiceImpl<PerformanceExamineSupplierMapper, PerformanceExamineSupplierPO> implements PerformanceExamineSupplierMapperService {

    @Override
    public IPage<PerformanceExamineSupplierPO> listPageByQuery(IPage<PerformanceExamineSupplierPO> page, PerformanceExamineSupplierPageReqDTO reqDTO) {
        LambdaQueryWrapper<PerformanceExamineSupplierPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceExamineSupplierPO::getPerformanceNo, reqDTO.getPerformanceNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getSupplierCode()), PerformanceExamineSupplierPO::getSupplierCode, reqDTO.getSupplierCode());
        queryWrapper.eq(Boolean.TRUE.equals(reqDTO.getIsHaveSetting()), PerformanceExamineSupplierPO::getIsSetting, YesOrNoEnum.YES.getCode());
        queryWrapper.eq(Boolean.FALSE.equals(reqDTO.getIsHaveSetting()), PerformanceExamineSupplierPO::getIsSetting, YesOrNoEnum.NO.getCode());
        if (reqDTO.getPageSize() != PerformanceCommonConstants.NO_PAGE_SIZE) {
            return this.page(page, queryWrapper);
        } else {
            List<PerformanceExamineSupplierPO> poList = this.list(queryWrapper);
            IPage<PerformanceExamineSupplierPO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public List<PerformanceExamineSupplierPO> listByPerformanceNo(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo).list();
    }

    @Override
    public List<PerformanceExamineSupplierPO> listByPerformanceNoAndNoSetting(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo).ne(PerformanceExamineSupplierPO::getIsSetting, YesOrNoEnum.YES.getCode()).list();
    }

    @Override
    public Integer countByPerformanceNo(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo).count();
    }

    @Override
    public Integer countNullCategoryByPerformanceNo(String performanceNo) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo)
                .ne(PerformanceExamineSupplierPO::getIsSetting, YesOrNoEnum.YES.getCode()).count();
    }

    @Override
    public Integer countByPerformanceNoAndSupplierCode(String performanceNo, String supplierCode) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo).eq(PerformanceExamineSupplierPO::getSupplierCode, supplierCode).count();

    }

    @Override
    public List<PerformanceExamineSupplierPO> listByPerformanceNoAndCategoryCodes(String performanceNo, List<String> categoryCodes) {
        return this.lambdaQuery().eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo).in(PerformanceExamineSupplierPO::getProcurementCategoryCode, categoryCodes).list();
    }

    @Override
    public List<PerformanceOrderPO> listCreateQualityOrder(String performanceNo, List<String> twoCategoryCodeList) {
        return this.getBaseMapper().listCreateQualityOrder(performanceNo, twoCategoryCodeList);
    }

    @Override
    public List<PerformanceOrderPO> listCreateBusinessAndProjectOrder(String performanceNo) {
        return this.getBaseMapper().listCreateBusinessAndProjectOrder(performanceNo);
    }
}
