package com.weifu.srm.supplier.performance.repository.atomicservice.biz.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.BizMaterialInspectionDataMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.biz.BizMaterialInspectionDataMapper;
import com.weifu.srm.supplier.performance.repository.po.biz.BizMaterialInspectionDataPO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BizMaterialInspectionDataMapperServiceImpl extends ServiceImpl<BizMaterialInspectionDataMapper, BizMaterialInspectionDataPO> implements BizMaterialInspectionDataMapperService {

    @Override
    public List<BizMaterialInspectionDataPO> selectListDay() {
        return this.baseMapper.selectListDay();
    }
}
