package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderDeliverPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsMaterialDeliveryPO;
import com.weifu.srm.supplier.performance.request.order.PerformanceOrderNoPageReqDTO;

/**
 * <p>
 * 绩效工单-交付指标上传 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceOrderDeliverMapperService extends IService<PerformanceOrderDeliverPO> {

    IPage<PerformanceOrderDeliverPO> listPageByQuery(IPage<PerformanceOrderDeliverPO> page, PerformanceOrderNoPageReqDTO reqDTO);

    void removeByOrderNo(String orderNo);

    Integer countByOrderNo(String orderNo);
}
