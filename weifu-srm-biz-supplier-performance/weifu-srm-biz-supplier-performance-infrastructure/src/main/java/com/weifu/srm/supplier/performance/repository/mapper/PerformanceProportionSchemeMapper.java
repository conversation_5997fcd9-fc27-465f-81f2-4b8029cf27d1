package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceProportionSchemePO;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.supplier.performance.response.scheme.PerformanceProportionSchemeQuotaRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @Description 权重方案 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceProportionSchemeMapper extends BaseMapper<PerformanceProportionSchemePO> {
    List<PerformanceProportionSchemeQuotaRespDTO> listQuotaByProportionNo(@Param("proportionNo") String proportionNo);
}
