package com.weifu.srm.supplier.performance.repository.atomicservice.computation.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationSupplierScoreMapperService;
import com.weifu.srm.supplier.performance.repository.mapper.computation.ComputationSupplierScoreMapper;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSupplierScorePO;
import org.springframework.stereotype.Service;

@Service
public class ComputaionSupplierScoreMapperServiceImpl extends ServiceImpl<ComputationSupplierScoreMapper, ComputationSupplierScorePO> implements ComputationSupplierScoreMapperService {

}
