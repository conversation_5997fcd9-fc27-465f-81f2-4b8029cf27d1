package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.weifu.srm.supplier.performance.repository.po.PerformanceEvaluateSchemeSettingPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.scheme.PerformanceEvaluateSchemeSettingReqDTO;

import java.util.List;

/**
 * <p>
 * 绩效评价方案详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceEvaluateSchemeSettingMapperService extends IService<PerformanceEvaluateSchemeSettingPO> {
    List<PerformanceEvaluateSchemeSettingPO> listByEvaluateNo(String evaluateNo);
    List<PerformanceEvaluateSchemeSettingPO> listSetting(PerformanceEvaluateSchemeSettingReqDTO reqDTO);
}
