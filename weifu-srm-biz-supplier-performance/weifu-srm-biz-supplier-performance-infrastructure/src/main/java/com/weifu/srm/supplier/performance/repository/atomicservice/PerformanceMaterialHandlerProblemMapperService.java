package com.weifu.srm.supplier.performance.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.weifu.srm.supplier.performance.repository.po.PerformanceMaterialHandlerProblemPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.supplier.performance.request.rule.PerformanceMaterialHandlerProblemPageReqDTO;

import java.util.List;

/**
 * <p>
 * 原材料热处理表面处理质量问题清单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface PerformanceMaterialHandlerProblemMapperService extends IService<PerformanceMaterialHandlerProblemPO> {
    IPage<PerformanceMaterialHandlerProblemPO> listPageByQuery(IPage<PerformanceMaterialHandlerProblemPO> page, PerformanceMaterialHandlerProblemPageReqDTO reqDTO);

    void updateIsNeedExamine(List<Long> ids);
}
