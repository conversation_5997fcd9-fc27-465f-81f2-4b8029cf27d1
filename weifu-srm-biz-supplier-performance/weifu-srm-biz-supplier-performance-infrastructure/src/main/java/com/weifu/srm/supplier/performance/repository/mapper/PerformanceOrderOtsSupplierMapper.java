package com.weifu.srm.supplier.performance.repository.mapper;

import com.weifu.srm.supplier.performance.repository.po.PerformanceOrderOtsSupplierPO;
import com.weifu.srm.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * @Description 绩效工单-ots及时送样率-供应商范围 Mapper 接口
 * @Date 2024-09-20
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PerformanceOrderOtsSupplierMapper extends BaseMapper<PerformanceOrderOtsSupplierPO> {
    void initData(@Param("performanceNo") String performanceNo);

}
