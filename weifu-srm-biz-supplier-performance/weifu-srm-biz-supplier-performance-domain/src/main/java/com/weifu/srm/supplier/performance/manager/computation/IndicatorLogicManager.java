package com.weifu.srm.supplier.performance.manager.computation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.context.SecurityContextHolder;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.composite.enums.SystemParameterEnum;
import com.weifu.srm.masterdata.response.CategoryWithOneAndTwoLevelDTO;
import com.weifu.srm.masterdata.response.MaterialAndCategoryInfoRespDTO;
import com.weifu.srm.supplier.cio.response.quality.QualityCountResDTO;
import com.weifu.srm.supplier.performance.constants.ComputationConstant;
import com.weifu.srm.supplier.performance.convert.ComputationSchemeTwoLogicResultConvert;
import com.weifu.srm.supplier.performance.convert.PerformanceSupplierScoreConvert;
import com.weifu.srm.supplier.performance.convert.PreComputationConvert;
import com.weifu.srm.supplier.performance.dto.ComputationScoreDTO;
import com.weifu.srm.supplier.performance.enums.*;
import com.weifu.srm.supplier.performance.manager.remote.base.SystemParameterApiManager;
import com.weifu.srm.supplier.performance.manager.remote.masterdata.CategoryManager;
import com.weifu.srm.supplier.performance.manager.remote.masterdata.MaterialManager;
import com.weifu.srm.supplier.performance.manager.remote.supplier.SupplierCertificationManager;
import com.weifu.srm.supplier.performance.manager.remote.supplier.SupplierQualityManager;
import com.weifu.srm.supplier.performance.repository.atomicservice.*;
import com.weifu.srm.supplier.performance.repository.atomicservice.biz.*;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.*;
import com.weifu.srm.supplier.performance.repository.po.*;
import com.weifu.srm.supplier.performance.repository.po.biz.*;
import com.weifu.srm.supplier.performance.repository.po.computation.*;
import com.weifu.srm.supplier.performance.request.precomputation.PreComputationReqDTO;
import com.weifu.srm.supplier.performance.response.computation.CalculateIndicatorLogicResultResDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationInfoResDTO;
import com.weifu.srm.supplier.performance.utils.QuarterDatesUtils;
import com.weifu.srm.supplier.request.certification.SupplierCerForPerResDTO;
import com.weifu.srm.supplier.response.certification.SupplierCerForPerReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.Security;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLogicManager {

    private final SupplierCertificationManager supplierCertificationManager;
    private final PerformanceOrderDeliverMapperService performanceOrderDeliverMapperService;
    private final PerformanceOrderDailyWillingnessMapperService orderDailyWillingnessMapperService;
    private final PerformanceOrderOtsMaterialDeliveryMapperService orderOtsMaterialDeliveryMapperService;
    private final PerformanceExamineMapperService performanceExamineMapperService;
    private final PerformanceExamineSupplierMapperService performanceExamineSupplierMapperService;
    private final IndicatorLogicQueueMapperService computationIndicatorLogicQueueMapperService;
    private final SchemeTwoLogicResultMapperService computationSchemeTwoLogicResultMapperService;
    private final ComputationSchemeTwoLogicResultConvert computationSchemeTwoLogicResultConvert;
    private final BizPurchaseInvoiceMapperService bizPurchaseInvoiceMapperService;
    private final PerformanceOrderCostReductionSupplierMapperService reductionSupplierMapperService;
    private final PerformanceOrderMapperService performanceOrderMapperService;
    private final PerformanceOrderCostReductionMaterialMapperService reductionMaterialMapperService;
    private final PerformanceOrderCostReductionRateMapperService reductionRateMapperService;
    private final SupplierQualityManager supplierQualityManager;
    private final BizMaterialInspectionPSWMapperService inspectionPSWMapperService;
    private final BizMaterialInspectionPSWDownMapperService bizMaterialInspectionPSWDownMapperService;
    private final PreComputationConvert preComputationConvert;
    private final BizReceiptGoodsPSWDataMapperService goodsPSWDataMapperService;
    private final PerformanceOrderQualityMapperService orderQualityMapperService;
    private final PerformanceProcessComplaintsBatchRateMapperService complaintsBatchRateMapperService;
    private final BizDefectivePartsPSWDownloadMapperService bizDefectivePartsPSWDownloadMapperService;
    private final BizDefectivePartsPSWMapperService bizDefectivePartsPSWMapperService;
    private final BizReceiptGoodsPSWDownloadMapperService bizReceiptGoodsPSWDownloadMapperService;
    private final BizPPAPPSWDownMapperService bizPPAPPSWDownMapperService;
    private final CategoryManager categoryManager;
    private final PreComputeQueueMapperService queueMapperService;
    private final ComputationInfoMapperService computationInfoMapperService;
    private final ComputationSchemeTwoMapperService computationSchemeTwoMapperService;
    private final ComputationSchemeOneMapperService computationSchemeOneMapperService;
    private final ComputationSupplierScoreMapperService computationSupplierScoreMapperService;
    private final ComputationLogicSupplierScoreMapperService logicSupplierScoreMapperService;
    private final PerformanceSupplierScoreMapperService performanceSupplierScoreMapperService;
    private final PerformanceSupplierScoreConvert performanceSupplierScoreConvert;
    private final MaterialManager materialManager;
    private final SystemParameterApiManager systemParameterApiManager;

    public void clearPerComputation(String performanceNo){
        // 清空逻辑计算队列数据
        log.info("清空逻辑计算队列数据【{}】",performanceNo);
        computationIndicatorLogicQueueMapperService.lambdaUpdate()
                .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, performanceNo)
                .set(ComputationIndicatorLogicQueuePO::getIsDelete,  YesOrNoEnum.YES.getCode())
                .update();
        // 清空逻辑计算数据
        log.info("清空逻辑计算数据【{}】",performanceNo);
        computationSchemeTwoLogicResultMapperService.lambdaUpdate()
                .eq(ComputationSchemeTwoLogicResultPO::getPerformanceNo, performanceNo)
                .set(ComputationSchemeTwoLogicResultPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        // 清空逻辑计算中锁定的供应商加减分
        log.info("清空逻辑计算中锁定的供应商加减分【{}】",performanceNo);
        logicSupplierScoreMapperService.lambdaUpdate()
                .eq(ComputationLogicSupplierScorePO::getPerformanceNo, performanceNo)
                .set(ComputationLogicSupplierScorePO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        // 清空预计算队列数据
        log.info("清空预计算队列数据【{}】",performanceNo);
        queueMapperService.lambdaUpdate()
                .eq(ComputationPreComputeQueuePO::getPerformanceNo, performanceNo)
                .set(ComputationPreComputeQueuePO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        // 清空预计算基本信息和一二级和加减分数据
        log.info("清空预计算基本信息和一二级和加减分数据【{}】",performanceNo);
        computationInfoMapperService.lambdaUpdate()
                .eq(ComputationInfoPO::getPerformanceNo, performanceNo)
                .set(ComputationInfoPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        computationSchemeOneMapperService.lambdaUpdate()
                .eq(ComputationSchemeOnePO::getPerformanceNo, performanceNo)
                .set(ComputationSchemeOnePO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        computationSchemeTwoMapperService.lambdaUpdate()
                .eq(ComputationSchemeTwoPO::getPerformanceNo, performanceNo)
                .set(ComputationSchemeTwoPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
       computationSupplierScoreMapperService.lambdaUpdate()
                .eq(ComputationSupplierScorePO::getPerformanceNo, performanceNo)
                .set(ComputationSupplierScorePO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
       // 清空中间逻辑计算数据
        bizMaterialInspectionPSWDownMapperService.lambdaUpdate()
                .eq(BizMaterialInspectionPSWDownloadDataPO::getPerformanceNo, performanceNo)
                .set(BizMaterialInspectionPSWDownloadDataPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        bizDefectivePartsPSWDownloadMapperService.lambdaUpdate()
                .eq(BizDefectivePartsPSWDownloadDataPO::getPerformanceNo, performanceNo)
                .set(BizDefectivePartsPSWDownloadDataPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
        bizReceiptGoodsPSWDownloadMapperService.lambdaUpdate()
                .eq(BizReceiptGoodsPSWDownloadDataPO::getPerformanceNo, performanceNo)
                .set(BizReceiptGoodsPSWDownloadDataPO::getIsDelete, YesOrNoEnum.YES.getCode())
                .update();
    }

    /**
     * 添加计算指标逻辑任务
     * @param reqDTO
     */
    public void saveIndicatorLogicInfo(PreComputationReqDTO reqDTO) {
        // 清空逻辑指标预计算
        clearPerComputation(reqDTO.getPerformanceNo());

        List<ComputationIndicatorLogicQueuePO> list = computationIndicatorLogicQueueMapperService.lambdaQuery()
                .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, reqDTO.getPerformanceNo())
                .list();
        if(CollUtil.isEmpty(list)){
            ComputationIndicatorLogicQueuePO logicQueuePO = new ComputationIndicatorLogicQueuePO();
            logicQueuePO.setStatus(IndicatorLogicStatusEnum.NOT_STARTED.getType());
            logicQueuePO.setPerformanceNo(reqDTO.getPerformanceNo());
            computationIndicatorLogicQueueMapperService.save(logicQueuePO);
            log.info("添加计算指标逻辑任务为未开始:{}",reqDTO.getPerformanceNo());
            return;
        }
        computationIndicatorLogicQueueMapperService.lambdaUpdate()
                .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, reqDTO.getPerformanceNo())
                .set(ComputationIndicatorLogicQueuePO::getStatus, IndicatorLogicStatusEnum.NOT_STARTED.getType())
                .update();
        log.info("更新计算指标逻辑任务为未开始:{}",reqDTO.getPerformanceNo());
    }

    /**
     * 查询绩效考核-计算指标逻辑状态结果
     * @param reqDTO PreComputationReqDTO
     * @return CalculateIndicatorLogicResultResDTO
     */
    public CalculateIndicatorLogicResultResDTO calculateIndicatorLogicResult(PreComputationReqDTO reqDTO) {
        ComputationIndicatorLogicQueuePO logicQueuePO = computationIndicatorLogicQueueMapperService.lambdaQuery()
                .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, reqDTO.getPerformanceNo())
                .one();
        return computationSchemeTwoLogicResultConvert.toPOLogic(logicQueuePO);
    }


    /**
     * 计算指标逻辑
     * @param reqDTO reqDTO
     */
    @Async
    public void calculateIndicatorLogic(PreComputationReqDTO reqDTO) {
        log.info("异步执行 计算指标逻辑，绩效编号【{}】", reqDTO.getPerformanceNo());


        performanceExamineMapperService.lambdaUpdate()
                .eq(PerformanceExaminePO::getPerformanceNo, reqDTO.getPerformanceNo())
                .set(PerformanceExaminePO::getStatus, ExamineStatusEnum.QUOTA_COMPUTING.getCode())
                .update();
        try {
            ComputationIndicatorLogicQueuePO logicQueuePO = computationIndicatorLogicQueueMapperService.lambdaQuery()
                    .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, reqDTO.getPerformanceNo())
                    .one();

            String performanceNo = logicQueuePO.getPerformanceNo();
            // 开始计算
            updateIndicatorLogicQueueStatus(performanceNo, IndicatorLogicStatusEnum.DOING);

            ComputationScoreDTO scoreDTO = new ComputationScoreDTO();
            // 查询基本信息
            ComputationInfoResDTO computationInfo = setComputationBaseInfo(performanceNo);
            // 6.1.11 制程千批投诉率配置”中勾选的一级品类下的二级品类
            setTwoCategoryCode(scoreDTO);
            // 限定PPAP时长
            setPPAMTime(scoreDTO);
            // 读取需要计算的供应商信息
            List<PerformanceExamineSupplierPO> suplierList = performanceExamineSupplierMapperService.lambdaQuery()
                    .eq(PerformanceExamineSupplierPO::getPerformanceNo, performanceNo)
                    .eq(PerformanceExamineSupplierPO::getIsDelete, YesOrNoEnum.NO.getCode())
                    .list();

            // 查询绩效工单信息
            setOrderInfo(performanceNo, scoreDTO);

            // 查询供应商质量问题
            setSupplierQualityCount(scoreDTO, computationInfo.getStartExamineCycleTime(), computationInfo.getEndExamineCycleTime());

            List<ComputationSchemeTwoLogicResultPO> twoList = CollUtil.newArrayList();
            List<ComputationLogicSupplierScorePO> logicSupplierScorePOList = CollUtil.newArrayList();
            for (PerformanceExamineSupplierPO po : suplierList){
                ComputationInfoResDTO compInfo = new ComputationInfoResDTO();
                compInfo.setSupplierCode(po.getSupplierCode());
                computationInfo.setSupplierCode(po.getSupplierCode());
                scoreDTO.setSupplierCode(po.getSupplierCode());
                BeanUtils.copyProperties(computationInfo, scoreDTO);
                // 计算二级指标得分
                List<ComputationSchemeTwoLogicResultPO> resultPOS = computationSchemeTwo(scoreDTO, po.getExamineCategoryCode());
                twoList.addAll(resultPOS);
                logicSupplierScorePOList.addAll(getComputationLogicSupplierScorePOS(reqDTO, po, computationInfo));
            }
            // 批量 添加
            computationSchemeTwoLogicResultMapperService.saveBatch(twoList,200);

            logicSupplierScoreMapperService.saveBatch(logicSupplierScorePOList,200);
            // 计算完成
            updateIndicatorLogicQueueStatus(performanceNo, IndicatorLogicStatusEnum.FINISH);

            performanceExamineMapperService.lambdaUpdate()
                    .eq(PerformanceExaminePO::getPerformanceNo, performanceNo)
                    .set(PerformanceExaminePO::getStatus, ExamineStatusEnum.QUOTA_COMPUTE_SUCCESS.getCode())
                    .update();
        }catch (Exception e){
            log.error("逻辑指标计算失败{}",reqDTO.getPerformanceNo(),e);
            performanceExamineMapperService.lambdaUpdate()
                    .eq(PerformanceExaminePO::getPerformanceNo, reqDTO.getPerformanceNo())
                    .set(PerformanceExaminePO::getStatus, ExamineStatusEnum.QUOTA_COMPUTE_FAILED.getCode())
                    .update();
            computationIndicatorLogicQueueMapperService.lambdaUpdate()
                    .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, reqDTO.getPerformanceNo())
                    .set(ComputationIndicatorLogicQueuePO::getStatus, IndicatorLogicStatusEnum.ERROR.getType())
                    .set(ComputationIndicatorLogicQueuePO::getUpdateTime, new Date())
                    .set(ComputationIndicatorLogicQueuePO::getUpdateBy, SecurityContextHolder.getUserId())
                    .set(ComputationIndicatorLogicQueuePO::getUpdateName, SecurityContextHolder.getRealName())
                    .update();
        }

    }

    private List<ComputationLogicSupplierScorePO> getComputationLogicSupplierScorePOS(PreComputationReqDTO reqDTO,
                                        PerformanceExamineSupplierPO po, ComputationInfoResDTO computationInfo) {
        List<PerformanceSupplierScorePO> list = performanceSupplierScoreMapperService.lambdaQuery()
                .eq(PerformanceSupplierScorePO::getSupplierCode, po.getSupplierCode())
                .between(PerformanceSupplierScorePO::getHappenTime, computationInfo.getStartExamineCycleTime()
                        , computationInfo.getEndExamineCycleTime())
                .eq(PerformanceSupplierScorePO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();

        List<ComputationLogicSupplierScorePO> logicSupplierScoreList = performanceSupplierScoreConvert.toLogicSupplierScoreList(list);
        logicSupplierScoreList = logicSupplierScoreList.stream()
                .peek(e->e.setPerformanceNo(reqDTO.getPerformanceNo()))
                .peek(e->e.setId(null))
                .collect(Collectors.toList());
        return logicSupplierScoreList;
    }

    private void setTwoCategoryCode(ComputationScoreDTO scoreDTO) {
        List<PerformanceProcessComplaintsBatchRatePO> list = complaintsBatchRateMapperService.list();
        List<String> codeList = list.stream()
                .map(PerformanceProcessComplaintsBatchRatePO::getCategoryCode)
                .collect(Collectors.toList());

        List<CategoryWithOneAndTwoLevelDTO> categoryTwoList = categoryManager.listTwoLevelCategory(codeList);
        scoreDTO.setTwoCategoryCode(categoryTwoList.stream()
                .map(CategoryWithOneAndTwoLevelDTO::getTwoLevelCategoryCode).collect(Collectors.toList()));
    }

    private void setSupplierQualityCount(ComputationScoreDTO scoreDTO, Date start, Date end) {
        Map<String, QualityCountResDTO> countMap = supplierQualityManager.querySupplierQualityCount(start, end);
        scoreDTO.setSupplierQualityCountMap(countMap);
    }

    private void setPPAMTime(ComputationScoreDTO scoreDTO) {
        // ppam 限定时长
        String time = systemParameterApiManager.getByParameterName(SystemParameterEnum.PPAP_DEFINE_DURATION.getCode());
        scoreDTO.setCheckPPAPTime(Long.parseLong(time));
    }

    private void setOrderInfo(String performanceNo, ComputationScoreDTO scoreDTO) {
        List<PerformanceOrderPO> list = performanceOrderMapperService.lambdaQuery()
                .eq(PerformanceOrderPO::getPerformanceNo, performanceNo)
                .list();
        Map<String, List<PerformanceOrderPO>> orderMap = list.stream()
                .collect(Collectors.groupingBy(PerformanceOrderPO::getOrderType));
        scoreDTO.setOrderMap(orderMap);
    }

    private void updateIndicatorLogicQueueStatus(String performanceNo, IndicatorLogicStatusEnum statusEnum) {
        computationIndicatorLogicQueueMapperService.lambdaUpdate()
                .eq(ComputationIndicatorLogicQueuePO::getPerformanceNo, performanceNo)
                .set(ComputationIndicatorLogicQueuePO::getStatus, statusEnum.getType())
                .set(ObjectUtil.equals(statusEnum.getType(),IndicatorLogicStatusEnum.FINISH.getType()),
                        ComputationIndicatorLogicQueuePO::getEndTime, new Date())
                .set(ObjectUtil.equals(statusEnum.getType(),IndicatorLogicStatusEnum.DOING.getType()),
                        ComputationIndicatorLogicQueuePO::getStartTime, new Date())
                .update();
    }

    /**
     * 查询绩效考核信息
     * @param performanceNo performanceNo
     * @return ComputationInfoResDTO computationInfoResDTO
     */
    public ComputationInfoResDTO setComputationBaseInfo(String performanceNo) {
        log.info("封装绩效考核基本信息，绩效考核【{}】", performanceNo);
        ComputationInfoResDTO computationInfo = new ComputationInfoResDTO();
        // 读取任务基本信息
        PerformanceExaminePO performanceExamine = performanceExamineMapperService.lambdaQuery()
                .eq(PerformanceExaminePO::getPerformanceNo, performanceNo)
                .one();

        String performanceType = performanceExamine.getPerformanceType();
        computationInfo.setPerformanceType(performanceType);
        computationInfo.setPerformanceNo(performanceNo);
        computationInfo.setThemes(performanceExamine.getThemes());
        // 考核周期开始-结束时间
        Date start;
        Date end;
        if(ObjectUtil.equals(performanceType, ScoringRuleCycleEnum.QUARTER.getCode())){
            start = QuarterDatesUtils.getQuarterStartDate(Integer.parseInt(performanceExamine.getExamineCycleYear()),
                                                Integer.parseInt(performanceExamine.getExamineCycleQuarter()));
            end = QuarterDatesUtils.getQuarterEndDate(Integer.parseInt(performanceExamine.getExamineCycleYear()),
                    Integer.parseInt(performanceExamine.getExamineCycleQuarter()));
            computationInfo.setExamineCycleYear(performanceExamine.getExamineCycleYear());
            computationInfo.setExamineCycleQuarter(performanceExamine.getExamineCycleQuarter());
        }else {
            start = QuarterDatesUtils.getYearStartDate(Integer.parseInt(performanceExamine.getExamineCycleYear()));
            end = QuarterDatesUtils.getYearEndDate(Integer.parseInt(performanceExamine.getExamineCycleYear()));
            computationInfo.setExamineCycleYear(performanceExamine.getExamineCycleYear());
        }
        computationInfo.setStartExamineCycleTime(start);
        computationInfo.setEndExamineCycleTime(end);
        log.info("封装绩效考核基本信息，绩效考核信息:【{}】", computationInfo);
        return computationInfo;
    }

    public List<ComputationSchemeTwoLogicResultPO> computationSchemeTwo(ComputationScoreDTO scoreDTO,
                                                                        String examineCategoryCode){
        log.info("计算二级指标,供应商是【{}】", scoreDTO.getSupplierCode());
        List<ComputationSchemeTwoLogicResultPO> relist = new ArrayList<>();
        // 计算-质量-制程PPM-二级指标计算结果
        BigDecimal ppm = scorePPM(scoreDTO);
        log.info("计算-质量-制程PPM-二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),ppm);
        relist.add(setComputationScore(scoreDTO, ppm, SecondLevelQuotaEnum.QUALITY_PROCESS_PPM));
        // 计算-质量-一次检验合格率FPY（来料检验）- 二级指标计算结果
        BigDecimal fyp = scoreFYP(scoreDTO);
        log.info("计算-质量-一次检验合格率FPY（来料检验）- 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),fyp);
        relist.add(setComputationScore(scoreDTO, fyp, SecondLevelQuotaEnum.QUALITY_ONE_VALID_FPY));
        // 计算-质量-制程千批投诉率 - 二级指标计算结果
        BigDecimal thousand = scoreProcessThousandComplaintRate(scoreDTO, examineCategoryCode);
        log.info("计算-质量-制程千批投诉率 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),thousand);
        relist.add(setComputationScore(scoreDTO, thousand, SecondLevelQuotaEnum.QUALITY_PROCESS_COMPLAINT_RATE));
        // 计算-质量-质量体系资质评分 - 二级指标计算结果
        BigDecimal qualitySystem = scoreQualitySystemQualificationRating(scoreDTO);
        log.info("计算-质量-质量体系资质评分 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),qualitySystem);
        relist.add(setComputationScore(scoreDTO, qualitySystem, SecondLevelQuotaEnum.QUALITY_QUALIFICATION_RATING));
        // 计算-质量-4Q&8D及时回复率 - 二级指标计算结果
        BigDecimal score4Q8D = score4Q8D(scoreDTO);
        log.info("计算-质量-4Q&8D及时回复率 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),score4Q8D);
        relist.add(setComputationScore(scoreDTO, score4Q8D, SecondLevelQuotaEnum.QUALITY_TIMELY_RESPONSE_RATE));
        // 计算-交付- 供应商准时交付率 - 二级指标计算结果
        BigDecimal onTime = onTimeDeliveryRate(scoreDTO);
        log.info("计算-交付- 供应商准时交付率 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),onTime);
        relist.add(setComputationScore(scoreDTO, onTime, SecondLevelQuotaEnum.DELIVER_SUPPLIER_TIMELY_DELIVERY_RATE));
        // 计算-商务- 降本目标达成度 - 二级指标计算结果 - 季度不计算
        if(ObjectUtil.equals(scoreDTO.getPerformanceType(),ScoringRuleCycleEnum.YEAR.getCode())){
            BigDecimal costReductionGoals = scoreAchievementCostReductionGoals(scoreDTO);
            log.info("计算-商务- 降本目标达成度 - 二级指标计算结果 - 季度不计算,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),costReductionGoals);
            relist.add(setComputationScore(scoreDTO, costReductionGoals,
                    SecondLevelQuotaEnum.BUSINESS_ACHIEVEMENT_COST_REDUCTION_GOALS));
        }
        // 计算-商务- 日常服务意愿 - 二级指标计算结果
        BigDecimal daily = scoreDailyServiceWillingness(scoreDTO);
        log.info("计算-商务- 日常服务意愿 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),daily);
        relist.add(setComputationScore(scoreDTO, daily, SecondLevelQuotaEnum.BUSINESS_DAILY_SERVICE_WILLINGNESS));
        // 计算-项目开发- OTS送样及时率 - 二级指标计算结果 - 季度不计算
        if(ObjectUtil.equals(scoreDTO.getPerformanceType(), ScoringRuleCycleEnum.YEAR.getCode())){
            BigDecimal ots = scoreOTS(scoreDTO);
            log.info("计算-项目开发- OTS送样及时率 - 二级指标计算结果 - 季度不计算,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),ots);
            relist.add(setComputationScore(scoreDTO, ots, SecondLevelQuotaEnum.PROJECT_DEVELOPMENT_OTS_TIMELINESS_RATE));
        }
        // 计算-项目开发- 批产早期批次合格率 - 二级指标计算结果
        BigDecimal ppap = scorePPAP(scoreDTO);
        log.info("计算-项目开发- 批产早期批次合格率 - 二级指标计算结果,供应商是【{}】score【{}】", scoreDTO.getSupplierCode(),ppap);
        relist.add(setComputationScore(scoreDTO, ppap, SecondLevelQuotaEnum.PROJECT_QUALIFICATION_RATE));
        return relist;
    }

    private ComputationSchemeTwoLogicResultPO setComputationScore(ComputationScoreDTO scoreDTO,
                                           BigDecimal calculationResult, SecondLevelQuotaEnum key) {
        ComputationSchemeTwoLogicResultPO resultPO = new ComputationSchemeTwoLogicResultPO();
        resultPO.setPerformanceNo(scoreDTO.getPerformanceNo());
        resultPO.setSupplierCode(scoreDTO.getSupplierCode());
        resultPO.setPerformanceType(scoreDTO.getPerformanceType());
        resultPO.setFirstLevel(key.getFirstCode());
        resultPO.setSecondLevel(key.getSecondCode());
        resultPO.setCalculationResult(calculationResult);
        return resultPO;
    }

    public BigDecimal scorePPM(ComputationScoreDTO scoreDTO){
        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();
        // PPAP界定时长
        Long checkPPAPTime = scoreDTO.getCheckPPAPTime();
        log.info("计算供应商【{}】,在周期【{}】->【{}】的ppm得分", supplierNo,start,end);
        // 查询数据集b（投诉日期）: “PSW签署时间 + PPAP界定时长”<“投诉日期”, “投诉日期” in “考核周期内”，
        List<BizDefectivePartsPSWDataPO> defectiveList = bizDefectivePartsPSWMapperService.lambdaQuery()
                .between(BizDefectivePartsPSWDataPO::getComplaintDate, start, end)
                .eq(BizDefectivePartsPSWDataPO::getSupplierCode, supplierNo)
                .isNotNull(BizDefectivePartsPSWDataPO::getPswTime)
                .apply("DATE_ADD(psw_time, INTERVAL {0} MONTH) < complaint_date", checkPPAPTime)
                .list();
        log.info("计算供应商【{}】,查询数据集b【{}】", supplierNo,defectiveList);
        // 存储中间数据b - 投诉日期；
        List<BizDefectivePartsPSWDownloadDataPO> defectiveSaveDataList = defectiveList.stream()
                .map(preComputationConvert::toDefectivePSWDownPO)
                .peek(e -> e.setPerformanceNo(scoreDTO.getPerformanceNo()))
                .collect(Collectors.toList());
        bizDefectivePartsPSWDownloadMapperService.saveBatch(defectiveSaveDataList,200);

        long puSumNum = defectiveSaveDataList.stream()
                .mapToLong(BizDefectivePartsPSWDownloadDataPO::getPuConfirmsNum).sum();
        log.info("计算供应商【{}】,getPuConfirmsNum->puSumNum【{}】", supplierNo,puSumNum);
        // 查询数据集c（凭证中的过账日期-BUDAT）：“PSW签署时间+PPAP界定时长”<“BUDAT”， “BUDAT” in “考核周期内”，
        List<BizReceiptGoodsPSWDataPO> receiptGoodsList = goodsPSWDataMapperService.lambdaQuery()
                .eq(BizReceiptGoodsPSWDataPO::getAccountNo, supplierNo)
                .between(BizReceiptGoodsPSWDataPO::getVoucherPostingDate, start, end)
                .isNotNull(BizReceiptGoodsPSWDataPO::getPswTime)
                .apply("DATE_ADD(psw_time, INTERVAL {0} MONTH) < voucher_posting_date", checkPPAPTime)
                .list();
        log.info("计算供应商【{}】,查询数据集c->receiptGoodsList【{}】", supplierNo,receiptGoodsList);
        if(CollUtil.isEmpty(receiptGoodsList)){
            return null;
        }

        // 存储中间数据c - 凭证中的过账日期；
        List<BizReceiptGoodsPSWDownloadDataPO> receiptGoodsSaveDataList = receiptGoodsList.stream()
                .map(preComputationConvert::toReceiptGoodsPSWDownPO)
                .peek(e -> e.setPerformanceNo(scoreDTO.getPerformanceNo()))
                .collect(Collectors.toList());
        log.info("计算供应商【{}】,查询数据集c->receiptGoodsSaveDataList【{}】", supplierNo,receiptGoodsSaveDataList);
        bizReceiptGoodsPSWDownloadMapperService.saveBatch(receiptGoodsSaveDataList,200);

        long cSunNum = receiptGoodsSaveDataList.stream().mapToLong(BizReceiptGoodsPSWDownloadDataPO::getNum).sum();
        log.info("计算供应商【{}】,查询数据集c->cSunNum【{}】", supplierNo,cSunNum);
        if(cSunNum <= 0){
            return null;
        }

        // 分子：数据集b -> PU确认数量总数；
        BigDecimal numerator = new BigDecimal(puSumNum);
        // 分母：数据集c -> MENGE；
        BigDecimal denominator = new BigDecimal(cSunNum);
        //结果
        log.info("计算供应商【{}】,分子：数据集b -> PU确认数量总数；numerator【{}】", supplierNo,numerator);
        log.info("计算供应商【{}】,分母：数据集c -> MENGE；；denominator【{}】", supplierNo,denominator);
        return numerator.divide(denominator, ComputationConstant.SCALE_6, ComputationConstant.ROUNDING_MODE)
                .multiply(ComputationConstant.NUM_1000000);
    }

    public BigDecimal scoreFYP(ComputationScoreDTO scoreDTO){
        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();
        Long checkPPAPTime = scoreDTO.getCheckPPAPTime();
        log.info("计算供应商【{}】,FYP 在周期【{}】->【{}】的FYP得分", supplierNo,start,end);
        // 查询数据集b（来料日期）: “PSW签署时间+PPAP界定时长”<“来料日期”, “来料日期” in “考核周期内”
        List<BizMaterialInspectionPSWDataPO> list = inspectionPSWMapperService.lambdaQuery()
                .between(BizMaterialInspectionPSWDataPO::getDeliveryDate, start, end)
                .eq(BizMaterialInspectionPSWDataPO::getSapSupplierCode, supplierNo)
                .isNotNull(BizMaterialInspectionPSWDataPO::getPswTime)
                .apply("DATE_ADD(psw_time, INTERVAL {0} MONTH) < delivery_date", checkPPAPTime)
                .list();
        log.info("计算供应商【{}】,FYP -> list【{}】", supplierNo,list);
        if(CollUtil.isEmpty(list)){
            return null;
        }

        // 存储中间数据b - 来料日期；
        List<BizMaterialInspectionPSWDownloadDataPO> saveDataList = list.stream()
                .map(preComputationConvert::toInspectionPSWDownPO)
                .peek(e -> e.setPerformanceNo(scoreDTO.getPerformanceNo()))
                .peek(e -> e.setIsBefore(YesOrNoEnum.NO.getCode()))
                .collect(Collectors.toList());
        bizMaterialInspectionPSWDownMapperService.saveBatch(saveDataList, 200);

        // “PU检验结果确认”字段为“OK”
        long okCountNum = saveDataList.stream()
                .filter(e-> ObjectUtil.equals(ComputationConstant.OK, e.getPuTestResults()))
                .count();
        // 分子
        BigDecimal numerator = new BigDecimal(okCountNum);
        // 分母
        BigDecimal denominator = new BigDecimal(saveDataList.size());
        //结果
        log.info("计算供应商【{}】,FYP -> numerator【{}】denominator【{}】", supplierNo,numerator,denominator);
        return numerator.divide(denominator, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE);
    }

    public BigDecimal scoreProcessThousandComplaintRate(ComputationScoreDTO scoreDTO, String examineCategoryCode){
        String supplierNo = scoreDTO.getSupplierCode();
        // 系统读取“6.1.11 制程千批投诉率配置”中勾选的一级品类的数据，并且关联查询出一级品类下的所有二级品类，获得数据集a：
        List<String> twoCategoryCode = scoreDTO.getTwoCategoryCode();
        // 供应商考核的品类
        if(!twoCategoryCode.contains(examineCategoryCode)){
            log.info("计算供应商【{}】,所考核的品类【{}】，不在制程千批投诉率配置中所属的二级品类中【{}】",
                    supplierNo,examineCategoryCode,twoCategoryCode);
            return null;
        }
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();

        // 数据集c-收货数据： “数量MENGE”不为负的数据）， 凭证中的过帐日期BUDAT in 考核周期,并且在配置的一级品类对应的二级品类
        List<BizReceiptGoodsPSWDataPO> list = goodsPSWDataMapperService.lambdaQuery()
                .in(BizReceiptGoodsPSWDataPO::getTwoCategoryCode, twoCategoryCode)
                .eq(BizReceiptGoodsPSWDataPO::getAccountNo, supplierNo)
                .ge(BizReceiptGoodsPSWDataPO::getNum, 0)
                .between(BizReceiptGoodsPSWDataPO::getVoucherPostingDate, start, end)
                .list();
        log.info("计算供应商【{}】数据集c-收货数据,list【{}】",supplierNo,list);
        // 3.2	使用数据集c，关联查询SRM的“物料和品类”的关系表，查询出每一条数据关联的三级品类，再查询出二级品类。
        //      如果二级品类在数据集a中，那么保留该数据；
        //      如果没有查询出三级品类，或者二级品类不在数据集a中，那么不保留该数据。
        //      获得数据集d
        if(CollUtil.isEmpty(list)){
            return null;
        }
//        List<String> materialCodeList = list.stream().map(BizReceiptGoodsPSWDataPO::getMaterialNo)
//                                        .distinct().collect(Collectors.toList());
//        List<MaterialAndCategoryInfoRespDTO> collect = materialManager.listMaterialAndCategoryInfoList(materialCodeList);
//        if(CollUtil.isEmpty(collect)){
//            return null;
//        }

        log.info("计算供应商【{}】数据集c-收货数据,d-<collect【{}】",supplierNo,list);
        List<BizReceiptGoodsPSWDataPO> dList = list.stream()
                .filter(e -> twoCategoryCode.contains(e.getTwoCategoryCode()))
                .filter(e -> ObjectUtil.isNotEmpty(e.getThreeCategoryCode()))
                .collect(Collectors.toList());
        log.info("计算供应商【{}】数据集c-收货数据,d->dList【{}】",supplierNo,dList);
        // 数据集d的count数作为分母，如果分母为0，则该供应商的“制程千批投诉率”的结果为“无数据情况”
        if(CollUtil.isEmpty(dList)){
            return null;
        }
        // 6.4.8 绩效工单核验数据_质量指标数据”中的数据，并且汇总计算数据的count数，作为分子；
        List<PerformanceOrderPO> performanceOrderPO = scoreDTO.getOrderMap()
                .get(OrderTypeEnum.QUALITY_INDICATOR_DATA.getCode());
        log.info("计算供应商【{}】绩效工单核验数据_质量指标数据,performanceOrderPO【{}】",supplierNo,performanceOrderPO);
        if(CollUtil.isEmpty(performanceOrderPO)){
            return null;
        }
        List<String> orderList = performanceOrderPO.stream()
                .map(PerformanceOrderPO::getOrderNo).distinct().collect(Collectors.toList());
        long countNum = orderQualityMapperService.lambdaQuery()
                .eq(PerformanceOrderQualityPO::getSupplierCode, supplierNo)
                .in(PerformanceOrderQualityPO::getOrderNo, orderList)
                .count();
        if(countNum == 0){
            return null;
        }
        log.info("计算供应商【{}】绩效工单核验数据_质量指标数据,countNum【{}】",supplierNo,countNum);

        log.info("计算供应商【{}】,在周期【{}】->【{}】的制程千批投诉率得分", supplierNo,start,end);
        BigDecimal numerator = new BigDecimal(countNum);
        // 分母；
        BigDecimal denominator = new BigDecimal(dList.size());
        //结果
        log.info("计算供应商【{}】绩效工单核验数据_质量指标数据,numerator【{}】denominator【{}】",supplierNo,numerator,denominator);
        return numerator.divide(denominator, ComputationConstant.SCALE_4, ComputationConstant.ROUNDING_MODE)
                    .multiply(ComputationConstant.NUM_1000);
    }

    public BigDecimal scoreQualitySystemQualificationRating(ComputationScoreDTO scoreDTO){
        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();
        // 查询供应商所有证书；
        SupplierCerForPerResDTO supplierCerForPerResDTO = new SupplierCerForPerResDTO();
        supplierCerForPerResDTO.setSupplierNo(supplierNo);
        supplierCerForPerResDTO.setNowDate(new Date());
        supplierCerForPerResDTO.setEnd(end);
        supplierCerForPerResDTO.setStart(start);
        List<SupplierCerForPerReqDTO> list = supplierCertificationManager
                .findQualityCertificationListBySupplierNotExpiry(supplierCerForPerResDTO);

        if(CollUtil.isEmpty(list)){
            return null;
        }

        // 包含有效的“IATF16949”证书的数量；
        List<SupplierCerForPerReqDTO> has16949List = list.stream()
                .filter(e -> ObjectUtil.equals(e.getCertificationType(),ComputationConstant.IATF16949))
                .collect(Collectors.toList());

        int size = 2;
        if(CollUtil.isNotEmpty(has16949List)){
            // 有就取证书的数量
            scoreDTO.setHasIATF16949(YesOrNoEnum.YES.getCode());
            size = 1;
        }
        return new BigDecimal(size);
    }

    public BigDecimal score4Q8D(ComputationScoreDTO scoreDTO){
        String supplierNo = scoreDTO.getSupplierCode();
        Map<String, QualityCountResDTO> supplierQualityCountMap = scoreDTO.getSupplierQualityCountMap();
        QualityCountResDTO qualityCountResDTO = supplierQualityCountMap.get(supplierNo);

        log.info("计算供应商【{}】4Q8D,qualityCountResDTO【{}】",supplierNo,qualityCountResDTO);
        if(ObjectUtil.isNull(qualityCountResDTO)){
            return null;
        }
        //1)如果是威孚内部“代录入根因分析”的数据，则直接认为逾期；
        //2)如果质量问题的状态为“已验证根因分析”、“供应商已提交长期措施”、“长期措施验证不通过”、“已验证长期措施”、“问题关闭”，
        //  则根据质量问题的“问题创建时间+原因分析及长期反馈期限天数”<该质量问题的“根因分析验证通过时间”，则认为逾期；否则认为未逾期；
        //3)如果质量问题的状态为“提出问题”、“已提交临时措施”、“待SQE确认”、“SQE退回”、“供应商已提出根因分析”、“根因分析验证不通过”，
        //  则根据质量问题的“问题创建时间+原因分析及长期反馈期限天数”<当前的系统日期，认为逾期；否则认为未逾期
        BigDecimal numerator = new BigDecimal(qualityCountResDTO.getNum());

        // 2.1获取从“供应商质量问题解决”模块，按照“质量问题”数据的“投诉日期”字段，获取该供应商相关的、
        // 且投诉日期在绩效考核周期内、且状态不为“草稿”、“已撤回”的质量问题数据，作为分母；如果没有数据则按照“无数据情况”对应分值给分；
        BigDecimal denominator = new BigDecimal(qualityCountResDTO.getAllNum());
        // “4Q&8D及时回复率”的计算结果=1-(逾期数/分母)。
        log.info("计算供应商【{}】4Q8D,numerator【{}】denominator【{}】",supplierNo,numerator,denominator);
        return ComputationConstant.NUM_1.subtract(numerator.divide(denominator,
                ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE));
    }

    public BigDecimal onTimeDeliveryRate(ComputationScoreDTO scoreDTO) {

        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();
        List<PerformanceOrderPO> performanceOrderPOS = scoreDTO.getOrderMap()
                .get(OrderTypeEnum.DELIVERY_INDICATOR_DATA.getCode());
        log.info("计算-交付- 供应商准时交付率 - 二级指标计算结果,【{}】", performanceOrderPOS);
        if(CollUtil.isEmpty(performanceOrderPOS)){
            return null;
        }
        List<String> orderList = performanceOrderPOS.stream()
                .map(PerformanceOrderPO::getOrderNo).distinct().collect(Collectors.toList());
        String startString = com.weifu.srm.common.util.DateUtil.dateFormat(start, ComputationConstant.DATE_YYYY_MM);
        String endString = com.weifu.srm.common.util.DateUtil.dateFormat(end, ComputationConstant.DATE_YYYY_MM);
        List<PerformanceOrderDeliverPO> list = performanceOrderDeliverMapperService.lambdaQuery()
                .eq(PerformanceOrderDeliverPO::getSupplierCode,supplierNo)
                .in(PerformanceOrderDeliverPO::getOrderNo,orderList)
                .between(PerformanceOrderDeliverPO::getYearMonthStr,startString,endString)
                .list();
        if(CollUtil.isEmpty(list)){
            return null;
        }
        BigDecimal numerator = ComputationConstant.NUM_0;
        BigDecimal denominator = ComputationConstant.NUM_0;
        for(PerformanceOrderDeliverPO po : list){
            // 订单准时交付批次
            numerator = numerator.add( new BigDecimal(po.getTimelyDeliveryBatch()));
            // 计划交付批次
            denominator = denominator.add( new BigDecimal(po.getPlanDeliveryBatch()));
        }
        log.info("计算-交付- 供应商准时交付率 - 二级指标计算结果,numerator【{}】denominator【{}】", numerator,denominator);
        if(denominator.compareTo(BigDecimal.ZERO) == 0){
            return new BigDecimal(0.8);
        }
        return numerator.divide(denominator, ComputationConstant.SCALE,
                ComputationConstant.ROUNDING_MODE);
    }

    public BigDecimal scoreAchievementCostReductionGoals(ComputationScoreDTO scoreDTO) {
        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();

        // 查该加供应商是否在降本增效的工单中，维度：降本物料和目标降本率已上传
        // 没有上传该供应商的使用无数据情况
        List<PerformanceOrderPO> performanceOrderPOList = scoreDTO.getOrderMap()
                                .get(OrderTypeEnum.BUSINESS_PROJECT_DEVELOPMENT_INDICATOR.getCode());
        log.info("降本目标达成度performanceOrderPOList【{}】",performanceOrderPOList);
        if(CollUtil.isEmpty(performanceOrderPOList)){
            return null;
        }
        List<String> orderList = performanceOrderPOList.stream()
                .map(PerformanceOrderPO::getOrderNo).distinct().collect(Collectors.toList());
        List<PerformanceOrderCostReductionSupplierPO> list = reductionSupplierMapperService.lambdaQuery()
                .eq(PerformanceOrderCostReductionSupplierPO::getSupplierCode, supplierNo)
                .eq(PerformanceOrderCostReductionSupplierPO::getIsUploadReductionRate, YesOrNoEnum.YES.getCode())
                .eq(PerformanceOrderCostReductionSupplierPO::getIsUploadMaterial, YesOrNoEnum.YES.getCode())
                .in(PerformanceOrderCostReductionSupplierPO::getOrderNo, orderList)
                .list();
        log.info("降本目标达成度- 降本目标达成 list【{}】",list);
        if(CollUtil.isEmpty(list)){
            return null;
        }
        // 降本物料清单
        List<String> materialCodeList = reductionMaterialMapperService.lambdaQuery()
                .in(PerformanceOrderCostReductionMaterialPO::getOrderNo, orderList)
                .eq(PerformanceOrderCostReductionMaterialPO::getSupplierCode, supplierNo)
                .list().stream().map(PerformanceOrderCostReductionMaterialPO::getMaterialCode)
                .distinct().collect(Collectors.toList());
        log.info("降本目标达成度- 降本物料清单 list【{}】",materialCodeList);
        if(CollUtil.isEmpty(materialCodeList)){
            return ComputationConstant.NUM_0;
        }
        Date startBefore = DateUtil.beginOfYear(DateUtil.offsetMonth(start, -12));

        String startString = com.weifu.srm.common.util.DateUtil.dateFormat(start, ComputationConstant.DATE_YYYY_MM_DD);
        String endString = com.weifu.srm.common.util.DateUtil.dateFormat(end, ComputationConstant.DATE_YYYY_MM_DD);
        String startBeforeString = com.weifu.srm.common.util.DateUtil.dateFormat(startBefore, ComputationConstant.DATE_YYYY_MM_DD);
        List<BizPurchaseInvoiceDataPO> nowList = bizPurchaseInvoiceMapperService.lambdaQuery()
                .between(BizPurchaseInvoiceDataPO::getPostingDate, startString, endString)
                .eq(BizPurchaseInvoiceDataPO::getVendorNum, supplierNo)
                .in(BizPurchaseInvoiceDataPO::getMaterialCode, materialCodeList)
                .list();
        log.info("降本目标达成度- 降本物料清单 nowList【{}】",nowList);
        List<BizPurchaseInvoiceDataPO> beforeList = bizPurchaseInvoiceMapperService.lambdaQuery()
                .between(BizPurchaseInvoiceDataPO::getPostingDate,startBeforeString, startString)
                .eq(BizPurchaseInvoiceDataPO::getVendorNum, supplierNo)
                .in(BizPurchaseInvoiceDataPO::getMaterialCode, materialCodeList)
                .list();
        log.info("降本目标达成度- 降本物料清单 beforeList【{}】",beforeList);
        Map<String, List<BizPurchaseInvoiceDataPO>> nowMap = nowList.stream()
                .collect(Collectors.groupingBy(e -> e.getCompany() + e.getMaterialCode()));
        Map<String, List<BizPurchaseInvoiceDataPO>> beforeMap = beforeList.stream()
                .collect(Collectors.groupingBy(e -> e.getCompany() + e.getMaterialCode()));
        List<String> keys = nowMap.keySet().stream().filter(beforeMap.keySet()::contains).collect(Collectors.toList());
        // 当前周期和上一周期没有相同的事业部和物料，计算得分为0
        if(CollUtil.isEmpty(keys)){
           return ComputationConstant.NUM_0;
        }

        // 系统再汇总该供应商在所有物料下的所有事业部的降本率的分子和实际降本率的分母
        BigDecimal numerator = ComputationConstant.NUM_0;
        BigDecimal denominator = ComputationConstant.NUM_0;
        for (String key: keys){
            List<BizPurchaseInvoiceDataPO> nowDataList = nowMap.get(key);
            List<BizPurchaseInvoiceDataPO> beforeDataList = beforeMap.get(key);
            // 每个事业部实际降本率的分子（降本额） (绩效考核前一年的采购额/前一年采购数量-当年的采购额/当年的采购数量)*当年的采购数量
            // 绩效考核前一年的采购额
            BigDecimal one = beforeDataList.stream().map(BizPurchaseInvoiceDataPO::getCnyDocCurrAmt1d)
                                                                .reduce(BigDecimal::add).get();
            // 前一年采购数量
            BigDecimal two = beforeDataList.stream().map(BizPurchaseInvoiceDataPO::getPurCnt1d)
                                                                .reduce(BigDecimal::add).get();
            // 当年的采购额
            BigDecimal there = nowDataList.stream().map(BizPurchaseInvoiceDataPO::getCnyDocCurrAmt1d)
                                                                .reduce(BigDecimal::add).get();
            // 当年的采购数量
            BigDecimal four = nowDataList.stream().map(BizPurchaseInvoiceDataPO::getPurCnt1d)
                                                                .reduce(BigDecimal::add).get();
            log.info("降本目标达成度- 降本率的分子和实际降本率的分母 key【{}】【{}】【{}】【{}】【{}】",key,one,two,there,four);
            // 每个事业部实际降本率的分子（降本额）
            numerator = numerator.add((
                    (one.divide(two,ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE))
                    .subtract(
                            there.divide(four,ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE)))
                    .multiply(four));

            // 每个事业部实际降本率的分母：当年的采购数量* (前一年的采购额/前一年采购数量)
            denominator = denominator.add(four.multiply(
                    one.divide(two, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE)));
            log.info("降本目标达成度- 降本率的分子和实际降本率的分母 denominator【{}】,denominator【{}】",numerator,denominator);
        }
        log.info("降本目标达成度- 降本率的分子和实际降本率的分母 denominator【{}】,denominator【{}】",numerator,denominator);
        if(0 == denominator.compareTo(ComputationConstant.NUM_0)){
            return null;
        }
        return compActual(orderList, supplierNo, numerator, denominator);
    }

    /**
     * 计算-降本目标达成度-计算结果
     */
    private BigDecimal compActual(List<String> orderNoList, String supplierNo, BigDecimal numerator, BigDecimal denominator) {
        // 目标降本率
        List<PerformanceOrderCostReductionRatePO> list = reductionRateMapperService.lambdaQuery()
                .in(PerformanceOrderCostReductionRatePO::getOrderNo, orderNoList)
                .eq(PerformanceOrderCostReductionRatePO::getSupplierCode, supplierNo)
                .list();
        log.info("降本目标达成度- 目标降本率 list【{}】",list);
        if(CollUtil.isEmpty(list)){
            return null;
        }
        // 技术降本
        BigDecimal costReductionAmount = list.get(0).getCostReductionAmount();
        // 返利
        BigDecimal rebateAmount = list.get(0).getRebateAmount();

        // 实际降本率 = (实际降本率的分子+“返利(元)”+“技术降本(元)”)/实际降本率的分母
        BigDecimal actual = (numerator.add(rebateAmount).add(costReductionAmount))
                .divide(denominator, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE);
        log.info("降本目标达成度- 技术降本 costReductionAmount【{}】返利rebateAmount【{}】,实际降本率actual【{}】"
                ,costReductionAmount,rebateAmount,actual);
        BigDecimal costReductionRate = list.get(0).getCostReductionRate().divide(ComputationConstant.NUM_100, ComputationConstant.SCALE_4, ComputationConstant.ROUNDING_MODE);
        BigDecimal divide = actual.divide(costReductionRate, ComputationConstant.SCALE_4, ComputationConstant.ROUNDING_MODE);
        BigDecimal multiply = divide.multiply(ComputationConstant.NUM_100);
        log.info("降本目标达成度- 技术降本 divide【{}】multiply【{}】,actual【{}】costReductionRate【{}】"
                ,divide,multiply,actual,costReductionRate);
        return multiply;
    }

    public BigDecimal scoreDailyServiceWillingness(ComputationScoreDTO scoreDTO) {
        String supplierNo = scoreDTO.getSupplierCode();
        List<PerformanceOrderPO> performanceOrderPOS = scoreDTO.getOrderMap()
                .get(OrderTypeEnum.BUSINESS_PROJECT_DEVELOPMENT_INDICATOR.getCode());
        if (CollUtil.isEmpty(performanceOrderPOS)){
            return null;
        }
        List<String> orderList = performanceOrderPOS.stream()
                .map(PerformanceOrderPO::getOrderNo).distinct().collect(Collectors.toList());
        // 查询服务意愿评分
        List<PerformanceOrderDailyWillingnessPO> list = orderDailyWillingnessMapperService.lambdaQuery()
                .eq(PerformanceOrderDailyWillingnessPO::getSupplierCode, supplierNo)
                //.in(PerformanceOrderDailyWillingnessPO::getCategoryCode, scoreDTO.getTwoCategoryCode())
                .in(PerformanceOrderDailyWillingnessPO::getOrderNo, orderList)
                // 增加字段
                .eq(PerformanceOrderDailyWillingnessPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        if(CollUtil.isEmpty(list)){
            return null;
        }
        return new BigDecimal(OrderDailyWillingnessEnum.getScoreByCode(list.get(0).getDailyWillingness()));
    }


    private BigDecimal scoreOTS(ComputationScoreDTO scoreDTO) {
        log.info("计算-项目开发- OTS送样及时率 - 二级指标计算结果 - 季度不计算");
        String supplierNo = scoreDTO.getSupplierCode();
        List<PerformanceOrderPO> performanceOrderPOS = scoreDTO.getOrderMap()
                .get(OrderTypeEnum.BUSINESS_PROJECT_DEVELOPMENT_INDICATOR.getCode());
        log.info("计算-项目开发- OTS送样及时率 - 二级指标计算结果 - 【{}】", performanceOrderPOS);
        if(CollUtil.isEmpty(performanceOrderPOS)){
            return null;
        }
        List<String> orderList = performanceOrderPOS.stream()
                .map(PerformanceOrderPO::getOrderNo).distinct().collect(Collectors.toList());
        String startString = com.weifu.srm.common.util.DateUtil.dateFormat(scoreDTO.getStartExamineCycleTime(), ComputationConstant.DATE_YYYY_MM_DD);
        String endString = com.weifu.srm.common.util.DateUtil.dateFormat(scoreDTO.getEndExamineCycleTime(), ComputationConstant.DATE_YYYY_MM_DD);
        // “6.4.4 绩效工单提交数据_商务&项目开发指标数据(OTS及时送样率)”
        List<PerformanceOrderOtsMaterialDeliveryPO> list = orderOtsMaterialDeliveryMapperService.lambdaQuery()
                .eq(PerformanceOrderOtsMaterialDeliveryPO::getSupplierCode, supplierNo)
                .in(PerformanceOrderOtsMaterialDeliveryPO::getOrderNo, orderList)
                .between(PerformanceOrderOtsMaterialDeliveryPO::getYearMonthDate, startString,endString)
                .eq(PerformanceOrderOtsMaterialDeliveryPO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        if(CollUtil.isEmpty(list)){
            return null;
        }
        // OTS零件及时送样批次数
        int onTimeCount = list.stream().mapToInt(PerformanceOrderOtsMaterialDeliveryPO::getBatchTimelyQuantity).sum();
        // OTS零件送样总批次数
        int allTimeCount = list.stream().mapToInt(PerformanceOrderOtsMaterialDeliveryPO::getBatchTotalQuantity).sum();
        log.info("计算-项目开发- OTS送样及时率 - 二级指标计算结果 - onTimeCount【{}】allTimeCount【{}】",onTimeCount, allTimeCount);
        return (new BigDecimal(onTimeCount)
                .divide(new BigDecimal(allTimeCount), ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE));
    }


    public BigDecimal scorePPAP(ComputationScoreDTO scoreDTO) {
        String supplierNo = scoreDTO.getSupplierCode();
        Date start = scoreDTO.getStartExamineCycleTime();
        Date end = scoreDTO.getEndExamineCycleTime();
        Long checkPPAPTime = scoreDTO.getCheckPPAPTime();
        // 数据集b(来料日期), “PWS签署时间+PPAP界定时长”>=“来料日期”, PSW签署时间不为空，来料日期 in 考核周期
        List<BizMaterialInspectionPSWDataPO> list = inspectionPSWMapperService.lambdaQuery()
                .between(BizMaterialInspectionPSWDataPO::getDeliveryDate, start, end)
                .eq(BizMaterialInspectionPSWDataPO::getSapSupplierCode, supplierNo)
                .isNotNull(BizMaterialInspectionPSWDataPO::getPswTime)
                .apply("DATE_ADD(psw_time, INTERVAL {0} MONTH) >= delivery_date", checkPPAPTime)
                .list();
        log.info("计算供应商【{}】,PPAP->list【{}】", supplierNo,list);
        if(CollUtil.isEmpty(list)){
            return null;
        }

        // 存储中间数据b
        List<BizPPAPPSWDownloadDataPO> saveDataList = list.stream()
                .map(preComputationConvert::toPPAPPSWDownloadDataPO)
                .peek(e -> e.setPerformanceNo(scoreDTO.getPerformanceNo()))
                .peek(e -> e.setIsBefore(YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());
        bizPPAPPSWDownMapperService.saveBatch(saveDataList, 200);

        // “PU检验结果确认”字段为“OK”
        long okCountNum = saveDataList.stream()
                .filter(e-> ObjectUtil.equals(ComputationConstant.OK, e.getPuTestResults()))
                .count();
        BigDecimal numerator = new BigDecimal(okCountNum);
        BigDecimal denominator = new BigDecimal(saveDataList.size());
        log.info("计算供应商【{}】,PPAP->numerator【{}】numerator【{}】", supplierNo,numerator,denominator);
        return numerator.divide(denominator, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE);
    }
}
