package com.weifu.srm.supplier.performance.manager.computation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.supplier.performance.constants.ComputationConstant;
import com.weifu.srm.supplier.performance.convert.PerformanceSupplierScoreConvert;
import com.weifu.srm.supplier.performance.dto.ComputationScoreDTO;
import com.weifu.srm.supplier.performance.enums.*;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceSupplierScoreMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.ComputationLogicSupplierScoreMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.computation.SchemeTwoLogicResultMapperService;
import com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationLogicSupplierScorePO;
import com.weifu.srm.supplier.performance.repository.po.computation.ComputationSchemeTwoLogicResultPO;
import com.weifu.srm.supplier.performance.response.PerformanceScoringRuleRespDTO;
import com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationInfoResDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationSchemeOneDTO;
import com.weifu.srm.supplier.performance.response.computation.ComputationSchemeTwoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ComputationLevelManager {

    private final PerformanceSupplierScoreConvert performanceSupplierScoreConvert;
    private final SchemeTwoLogicResultMapperService schemeTwoLogicResultMapperService;
    private final ComputationLogicSupplierScoreMapperService logicSupplierScoreMapperService;

    public ComputationInfoResDTO computationScore(ComputationScoreDTO scoreDTO, ComputationInfoResDTO computationInfo){
        // 计算加减分
        computationQuotaScore(computationInfo);
        // 计算一级得分
        computationSchemeOne(scoreDTO, computationInfo);
        // 计算总得分
        computationAllScore(scoreDTO, computationInfo);
        return computationInfo;
    }

    public void computationQuotaScore(ComputationInfoResDTO computationInfo){
        String supplierNo = computationInfo.getSupplierCode();
        log.info("计算加减分,供应商是【{}】", computationInfo.getSupplierCode());
        // 查询供应商在周期内的加减分
        List<ComputationLogicSupplierScorePO> list = logicSupplierScoreMapperService.lambdaQuery()
                .eq(ComputationLogicSupplierScorePO::getSupplierCode, supplierNo)
                .eq(ComputationLogicSupplierScorePO::getPerformanceNo, computationInfo.getPerformanceNo())
                .eq(ComputationLogicSupplierScorePO::getIsDelete, YesOrNoEnum.NO.getCode())
                .list();
        Map<String, ComputationSchemeOneDTO> schemeOneMap = computationInfo.getComputationSchemeOneDTOMap();
        if(CollUtil.isEmpty(list)){
            log.info("供应商【{}】没有加减分记录", supplierNo);
            Collection<ComputationSchemeOneDTO> values = schemeOneMap.values();
            for (ComputationSchemeOneDTO value :values){
                value.setFirstLevelBonus(0);
                value.setFirstLevelDeduct(0);
            }
            return;
        }
        List<PerformanceSupplierScoreRespDTO> scoreList = list.stream()
                    .map(performanceSupplierScoreConvert::toDTOLogicSupplierScore).collect(Collectors.toList());
        computationInfo.setSupplierAddScoreList(scoreList);

        Map<String, List<PerformanceSupplierScoreRespDTO>> scoreMap = scoreList.stream()
                                .collect(Collectors.groupingBy(PerformanceSupplierScoreRespDTO::getFirstLevel));


        for (int i = 0; i < LevelOneEnum.LEVEL_ONE.getName().length; i++) {
            String levelOne = LevelOneEnum.LEVEL_ONE.getName()[i];
            ComputationSchemeOneDTO firstLevel = getFirstLeveScoreBonusAndDeduct(schemeOneMap, levelOne, scoreMap, supplierNo);
            schemeOneMap.put(levelOne, firstLevel);
        }
    }

    private static ComputationSchemeOneDTO getFirstLeveScoreBonusAndDeduct(Map<String, ComputationSchemeOneDTO> schemeOneMap, String levelOne, Map<String, List<PerformanceSupplierScoreRespDTO>> scoreMap, String supplierNo) {
        ComputationSchemeOneDTO firstLevel = schemeOneMap.get(levelOne);
        List<PerformanceSupplierScoreRespDTO> listTemp = scoreMap.get(levelOne);

        if(CollUtil.isEmpty(listTemp)){
            listTemp = CollUtil.newArrayList();
            log.info("该供应商 [{} 的一级指标:{}没有加减分条目", supplierNo, levelOne);
        }
        int firstLevelBonus = 0;
        int firstLevelDeduct = 0;
        for (PerformanceSupplierScoreRespDTO supplierScorePO : listTemp){
            if(ObjectUtil.equals(supplierScorePO.getScoreType(), ScoreTypeEnum.ADD.getCode())){
                firstLevelBonus += supplierScorePO.getScore();
                if(firstLevelBonus > firstLevel.getFirstLevelBonusCeiling()){
                    firstLevelBonus = firstLevel.getFirstLevelBonusCeiling();
                }
            }else {
                firstLevelDeduct += supplierScorePO.getScore();
                if(firstLevelDeduct > firstLevel.getFirstLevelDeductCeiling()){
                    firstLevelDeduct = firstLevel.getFirstLevelDeductCeiling();
                }
            }
        }
        firstLevel.setFirstLevelBonus(firstLevelBonus);
        firstLevel.setFirstLevelDeduct(firstLevelDeduct);
        return firstLevel;
    }

    public void computationSchemeOne(ComputationScoreDTO scoreDTO, ComputationInfoResDTO computationInfo){
        log.info("计算一级指标,供应商是【{}】", computationInfo.getSupplierCode());
        Map<String, BigDecimal> twoLogicResultMap = getCalculationResultMap(computationInfo);

        Map<String, ComputationSchemeTwoDTO> computationSchemeTwoDTOMap = computationInfo.getComputationSchemeTwoDTOMap();

        for (String key : computationSchemeTwoDTOMap.keySet()) {
            ComputationSchemeTwoDTO schemeTwo = computationSchemeTwoDTOMap.get(key);
            setTwoScoreToOneLevel(computationInfo, twoLogicResultMap.get(key), schemeTwo,
                    scoreDTO.getPerformanceScoringRuleMap());
        }

        Map<String, ComputationSchemeOneDTO> schemeOneMap = computationInfo.getComputationSchemeOneDTOMap();
        for (int i = 0; i < LevelOneEnum.LEVEL_ONE.getName().length; i++) {
            String levelOne = LevelOneEnum.LEVEL_ONE.getName()[i];
            ComputationSchemeOneDTO firstLevel = schemeOneMap.get(levelOne);
            firstLevel.setOneScore(firstLevel.getAddBeforeScore()
                    .add(new BigDecimal(firstLevel.getFirstLevelBonus()))
                    .subtract(new BigDecimal(firstLevel.getFirstLevelDeduct())));
            schemeOneMap.put(levelOne, firstLevel);
            log.debug("一级指标【{}】，info is 【{}】", levelOne, firstLevel);
        }
    }

    private Map<String, BigDecimal> getCalculationResultMap(ComputationInfoResDTO computationInfo) {
        List<ComputationSchemeTwoLogicResultPO> twoLogicResultList = schemeTwoLogicResultMapperService.lambdaQuery()
                .eq(ComputationSchemeTwoLogicResultPO::getPerformanceNo, computationInfo.getPerformanceNo())
                .eq(ComputationSchemeTwoLogicResultPO::getSupplierCode, computationInfo.getSupplierCode())
                .list();
        Map<String, BigDecimal> map = new HashMap<>();
        for(ComputationSchemeTwoLogicResultPO po : twoLogicResultList){
            map.put(po.getSecondLevel(), po.getCalculationResult());
        }
        return map;
    }

    public void setTwoScoreToOneLevel(ComputationInfoResDTO computationInfo,BigDecimal calculationResult,
                                      ComputationSchemeTwoDTO schemeTwo, Map<String, List<PerformanceScoringRuleRespDTO>> scoringRuleMap){
        log.info("计算二级指标getSecondLevel【{}】calculationResult【{}】",schemeTwo.getSecondLevel(), calculationResult);
        ComputationSchemeOneDTO schemeOne = computationInfo.getComputationSchemeOneDTOMap()
                .get(schemeTwo.getFirstLevel());
        // 计算梯度后得分 DELIVER_SUPPLIER_TIMELY_DELIVERY_RATE
        List<PerformanceScoringRuleRespDTO> ruleList = scoringRuleMap.get(schemeTwo.getSecondLevel());
        schemeTwo.setCalculationResult(calculationResult);
        BigDecimal score = ComputationConstant.NUM_0;
        try{
            score =  addGradient(calculationResult,schemeTwo.getSecondLevel(), ruleList);
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }

        log.info("计算二级指标getSecondLevel【{}】score【{}】",schemeTwo.getSecondLevel(), score);
        schemeTwo.setScore(score);
        // 二级加权后得分
        BigDecimal weightScore = score.multiply(new BigDecimal(schemeTwo.getSecondLevelProportion())
                .divide(ComputationConstant.NUM_100, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE));
        schemeTwo.setAfterScore(weightScore);
        BigDecimal addBeforeScore = schemeOne.getAddBeforeScore();
        if(ObjectUtil.isNull(addBeforeScore)){
            addBeforeScore = ComputationConstant.NUM_0;
        }
        // 汇总二级得分
        schemeOne.setAddBeforeScore(addBeforeScore.add(weightScore));
        log.debug("二级指标【{}】单项计算完成【{}】，一级指标得分【{}】",schemeTwo.getSecondLevel(),score,schemeOne.getAddBeforeScore());
    }

    public void computationAllScore(ComputationScoreDTO scoreDTO, ComputationInfoResDTO computationInfo) {
        log.info("计算总分,供应商是【{}】", computationInfo.getSupplierCode());

        // 实际得分=“质量”得分*一级指标权重 + “交付”得分*一级指标权重 + “商务”得分*一级指标权重 +“项目开发”得分*一级指标权重
        BigDecimal supplierScore = getSupplierScore(computationInfo);
        computationInfo.setSupplierScore(supplierScore);
        // 年度满总=100
        BigDecimal fullScore = ComputationConstant.NUM_100;
        if(ObjectUtil.equals(scoreDTO.getPerformanceType(), ScoringRuleCycleEnum.YEAR_QUARTER.getCode())){
            // 季度 满分总分=100- 100*(【“商务”二级指标排除的权重】*一级指标“商务”权重 +【“项目开发”二级指标排除的权重】*一级指标“项目开发”权重
            fullScore = getQuarterFullScore(computationInfo);
        }
        computationInfo.setFullScore(fullScore);
        // 得分率 = (实际得分/满分)*100
        computationInfo.setScoringRate(supplierScore
                .divide(fullScore,ComputationConstant.SCALE_4, ComputationConstant.ROUNDING_MODE)
                .multiply(ComputationConstant.NUM_100));
        log.info("满分【{}】实际得分【{}】，得分率【{}】", fullScore,supplierScore, computationInfo.getScoringRate());
    }

    private static BigDecimal getSupplierScore(ComputationInfoResDTO computationInfo) {
        Map<String, ComputationSchemeOneDTO> oneDTOMap = computationInfo.getComputationSchemeOneDTOMap();
        BigDecimal supplierScore = ComputationConstant.NUM_0;
        for (ComputationSchemeOneDTO schemeOne : oneDTOMap.values()) {
            BigDecimal afterScore = schemeOne.getOneScore().multiply(new BigDecimal(schemeOne.getFirstLevelProportion())
                    .divide(ComputationConstant.NUM_100, ComputationConstant.SCALE, ComputationConstant.ROUNDING_MODE));
            supplierScore = supplierScore.add(afterScore);
            schemeOne.setAfterScore(afterScore);
        }
        return supplierScore;
    }

    public BigDecimal getQuarterFullScore(ComputationInfoResDTO computationInfo){
        // 季度 满分总分=100- 100*(【“商务”二级指标排除的权重】*一级指标“商务”权重 +【“项目开发”二级指标排除的权重】*一级指标“项目开发”权重
        Map<String, ComputationSchemeTwoDTO> twoDTOMap = computationInfo.getComputationSchemeTwoDTOMap();
        Map<String, ComputationSchemeOneDTO> oneDTOMap = computationInfo.getComputationSchemeOneDTOMap();

        // “商务”二级指标排除的权重
        ComputationSchemeTwoDTO busTwo = twoDTOMap.get(SecondLevelQuotaEnum.BUSINESS_ACHIEVEMENT_COST_REDUCTION_GOALS.getSecondCode());
        BigDecimal busWeightTwoOut = ComputationConstant.NUM_100.subtract(new BigDecimal(busTwo.getSecondLevelProportion()));
        // 一级指标“商务”权重
        ComputationSchemeOneDTO busOne = oneDTOMap.get(FirstLevelQuotaEnum.BUSINESS.getCode());
        BigDecimal busWeight = new BigDecimal(busOne.getFirstLevelProportion());
        // “项目开发”二级指标排除的权重
        ComputationSchemeTwoDTO projectTwo = twoDTOMap.get(SecondLevelQuotaEnum.PROJECT_QUALIFICATION_RATE.getSecondCode());
        BigDecimal projectWeightTwoOut = ComputationConstant.NUM_100.subtract(new BigDecimal(projectTwo.getSecondLevelProportion()));
        // 一级指标“项目开发”权重
        ComputationSchemeOneDTO projectOne = oneDTOMap.get(FirstLevelQuotaEnum.PROJECT_DEVELOPMENT.getCode());
        BigDecimal projectWeight = new BigDecimal(projectOne.getFirstLevelProportion());
        return ComputationConstant.NUM_100.subtract(ComputationConstant.NUM_100
                        .multiply((busWeightTwoOut.multiply(busWeight))
                        .add(projectWeightTwoOut.multiply(projectWeight))));
    }

    public BigDecimal addGradient(BigDecimal calculationResult, String secondLevel,
                                                     List<PerformanceScoringRuleRespDTO> ruleList){
        ScoringRuleEnum ruleEnum = null;
        GradientTypeEnum gradientTypeEnum = null;
        if(ObjectUtil.isNull(calculationResult)){
            log.info("addGradient {}", secondLevel);
            ruleEnum = ScoringRuleEnum.getSecondLevelNull(secondLevel);
            log.info("addGradient {}", ruleEnum);
        }else {
            List<ScoringRuleEnum> secondLevelRuleList = ScoringRuleEnum.getSecondLevel(secondLevel);
            for (ScoringRuleEnum scoringRuleEnum : secondLevelRuleList){
                String[] range = scoringRuleEnum.getRange();
                if(ObjectUtil.isNull(range)){
                    continue;
                }
                int start = new BigDecimal(range[0]).compareTo(calculationResult);
                int end = calculationResult.compareTo(new BigDecimal(range[1]));
                log.info("addGradient secondLevel【{}】,scoringRuleEnum【{}】 start【{}】, end【{}】",secondLevel,scoringRuleEnum.getName(), start, end);
                ruleEnum = getScoringRuleEnum(scoringRuleEnum, start, end, ruleEnum);
                if(ObjectUtil.equals(GradientTypeEnum.TYPE_02,scoringRuleEnum.getType())){
                    gradientTypeEnum = GradientTypeEnum.TYPE_02;
                    ruleEnum = scoringRuleEnum;
                }
            }
        }
        log.info("addGradient secondLevel【{}】,ruleEnum【{}】calculationResult【{}】",secondLevel, ruleEnum,calculationResult);
        if(ObjectUtil.isNull(ruleEnum)){
            return null;
        }
        return getScoreFormRule(calculationResult, ruleList, ruleEnum, gradientTypeEnum);
    }

    private static ScoringRuleEnum getScoringRuleEnum(ScoringRuleEnum scoringRuleEnum, int start, int end, ScoringRuleEnum ruleEnum) {

        ruleEnum = getScoringRuleEnumOther(scoringRuleEnum, start, end, ruleEnum);

        if(ObjectUtil.equals(GradientTypeEnum.NUM_04, scoringRuleEnum.getType()) && start == 0){
                ruleEnum = scoringRuleEnum;
        }
        if(ObjectUtil.equals(GradientTypeEnum.NUM_05, scoringRuleEnum.getType()) && end <= 0){
            ruleEnum = scoringRuleEnum;
        }
        if(ObjectUtil.equals(GradientTypeEnum.NUM_06, scoringRuleEnum.getType()) && start <=0 &&  end <= 0){
            ruleEnum = scoringRuleEnum;
        }
        if(ObjectUtil.equals(GradientTypeEnum.TYPE_01, scoringRuleEnum.getType()) && start == 0){
                ruleEnum = scoringRuleEnum;
        }
        return ruleEnum;
    }

    private static ScoringRuleEnum getScoringRuleEnumOther(ScoringRuleEnum scoringRuleEnum, int start, int end, ScoringRuleEnum ruleEnum) {
        if(ObjectUtil.equals(GradientTypeEnum.NUM_01, scoringRuleEnum.getType()) && start <0 && end <= 0){
            ruleEnum = scoringRuleEnum;
        }
        if(ObjectUtil.equals(GradientTypeEnum.NUM_02, scoringRuleEnum.getType()) && start <=0 && end < 0){
            ruleEnum = scoringRuleEnum;
        }

        if(ObjectUtil.equals(GradientTypeEnum.NUM_03, scoringRuleEnum.getType()) && start <0 && end < 0){
            ruleEnum = scoringRuleEnum;
        }
        return ruleEnum;
    }

    private static BigDecimal getScoreFormRule(BigDecimal calculationResult, List<PerformanceScoringRuleRespDTO> ruleList, ScoringRuleEnum ruleEnum, GradientTypeEnum gradientTypeEnum) {
        for (PerformanceScoringRuleRespDTO rule : ruleList){
            if(ObjectUtil.equals(rule.getComputeResult(), ruleEnum.getCode())){
                if(ObjectUtil.isNotNull(gradientTypeEnum) && gradientTypeEnum.equals(GradientTypeEnum.TYPE_02)){
                    return calculationResult.multiply(ComputationConstant.NUM_100);
                }
                return new BigDecimal(rule.getScore());
            }
        }
        return null;
    }

}
