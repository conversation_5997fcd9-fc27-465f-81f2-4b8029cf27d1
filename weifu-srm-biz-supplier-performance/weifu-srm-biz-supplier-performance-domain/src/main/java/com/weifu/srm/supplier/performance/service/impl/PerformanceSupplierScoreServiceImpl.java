package com.weifu.srm.supplier.performance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.supplier.performance.constants.ServiceConstants;
import com.weifu.srm.supplier.performance.enums.BizNoRuleEnum;
import com.weifu.srm.supplier.performance.enums.FirstLevelQuotaEnum;
import com.weifu.srm.supplier.performance.enums.PerformanceAttachmentRecordEnum;
import com.weifu.srm.supplier.performance.manager.AttachmentRecordManager;
import com.weifu.srm.supplier.performance.manager.DictDataManager;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceQuotaScoreMapperService;
import com.weifu.srm.supplier.performance.repository.atomicservice.PerformanceSupplierScoreMapperService;
import com.weifu.srm.supplier.performance.repository.po.PerformanceQuotaScorePO;
import com.weifu.srm.supplier.performance.repository.po.PerformanceSupplierScorePO;
import com.weifu.srm.supplier.performance.request.OperatorBaseReqDTO;
import com.weifu.srm.supplier.performance.request.PerformanceIdReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScoreAddReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScoreEditReqDTO;
import com.weifu.srm.supplier.performance.request.rule.PerformanceSupplierScorePageReqDTO;
import com.weifu.srm.supplier.performance.response.AttachmentRecordRespDTO;
import com.weifu.srm.supplier.performance.response.KeyValueRespDTO;
import com.weifu.srm.supplier.performance.response.PerformanceSupplierScoreRespDTO;
import com.weifu.srm.supplier.performance.service.PerformanceSupplierScoreService;
import com.weifu.srm.supplier.performance.utils.BizNoUtil;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.constants.InternalUserRoleConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商加减分-服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformanceSupplierScoreServiceImpl implements PerformanceSupplierScoreService {
    public static final String DELIVERY_INDICATOR_OWNER = "DELIVERY_INDICATOR_OWNER";
    private final PerformanceSupplierScoreMapperService performanceSupplierScoreMapperService;
    private final PerformanceQuotaScoreMapperService performanceQuotaScoreMapperService;
    private final AttachmentRecordManager attachmentRecordManager;
    private final SysUserApi sysUserApi;
    private final LocaleMessage localeMessage;
    private final DictDataManager dictDataManager;


    @Override
    public PageResponse<PerformanceSupplierScoreRespDTO> page(PerformanceSupplierScorePageReqDTO reqDTO) {
        Page<PerformanceSupplierScoreRespDTO> page = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize());
        IPage<PerformanceSupplierScoreRespDTO> pageResult = performanceSupplierScoreMapperService.listPageByQuery(page, reqDTO);
        List<PerformanceSupplierScoreRespDTO> dtoList = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            List<String> scoreNoList = dtoList.stream().map(PerformanceSupplierScoreRespDTO::getScoreNo).collect(Collectors.toList());
            Map<String, List<AttachmentRecordRespDTO>> annexMap = attachmentRecordManager.listByBusinessNoAndBusinessType(scoreNoList, PerformanceAttachmentRecordEnum.PERFORMANCE_SUPPLIER_SCORE_FILE.getCode());
            Map<String, String> firstDictMap = dictDataManager.getDictValueMap(ServiceConstants.PERFORMANCE_FIRST_LEVEL_QUOTA);
            for (PerformanceSupplierScoreRespDTO dto : dtoList) {
                dto.setFirstLevelName(firstDictMap.get(dto.getFirstLevel()));
                dto.setAnnexList(annexMap.get(dto.getScoreNo()));
            }
        }
        return PageResponse.toResult(reqDTO.getPageNum(), reqDTO.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public List<KeyValueRespDTO<String, String>> listQuota(OperatorBaseReqDTO reqDTO) {
        ApiResponse<List<String>> apiResponse = sysUserApi.listRoleIdByUserId(reqDTO.getOperationBy());
        if (apiResponse == null || CollectionUtils.isEmpty(apiResponse.getData())) {
            return Collections.emptyList();
        }
        List<String> roleList = apiResponse.getData();
        List<KeyValueRespDTO<String, String>> list = Lists.newArrayList();
        Map<String, String> firstDictMap = dictDataManager.getDictValueMap(ServiceConstants.PERFORMANCE_FIRST_LEVEL_QUOTA);
        for (String role : roleList) {
            if (InternalUserRoleConstants.CPE.equals(role)) {
                list.add(getKeyValue(FirstLevelQuotaEnum.BUSINESS, firstDictMap));
                list.add(getKeyValue(FirstLevelQuotaEnum.PROJECT_DEVELOPMENT, firstDictMap));
            } else if (InternalUserRoleConstants.SQE.equals(role)) {
                list.add(getKeyValue(FirstLevelQuotaEnum.QUALITY, firstDictMap));
            } else if (DELIVERY_INDICATOR_OWNER.equals(role)) {
                list.add(getKeyValue(FirstLevelQuotaEnum.DELIVER, firstDictMap));
            }

        }
        return list;
    }

    private KeyValueRespDTO<String, String> getKeyValue(FirstLevelQuotaEnum firstLevelQuotaEnum, Map<String, String> firstDictMap) {
        KeyValueRespDTO<String, String> dto = new KeyValueRespDTO<>();
        dto.setKey(firstDictMap.get(firstLevelQuotaEnum.getCode()));
        dto.setValue(firstLevelQuotaEnum.getCode());
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long add(PerformanceSupplierScoreAddReqDTO reqDTO) {
        boolean isRepeat = performanceSupplierScoreMapperService.validRepeatData(null, reqDTO.getFirstLevel(), reqDTO.getQuotaId(), reqDTO.getSupplierCode(), DateUtil.format(reqDTO.getHappenTime(), DatePattern.NORM_DATE_FORMAT));
        if (isRepeat) {
            throw new BizFailException(localeMessage.getMessage("performance.supplier.score.valid.repeat"));
        }
        PerformanceSupplierScorePO po = BeanUtil.copyProperties(reqDTO, PerformanceSupplierScorePO.class);
        PerformanceQuotaScorePO performanceQuotaScore = performanceQuotaScoreMapperService.getById(reqDTO.getQuotaId());
        if (performanceQuotaScore != null) {
            po.setScore(performanceQuotaScore.getScore());
            po.setScoreType(performanceQuotaScore.getScoreType());
        }
        po.setScoreNo(BizNoUtil.generateNo(BizNoRuleEnum.PERFORMANCE_SUPPLIER_SCORE));
        BaseEntityUtil.setCommon(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), DateUtil.date());
        performanceSupplierScoreMapperService.save(po);
        attachmentRecordManager.saveBatchAttachmentRecord(reqDTO.getAnnexList(), PerformanceAttachmentRecordEnum.PERFORMANCE_SUPPLIER_SCORE_FILE.getCode(), po.getScoreNo(), reqDTO);
        return po.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PerformanceSupplierScoreEditReqDTO reqDTO) {
        PerformanceSupplierScorePO po = performanceSupplierScoreMapperService.getById(reqDTO.getId());
        boolean isRepeat = performanceSupplierScoreMapperService.validRepeatData(reqDTO.getId(), po.getFirstLevel(), po.getQuotaId(), po.getSupplierCode(), DateUtil.format(reqDTO.getHappenTime(), DatePattern.NORM_DATE_FORMAT));
        if (isRepeat) {
            throw new BizFailException(localeMessage.getMessage("performance.supplier.score.valid.repeat"));
        }
        po.setId(reqDTO.getId());
        po.setHappenTime(reqDTO.getHappenTime());
        po.setEventDesc(reqDTO.getEventDesc());
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperationBy(), reqDTO.getOperationByName(), DateUtil.date());
        performanceSupplierScoreMapperService.updateById(po);
        attachmentRecordManager.saveBatchAttachmentRecord(reqDTO.getAnnexList(), PerformanceAttachmentRecordEnum.PERFORMANCE_SUPPLIER_SCORE_FILE.getCode(), po.getScoreNo(), reqDTO);
    }

    @Override
    public void delete(PerformanceIdReqDTO reqDTO) {
        performanceSupplierScoreMapperService.removeById(reqDTO.getId());
    }
}
