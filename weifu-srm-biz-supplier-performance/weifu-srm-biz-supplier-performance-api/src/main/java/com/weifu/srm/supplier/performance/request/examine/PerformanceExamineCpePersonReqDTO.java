package com.weifu.srm.supplier.performance.request.examine;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 绩效考核-人员设定
 * @Date 2024-09-20
 */
@Data
@ApiModel("绩效考核-人员设定")
public class PerformanceExamineCpePersonReqDTO {

    @ExcelProperty("二级品类编码*")
    @ApiModelProperty(value = "二级品类编码")
    private String categoryCode;

    @ExcelProperty("二级品类")
    @ApiModelProperty(value = "二级品类名称")
    private String categoryName;

    @ExcelProperty("CPE工程师域账号*")
    @ApiModelProperty(value = "cpe工程师名称")
    private String cpeName;

    @ExcelProperty("CPE工程师组长域账号*")
    @ApiModelProperty(value = "cpe工程师组长名称")
    private String cpeMasterName;

}
