package com.weifu.srm.sourcing.service.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.composite.constants.CompositeTopicConstants;
import com.weifu.srm.composite.mq.CreateSendEmailTaskMQ;
import com.weifu.srm.masterdata.api.CategoryApi;
import com.weifu.srm.masterdata.enums.CategoryRoleEnum;
import com.weifu.srm.masterdata.response.CategoryEngineerResultDTO;
import com.weifu.srm.mq.sender.manager.MqManager;
import com.weifu.srm.sourcing.eunms.SourcingMsgTemplateEnum;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingSupplierMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderNotificationMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderSupplierMapperService;
import com.weifu.srm.sourcing.repository.constants.MsgConstants;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderSupplierPO;
import com.weifu.srm.user.api.SysUserApi;
import com.weifu.srm.user.response.BaseSysUserRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SourcingEmailBiz {

    private final MqManager mqManager;
    private final CategoryApi categoryApi;
    private final SysUserApi sysUserApi;
    private final InquiryOrderMapperService inquiryOrderMapperService;
    private final InquiryOrderSupplierMapperService inquiryOrderSupplierMapperService;
    private final BusinessDesignationOrderNotificationMapperService businessDesignationOrderNotificationMapperService;
    private final BiddingSupplierMapperService biddingSupplierMapperService;

    public void sendInquiryResultNotification(InquiryOrderPO inquiryOrder) {
        String toUser = inquiryOrder.getInquiryEngineerEmail();
        String title = SourcingMsgTemplateEnum.MAIL_INQUIRY_VIEW_QUOTATION.getTitle();
        String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_INQUIRY_VIEW_QUOTATION, inquiryOrder.getInquiryNo());

        CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
        paramDTO.setTitle(title);
        paramDTO.setContent(content);
        paramDTO.setRecipients(toUser);
        paramDTO.setBusinessNo(inquiryOrder.getInquiryNo());
        mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
    }

    public void sendProxyQuotationInvitationNotification(InquiryOrderPO inquiryOrder, InquiryOrderSupplierPO inquirySupplier) {
        String title = SourcingMsgTemplateEnum.MAIL_INQUIRY_MATERIAL_QUOTE_INVITATION.getTitle();
        String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_INQUIRY_MATERIAL_QUOTE_INVITATION,
                inquirySupplier.getSupplierName(), inquiryOrder.getInquiryNo(), this.round(inquiryOrder),
                DateUtil.format(inquiryOrder.getQuotationFeedbackDeadline(), DatePattern.NORM_DATE_PATTERN));

        CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
        paramDTO.setTitle(title);
        paramDTO.setContent(content);
        paramDTO.setRecipients(inquiryOrder.getInquiryEngineerEmail());
        paramDTO.setBusinessNo(inquiryOrder.getInquiryNo());
        mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
    }

    public void sendQuotationInvitationNotification(InquiryOrderPO inquiryOrder, InquiryOrderSupplierPO inquirySupplier) {
        Long cpeMasterUserId = getCpeMasterByCategoryCode(inquiryOrder.getCategoryCode(), inquiryOrder.getInquiryNo());

        Long supplierUserId = inquirySupplier.getSupplierUserId();
        List<Long> userIds = List.of(cpeMasterUserId, supplierUserId);
        userIds = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, String> emailMap = getEmailByUserIds(userIds, inquiryOrder.getInquiryNo());

        String toUser = emailMap.get(supplierUserId);

        String cc = inquiryOrder.getInquiryEngineerEmail();
        /*String cpeMaterEmail = emailMap.get(cpeMasterUserId);
        if (StringUtils.isNotBlank(cpeMaterEmail)) {
            cc += (";" + cpeMaterEmail);
        }*/

        String title = SourcingMsgTemplateEnum.MAIL_INQUIRY_MATERIAL_QUOTE_INVITATION.getTitle();
        String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_INQUIRY_MATERIAL_QUOTE_INVITATION,
                inquirySupplier.getSupplierName(), inquiryOrder.getInquiryNo(), this.round(inquiryOrder),
                DateUtil.format(inquiryOrder.getQuotationFeedbackDeadline(), DatePattern.NORM_DATE_PATTERN));

        CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
        paramDTO.setTitle(title);
        paramDTO.setContent(content);
        paramDTO.setRecipients(toUser);
        paramDTO.setCcRecipients(cc);
        paramDTO.setBusinessNo(inquiryOrder.getInquiryNo());
        mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
    }

    public void sendDesignationNotification(BusinessDesignationOrderPO designationOrder) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(designationOrder.getInquiryNo());

        List<InquiryOrderSupplierPO> inquirySuppliers = inquiryOrderSupplierMapperService
                .queryBy(designationOrder.getInquiryNo(), YesOrNoEnum.YES.getCode());
        Map<String, InquiryOrderSupplierPO> inquirySupplierMap = inquirySuppliers.stream()
                .collect(Collectors.toMap(InquiryOrderSupplierPO::getSapSupplierCode, o -> o));

        List<Long> userIds = inquirySuppliers.stream()
                .map(InquiryOrderSupplierPO::getSupplierUserId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> emailMap = getEmailByUserIds(userIds, designationOrder.getDesignationOrderNo());

        Long cpeMasterUserId = getCpeMasterByCategoryCode(designationOrder.getCategoryCode(), designationOrder.getDesignationOrderNo());

        List<BusinessDesignationOrderNotificationPO> notifications = businessDesignationOrderNotificationMapperService
                .queryByDesignationOrderNo(designationOrder.getDesignationOrderNo());
        for (BusinessDesignationOrderNotificationPO notification : notifications) {
            InquiryOrderSupplierPO inquirySupplier = inquirySupplierMap.get(notification.getSapSupplierCode());

            Long supplierUserId = inquirySupplier.getSupplierUserId();

            String toUser = emailMap.get(supplierUserId);

            String cpeMaterEmail = emailMap.get(cpeMasterUserId);

            String cc = inquiryOrder.getInquiryEngineerEmail();
            if (StringUtils.isNotBlank(cpeMaterEmail)) {
                cc += (";" + cpeMaterEmail);
            }

            String title = SourcingMsgTemplateEnum.MAIL_DESIGNATION_NOTIFICATION.getTitle();
            String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_DESIGNATION_NOTIFICATION,
                    inquirySupplier.getSupplierName(), notification.getDesignationOrderNo(), notification.getInquiryNo());

            CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
            paramDTO.setTitle(title);
            paramDTO.setContent(content);
            paramDTO.setRecipients(toUser);
            paramDTO.setCcRecipients(cc);
            paramDTO.setBusinessNo(designationOrder.getDesignationOrderNo());
            mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
        }
    }

    private long getCpeMasterByCategoryCode(String categoryCode, String businessNo) {
        try {
            ApiResponse<List<CategoryEngineerResultDTO>> response = categoryApi
                    .queryCategoryEngineerByCategoryCodes(List.of(categoryCode));
            if (Objects.equals(Boolean.TRUE, response.getSucc())) {
                if (CollectionUtils.isNotEmpty(response.getData())) {
                    for (CategoryEngineerResultDTO o : response.getData()) {
                        if (Objects.equals(CategoryRoleEnum.CPE_MASTER.getCode(), o.getRoleId())) {
                            return o.getUserId();
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.info("获取品类组长失败 业务单号：{}，品类：{}", businessNo, categoryCode, ex);
        }
        return -1;
    }

    private Map<Long, String> getEmailByUserIds(List<Long> userIds, String businessNo) {
        Map<Long, String> emailMap = new HashMap<>();
        try {
            ApiResponse<List<BaseSysUserRespDTO>> response = sysUserApi.findByIds(userIds);
            if (Objects.equals(Boolean.TRUE, response.getSucc())) {
                if (CollectionUtils.isNotEmpty(response.getData())) {
                    for (BaseSysUserRespDTO o : response.getData()) {
                        emailMap.put(o.getId(), o.getEmail());
                    }
                }
            }
        } catch (Exception ex) {
            log.info("获取用户邮箱失败 业务单号：{}，用户Ids：{}", businessNo, userIds, ex);
        }
        return emailMap;
    }

    public void sendBiddingCompleteNotification(BiddingPO bidding) {
        ApiResponse<List<BaseSysUserRespDTO>> response = sysUserApi.findByIds(List.of(bidding.getCreateBy()));
        if (Objects.equals(Boolean.TRUE, response.getSucc())) {
            List<BaseSysUserRespDTO> sysUsers = response.getData();
            Map<Long, BaseSysUserRespDTO> sysUserMap = sysUsers.stream().collect(Collectors.toMap(BaseSysUserRespDTO::getId, o -> o));
            BaseSysUserRespDTO sysUser = sysUserMap.get(bidding.getCreateBy());
            if (sysUser != null) {
                String title = buildMailTitle(SourcingMsgTemplateEnum.MAIL_BIDDING_COMPLETE, bidding.getBiddingNo(), bidding.getBiddingName());
                String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_BIDDING_COMPLETE, bidding.getBiddingName());

                CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
                paramDTO.setTitle(title);
                paramDTO.setContent(content);
                paramDTO.setRecipients(sysUser.getEmail());
                paramDTO.setBusinessNo(bidding.getBiddingNo());
                mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
            }
        }
    }

    public void sendBiddingFailureNotification(BiddingPO bidding) {
        ApiResponse<List<BaseSysUserRespDTO>> response = sysUserApi.findByIds(List.of(bidding.getCreateBy()));
        if (Objects.equals(Boolean.TRUE, response.getSucc())) {
            List<BaseSysUserRespDTO> sysUsers = response.getData();
            Map<Long, BaseSysUserRespDTO> sysUserMap = sysUsers.stream().collect(Collectors.toMap(BaseSysUserRespDTO::getId, o -> o));
            BaseSysUserRespDTO sysUser = sysUserMap.get(bidding.getCreateBy());
            if (sysUser != null) {
                String title = buildMailTitle(SourcingMsgTemplateEnum.MAIL_BIDDING_FAILURE, bidding.getBiddingNo(), bidding.getBiddingName());
                String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_BIDDING_FAILURE, bidding.getBiddingName());

                CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
                paramDTO.setTitle(title);
                paramDTO.setContent(content);
                paramDTO.setRecipients(sysUser.getEmail());
                paramDTO.setBusinessNo(bidding.getBiddingNo());
                mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
            }
        }
    }

    public void sendBiddingInvitationNotification(BiddingPO bidding) {
        List<BiddingSupplierPO> biddingSuppliers = biddingSupplierMapperService.queryBy(bidding.getBiddingNo());
        List<Long> userIds = biddingSuppliers.stream().map(BiddingSupplierPO::getSupplierUserId).collect(Collectors.toList());
        userIds.add(bidding.getCreateBy());
        ApiResponse<List<BaseSysUserRespDTO>> response = sysUserApi.findByIds(userIds);
        if (Objects.equals(Boolean.TRUE, response.getSucc())) {
            String feedbackDeadline = DateUtil.format(bidding.getFeedbackDeadline(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
            String biddingStartTime = DateUtil.format(bidding.getBiddingStartTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);

            List<BaseSysUserRespDTO> sysUsers = response.getData();
            Map<Long, BaseSysUserRespDTO> sysUserMap = sysUsers.stream().collect(Collectors.toMap(BaseSysUserRespDTO::getId, o -> o));
            BaseSysUserRespDTO ccUser = sysUserMap.get(bidding.getCreateBy());
            String cc = ccUser == null ? "" : ccUser.getEmail();

            String title = buildMailTitle(SourcingMsgTemplateEnum.MAIL_BIDDING_INVITATION, bidding.getBiddingNo(), bidding.getBiddingName());
            for (BiddingSupplierPO biddingSupplier : biddingSuppliers) {
                BaseSysUserRespDTO supplierUser = sysUserMap.get(biddingSupplier.getSupplierUserId());
                if (supplierUser != null) {
                    String content = buildMailContentBody(SourcingMsgTemplateEnum.MAIL_BIDDING_INVITATION,
                            biddingSupplier.getSupplierName(), bidding.getBiddingName(), feedbackDeadline, biddingStartTime);

                    CreateSendEmailTaskMQ paramDTO = new CreateSendEmailTaskMQ();
                    paramDTO.setTitle(title);
                    paramDTO.setContent(content);
                    paramDTO.setRecipients(supplierUser.getEmail());
                    paramDTO.setCcRecipients(cc);
                    paramDTO.setBusinessNo(bidding.getBiddingNo());
                    mqManager.sendTopic(CompositeTopicConstants.CREATE_SEND_EMAIL_TASK, JacksonUtil.bean2Json(paramDTO));
                }
            }
        }
    }

    private String buildMailTitle(SourcingMsgTemplateEnum templateEnum, String... params) {
        String title = templateEnum.getTitle();
        if (params != null && params.length > 0) {
            for (int i = 0; i < params.length; i++) {
                title = title.replace("${" + i + "}", params[i]);
            }
        }
        return title;
    }

    private String buildMailContentBody(SourcingMsgTemplateEnum templateEnum, String... params) {
        String content = templateEnum.getContent();
        if (params != null && params.length > 0) {
            for (int i = 0; i < params.length; i++) {
                content = content.replace("${" + i + "}", params[i]);
            }
        }
        return content;
    }

    private String round(InquiryOrderPO inquiryOrder) {
        if (Objects.equals(YesOrNoEnum.YES.getCode(), inquiryOrder.getIsLastCall())) {
            return MsgConstants.INQUIRY_LAST_CALL_DESC_MSG;
        }
        return inquiryOrder.getInquiryRound().toString();
    }
}
