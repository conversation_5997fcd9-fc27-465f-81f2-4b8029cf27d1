package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.masterdata.response.MaterialResultDTO;
import com.weifu.srm.mybatis.base.BaseEntity;
import com.weifu.srm.price.response.MaterialMinPriceRespDTO;
import com.weifu.srm.sourcing.manager.MaterialManager;
import com.weifu.srm.sourcing.manager.PriceManager;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMaterialMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderBaseFeeMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMaterialMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderRelationFeeMapperService;
import com.weifu.srm.sourcing.repository.enums.QuotationTemplateEnum;
import com.weifu.srm.sourcing.repository.enums.SupplierQuotationStatusEnum;
import com.weifu.srm.sourcing.repository.enums.TwoLevelCostEnum;
import com.weifu.srm.sourcing.repository.po.InquiryOrderMaterialPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderBaseFeePO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMaterialPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRelationFeePO;
import com.weifu.srm.sourcing.request.QuotationAnalysisReqDTO;
import com.weifu.srm.sourcing.request.QuotationAnalysisSingleReqDTO;
import com.weifu.srm.sourcing.response.InquiryOrderSupplierRespDTO;
import com.weifu.srm.sourcing.response.MaterialPriceRespDTO;
import com.weifu.srm.sourcing.response.QuotationAnalysisDetailRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderConditionRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderMaterialRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderSupplierRespDTO;
import com.weifu.srm.sourcing.response.SupplierQuotationCostDetailRespDTO;
import com.weifu.srm.sourcing.response.SupplierQuotationCostRespDTO;
import com.weifu.srm.sourcing.response.SupplierQuotationRespDTO;
import com.weifu.srm.sourcing.service.BasicQuotationAnalysisService;
import com.weifu.srm.sourcing.service.InquiryOrderSupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class BasicQuotationAnalysisServiceImpl implements BasicQuotationAnalysisService {
    private final InquiryOrderMapperService inquiryOrderService;
    private final SupplierQuotationOrderMapperService supplierQuotationOrderService;
    private final SupplierQuotationOrderMaterialMapperService supplierQuotationOrderMaterialService;
    private final SupplierQuotationOrderBaseFeeMapperService l1Service;
    private final SupplierQuotationOrderRelationFeeMapperService l2Service;
    private final InquiryOrderMaterialMapperService inquiryOrderMaterialMapperService;
    private final BusinessDesignationOrderMapperService designationOrderService;
    private final InquiryOrderSupplierService inquiryOrderSupplierService;
    private final PriceManager priceManager;
    private final MaterialManager materialManager;

    @Override
    public List<QuotationAnalysisDetailRespDTO> quotationAnalysisMultiSupplier(QuotationAnalysisReqDTO param) {
        //是否lastCall
        boolean isLastCall = StringUtils.equals("LastCall", param.getRound());
        InquiryOrderPO inquiryOrder = inquiryOrderService.queryBy(param.getInquiryNo());
        if (isLastCall) {
            //如果查询lastCall 但是询价单没有发起LastCall 返回null
            if (!Objects.equals(inquiryOrder.getIsLastCall(), YesOrNoEnum.YES.getCode()))
                return Collections.emptyList();
        }
        //查询询价单数据
        //轮次
        Integer round = NumberUtil.isInteger(param.getRound()) ? NumberUtil.parseInt(param.getRound()) : inquiryOrder.getInquiryRound();

        //查供应商 与轮次 (报价完成的数据)
        LambdaQueryWrapper<SupplierQuotationOrderPO> query = new LambdaQueryWrapper<>();
        query.eq(SupplierQuotationOrderPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.eq(SupplierQuotationOrderPO::getInquiryNo, param.getInquiryNo());
        query.eq(SupplierQuotationOrderPO::getQuotationStatus, SupplierQuotationStatusEnum.QUOTED.getCode());
        query.in(CollUtil.isNotEmpty(param.getSapSupplierCodes()), SupplierQuotationOrderPO::getSapSupplierCode, param.getSapSupplierCodes());
        query.eq(true, SupplierQuotationOrderPO::getQuotationRound, round);
        query.orderByDesc(SupplierQuotationOrderPO::getQuotationRound);

        List<SupplierQuotationOrderPO> quotationOrders = supplierQuotationOrderService.list(query);
        if (CollUtil.isEmpty(quotationOrders)) return Collections.emptyList();


        List<Long> quotationOrderIds = quotationOrders.stream().map(SupplierQuotationOrderPO::getId).collect(Collectors.toList());

        //查物料 与轮次维度
        LambdaQueryWrapper<SupplierQuotationOrderMaterialPO> queryMaterial = new LambdaQueryWrapper<>();
        queryMaterial.eq(SupplierQuotationOrderMaterialPO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryMaterial.in(SupplierQuotationOrderMaterialPO::getQuotationOrderId, quotationOrderIds);
        queryMaterial.in(CollUtil.isNotEmpty(param.getMaterialCodes()), SupplierQuotationOrderMaterialPO::getMaterialCode, param.getMaterialCodes());
        //查询未放弃报价的数据
        queryMaterial.eq(SupplierQuotationOrderMaterialPO::getQuotationStatus, YesOrNoEnum.YES.getCode());
        queryMaterial.orderByDesc(SupplierQuotationOrderMaterialPO::getQuotationRound);

        List<SupplierQuotationOrderMaterialPO> materials = supplierQuotationOrderMaterialService.list(queryMaterial);
        if (CollUtil.isEmpty(materials)) return Collections.emptyList();

        //查询最新一轮 物料+供应商报价 (可能有相同轮次 但是不同报价类型的数据)
        List<SupplierQuotationOrderMaterialPO> materialQuotations = new ArrayList<>();
        materials.stream()
                .collect(Collectors.groupingBy(v -> v.getMaterialCode() + v.getSapSupplierCode()))
                .forEach((k, v) -> materialQuotations.addAll(v.stream()
                        .filter(v1 -> Objects.equals(v.get(0).getQuotationRound(), v1.getQuotationRound()))
                        .collect(Collectors.toList())));

        //相同物料优先取批产报价类型 其次取样件报价
        List<SupplierQuotationOrderMaterialPO> materialList = this.getList(materialQuotations);
        List<Long> allIds = materialList.stream().map(SupplierQuotationOrderMaterialPO::getId).collect(Collectors.toList());
        //需要查询L2(L3 也查L2)层级的 物料
        List<String> l2MaterialCodes = materialList.stream()
                .filter(v -> QuotationTemplateEnum.MASS_PROD_L3.getCode().equals(v.getQuotationTemplate())
                        || QuotationTemplateEnum.MASS_PROD_L2.getCode().equals(v.getQuotationTemplate()))
                .map(SupplierQuotationOrderMaterialPO::getMaterialCode).distinct().collect(Collectors.toList());
        //先查L1的费用数据
        LambdaQueryWrapper<SupplierQuotationOrderBaseFeePO> queryL1 = new LambdaQueryWrapper<>();
        queryL1.eq(SupplierQuotationOrderBaseFeePO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryL1.in(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, allIds);

        List<SupplierQuotationOrderBaseFeePO> l1Prices = l1Service.list(queryL1);
        if (CollUtil.isEmpty(l1Prices)) return Collections.emptyList();

        List<SupplierQuotationOrderRelationFeePO> l2Prices = this.queryL2Price(l2MaterialCodes, allIds);

        List<InquiryOrderMaterialPO> inquiryMaterials = inquiryOrderMaterialMapperService.queryBy(param.getInquiryNo(), param.getMaterialCodes());
        //K:materialCode V:unit
        Map<String, String> unitMap = null;
        if (CollUtil.isNotEmpty(inquiryMaterials)) {
            unitMap = inquiryMaterials.stream().collect(Collectors.toMap(InquiryOrderMaterialPO::getMaterialCode, InquiryOrderMaterialPO::getUnit, (v1, v2) -> v1));
        }
        //物料维度 分组进行报价分析 有L1层级报价数据的
        List<QuotationAnalysisDetailRespDTO> result = new ArrayList<>(materialList.stream()
                .filter(v -> l1Prices.stream().anyMatch(v1 -> Objects.equals(v.getId(), v1.getQuotationOrderMaterialId())))
                .collect(Collectors.toMap(v -> new QuotationAnalysisDetailRespDTO(v.getMaterialCode(), v.getMaterialDesc()), Function.identity(), (v1, v2) -> v1))
                .keySet());

        Map<String, String> supplierMap = this.getSupplierMap(param.getInquiryNo());
        Map<Long, SupplierQuotationRespDTO> supplierL1Map = materialList.stream()
                .collect(Collectors.toMap(BaseEntity::getId, v -> new SupplierQuotationRespDTO(v.getSapSupplierCode(), MapUtil.getStr(supplierMap, v.getSapSupplierCode())), (v1, v2) -> v1));

        for (QuotationAnalysisDetailRespDTO v : result) {
            v.setBaseUnit(MapUtil.getStr(unitMap, v.getMaterialCode()));
            //L1层级数据
            List<SupplierQuotationRespDTO> l1List = new ArrayList<>();
            //L2层级数据
            List<SupplierQuotationCostRespDTO> l2List = new ArrayList<>();
            List<SupplierQuotationOrderRelationFeePO> finalL2Prices = l2Prices;
            //处理 L1 L2数据
            handleL1AndL2Data(param, v, l1Prices, supplierL1Map, l1List, finalL2Prices, l2List);

            v.setL1List(l1List);
            v.setL2List(l2List);
        }

        return result;
    }

    private void handleL1AndL2Data(QuotationAnalysisReqDTO param,
                                   QuotationAnalysisDetailRespDTO v,
                                   List<SupplierQuotationOrderBaseFeePO> l1Prices,
                                   Map<Long, SupplierQuotationRespDTO> supplierL1Map,
                                   List<SupplierQuotationRespDTO> l1List,
                                   List<SupplierQuotationOrderRelationFeePO> finalL2Prices,
                                   List<SupplierQuotationCostRespDTO> l2List) {
        l1Prices.stream()
                .filter(v1 -> StringUtils.equals(v.getMaterialCode(), v1.getMaterialCode()))
                .forEach(v1 -> {
                    SupplierQuotationRespDTO l1 = supplierL1Map.get(v1.getQuotationOrderMaterialId());
                    BigDecimal price = getPrice(v1);
                    l1.setPrice(price);
                    l1List.add(l1);

                    //判断是否有L2
                    if (CollUtil.isNotEmpty(finalL2Prices)) {
                        SupplierQuotationOrderRelationFeePO l2Price = finalL2Prices.stream()
                                .filter(v2 -> Objects.equals(v1.getQuotationOrderMaterialId(), v2.getQuotationOrderMaterialId()))
                                .findFirst().orElse(null);
                        if (Objects.nonNull(l2Price)) {
                            SupplierQuotationCostRespDTO l2 = new SupplierQuotationCostRespDTO();
                            l2.setSapSupplierCode(l1.getSapSupplierCode());
                            l2.setSupplierName(l1.getSupplierName());
                            l2.setTotalCost(this.getTotalCost(l2Price));
                            l2.setCostDetails(this.getCostDetails(l2Price, param.getTwoLevelConditions(), l2.getTotalCost()));
                            l2List.add(l2);
                        }
                    }
                });
    }

    private static BigDecimal getPrice(SupplierQuotationOrderBaseFeePO v1) {
        return Objects.nonNull(v1.getPrice()) && Objects.nonNull(v1.getPriceUnit()) && !Objects.equals(v1.getPriceUnit(), 0)
                ? NumberUtil.div(v1.getPrice(), v1.getPriceUnit(), 2) : v1.getPrice();
    }

    private List<SupplierQuotationOrderRelationFeePO> queryL2Price(List<String> l2MaterialCodes, List<Long> allIds) {
        if (CollUtil.isNotEmpty(l2MaterialCodes)) {
            LambdaQueryWrapper<SupplierQuotationOrderRelationFeePO> queryL2 = new LambdaQueryWrapper<>();
            queryL2.eq(SupplierQuotationOrderRelationFeePO::getIsDelete, YesOrNoEnum.NO.getCode());
            queryL2.in(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, allIds);
            queryL2.in(SupplierQuotationOrderRelationFeePO::getMaterialCode, l2MaterialCodes);

            return l2Service.list(queryL2);
        }
        return null;
    }


    @Override
    public QuotationAnalysisDetailRespDTO quotationAnalysisSingleSupplier(QuotationAnalysisSingleReqDTO param) {
        InquiryOrderPO inquiryOrder = inquiryOrderService.queryBy(param.getInquiryNo());
        Boolean isLastCall = Objects.equals(inquiryOrder.getIsLastCall(), 1);
        Integer maxRound = inquiryOrder.getInquiryRound();
        //查询供应商报价
        LambdaQueryWrapper<SupplierQuotationOrderPO> query = new LambdaQueryWrapper<>();
        query.eq(SupplierQuotationOrderPO::getIsDelete, YesOrNoEnum.NO.getCode());
        query.eq(SupplierQuotationOrderPO::getInquiryNo, param.getInquiryNo());
        //查报价完成的
        query.eq(SupplierQuotationOrderPO::getQuotationStatus, SupplierQuotationStatusEnum.QUOTED.getCode());
        query.eq(SupplierQuotationOrderPO::getSapSupplierCode, param.getSapSupplierCode());

        List<SupplierQuotationOrderPO> quotationOrders = supplierQuotationOrderService.list(query);
        if (CollUtil.isEmpty(quotationOrders)) return null;

        List<Long> quotationOrderIds = quotationOrders.stream()
                .map(SupplierQuotationOrderPO::getId)
                .collect(Collectors.toList());

        //查物料 与轮次维度
        LambdaQueryWrapper<SupplierQuotationOrderMaterialPO> queryMaterial = new LambdaQueryWrapper<>();
        queryMaterial.eq(SupplierQuotationOrderMaterialPO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryMaterial.in(SupplierQuotationOrderMaterialPO::getQuotationOrderId, quotationOrderIds);
        queryMaterial.eq(SupplierQuotationOrderMaterialPO::getMaterialCode, param.getMaterialCode());
        //查询未放弃报价的数据
        queryMaterial.eq(SupplierQuotationOrderMaterialPO::getQuotationStatus, YesOrNoEnum.YES.getCode());
        queryMaterial.orderByAsc(SupplierQuotationOrderMaterialPO::getQuotationRound);

        //轮次升序
        List<SupplierQuotationOrderMaterialPO> materials = supplierQuotationOrderMaterialService.list(queryMaterial);
        if (CollUtil.isEmpty(materials)) return null;

        QuotationAnalysisDetailRespDTO result = new QuotationAnalysisDetailRespDTO();
        result.setMaterialCode(materials.get(0).getMaterialCode());
        result.setMaterialDesc(materials.get(0).getMaterialDesc());

        List<InquiryOrderMaterialPO> inquiryMaterials = inquiryOrderMaterialMapperService.queryBy(param.getInquiryNo(), param.getMaterialCode());
        //K:materialCode V:unit
        Map<String, String> unitMap = null;
        if (CollUtil.isNotEmpty(inquiryMaterials)) {
            unitMap = inquiryMaterials.stream().collect(Collectors.toMap(InquiryOrderMaterialPO::getMaterialCode, InquiryOrderMaterialPO::getUnit, (v1, v2) -> v1));
        }
        result.setBaseUnit(MapUtil.getStr(unitMap, result.getMaterialCode()));

        //处理相同轮次数据
        Map<Integer, List<SupplierQuotationOrderMaterialPO>> materialGroup = materials.stream()
                .collect(Collectors.groupingBy(SupplierQuotationOrderMaterialPO::getQuotationRound));

        //轮次升序 且唯一
        List<SupplierQuotationOrderMaterialPO> materialQuotations = new ArrayList<>();
        for (int round = 1; round <= inquiryOrder.getInquiryRound(); round++) {
            //计算模板优先级
            List<SupplierQuotationOrderMaterialPO> list = sortDescByTemplate(materialGroup.get(round));
            if (CollUtil.isNotEmpty(list)) materialQuotations.add(list.get(0));
        }
        List<Long> allIds = materialQuotations.stream().map(SupplierQuotationOrderMaterialPO::getId).collect(Collectors.toList());
        //需要查询L2(L3 也查L2)层级的 物料
        List<String> l2MaterialCodes = materialQuotations.stream()
                .filter(v -> QuotationTemplateEnum.MASS_PROD_L3.getCode().equals(v.getQuotationTemplate())
                        || QuotationTemplateEnum.MASS_PROD_L2.getCode().equals(v.getQuotationTemplate()))
                .map(SupplierQuotationOrderMaterialPO::getMaterialCode).distinct().collect(Collectors.toList());
        //先查L1的费用数据
        LambdaQueryWrapper<SupplierQuotationOrderBaseFeePO> queryL1 = new LambdaQueryWrapper<>();
        queryL1.eq(SupplierQuotationOrderBaseFeePO::getIsDelete, YesOrNoEnum.NO.getCode());
        queryL1.in(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, allIds);

        List<SupplierQuotationOrderBaseFeePO> l1Prices = l1Service.list(queryL1);
        if (CollUtil.isEmpty(l1Prices)) return null;

        List<SupplierQuotationOrderRelationFeePO> l2Prices = null;
        if (CollUtil.isNotEmpty(l2MaterialCodes)) {
            LambdaQueryWrapper<SupplierQuotationOrderRelationFeePO> queryL2 = new LambdaQueryWrapper<>();
            queryL2.eq(SupplierQuotationOrderRelationFeePO::getIsDelete, YesOrNoEnum.NO.getCode());
            queryL2.in(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, allIds);
            queryL2.in(SupplierQuotationOrderRelationFeePO::getMaterialCode, l2MaterialCodes);

            l2Prices = l2Service.list(queryL2);
        }

        SupplierQuotationOrderMaterialPO lastMaterial = CollUtil.getLast(materialQuotations);
        result.setQuotationOrderId(lastMaterial.getQuotationOrderId());
        result.setQuotationTemplate(lastMaterial.getQuotationTemplate());
        result.setRequirementPartsNo(lastMaterial.getRequirementPartsNo());
        //若存在样件报价
        if (materials.stream().anyMatch(v -> StringUtils.equals(QuotationTemplateEnum.SAMPLE_L1.getCode(), v.getQuotationTemplate()))) {
            result.setQuotationSimpleTemplate(QuotationTemplateEnum.SAMPLE_L1.getCode());
        }

        Map<Long, SupplierQuotationOrderBaseFeePO> l1Map = l1Prices.stream()
                .collect(Collectors.toMap(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, Function.identity(), (v1, v2) -> v1));
        Map<Long, SupplierQuotationOrderRelationFeePO> l2Map = null;
        if (CollUtil.isNotEmpty(l2Prices) && Objects.nonNull(l2Prices)) l2Map = l2Prices.stream()
                .collect(Collectors.toMap(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, Function.identity(), (v1, v2) -> v1));

        List<SupplierQuotationRespDTO> l1List = new ArrayList<>();
        List<SupplierQuotationCostRespDTO> l2List = new ArrayList<>();
        Integer i = 1;
        Integer size = CollUtil.size(materialQuotations);
        List<String> twoLevelConditions = Arrays.stream(TwoLevelCostEnum.values()).map(TwoLevelCostEnum::name).collect(Collectors.toList());
        for (SupplierQuotationOrderMaterialPO v : materialQuotations) {
            this.handleLevel(v, l1Map, i, size, isLastCall, maxRound, l2Map, twoLevelConditions, l2List, l1List);
            i++;
        }

        result.setL1List(l1List);
        result.setL2List(l2List);

        //处理相似物料价格
        result.setSimilarMaterials(this.handleSimilarMaterialPrice(inquiryMaterials));

        return result;
    }

    private List<MaterialPriceRespDTO> handleSimilarMaterialPrice(List<InquiryOrderMaterialPO> inquiryMaterials) {
        if (CollUtil.isEmpty(inquiryMaterials)) return null;
        List<MaterialPriceRespDTO> similarMaterialResult = null;
        List<InquiryOrderMaterialPO> similarMaterials = inquiryMaterials.stream().filter(v -> StringUtils.isNotBlank(v.getSimilarMaterialCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(similarMaterials)) {
            List<String> similarMaterialCodes = similarMaterials.stream().map(InquiryOrderMaterialPO::getSimilarMaterialCode).distinct().collect(Collectors.toList());
            Map<String, MaterialMinPriceRespDTO> priceMap = priceManager.getMaterialMinPrice(similarMaterialCodes);
            Map<String, MaterialResultDTO> materialMap = materialManager.getMaterialMap(similarMaterialCodes);
            similarMaterialResult = similarMaterialCodes.stream().map(v -> {
                MaterialPriceRespDTO target = new MaterialPriceRespDTO(v);
                MaterialMinPriceRespDTO price = getMapValue(priceMap,v);
                if (Objects.nonNull(price)) {
                    target.setPriceUnit(price.getPriceUnit());
                    target.setCurrentUnitPrice(price.getCurrentUnitPrice());
                }
                MaterialResultDTO material = getMapValue(materialMap,v);
                if (Objects.nonNull(material)) target.setMaterialDesc(material.getMaterialDesc());
                return target;
            }).collect(Collectors.toList());
        }
        return similarMaterialResult;
    }

    private void handleLevel(SupplierQuotationOrderMaterialPO v,
                             Map<Long, SupplierQuotationOrderBaseFeePO> l1Map,
                             Integer i,
                             Integer size,
                             Boolean isLastCall,
                             Integer maxRound,
                             Map<Long, SupplierQuotationOrderRelationFeePO> l2Map,
                             List<String> twoLevelConditions,
                             List<SupplierQuotationCostRespDTO> l2List,
                             List<SupplierQuotationRespDTO> l1List) {
        int lastCall = 0;
        SupplierQuotationRespDTO l1 = new SupplierQuotationRespDTO();
        SupplierQuotationOrderBaseFeePO l1Price = l1Map.get(v.getId());
        l1.setQuotationRound(v.getQuotationRound());
        BigDecimal price = Objects.nonNull(l1Price.getPrice()) && Objects.nonNull(l1Price.getPriceUnit()) && !Objects.equals(l1Price.getPriceUnit(), 0)
                ? NumberUtil.div(l1Price.getPrice(), l1Price.getPriceUnit(), 2) : l1Price.getPrice();
        l1.setPrice(price);

        //最后一轮  发起了lastCall 且 轮次与最大轮次相同
        if (Objects.equals(i, size) && BooleanUtil.isTrue(isLastCall) && Objects.equals(maxRound, v.getQuotationRound()))
            lastCall = 1;
        l1.setIsLastCall(lastCall);

        if (QuotationTemplateEnum.MASS_PROD_L3.getCode().equals(v.getQuotationTemplate())
                || QuotationTemplateEnum.MASS_PROD_L2.getCode().equals(v.getQuotationTemplate())) {
            SupplierQuotationCostRespDTO l2 = new SupplierQuotationCostRespDTO();
            l2.setQuotationRound(v.getQuotationRound());
            l2.setIsLastCall(lastCall);
            if (null != l2Map && l2Map.containsKey(v.getId())) {
                SupplierQuotationOrderRelationFeePO l2Price = l2Map.get(v.getId());
                l2.setTotalCost(this.getTotalCost(l2Price));
                l2.setCostDetails(this.getCostDetails(l2Price, twoLevelConditions, l2.getTotalCost()));
            }
            l2List.add(l2);
        }

        l1List.add(l1);
    }

    @Override
    public QuotationOrderConditionRespDTO getConditionByInquiry(String inquiryNo) {
        InquiryOrderPO inquiryOrder = inquiryOrderService.queryBy(inquiryNo);
        //轮次
        List<String> rounds = new ArrayList<>();
        boolean isLastCall = Objects.equals(YesOrNoEnum.YES.getCode(), inquiryOrder.getIsLastCall());
        if (inquiryOrder.getInquiryRound() != null && inquiryOrder.getInquiryRound() > 0) {
            int maxRound = isLastCall ? inquiryOrder.getInquiryRound() - 1 : inquiryOrder.getInquiryRound();
            if (maxRound > 0)
                rounds = IntStream.rangeClosed(1, maxRound).mapToObj(String::valueOf).collect(Collectors.toList());
            if (isLastCall) rounds.add("LastCall");
        }

        List<QuotationOrderSupplierRespDTO> suppliers = null;
        List<String> twoLevelConditions = null;
        List<QuotationOrderMaterialRespDTO> materials = null;
        List<SupplierQuotationOrderPO> supplierList = supplierQuotationOrderService.queryBy(inquiryNo);
        List<SupplierQuotationOrderMaterialPO> materialList = supplierQuotationOrderMaterialService.queryBy(inquiryNo);
        if (CollUtil.isNotEmpty(materialList)) {
            materials = materialList.stream()
                    .collect(Collectors.toMap(SupplierQuotationOrderMaterialPO::getMaterialCode, Function.identity(), (v1, v2) -> v1))
                    .values().stream()
                    .map(v -> new QuotationOrderMaterialRespDTO(v.getMaterialCode(), v.getMaterialDesc()))
                    .collect(Collectors.toList());
            boolean existsL2OrL3 = materialList.stream().anyMatch(v -> StringUtils.equals(QuotationTemplateEnum.MASS_PROD_L2.getCode(), v.getQuotationTemplate())
                    || StringUtils.equals(QuotationTemplateEnum.MASS_PROD_L3.getCode(), v.getQuotationTemplate()));
            if (existsL2OrL3)
                twoLevelConditions = Arrays.stream(TwoLevelCostEnum.values()).map(TwoLevelCostEnum::name).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(supplierList)) {
            suppliers = supplierList.stream()
                    .collect(Collectors.toMap(SupplierQuotationOrderPO::getSapSupplierCode, Function.identity(), (v1, v2) -> v1))
                    .values().stream()
                    .map(v -> new QuotationOrderSupplierRespDTO(v.getSapSupplierCode(), v.getSupplierName(), v.getSupplierNameEn()))
                    .collect(Collectors.toList());
        }

        return new QuotationOrderConditionRespDTO(materials, suppliers, twoLevelConditions, rounds);
    }


    /**
     * 相同物料+相同供应商 优先取批产报价类型 其次取样件报价
     * 相同物料+相同供应商 优先取层级最大
     */
    private List<SupplierQuotationOrderMaterialPO> getList(List<SupplierQuotationOrderMaterialPO> materials) {
        Map<String, List<SupplierQuotationOrderMaterialPO>> map = materials
                .stream()
                .collect(Collectors.groupingBy(v -> v.getMaterialCode() + v.getSapSupplierCode()));

        List<SupplierQuotationOrderMaterialPO> result = new ArrayList<>();
        map.forEach((k, v) -> {
            //L3层级 排前面
            List<SupplierQuotationOrderMaterialPO> sortMaterials = sortDescByTemplate(v);
            result.add(sortMaterials.get(0));
        });
        return result;
    }

    private BigDecimal getTotalCost(SupplierQuotationOrderRelationFeePO price) {
        return NumberUtil.add(price.getRawMaterialCostFee(), price.getOutsourcingCostFee(),
                price.getManufacturingCostFee(), price.getMouldAmortizationFee(),
                price.getPackageFee(), price.getManagementFee(), price.getFinancialFee(), price.getProfitMarkup(),
                price.getLogisticsFee()
        );
    }

    private List<SupplierQuotationCostDetailRespDTO> getCostDetails(SupplierQuotationOrderRelationFeePO priceData, List<String> twoLevelConditions, BigDecimal totalCost) {
        List<TwoLevelCostEnum> types = ListUtil.toList(TwoLevelCostEnum.values());
        if (CollUtil.isNotEmpty(twoLevelConditions))
            types = types.stream()
                    .filter(v -> twoLevelConditions.stream().anyMatch(v1 -> StringUtils.equals(v1, v.name())))
                    .collect(Collectors.toList());

        List<SupplierQuotationCostDetailRespDTO> result = new ArrayList<>();
        types.forEach(v -> {
            SupplierQuotationCostDetailRespDTO rs = new SupplierQuotationCostDetailRespDTO();
            rs.setCostType(v.name());
            BigDecimal price = null;
            switch (v) {
                case RAW_MATERIAL_COST_FEE:
                    price = priceData.getRawMaterialCostFee();
                    break;
                case OUTSOURCING_COST_FEE:
                    price = priceData.getOutsourcingCostFee();
                    break;
                case MANUFACTURING_COST_FEE:
                    price = priceData.getManufacturingCostFee();
                    break;
                case MOULD_AMORTIZATION_FEE:
                    price = priceData.getMouldAmortizationFee();
                    break;
                case PACKAGE_FEE:
                    price = priceData.getPackageFee();
                    break;
                case MANAGEMENT_FEE:
                    price = priceData.getManagementFee();
                    break;
                case FINANCIAL_FEE:
                    price = priceData.getFinancialFee();
                    break;
                case PROFIT_MARKUP:
                    price = priceData.getProfitMarkup();
                    break;
                case LOGISTICS_FEE:
                    price = priceData.getLogisticsFee();
                    break;
                default:
                    price = BigDecimal.ZERO;
                    break;
            }
            rs.setCost(price);
            if (Objects.nonNull(totalCost) && BigDecimal.ZERO.compareTo(totalCost) != 0)
                rs.setProportion(NumberUtil.div(price, totalCost, 2));
            result.add(rs);
        });
        return result;
    }

    /**
     * 根据模板进行排序 优先批产模板 其次样件模板 批产模板L3优先 其次L2、L1
     */
    private static List<SupplierQuotationOrderMaterialPO> sortDescByTemplate(List<SupplierQuotationOrderMaterialPO> v) {
        if (CollUtil.size(v) <= 1) return v;
        return v.stream()
                .sorted(Comparator.comparing(v1 -> {
                    if (QuotationTemplateEnum.MASS_PROD_L3.getCode().equals(v1.getQuotationTemplate())) {
                        return 1;
                    } else if (QuotationTemplateEnum.MASS_PROD_L2.getCode().equals(v1.getQuotationTemplate())) {
                        return 2;
                    } else if (QuotationTemplateEnum.MASS_PROD_L1.getCode().equals(v1.getQuotationTemplate())) {
                        return 3;
                    } else if (QuotationTemplateEnum.SAMPLE_L1.getCode().equals(v1.getQuotationTemplate())) {
                        return 4;
                    } else {
                        return 5;
                    }
                })).collect(Collectors.toList());
    }

    private Map<String, String> getSupplierMap(String inquiryNo) {
        List<InquiryOrderSupplierRespDTO> list = inquiryOrderSupplierService.queryBy(inquiryNo);
        if (CollUtil.isEmpty(list)) return null;

        return list.stream()
                .collect(Collectors.toMap(InquiryOrderSupplierRespDTO::getSapSupplierCode, InquiryOrderSupplierRespDTO::getSupplierName, (v1, v2) -> v1));
    }

    private static <K,V> V getMapValue(Map<K,V> map,K key){
        return Objects.nonNull(map) && Objects.nonNull(key) && map.containsKey(key) ? map.get(key) : null;
    }
}
