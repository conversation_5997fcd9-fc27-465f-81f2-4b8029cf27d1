package com.weifu.srm.sourcing.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.common.exception.BizFailException;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.common.util.JacksonUtil;
import com.weifu.srm.common.util.LocaleMessage;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.enums.InquiryOrderOperateTypeEnum;
import com.weifu.srm.sourcing.enums.InquiryOrderRequirementPartsStatusEnum;
import com.weifu.srm.sourcing.enums.InquiryOrderStatusEnum;
import com.weifu.srm.sourcing.enums.SupplierClassificationEnum;
import com.weifu.srm.sourcing.manager.DataPermissionManager;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingMapperService;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMapperService;
import com.weifu.srm.sourcing.repository.constants.MsgConstants;
import com.weifu.srm.sourcing.repository.enums.AttachmentRecordTypeEnum;
import com.weifu.srm.sourcing.repository.enums.BizNoRuleEnum;
import com.weifu.srm.sourcing.repository.enums.DataPermissionTypeEnum;
import com.weifu.srm.sourcing.repository.enums.DesignationOrderCloseTypeEnum;
import com.weifu.srm.sourcing.repository.enums.InquiryOrderCloseTypeEnum;
import com.weifu.srm.sourcing.repository.enums.SourceTypeEnum;
import com.weifu.srm.sourcing.repository.enums.SupplierQuotationOrderStatusEnum;
import com.weifu.srm.sourcing.repository.enums.SupplierQuotationStatusEnum;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderMaterialPO;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderCloseReqDTO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderCloseReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderDelayQuotationDeadlineReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderEndReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderLastCallReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderMaterialReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderQueryReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReInquiryReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReleaseRequirementReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderSampleOrderReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderSubmitReqDTO;
import com.weifu.srm.sourcing.request.InquiryOrderSupplierReqDTO;
import com.weifu.srm.sourcing.request.SourcingApprovalReqDTO;
import com.weifu.srm.sourcing.request.SupplierLastCallQueryReqDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderDetailRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderMaterialDetailRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderSupplierQuotationInfoRespDTO;
import com.weifu.srm.sourcing.response.InquiryOrderSupplierRespDTO;
import com.weifu.srm.sourcing.response.SupplierLastCallRespDTO;
import com.weifu.srm.sourcing.response.bidding.BiddingListRespDTO;
import com.weifu.srm.sourcing.service.AttachmentRecordService;
import com.weifu.srm.sourcing.service.InquiryOrderMaterialService;
import com.weifu.srm.sourcing.service.InquiryOrderService;
import com.weifu.srm.sourcing.service.InquiryOrderSupplierService;
import com.weifu.srm.sourcing.service.SupplierLastCallService;
import com.weifu.srm.sourcing.service.SupplierQuotationOrderService;
import com.weifu.srm.sourcing.service.biz.SourcingInquiryOrderChangeBiz;
import com.weifu.srm.sourcing.service.biz.SourcingSitMsgBiz;
import com.weifu.srm.sourcing.service.biz.SourcingTicketBiz;
import com.weifu.srm.sourcing.util.BizNoUtil;
import com.weifu.srm.sourcing.util.OperateUserUtil;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InquiryOrderServiceImpl implements InquiryOrderService {

    private final InquiryOrderMapperService inquiryOrderMapperService;
    private final AttachmentRecordService attachmentRecordService;
    private final SupplierLastCallService supplierLastCallService;
    private final InquiryOrderMaterialService inquiryOrderMaterialService;
    private final InquiryOrderSupplierService inquiryOrderSupplierService;
    private final BiddingMapperService biddingMapperService;
    private final SourcingTicketBiz sourcingTicketBiz;
    private final SourcingSitMsgBiz sourcingSitMsgBiz;
    private final SourcingInquiryOrderChangeBiz sourcingInquiryOrderChangeBiz;
    private final DataPermissionManager dataPermissionManager;
    private final LocaleMessage localeMessage;
    @Lazy
    @Autowired
    private SupplierQuotationOrderService supplierQuotationOrderService;

    @Override
    public PageResponse<InquiryOrderRespDTO> queryPage(InquiryOrderQueryReqDTO paramDTO) {
        DataPermissionRespDTO dataPermissionDTO = dataPermissionManager
                .queryUserDataPermission(paramDTO.getUserId(), DataPermissionTypeEnum.PC_INTERNAL_PAGE_SOURCING_RFQ_MANAGE.getCode());
        log.info("Inquiry order data permission info：{}", JacksonUtil.bean2Json(dataPermissionDTO));
        if (dataPermissionDTO.isNo()) {
            log.info("分页查询询价单无数据权限，userId：{}", paramDTO.getUserId());
            return PageResponse.toResult(paramDTO.getPageNum(), paramDTO.getPageSize(), 0L, List.of());
        }
        Page<InquiryOrderPO> page = new Page<>(paramDTO.getPageNum(), paramDTO.getPageSize());
        IPage<InquiryOrderPO> pr = inquiryOrderMapperService.queryPage(page, paramDTO, dataPermissionDTO.getKeys());
        List<InquiryOrderRespDTO> inquiryOrderRespDTOs = BeanUtil.copyToList(pr.getRecords(), InquiryOrderRespDTO.class);
        if (CollectionUtils.isNotEmpty(inquiryOrderRespDTOs)) {
            List<String> inquiryNos = inquiryOrderRespDTOs.stream()
                    .map(InquiryOrderRespDTO::getInquiryNo)
                    .collect(Collectors.toList());
            List<AttachmentRecordPO> fullAttachments = attachmentRecordService.queryBy(inquiryNos,
                    AttachmentRecordTypeEnum.INQUIRY_ORDER_CLOSE_FILE.getCode());

            List<SupplierQuotationOrderPO> fullSupplierQuotationOrders = supplierQuotationOrderService.queryLatestRoundBy(inquiryNos);
            Map<String, List<SupplierQuotationOrderPO>> supplierQuotationOrdersMap = fullSupplierQuotationOrders.stream()
                    .collect(Collectors.groupingBy(SupplierQuotationOrderPO::getInquiryNo));

            for (InquiryOrderRespDTO inquiryOrderRespDTO : inquiryOrderRespDTOs) {
                List<SupplierQuotationOrderPO> supplierQuotationOrders = supplierQuotationOrdersMap.get(inquiryOrderRespDTO.getInquiryNo());
                inquiryOrderRespDTO.setSupplierQuotationInfos(transform(supplierQuotationOrders));

                List<AttachmentRecordPO> attachments = fullAttachments.stream()
                        .filter(o -> Objects.equals(o.getBusinessNo(), inquiryOrderRespDTO.getInquiryNo()))
                        .collect(Collectors.toList());
                List<AttachmentRecordRespDTO> attachmentDTOs = BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class);
                inquiryOrderRespDTO.setCloseAttachments(attachmentDTOs);

                inquiryOrderRespDTO.setIsCreatedBusinessDesignationOrder(inquiryOrderRespDTO.getIsDesignated());
            }
        }

        return PageResponse.toResult(paramDTO.getPageNum(), paramDTO.getPageSize(), pr.getTotal(), inquiryOrderRespDTOs);
    }

    @Override
    public List<InquiryOrderSupplierQuotationInfoRespDTO> queryCurrentRoundQuotationGeneral(String inquiryNo) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(inquiryNo);
        List<SupplierQuotationOrderPO> supplierQuotationOrders = supplierQuotationOrderService.queryBy(inquiryNo, inquiryOrder.getInquiryRound());
        return transform(supplierQuotationOrders);
    }

    @Override
    public PageResponse<InquiryOrderRespDTO> queryByInquiryNoLike(InquiryOrderQueryReqDTO paramDTO) {
        List<InquiryOrderRespDTO> inquiryOrderDTOs = new ArrayList<>();
        long total = 0;
        if (StringUtils.isNotBlank(paramDTO.getInquiryNo())) {
            Page<InquiryOrderPO> page = new Page<>(paramDTO.getPageNum(), paramDTO.getPageSize());
            page = inquiryOrderMapperService.lambdaQuery()
                    .like(InquiryOrderPO::getInquiryNo, paramDTO.getInquiryNo())
                    .eq(InquiryOrderPO::getCreateBy, paramDTO.getUserId())
                    .eq(InquiryOrderPO::getStatus, InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode())
                    .page(page);
            total = page.getTotal();
            inquiryOrderDTOs = BeanUtil.copyToList(page.getRecords(), InquiryOrderRespDTO.class);
        }
        return PageResponse.toResult(paramDTO.getPageNum(), paramDTO.getPageSize(), total, inquiryOrderDTOs);
    }

    private List<InquiryOrderSupplierQuotationInfoRespDTO> transform(List<SupplierQuotationOrderPO> supplierQuotationOrders) {
        List<InquiryOrderSupplierQuotationInfoRespDTO> respDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(supplierQuotationOrders)) {
            List<String> quotationStatusAry = new ArrayList<>();
            quotationStatusAry.add(SupplierQuotationStatusEnum.PENDING.getCode());
            quotationStatusAry.add(SupplierQuotationStatusEnum.QUOTING.getCode());
            supplierQuotationOrders.stream()
                    .filter(o -> Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsShow()))
                    .forEach(o -> {
                        InquiryOrderSupplierQuotationInfoRespDTO respDTO = BeanUtil
                                .toBean(o, InquiryOrderSupplierQuotationInfoRespDTO.class);
                        if (quotationStatusAry.contains(respDTO.getQuotationStatus())) {
                            respDTO.setIsInputData(YesOrNoEnum.NO.getCode());
                        } else {
                            respDTO.setIsInputData(YesOrNoEnum.YES.getCode());
                        }
                        respDTOs.add(respDTO);
                    });
        }
        return respDTOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquiryOrderPO save(InquiryOrderReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = saveOrUpdate(paramDTO, true);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.SAVE, "");
        return inquiryOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquiryOrderPO copySave(InquiryOrderReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = copySaveOrUpdate(paramDTO, true);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.SAVE_CHANGE_SOURCING_STRATEGY, paramDTO.getCopyFromInquiryNo());
        return inquiryOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquiryOrderPO submit(InquiryOrderReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = saveOrUpdate(paramDTO, false);
        sourcingTicketBiz.createApprovalTicket4InquiryOrder(inquiryOrder);
        sourcingSitMsgBiz.sendSourcingNotification(inquiryOrder);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.SUBMIT, "");
        return inquiryOrder;
    }

    @Override
    public InquiryOrderPO submitNew(InquiryOrderSubmitReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        this.validateSubmitNewRule(inquiryOrder);
        inquiryOrder.setStatus(InquiryOrderStatusEnum.APPROVING.getCode());
        if (inquiryOrder.getFirstSubmitTime() == null) {
            inquiryOrder.setFirstSubmitTime(DateUtil.date());
        }
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);
        sourcingTicketBiz.createApprovalTicket4InquiryOrder(inquiryOrder);
        sourcingSitMsgBiz.sendSourcingNotification(inquiryOrder);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.SUBMIT, "");
        return inquiryOrder;
    }

    private void validateSubmitNewRule(InquiryOrderPO inquiryOrder) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.DRAFT.getCode());
        statusAry.add(InquiryOrderStatusEnum.REJECTED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        if (inquiryOrder.getId() != null && !statusAry.contains(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquiryOrderPO copySubmit(InquiryOrderReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = copySaveOrUpdate(paramDTO, false);
        sourcingTicketBiz.createApprovalTicket4InquiryOrder(inquiryOrder);
        sourcingSitMsgBiz.sendSourcingNotification(inquiryOrder);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.SUBMIT_CHANGE_SOURCING_STRATEGY, paramDTO.getCopyFromInquiryNo());
        return inquiryOrder;
    }

    private InquiryOrderPO saveOrUpdate(InquiryOrderReqDTO paramDTO, boolean saved) {
        InquiryOrderPO inquiryOrder = BeanUtil.copyProperties(paramDTO, InquiryOrderPO.class);
        boolean isCanEditSupplier = true;
        if (StringUtils.isBlank(inquiryOrder.getInquiryNo())) {
            // 如果是第一次保存或直接提交则生成询价单编号
            this.save(inquiryOrder, paramDTO, saved);
        } else {
            inquiryOrder = inquiryOrderMapperService.queryBy(inquiryOrder.getInquiryNo());
            List<String> canEditSupplierStatusAry = new ArrayList<>();
            canEditSupplierStatusAry.add(InquiryOrderStatusEnum.DRAFT.getCode());
            canEditSupplierStatusAry.add(InquiryOrderStatusEnum.REJECTED.getCode());
            isCanEditSupplier = canEditSupplierStatusAry.contains(inquiryOrder.getStatus());

            this.update(inquiryOrder, paramDTO, saved);
        }
        // 保存物料...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryMaterials());
        inquiryOrderMaterialService.save(inquiryOrder, paramDTO.getInquiryMaterials());
        // "草稿"和"审核拒绝"状态可以修改供应商...
        if (isCanEditSupplier) {
            OperateUserUtil.copyTo(paramDTO, paramDTO.getInquirySuppliers());
            inquiryOrderSupplierService.save(inquiryOrder, paramDTO.getInquirySuppliers());
        }
        // 保存询价单附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.INQUIRY_ORDER_FILE.getCode(), paramDTO.getInquiryAttachments());
        // 保存代录入报价说明附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryProxyQuotationAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.PROXY_QUOTATION_EXPLAIN_FILE.getCode(), paramDTO.getInquiryProxyQuotationAttachments());
        // 保存供应商选择评审依据附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquirySupplierReviewBasisAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.SUPPLIER_REVIEW_BASIS_FILE.getCode(), paramDTO.getInquirySupplierReviewBasisAttachments());

        return inquiryOrder;
    }

    private void update(InquiryOrderPO inquiryOrder, InquiryOrderReqDTO paramDTO, boolean saved) {
        // 校验数据...
        this.validateEditRule(inquiryOrder, paramDTO);
        BeanUtil.copyProperties(paramDTO, inquiryOrder, CopyOptions.create().ignoreNullValue());
        // 如果物料为空说明删除了所有物料 关闭询价单...
        if (CollectionUtils.isEmpty(paramDTO.getInquiryMaterials())) {
            inquiryOrder.setStatus(InquiryOrderStatusEnum.CLOSED.getCode());
        }
        if (!InquiryOrderStatusEnum.CLOSED.equalsByCode(inquiryOrder.getStatus())) {
            // 非首次保存操作状态不做变更...
            // 非首次提交操作状做如下变更:
            if (!saved) {
                if (InquiryOrderStatusEnum.DRAFT.equalsByCode(inquiryOrder.getStatus()) ||
                        InquiryOrderStatusEnum.REJECTED.equalsByCode(inquiryOrder.getStatus())) {
                    // DRAFT/REJECTED->APPROVING
                    inquiryOrder.setStatus(InquiryOrderStatusEnum.APPROVING.getCode());
                    if (inquiryOrder.getFirstSubmitTime() == null) {
                        inquiryOrder.setFirstSubmitTime(DateUtil.date());
                    }
                } else if (InquiryOrderStatusEnum.INQUIRY_FAILED.equalsByCode(inquiryOrder.getStatus()) ||
                        InquiryOrderStatusEnum.INQUIRY_COMPLETED.equalsByCode(inquiryOrder.getStatus())) {
                    // INQUIRY_FAILED/INQUIRY_COMPLETED->INQUIRING
                    inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRING.getCode());
                    inquiryOrder.setInquiryRound(inquiryOrder.getInquiryRound() + 1);
                    inquiryOrder.setInquiryStartTime(DateUtil.date());
                }
            }
        }
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);
    }

    private void save(InquiryOrderPO inquiryOrder, InquiryOrderReqDTO paramDTO, boolean saved) {
        inquiryOrder.setInquiryNo(BizNoUtil.generateNo(BizNoRuleEnum.INQUIRY_ORDER));
        paramDTO.setInquiryNo(inquiryOrder.getInquiryNo());
        inquiryOrder.setInquiryRound(0);
        inquiryOrder.setIsDesignated(YesOrNoEnum.NO.getCode());
        inquiryOrder.setIsLastCall(YesOrNoEnum.NO.getCode());
        inquiryOrder.setIsDelete(YesOrNoEnum.NO.getCode());
        // 校验数据...
        this.validateEditRule(inquiryOrder, paramDTO);
        // 如果物料为空说明删除了所有物料 关闭询价单...
        if (CollectionUtils.isEmpty(paramDTO.getInquiryMaterials())) {
            inquiryOrder.setStatus(InquiryOrderStatusEnum.CLOSED.getCode());
        }
        if (!InquiryOrderStatusEnum.CLOSED.equalsByCode(inquiryOrder.getStatus())) {
            if (saved) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.DRAFT.getCode());
            } else {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.APPROVING.getCode());
                if (inquiryOrder.getFirstSubmitTime() == null) {
                    inquiryOrder.setFirstSubmitTime(DateUtil.date());
                }
            }
        }
        BaseEntityUtil.setCommon(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.save(inquiryOrder);
    }

    private InquiryOrderPO copySaveOrUpdate(InquiryOrderReqDTO paramDTO, boolean saved) {
        InquiryOrderPO inquiryOrder = BeanUtil.copyProperties(paramDTO, InquiryOrderPO.class);
        inquiryOrder.setInquiryNo(BizNoUtil.generateNo(BizNoRuleEnum.INQUIRY_ORDER));

        String copyFromInquiryNo = paramDTO.getCopyFromInquiryNo();
        InquiryOrderPO copyFromInquiryOrder = inquiryOrderMapperService.queryBy(copyFromInquiryNo);
        // 校验规则...
        this.validateCopyEditRule(copyFromInquiryOrder, paramDTO);

        //关闭原询价单...
        InquiryOrderCloseReqDTO closeParamDTO = new InquiryOrderCloseReqDTO();
        closeParamDTO.setInquiryNo(copyFromInquiryNo);
        closeParamDTO.setCloseType(InquiryOrderCloseTypeEnum.RELEASE.getCode());
        closeParamDTO.setCloseReason(MsgConstants.INQUIRY_CHANGE_STRATEGY_CLOSE_DESC_MSG);
        closeParamDTO.setOperateBy(paramDTO.getOperateBy());
        closeParamDTO.setOperateName(paramDTO.getOperateName());
        this.closeInternal(copyFromInquiryOrder, closeParamDTO);

        //保存新询价单...
        inquiryOrder.setInquiryRound(0);
        inquiryOrder.setIsDesignated(YesOrNoEnum.NO.getCode());
        inquiryOrder.setIsDelete(YesOrNoEnum.NO.getCode());
        // 如果物料为空说明删除了所有物料 关闭询价单...
        if (CollectionUtils.isEmpty(paramDTO.getInquiryMaterials())) {
            inquiryOrder.setStatus(InquiryOrderStatusEnum.CLOSED.getCode());
        }
        if (!InquiryOrderStatusEnum.CLOSED.equalsByCode(inquiryOrder.getStatus())) {
            if (saved) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.DRAFT.getCode());
            } else {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.APPROVING.getCode());
                if (inquiryOrder.getFirstSubmitTime() == null) {
                    inquiryOrder.setFirstSubmitTime(DateUtil.date());
                }
            }
        }
        BaseEntityUtil.setCommon(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.save(inquiryOrder);

        // 保存物料...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryMaterials());
        inquiryOrderMaterialService.save(inquiryOrder, paramDTO.getInquiryMaterials());
        // 保存供应商...询价中和询价完成状态不能修改供应商信息
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquirySuppliers());
        inquiryOrderSupplierService.save(inquiryOrder, paramDTO.getInquirySuppliers());
        // 保存询价单附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.INQUIRY_ORDER_FILE.getCode(), paramDTO.getInquiryAttachments());
        // 保存代录入报价说明附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquiryProxyQuotationAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.PROXY_QUOTATION_EXPLAIN_FILE.getCode(), paramDTO.getInquiryProxyQuotationAttachments());
        // 保存供应商选择评审依据附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getInquirySupplierReviewBasisAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.SUPPLIER_REVIEW_BASIS_FILE.getCode(), paramDTO.getInquirySupplierReviewBasisAttachments());

        return inquiryOrder;
    }

    private void validateEditRule(InquiryOrderPO inquiryOrder, InquiryOrderReqDTO paramDTO) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.DRAFT.getCode());
        statusAry.add(InquiryOrderStatusEnum.REJECTED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        if (inquiryOrder.getId() != null && !statusAry.contains(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        this.validateEditMaterialRule(paramDTO);
        this.validateEditSupplierRule(paramDTO);
        if (Objects.equals(paramDTO.getSourceType(), SourceTypeEnum.SINGLE_SOURCE.getCode())) {
            // 单一寻源
            this.validateEditSingleSourcingRule(paramDTO);
        } else {
            // 询比价
            this.validateEditInquiryPriceParityRule(paramDTO);
        }
        if (StringUtils.isBlank(paramDTO.getSupplierReviewBasisDesc())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_SUPPLIER_REVIEW_BASIS_DESC_EMPTY_ERROR_MSG));
        }
        // 校验询价单是否已定点...
        this.validateIsDesignatedRule(inquiryOrder);
    }

    private void validateEditMaterialRule(InquiryOrderReqDTO paramDTO) {
        List<InquiryOrderMaterialReqDTO> inquiryMaterials = paramDTO.getInquiryMaterials();
        if (CollectionUtils.isNotEmpty(inquiryMaterials)) {
            List<String> requirementPartsNos = inquiryMaterials.stream()
                    .map(InquiryOrderMaterialReqDTO::getRequirementPartsNo)
                    .collect(Collectors.toList());
            List<String> lockedRequirementPartsNos = inquiryOrderMaterialService
                    .queryLockedRequirementPartsBy(paramDTO.getInquiryNo(), requirementPartsNos);
            if (CollectionUtils.isNotEmpty(lockedRequirementPartsNos)) {
                throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_REQUIREMENT_PARTS_LOCKED_ERROR_MSG));
            }
        }
    }

    private void validateEditSupplierRule(InquiryOrderReqDTO paramDTO) {
        List<InquiryOrderSupplierReqDTO> inquirySuppliers = paramDTO.getInquirySuppliers();
        List<InquiryOrderSupplierReqDTO> noAgreementSuppliers = inquirySuppliers.stream()
                .filter(o -> Objects.equals(o.getIsConfidentialityAgreement(), -1)
                        || Objects.equals(o.getIsIntegrityAgreement(), -1))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noAgreementSuppliers)) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_SUPPLIER_NO_AGREEMENTS_ERROR_MSG));
        }
    }

    private void validateEditSingleSourcingRule(InquiryOrderReqDTO paramDTO) {
        if (StringUtils.isBlank(paramDTO.getSingleSourceDesc())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_SINGLE_SOURCING_DESC_EMPTY_ERROR_MSG));
        }
        List<InquiryOrderSupplierReqDTO> supplierReqDTOs = paramDTO.getInquirySuppliers();
        supplierReqDTOs = supplierReqDTOs == null ? List.of() : supplierReqDTOs;
        supplierReqDTOs = supplierReqDTOs.stream()
                .filter(o -> Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsChecked()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierReqDTOs) || supplierReqDTOs.size() != 1) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_MATCH_SUPPLIER_COUNT_ERROR_MSG));
        }
        if (Objects.equals(paramDTO.getIsProxyQuotation(), YesOrNoEnum.YES.getCode())) {
            if (CollectionUtils.isEmpty(paramDTO.getInquiryProxyQuotationAttachments())) {
                throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_PROXY_QUOTATION_ATTACHMENT_EMPTY_ERROR_MSG));
            }
        }
    }

    private void validateEditInquiryPriceParityRule(InquiryOrderReqDTO paramDTO) {
        if (Objects.equals(paramDTO.getIsProxyQuotation(), YesOrNoEnum.YES.getCode())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_PROXY_QUOTATION_NOT_ALLOW_ERROR_MSG));
        }
        List<InquiryOrderSupplierReqDTO> supplierReqDTOs = paramDTO.getInquirySuppliers();
        supplierReqDTOs = supplierReqDTOs == null ? List.of() : supplierReqDTOs;
        supplierReqDTOs = supplierReqDTOs.stream()
                .filter(o -> Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsChecked()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierReqDTOs) || supplierReqDTOs.size() < 2) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_MATCH_SUPPLIER_COUNT_ERROR_MSG));
        }
    }

    private void validateCopyEditRule(InquiryOrderPO inquiryOrder, InquiryOrderReqDTO paramDTO) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        if (!statusAry.contains(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        this.validateCopyEditMaterialRule(paramDTO);
        this.validateEditSupplierRule(paramDTO);
        if (Objects.equals(paramDTO.getSourceType(), SourceTypeEnum.SINGLE_SOURCE.getCode())) {
            //单一寻源
            this.validateEditSingleSourcingRule(paramDTO);
        } else {
            // 询比价
            this.validateEditInquiryPriceParityRule(paramDTO);
        }
        if (StringUtils.isBlank(paramDTO.getSupplierReviewBasisDesc())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_SUPPLIER_REVIEW_BASIS_DESC_EMPTY_ERROR_MSG));
        }
        // 校验询价单是否已定点...
        this.validateIsDesignatedRule(inquiryOrder);
    }

    private void validateCopyEditMaterialRule(InquiryOrderReqDTO paramDTO) {
        String copyFromInquiryNo = paramDTO.getCopyFromInquiryNo();
        List<InquiryOrderMaterialPO> oriInquiryMaterials = inquiryOrderMaterialService.queryBy(copyFromInquiryNo);
        List<String> withSampleOrderRequirementPartsNos = oriInquiryMaterials.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSampleOrderNo()))
                .map(InquiryOrderMaterialPO::getRequirementPartsNo)
                .collect(Collectors.toList());
        List<String> errorRequirementPartsNos = paramDTO.getInquiryMaterials().stream()
                .map(InquiryOrderMaterialReqDTO::getRequirementPartsNo)
                .filter(withSampleOrderRequirementPartsNos::contains)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorRequirementPartsNos)) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_REQUIREMENT_WITH_SAMPLE_ORDER_ERROR_MSG,
                    new Object[]{String.join("，", errorRequirementPartsNos)}));
        }
    }

    @Override
    public InquiryOrderDetailRespDTO queryDetail(String inquiryNo, Integer useTo) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(inquiryNo);
        if (inquiryOrder == null) {
            return null;
        }
        InquiryOrderDetailRespDTO respDTO = BeanUtil.copyProperties(inquiryOrder, InquiryOrderDetailRespDTO.class);
        // 供应商...
        List<InquiryOrderSupplierRespDTO> inquirySuppliers = inquiryOrderSupplierService.queryBy(inquiryNo);
        List<InquiryOrderSupplierRespDTO> checkedInquirySuppliers = inquirySuppliers.stream()
                .filter(o -> Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsChecked()))
                .collect(Collectors.toList());
        List<InquiryOrderSupplierRespDTO> notCheckedInquirySuppliers = inquirySuppliers.stream()
                .filter(o -> Objects.equals(YesOrNoEnum.NO.getCode(), o.getIsChecked()))
                .collect(Collectors.toList());
        List<InquiryOrderSupplierRespDTO> sortedInquirySuppliers = new ArrayList<>();
        sortedInquirySuppliers.addAll(checkedInquirySuppliers);
        sortedInquirySuppliers.addAll(notCheckedInquirySuppliers);
        respDTO.setInquirySuppliers(sortedInquirySuppliers);
        // 物料...
        List<InquiryOrderMaterialDetailRespDTO> inquiryMaterials = inquiryOrderMaterialService.queryDetail(inquiryNo);
        if (Objects.equals(YesOrNoEnum.YES.getCode(), useTo)) {
            inquiryMaterials.forEach(o -> {
                List<AttachmentRecordRespDTO> attachments = o.getOtherAttachments();
                attachments = attachments == null ? new ArrayList<>() : attachments;
                attachments.addAll(o.getCustomerSpecifiedSupplierAttachments());
                o.setOtherAttachments(attachments);
            });
        }
        respDTO.setInquiryMaterials(inquiryMaterials);
        // 附件...
        List<AttachmentRecordPO> fullAttachments = attachmentRecordService.queryBy(inquiryNo);
        // 询价单附件...
        List<AttachmentRecordPO> attachments = fullAttachments.stream()
                .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.INQUIRY_ORDER_FILE.getCode()))
                .collect(Collectors.toList());
        respDTO.setInquiryAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
        // 代录入说明附件...
        attachments = fullAttachments.stream()
                .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.PROXY_QUOTATION_EXPLAIN_FILE.getCode()))
                .collect(Collectors.toList());
        respDTO.setInquiryProxyQuotationAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
        // 供应商选择审批依据附件...
        attachments = fullAttachments.stream()
                .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.SUPPLIER_REVIEW_BASIS_FILE.getCode()))
                .collect(Collectors.toList());
        respDTO.setInquirySupplierReviewBasisAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
        // 询价完成的询价单是否可以创建商务定点单...
        if (Objects.equals(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode(), respDTO.getStatus())) {
            if (Objects.equals(YesOrNoEnum.YES.getCode(), inquiryOrder.getIsDesignated())) {
                respDTO.setIsCanCreateBusinessDesignationOrder(YesOrNoEnum.NO.getCode());
            } else {
                respDTO.setIsCanCreateBusinessDesignationOrder(YesOrNoEnum.YES.getCode());
            }
        }
        String historyBidNo = respDTO.getHistoryBidNo();
        if (StringUtils.isNotBlank(historyBidNo)) {
            BiddingPO biddingPO = biddingMapperService.queryBy(historyBidNo);
            respDTO.setHistoryBiddingOrder(BeanUtil.toBean(biddingPO, BiddingListRespDTO.class));
        }
        return respDTO;
    }

    @Override
    public InquiryOrderDetailRespDTO queryAllDetail(String inquiryNo, Integer useTo) {
        InquiryOrderDetailRespDTO respDTO = null;
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(inquiryNo);
        if (inquiryOrder != null) {
            respDTO = BeanUtil.copyProperties(inquiryOrder, InquiryOrderDetailRespDTO.class);
            // 供应商...
            List<InquiryOrderSupplierRespDTO> inquirySuppliers = inquiryOrderSupplierService.queryBy(inquiryNo);
            List<InquiryOrderSupplierRespDTO> checkedInquirySuppliers = inquirySuppliers.stream()
                    .filter(o -> Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsChecked()))
                    .collect(Collectors.toList());
            List<InquiryOrderSupplierRespDTO> notCheckedInquirySuppliers = inquirySuppliers.stream()
                    .filter(o -> Objects.equals(YesOrNoEnum.NO.getCode(), o.getIsChecked()))
                    .collect(Collectors.toList());
            List<InquiryOrderSupplierRespDTO> sortedInquirySuppliers = new ArrayList<>();
            sortedInquirySuppliers.addAll(checkedInquirySuppliers);
            sortedInquirySuppliers.addAll(notCheckedInquirySuppliers);
            respDTO.setInquirySuppliers(sortedInquirySuppliers);
            // 物料...
            List<InquiryOrderMaterialDetailRespDTO> inquiryMaterials = inquiryOrderMaterialService.queryAllDetail(inquiryNo);
            if (Objects.equals(YesOrNoEnum.YES.getCode(), useTo)) {
                inquiryMaterials.forEach(o -> {
                    List<AttachmentRecordRespDTO> attachments = o.getOtherAttachments();
                    attachments = attachments == null ? new ArrayList<>() : attachments;
                    attachments.addAll(o.getCustomerSpecifiedSupplierAttachments());
                    o.setOtherAttachments(attachments);
                });
            }
            respDTO.setInquiryMaterials(inquiryMaterials);
            // 附件...
            List<AttachmentRecordPO> fullAttachments = attachmentRecordService.queryBy(inquiryNo);
            // 询价单附件...
            List<AttachmentRecordPO> attachments = fullAttachments.stream()
                    .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.INQUIRY_ORDER_FILE.getCode()))
                    .collect(Collectors.toList());
            respDTO.setInquiryAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
            // 代录入说明附件...
            attachments = fullAttachments.stream()
                    .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.PROXY_QUOTATION_EXPLAIN_FILE.getCode()))
                    .collect(Collectors.toList());
            respDTO.setInquiryProxyQuotationAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
            // 供应商选择审批依据附件...
            attachments = fullAttachments.stream()
                    .filter(o -> Objects.equals(o.getBusinessType(), AttachmentRecordTypeEnum.SUPPLIER_REVIEW_BASIS_FILE.getCode()))
                    .collect(Collectors.toList());
            respDTO.setInquirySupplierReviewBasisAttachments(BeanUtil.copyToList(attachments, AttachmentRecordRespDTO.class));
        }
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delayQuotationCutoffDeadline(InquiryOrderDelayQuotationDeadlineReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        // 校验延期报价规则...
        this.validateDelayQuotationDeadline(inquiryOrder);
        inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRING.getCode());
        inquiryOrder.setQuotationFeedbackDeadline(DateUtil.offsetDay(DateUtil.date(), paramDTO.getDelayDays()));
        inquiryOrder.setQuotationCutoffDeadline(DateUtil.offsetDay(DateUtil.date(), paramDTO.getDelayDays()));
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);
        // 修改供应商报价单状态...
        supplierQuotationOrderService.delayQuotationCutoffDeadline(inquiryOrder, paramDTO);
    }

    private void validateDelayQuotationDeadline(InquiryOrderPO inquiryOrder) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        if (!statusAry.contains(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(InquiryOrderCloseReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        //校验关闭询价单规则...
        this.validateCloseRule(inquiryOrder, paramDTO.getIsValidateDesignationOrder());
        this.closeInternal(inquiryOrder, paramDTO);
        if (Objects.equals(InquiryOrderCloseTypeEnum.RELEASE.getCode(), paramDTO.getCloseType())) {
            sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.CLS_AND_RELEASE_REQUIREMENT, "");
        } else {
            sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.CLS_AND_CLS_REQUIREMENT, "");
        }
    }

    private void validateCloseRule(InquiryOrderPO inquiryOrder, Boolean isValidateDesignationOrder) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.DRAFT.getCode());
        statusAry.add(InquiryOrderStatusEnum.REJECTED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        if (!statusAry.contains(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        if (Objects.equals(Boolean.TRUE, isValidateDesignationOrder)) {
            // 校验询价单是否已定点...
            this.validateIsDesignatedRule(inquiryOrder);
        }
    }

    private void closeInternal(InquiryOrderPO inquiryOrder, InquiryOrderCloseReqDTO paramDTO) {
        inquiryOrder.setCloseReason(paramDTO.getCloseReason());
        inquiryOrder.setStatus(InquiryOrderStatusEnum.CLOSED.getCode());
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);
        // 保存询价单关闭佐证材料附件...
        OperateUserUtil.copyTo(paramDTO, paramDTO.getAttachments());
        attachmentRecordService.save(inquiryOrder.getInquiryNo(), null,
                AttachmentRecordTypeEnum.INQUIRY_ORDER_CLOSE_FILE.getCode(), paramDTO.getAttachments());
        if (Objects.equals(InquiryOrderCloseTypeEnum.CLOSE.getCode(), paramDTO.getCloseType())) {
            // 关闭询价单中的需求...
            inquiryOrderMaterialService.closeRequirements(inquiryOrder, paramDTO);
        } else {
            // 释放询价单中的需求...
            inquiryOrderMaterialService.releaseRequirements(inquiryOrder, paramDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approval(SourcingApprovalReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getBusinessNo());
        if (InquiryOrderStatusEnum.APPROVING.equalsByCode(inquiryOrder.getStatus())) {
            BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), paramDTO.getOperateTime());
            if (InquiryOrderStatusEnum.APPROVED.equalsByCode(paramDTO.getStatus())) {
                // 审核通过...
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRING.getCode());
                inquiryOrder.setInquiryRound(inquiryOrder.getInquiryRound() + 1);
                inquiryOrder.setFirstInquiryStartTime(DateUtil.date());
                inquiryOrder.setInquiryStartTime(DateUtil.date());
                inquiryOrder.setQuotationFeedbackDeadline(DateUtil.offsetDay(DateUtil.date(), inquiryOrder.getQuotationFeedbackDays()));
                inquiryOrder.setQuotationCutoffDeadline(DateUtil.offsetDay(DateUtil.date(), inquiryOrder.getQuotationCutoffDays()));
                //审核通过为供应商初始化报价单主数据...
                supplierQuotationOrderService.instantiate(inquiryOrder, List.of());
            } else if (InquiryOrderStatusEnum.REJECTED.equalsByCode(paramDTO.getStatus())) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.DRAFT.getCode());
            } else if (InquiryOrderStatusEnum.CANCELED.equalsByCode(paramDTO.getStatus())) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.DRAFT.getCode());
            }
            inquiryOrderMapperService.updateById(inquiryOrder);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reInquiry(InquiryOrderReInquiryReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        this.validateReInquiryRule(inquiryOrder);
        Date now = DateUtil.date();
        inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRING.getCode());
        inquiryOrder.setInquiryRound(inquiryOrder.getInquiryRound() + 1);
        inquiryOrder.setInquiryStartTime(now);
        inquiryOrder.setQuotationFeedbackDeadline(DateUtil.offsetDay(now, inquiryOrder.getQuotationFeedbackDays()));
        inquiryOrder.setQuotationCutoffDeadline(DateUtil.offsetDay(now, inquiryOrder.getQuotationCutoffDays()));
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), now);
        inquiryOrderMapperService.updateById(inquiryOrder);
        //再次询价为供应商初始化报价单主数据...
        supplierQuotationOrderService.instantiate(inquiryOrder, List.of());
    }

    private void validateReInquiryRule(InquiryOrderPO inquiryOrder) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        if (!statusAry.contains(inquiryOrder.getStatus()) ||
                Objects.equals(inquiryOrder.getIsLastCall(), YesOrNoEnum.YES.getCode())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        // 校验询价单是否已定点...
        this.validateIsDesignatedRule(inquiryOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void end(InquiryOrderEndReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        this.validateEndRule(inquiryOrder);
        List<SupplierQuotationOrderPO> sqos = supplierQuotationOrderService.end(paramDTO);
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        this.commitInquiryResultAnalysis(inquiryOrder, sqos);
    }

    private void validateEndRule(InquiryOrderPO inquiryOrder) {
        if (!InquiryOrderStatusEnum.INQUIRING.equalsByCode(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        Date quotationFeedbackDeadline = inquiryOrder.getQuotationFeedbackDeadline();
        int compare = DateUtil.compare(DateUtil.date(), quotationFeedbackDeadline, DatePattern.NORM_DATE_PATTERN);
        if (compare <= 0) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_QUOTATION_FEEDBACK_DEADLINE_NOT_COMPLETE_ERROR_MSG));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commitInquiryResultAnalysis(InquiryOrderPO inquiryOrder, List<SupplierQuotationOrderPO> sqos) {
        // 统计完成报价和拒绝报价的供应商数量...
        int participationNumber = 0;
        for (SupplierQuotationOrderPO sqo : sqos) {
            if (Objects.equals(SupplierQuotationStatusEnum.QUOTED.getCode(), sqo.getQuotationStatus())) {
                participationNumber++;
            }
        }
        if (Objects.equals(SourceTypeEnum.SINGLE_SOURCE.getCode(), inquiryOrder.getSourceType())) {
            if (participationNumber > 0) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
            } else {
                inquiryOrder.setFailReason(MsgConstants.INQUIRY_NO_SUPPLIER_QUOTE_CLOSE_DESC_MSG);
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
            }
        } else {
            if (participationNumber >= 2) {
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_COMPLETED.getCode());
            } else if (participationNumber == 1) {
                inquiryOrder.setFailReason(MsgConstants.INQUIRY_NOT_ENOUGH_QUOTE_SUPPLIER_CLOSE_DESC_MSG);
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
            } else {
                inquiryOrder.setFailReason(MsgConstants.INQUIRY_NO_SUPPLIER_QUOTE_CLOSE_DESC_MSG);
                inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
            }
        }
        inquiryOrderMapperService.updateById(inquiryOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lastCall(InquiryOrderLastCallReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        this.validateLastCallRule(inquiryOrder, paramDTO);
        Date now = DateUtil.date();
        inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRING.getCode());
        inquiryOrder.setInquiryRound(inquiryOrder.getInquiryRound() + 1);
        inquiryOrder.setInquiryStartTime(now);
        inquiryOrder.setIsLastCall(YesOrNoEnum.YES.getCode());
        inquiryOrder.setQuotationFeedbackDeadline(DateUtil.offsetDay(now, inquiryOrder.getQuotationFeedbackDays()));
        inquiryOrder.setQuotationCutoffDeadline(DateUtil.offsetDay(now, inquiryOrder.getQuotationCutoffDays()));
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), now);
        inquiryOrderMapperService.updateById(inquiryOrder);
        for (String sapSupplierCode : paramDTO.getSapSupplierCodes()) {
            supplierLastCallService.incrUsedTimes(sapSupplierCode, DateUtil.year(now));
        }
        //LastCall再次询价为供应商初始化报价单主数据...
        supplierQuotationOrderService.instantiate(inquiryOrder, paramDTO.getSapSupplierCodes());
    }

    private void validateLastCallRule(InquiryOrderPO inquiryOrder, InquiryOrderLastCallReqDTO paramDTO) {
        // 非"询价完成状态"不能发起Last Call...
        if (!InquiryOrderStatusEnum.INQUIRY_COMPLETED.equalsByCode(inquiryOrder.getStatus())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.NONE_MATCH_STATUS_ERROR_MSG));
        }
        // 已发起过Last Call不能再次发起...
        if (Objects.equals(YesOrNoEnum.YES.getCode(), inquiryOrder.getIsLastCall())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_LAST_CALL_OCCURRED_ERROR_MSG));
        }
        // 包含非战略供应商不能发起Last Call...
        List<InquiryOrderSupplierRespDTO> inquirySuppliers = inquiryOrderSupplierService
                .queryBy(inquiryOrder.getInquiryNo(), YesOrNoEnum.YES.getCode());
        List<String> sapSupplierCodes = paramDTO.getSapSupplierCodes();
        inquirySuppliers = inquirySuppliers.stream()
                .filter(o -> sapSupplierCodes.contains(o.getSapSupplierCode()))
                .collect(Collectors.toList());
        for (InquiryOrderSupplierRespDTO inquirySupplier : inquirySuppliers) {
            if (!Objects.equals(SupplierClassificationEnum.STRATEGY_SUPPLIER.getCode(), inquirySupplier.getSupplierClassification())) {
                throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_NO_STRATEGY_SUPPLIER_ERROR_MSG));
            }
        }
        // 包含Last Call次数已使用完的供应商不能发起Last Call...
        int maxLastCallTimes = paramDTO.getMaxLastCallTimes();
        Map<String, Integer> lastCallUsedTimeMap = supplierLastCallService.queryUsedTimesBy(sapSupplierCodes, DateUtil.year(DateUtil.date()));
        for (String reqSapSupplierCode : sapSupplierCodes) {
            Integer lastCallUsedTimes = lastCallUsedTimeMap.get(reqSapSupplierCode);
            lastCallUsedTimes = lastCallUsedTimes == null ? 0 : lastCallUsedTimes;
            if (lastCallUsedTimes >= maxLastCallTimes) {
                throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_LAST_CALL_TIMES_LIMIT_ERROR_MSG));
            }
        }
        // 包含最新轮次拒绝报价的战略供应商不能发起Last Call...
        List<SupplierQuotationOrderPO> supplierQuotationOrders = supplierQuotationOrderService
                .queryBy(inquiryOrder.getInquiryNo(), inquiryOrder.getInquiryRound());
        Map<String, SupplierQuotationOrderPO> supplierQuotationOrderMap = supplierQuotationOrders.stream()
                .collect(Collectors.toMap(SupplierQuotationOrderPO::getSapSupplierCode, o -> o));
        for (String sapSupplierCode : sapSupplierCodes) {
            SupplierQuotationOrderPO sqo = supplierQuotationOrderMap.get(sapSupplierCode);
            if (sqo != null && !Objects.equals(SupplierQuotationStatusEnum.QUOTED.getCode(), sqo.getQuotationStatus())) {
                throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_LAST_CALL_REJECT_SUPPLIER_ERROR_MSG));
            }
        }
        // 校验询价单是否已定点...
        this.validateIsDesignatedRule(inquiryOrder);
    }

    @Override
    public List<SupplierLastCallRespDTO> queryLastCallSupplier(SupplierLastCallQueryReqDTO paramDTO) {
        List<SupplierLastCallRespDTO> supplierLastCallRespDTOs = new ArrayList<>();

        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());

        List<InquiryOrderSupplierRespDTO> inquirySuppliers = inquiryOrderSupplierService
                .queryBy(paramDTO.getInquiryNo(), YesOrNoEnum.YES.getCode());
        // 只获取战略供应商...
        inquirySuppliers = inquirySuppliers.stream()
                .filter(o -> Objects.equals(SupplierClassificationEnum.STRATEGY_SUPPLIER.getCode(), o.getSupplierClassification()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(inquirySuppliers)) {
            List<String> supplierCodes = inquirySuppliers.stream()
                    .map(InquiryOrderSupplierRespDTO::getSapSupplierCode)
                    .collect(Collectors.toList());
            // 供应商本轮报价情况...
            List<SupplierQuotationOrderPO> supplierQuotationOrders = supplierQuotationOrderService
                    .queryBy(inquiryOrder.getInquiryNo(), inquiryOrder.getInquiryRound());
            Map<String, SupplierQuotationOrderPO> supplierQuotationOrderMap = supplierQuotationOrders.stream()
                    .collect(Collectors.toMap(SupplierQuotationOrderPO::getSapSupplierCode, o -> o));
            // 供应商已使用Last Call次数...
            Map<String, Integer> usedTimesMap = supplierLastCallService.queryUsedTimesBy(supplierCodes, DateUtil.year(DateUtil.date()));

            supplierLastCallRespDTOs = BeanUtil.copyToList(inquirySuppliers, SupplierLastCallRespDTO.class);
            for (SupplierLastCallRespDTO supplierLastCallRespDTO : supplierLastCallRespDTOs) {
                supplierLastCallRespDTO.setUsedLastCallTimes(usedTimesMap.get(supplierLastCallRespDTO.getSapSupplierCode()));
                SupplierQuotationOrderPO sqo = supplierQuotationOrderMap.get(supplierLastCallRespDTO.getSapSupplierCode());
                if (sqo != null) {
                    supplierLastCallRespDTO.setQuotationStatus(sqo.getQuotationStatus());
                }
            }
            supplierLastCallRespDTOs = supplierLastCallRespDTOs.stream()
                    .filter(o -> Objects.equals(SupplierQuotationStatusEnum.QUOTED.getCode(), o.getQuotationStatus()))
                    .collect(Collectors.toList());
        }
        return supplierLastCallRespDTOs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void feedbackDeadlineDataRectify(InquiryOrderPO inquiryOrder) {
        List<SupplierQuotationOrderPO> supplierQuotationOrders =
                supplierQuotationOrderService.feedbackDeadlineDataRectify(inquiryOrder);
        boolean isAllSupplierRejected = true; // 是否所有供应商的意向反馈都是拒绝报价，是则修改询价单状态为"询价失败"...
        for (SupplierQuotationOrderPO o : supplierQuotationOrders) {
            // quotationIntention == null || quotationIntention = 1
            if (!Objects.equals(o.getQuotationIntention(), YesOrNoEnum.NO.getCode())) {
                isAllSupplierRejected = false;
                break;
            }
        }
        if (isAllSupplierRejected) {
            inquiryOrder.setFailReason(MsgConstants.INQUIRY_NO_SUPPLIER_QUOTE_CLOSE_DESC_MSG);
            inquiryOrder.setStatus(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
            inquiryOrderMapperService.updateById(inquiryOrder);
            sourcingSitMsgBiz.sendInquiryResultNotification(inquiryOrder);
            // 修改供应商报价单状态...
            supplierQuotationOrderService.updateStatusBy(inquiryOrder, SupplierQuotationOrderStatusEnum.INQUIRY_COMPLETED.getCode());
        }
    }

    private void validateIsDesignatedRule(InquiryOrderPO inquiryOrder) {
        if (Objects.equals(YesOrNoEnum.YES.getCode(), inquiryOrder.getIsDesignated())) {
            throw new BizFailException(localeMessage.getMessage(MsgConstants.INQUIRY_BUSINESS_DESIGNATION_CREATED_ERROR_MSG));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void designation(BusinessDesignationOrderReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        inquiryOrder.setIsDesignated(YesOrNoEnum.YES.getCode());
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);

        inquiryOrderMaterialService.designation(paramDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeDesignation(BusinessDesignationOrderCloseReqDTO paramDTO) {
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        // 撤销商务定点标记...
        inquiryOrder.setIsDesignated(YesOrNoEnum.NO.getCode());
        BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
        inquiryOrderMapperService.updateById(inquiryOrder);
        inquiryOrderMaterialService.revokeDesignation(paramDTO);

        if (Objects.equals(DesignationOrderCloseTypeEnum.DEEP_CLOSE.getCode(), paramDTO.getCloseType())) {
            // 关闭询价单并释放需求...
            InquiryOrderCloseReqDTO closeParamDTO = new InquiryOrderCloseReqDTO();
            closeParamDTO.setInquiryNo(paramDTO.getInquiryNo());
            closeParamDTO.setCloseType(InquiryOrderCloseTypeEnum.RELEASE.getCode());
            closeParamDTO.setCloseReason(paramDTO.getCloseReason());
            closeParamDTO.setOperateBy(paramDTO.getOperateBy());
            closeParamDTO.setOperateName(paramDTO.getOperateName());
            closeParamDTO.setIsValidateDesignationOrder(false);
            this.close(closeParamDTO);
        }
    }

    @Override
    public int statisticsPendingBy(Long userId) {
        List<String> statusAry = new ArrayList<>();
        statusAry.add(InquiryOrderStatusEnum.DRAFT.getCode());
        statusAry.add(InquiryOrderStatusEnum.INQUIRY_FAILED.getCode());
        return inquiryOrderMapperService.lambdaQuery()
                .eq(InquiryOrderPO::getInquiryEngineerUserId, userId)
                .eq(InquiryOrderPO::getIsDesignated, YesOrNoEnum.NO.getCode())
                .in(InquiryOrderPO::getStatus, statusAry)
                .count();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSampleOrder(List<InquiryOrderSampleOrderReqDTO> paramDTOs) {
        for (InquiryOrderSampleOrderReqDTO paramDTO : paramDTOs) {
            inquiryOrderMaterialService.createSampleOrder(paramDTO);
            // 标记询价单已有物料创建样件订单...
            InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
            inquiryOrder.setIsCreatedSampleOrder(YesOrNoEnum.YES.getCode());
            BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            inquiryOrderMapperService.updateById(inquiryOrder);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSampleOrder(List<InquiryOrderSampleOrderReqDTO> paramDTOs) {
        for (InquiryOrderSampleOrderReqDTO paramDTO : paramDTOs) {
            inquiryOrderMaterialService.deleteSampleOrder(paramDTO);
            // 如果询价单下所有物料的样件订单都已删除，则标记询价单没有任何物料存在样件订单...
            List<InquiryOrderMaterialPO> inquiryMaterials = inquiryOrderMaterialService.queryBy(paramDTO.getInquiryNo());
            List<InquiryOrderMaterialPO> withSampleOrderMaterials = inquiryMaterials
                    .stream().filter(o -> StringUtils.isNotBlank(o.getSampleOrderNo()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(withSampleOrderMaterials)) {
                InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
                inquiryOrder.setIsCreatedSampleOrder(YesOrNoEnum.NO.getCode());
                BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
                inquiryOrderMapperService.updateById(inquiryOrder);
            }
        }
    }

    @Override
    public void releaseNoDesignatedRequirements(InquiryOrderPO inquiryOrder) {
        inquiryOrderMaterialService.releaseNoDesignatedRequirements(inquiryOrder);
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.BUSINESS_DESIGNATION, "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ppapClose(InquiryOrderReleaseRequirementReqDTO paramDTO) {
        List<InquiryOrderMaterialPO> inquiryMaterials = inquiryOrderMaterialService.releaseRequirements(paramDTO);
        long lockedRequirements = inquiryMaterials.stream()
                .filter(o -> Objects.equals(InquiryOrderRequirementPartsStatusEnum.LOCK.getCode(), o.getRequirementPartsStatus()))
                .count();
        InquiryOrderPO inquiryOrder = inquiryOrderMapperService.queryBy(paramDTO.getInquiryNo());
        if (lockedRequirements == 0) {
            inquiryOrder.setStatus(InquiryOrderStatusEnum.CLOSED.getCode());
            inquiryOrder.setCloseReason(MsgConstants.INQUIRY_PPAP_CLOSE_DESC_MSG);
            BaseEntityUtil.setCommonForU(inquiryOrder, paramDTO.getOperateBy(), paramDTO.getOperateName(), DateUtil.date());
            inquiryOrderMapperService.updateById(inquiryOrder);
        }
        sourcingInquiryOrderChangeBiz.sendChangeMq(inquiryOrder, InquiryOrderOperateTypeEnum.PPAP_CLS_RELEASE_REQUIREMENT, "");
    }
}
