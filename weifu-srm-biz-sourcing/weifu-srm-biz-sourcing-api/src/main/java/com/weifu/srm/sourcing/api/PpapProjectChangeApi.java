package com.weifu.srm.sourcing.api;

import com.weifu.srm.common.restful.ApiResponse;
import com.weifu.srm.common.restful.PageResponse;
import com.weifu.srm.sourcing.constants.ServiceConstants;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangePageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangeSaveReqDTO;
import com.weifu.srm.sourcing.response.AttachmentRecordRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeDetailRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 工程变更清单 api
 * @Date 2024-09-04
 */

@Api("工程变更清单")
@FeignClient(value = ServiceConstants.APPLICATION_NAME)
public interface PpapProjectChangeApi {
    @ApiOperation("工程变更清单-分页查询")
    @PostMapping("/source/ppap/project/change/page")
    ApiResponse<PageResponse<PpapProjectChangeRespDTO>> page(@RequestBody @Valid PpapProjectChangePageReqDTO reqDTO);
    @ApiOperation("工程变更清单-供应商端")
    @PostMapping("/source/ppap/project/change/pageBySupplier")
    ApiResponse<PageResponse<PpapProjectChangeRespDTO>> pageBySupplier(@RequestBody @Valid PpapProjectChangePageReqDTO reqDTO);

    @ApiOperation("工程变更清单-详情")
    @GetMapping("/source/ppap/project/change/detail")
    ApiResponse<PpapProjectChangeDetailRespDTO> detail(@SpringQueryMap @RequestParam("id") Long id);

    @ApiOperation("工程变更清单-工程变更轨迹")
    @GetMapping("/source/ppap/project/change/listByReleaseNo")
    ApiResponse<PageResponse<PpapProjectChangeDetailRespDTO>> listByReleaseNo(@SpringQueryMap PpapProjectChangePageReqDTO reqDTO);

    @ApiOperation("工程变更清单-创建")
    @PostMapping("/source/ppap/project/change/create")
    ApiResponse<Void> create(@RequestBody @Valid PpapProjectChangeSaveReqDTO reqDTO);

    @ApiOperation("查看批产放行批产放行前所有附件")
    @GetMapping("/source/ppap/project/change/listBeforeChangeAnnex")
    ApiResponse<List<AttachmentRecordRespDTO> > listBeforeChangeAnnex(@RequestParam("changeNo") String changeNo);


}
