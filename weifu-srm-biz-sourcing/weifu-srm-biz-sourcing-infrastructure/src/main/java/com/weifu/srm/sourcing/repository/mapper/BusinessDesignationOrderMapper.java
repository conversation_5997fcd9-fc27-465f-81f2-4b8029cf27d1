package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderQueryReqDTO;
import com.weifu.srm.sourcing.response.BusinessDesignationOrderPriceRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessDesignationOrderMapper extends BaseMapper<BusinessDesignationOrderPO> {

    IPage<BusinessDesignationOrderPO> queryPage(Page<BusinessDesignationOrderPO> page, @Param("model") BusinessDesignationOrderQueryReqDTO paramDTO,
                                                @Param("dataPermissionKeys") List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    List<BusinessDesignationOrderPriceRespDTO> queryPrice(@Param("sapSupplierNo") String sapSupplierNo, @Param("materialCodes") List<String> materialCodes);
}
