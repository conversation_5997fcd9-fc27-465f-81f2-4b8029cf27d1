package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.InquiryOrderQuotationLadderPO;

import java.util.List;

public interface InquiryOrderQuotationLadderMapperService extends IService<InquiryOrderQuotationLadderPO> {

    List<InquiryOrderQuotationLadderPO> queryBy(String inquiryNo, String requirementPartsNo);

    List<InquiryOrderQuotationLadderPO> queryBy(String inquiryNo);

    List<InquiryOrderQuotationLadderPO> queryAll(String inquiryNo);

    void deleteBy(String inquiryNo, String requirementPartsNo);
}
