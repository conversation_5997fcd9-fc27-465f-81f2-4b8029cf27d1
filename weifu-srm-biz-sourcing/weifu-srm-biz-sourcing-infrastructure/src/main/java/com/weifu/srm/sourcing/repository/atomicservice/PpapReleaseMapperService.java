package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.bo.PpapEngineeringChangeMinTimeBO;
import com.weifu.srm.sourcing.repository.po.PpapReleasePO;
import com.weifu.srm.sourcing.request.OperateUserReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseExecuteSituationReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleasePageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseReportPageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseWorkbenchReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseEngineerRowRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseWorkbenchRespDTO;

import java.util.List;

/**
 * <p>
 * 批产放行 Mapper服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface PpapReleaseMapperService extends IService<PpapReleasePO> {
    IPage<PpapReleasePO> listPageByQuery(Page<PpapReleasePO> page, PpapReleasePageReqDTO reqDTO);

    IPage<PpapReleaseReportRespDTO> listReportPageByQuery(Page<PpapReleaseReportRespDTO> page, PpapReleaseReportPageReqDTO reqDTO);

    PpapReleasePO getByReleaseNo(String releaseNo);

    PpapReleaseWorkbenchRespDTO workbench(PpapReleaseWorkbenchReqDTO reqDTO);

    List<PpapReleaseEngineerRowRespDTO> executeSituation(PpapReleaseExecuteSituationReqDTO reqDTO);

    void saveOrUpdateRelease(PpapReleasePO po);

    PpapReleasePO updatePlanNoByReleaseNo(String releaseNo, String planNo, OperateUserReqDTO reqDTO);

    PpapEngineeringChangeMinTimeBO selectOptMinTime(String requirementNo, String releaseNo);

    Integer countByInternal(Long operationBy,
                            List<String> cpeCategoryCodeList,
                            List<String> sqeCategoryCodeList);

    List<PpapReleasePO> listByPendingProcessing(Long operationBy,
                            List<String> cpeCategoryCodeList,
                            List<String> sqeCategoryCodeList);
}
