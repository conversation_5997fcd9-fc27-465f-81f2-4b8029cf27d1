package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQuery4SupplierReqDTO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQueryDTO;
import com.weifu.srm.sourcing.response.bidding.BiddingSubmitterRespDTO;
import com.weifu.srm.sourcing.response.bidding.SupplierBiddingListRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BiddingMapper extends BaseMapper<BiddingPO> {

    IPage<BiddingPO> queryPage(Page<BiddingPO> page, @Param("model") BiddingListQueryDTO paramDTO,
                               @Param("dataPermissionKeys") List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    IPage<SupplierBiddingListRespDTO> queryPage4Supplier(Page<SupplierBiddingListRespDTO> page, @Param("model") BiddingListQuery4SupplierReqDTO paramDTO);

    List<BiddingSubmitterRespDTO> querySubmitter(@Param("userId") Long userId,
                                                 @Param("dataPermissionKeys") List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);
}
