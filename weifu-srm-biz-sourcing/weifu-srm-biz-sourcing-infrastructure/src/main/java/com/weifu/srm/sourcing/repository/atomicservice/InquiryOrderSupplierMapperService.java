package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.InquiryOrderSupplierPO;

import java.io.Serializable;
import java.util.List;

public interface InquiryOrderSupplierMapperService extends IService<InquiryOrderSupplierPO> {

    InquiryOrderSupplierPO queryBy(String inquiryNo, String sapSupplierCode);

    List<InquiryOrderSupplierPO> queryBy(String inquiryNo);

    List<InquiryOrderSupplierPO> queryBy(String inquiryNo, Integer isChecked);

    List<InquiryOrderSupplierPO> queryAll(String inquiryNo);

    int restoreById(Serializable id);
}
