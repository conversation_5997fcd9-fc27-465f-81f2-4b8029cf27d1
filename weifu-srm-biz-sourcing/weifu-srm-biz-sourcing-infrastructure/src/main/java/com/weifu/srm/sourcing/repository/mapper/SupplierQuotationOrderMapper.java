package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.QuotationOrderHistoryQueryReqDTO;
import com.weifu.srm.sourcing.request.QuotationOrderQueryReqDTO;
import com.weifu.srm.sourcing.request.SampleOrderRefQueryReqDTO;
import com.weifu.srm.sourcing.response.QuotationOrderHistoryRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderRespDTO;
import com.weifu.srm.sourcing.response.SampleOrderRefRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SupplierQuotationOrderMapper extends BaseMapper<SupplierQuotationOrderPO> {

    IPage<QuotationOrderRespDTO> queryPage(Page<QuotationOrderRespDTO> page, @Param("model") QuotationOrderQueryReqDTO paramDTO);

    IPage<QuotationOrderHistoryRespDTO> queryHistoryPage(Page<QuotationOrderHistoryRespDTO> page, @Param("model") QuotationOrderHistoryQueryReqDTO paramDTO);

    List<SampleOrderRefRespDTO> queryForSampleOrderRef(@Param("model") SampleOrderRefQueryReqDTO paramDTO);

    List<SupplierQuotationOrderPO> queryLatestRoundQuotationOrderBy(@Param("inquiryNos") List<String> inquiryNos);
}
