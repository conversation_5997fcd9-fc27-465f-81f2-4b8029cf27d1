package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.bo.PpapEngineeringChangeMinTimeBO;
import com.weifu.srm.sourcing.repository.po.PpapReleasePO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseExecuteSituationReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseReportPageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseWorkbenchReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseEngineerRowRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseWorkbenchRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 批产放行 Mapper 接口
 * @Date 2024-09-04
 */
@Mapper
public interface PpapReleaseMapper extends BaseMapper<PpapReleasePO> {
    IPage<PpapReleaseReportRespDTO> listReportPageByQuery(Page<PpapReleaseReportRespDTO> page, @Param("dto") PpapReleaseReportPageReqDTO reqDTO);

    List<PpapReleaseReportRespDTO> listReportByQuery(@Param("dto") PpapReleaseReportPageReqDTO reqDTO);

    PpapReleaseWorkbenchRespDTO workbench(PpapReleaseWorkbenchReqDTO reqDTO);

    List<PpapReleaseEngineerRowRespDTO> executeSituation(PpapReleaseExecuteSituationReqDTO reqDTO);

    PpapEngineeringChangeMinTimeBO selectOptMinTime(@Param("requirementNo") String requirementNo, @Param("releaseNo") String releaseNo);

    Integer countByInternal(@Param("operationBy") Long operationBy,
                            @Param("cpeCategoryCodeList") List<String> cpeCategoryCodeList,
                            @Param("sqeCategoryCodeList") List<String> sqeCategoryCodeList);

    List<PpapReleasePO> listByPendingProcessing(@Param("operationBy") Long operationBy,
                            @Param("cpeCategoryCodeList") List<String> cpeCategoryCodeList,
                            @Param("sqeCategoryCodeList") List<String> sqeCategoryCodeList);
}
