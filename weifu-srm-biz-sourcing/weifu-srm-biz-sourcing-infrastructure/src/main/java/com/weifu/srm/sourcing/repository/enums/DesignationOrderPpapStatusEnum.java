package com.weifu.srm.sourcing.repository.enums;

import lombok.Getter;

@Getter
public enum DesignationOrderPpapStatusEnum {

    NOT_CREATED("NOT_CREATED", "未创建"),
    ACTIVE("ACTIVE", "全部有效"),
    PARTIALLY_ACTIVE("PARTIALLY_ACTIVE", "部分有效"),
    INACTIVE("INACTIVE", "全部无效");

    private final String code;
    private final String desc;

    DesignationOrderPpapStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
