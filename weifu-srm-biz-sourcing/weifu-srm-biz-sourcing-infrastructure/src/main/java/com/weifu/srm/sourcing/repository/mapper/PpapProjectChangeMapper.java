package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.PpapProjectChangePO;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangePageReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 * @Description 工程变更清单 Mapper 接口
 * @Date 2024-09-04
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PpapProjectChangeMapper extends BaseMapper<PpapProjectChangePO> {
    IPage<PpapProjectChangeRespDTO> listPageByQuery(Page<PpapProjectChangeRespDTO> page, @Param("dto") PpapProjectChangePageReqDTO reqDTO);
}
