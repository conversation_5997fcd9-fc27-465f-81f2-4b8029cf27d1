package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.common.enums.YesOrNoEnum;
import com.weifu.srm.sourcing.repository.atomicservice.PpapPlanGradeConfigMapperService;
import com.weifu.srm.sourcing.repository.mapper.PpapPlanGradeConfigMapper;
import com.weifu.srm.sourcing.repository.po.PpapPlanGradeConfigPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * PPAP计划等级设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapPlanGradeConfigMapperServiceImpl extends ServiceImpl<PpapPlanGradeConfigMapper, PpapPlanGradeConfigPO> implements PpapPlanGradeConfigMapperService {

    @Override
    public List<PpapPlanGradeConfigPO> listLockData() {
        return this.lambdaQuery().eq(PpapPlanGradeConfigPO::getIsLock, YesOrNoEnum.YES.getCode()).list();
    }
}
