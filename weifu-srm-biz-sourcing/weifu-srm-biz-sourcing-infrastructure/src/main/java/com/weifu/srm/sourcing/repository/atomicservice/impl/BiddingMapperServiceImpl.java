package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingMapperService;
import com.weifu.srm.sourcing.repository.mapper.BiddingMapper;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQuery4SupplierReqDTO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQueryDTO;
import com.weifu.srm.sourcing.response.bidding.BiddingSubmitterRespDTO;
import com.weifu.srm.sourcing.response.bidding.SupplierBiddingListRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BiddingMapperServiceImpl extends ServiceImpl<BiddingMapper, BiddingPO> implements BiddingMapperService {

    @Override
    public BiddingPO queryBy(String biddingNo) {
        return this.lambdaQuery()
                .eq(BiddingPO::getBiddingNo, biddingNo)
                .one();
    }

    @Override
    public IPage<BiddingPO> queryPage(Page<BiddingPO> page, BiddingListQueryDTO paramDTO,
                                      List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys) {
        return baseMapper.queryPage(page, paramDTO, dataPermissionKeys);
    }

    @Override
    public IPage<SupplierBiddingListRespDTO> queryPage4Supplier(Page<SupplierBiddingListRespDTO> page, BiddingListQuery4SupplierReqDTO paramDTO) {
        return baseMapper.queryPage4Supplier(page, paramDTO);
    }

    @Override
    public List<BiddingPO> queryBy(List<String> statusAry) {
        return this.lambdaQuery()
                .in(BiddingPO::getStatus, statusAry)
                .list();
    }

    @Override
    public List<BiddingSubmitterRespDTO> querySubmitter(Long userId, List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys) {
        return baseMapper.querySubmitter(userId, dataPermissionKeys);
    }
}
