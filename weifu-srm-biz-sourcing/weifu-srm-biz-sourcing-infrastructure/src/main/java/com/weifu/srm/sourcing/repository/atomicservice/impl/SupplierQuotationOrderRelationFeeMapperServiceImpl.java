package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderRelationFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderRelationFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRelationFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderRelationFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderRelationFeeMapper, SupplierQuotationOrderRelationFeePO>
        implements SupplierQuotationOrderRelationFeeMapperService {

    @Override
    public SupplierQuotationOrderRelationFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderRelationFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .one();
    }

    @Override
    public List<SupplierQuotationOrderRelationFeePO> queryBy(List<Long> quotationOrderMaterialIds) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, quotationOrderMaterialIds)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderRelationFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderRelationFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
