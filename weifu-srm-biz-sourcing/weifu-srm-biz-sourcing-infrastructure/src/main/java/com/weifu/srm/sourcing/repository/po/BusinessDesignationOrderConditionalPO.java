package com.weifu.srm.sourcing.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.weifu.srm.mybatis.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("business_designation_order_conditional")
public class BusinessDesignationOrderConditionalPO extends BaseEntity {
    /**
     * 定点单号
     */
    private String designationOrderNo;
    /**
     * 需求编码
     */
    private String requirementPartsNo;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料编码
     */
    private String sapSupplierCode;
    /**
     * 条件类型
     */
    private String conditionType;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 比率单位
     */
    private String ratioUnit;
    /**
     * 条件定价单位
     */
    private String conditionPricingUnit;
    /**
     * 条件单位
     */
    private String conditionUnit;
}
