package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderConditionalPO;

import java.util.List;

public interface BusinessDesignationOrderConditionalMapperService extends IService<BusinessDesignationOrderConditionalPO> {

    void deleteBy(String designationOrderNo);

    List<BusinessDesignationOrderConditionalPO> queryBy(String designationOrderNo);
}
