package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderSupplierMapperService;
import com.weifu.srm.sourcing.repository.mapper.InquiryOrderSupplierMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderSupplierPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Service
public class InquiryOrderSupplierMapperServiceImpl
        extends ServiceImpl<InquiryOrderSupplierMapper, InquiryOrderSupplierPO> implements InquiryOrderSupplierMapperService {

    @Override
    public InquiryOrderSupplierPO queryBy(String inquiryNo, String sapSupplierCode) {
        return this.lambdaQuery()
                .eq(InquiryOrderSupplierPO::getInquiryNo, inquiryNo)
                .eq(InquiryOrderSupplierPO::getSapSupplierCode, sapSupplierCode)
                .one();
    }

    @Override
    public List<InquiryOrderSupplierPO> queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .eq(InquiryOrderSupplierPO::getInquiryNo, inquiryNo)
                .list();
    }

    @Override
    public List<InquiryOrderSupplierPO> queryBy(String inquiryNo, Integer isChecked) {
        return this.lambdaQuery()
                .eq(InquiryOrderSupplierPO::getInquiryNo, inquiryNo)
                .eq(InquiryOrderSupplierPO::getIsChecked, isChecked)
                .list();
    }

    @Override
    public List<InquiryOrderSupplierPO> queryAll(String inquiryNo) {
        return this.baseMapper.selectAll(inquiryNo);
    }

    @Override
    public int restoreById(Serializable id) {
        return this.baseMapper.restoreById(id);
    }
}
