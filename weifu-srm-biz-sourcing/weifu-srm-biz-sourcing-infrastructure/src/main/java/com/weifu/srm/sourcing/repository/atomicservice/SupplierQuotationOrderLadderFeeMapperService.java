package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderLadderFeePO;

import java.util.List;

public interface SupplierQuotationOrderLadderFeeMapperService extends IService<SupplierQuotationOrderLadderFeePO> {

    List<SupplierQuotationOrderLadderFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);

    List<SupplierQuotationOrderLadderFeePO> queryBy(List<Long> quotationOrderMaterialIds);
}
