package com.weifu.srm.sourcing.repository.constants;

public class MsgConstants {

    public static final String NONE_MATCH_STATUS_ERROR_MSG = "none.match.status.error.msg";

    public static final String INQUIRY_REQUIREMENT_PARTS_LOCKED_ERROR_MSG = "inquiry.requirement.parts.locked.error.msg";
    public static final String INQUIRY_REQUIREMENT_WITH_SAMPLE_ORDER_ERROR_MSG = "inquiry.requirement.with.sample.order.error.msg";
    public static final String INQUIRY_SUPPLIER_NO_AGREEMENTS_ERROR_MSG = "inquiry.supplier.no.agreements.error.msg";
    public static final String INQUIRY_MATCH_SUPPLIER_COUNT_ERROR_MSG = "inquiry.match.supplier.count.error.msg";
    public static final String INQUIRY_SINGLE_SOURCING_DESC_EMPTY_ERROR_MSG = "inquiry.single.sourcing.desc.empty.error.msg";
    public static final String INQUIRY_PROXY_QUOTATION_ATTACHMENT_EMPTY_ERROR_MSG = "inquiry.proxy.quotation.attachment.empty.error.msg";
    public static final String INQUIRY_PROXY_QUOTATION_NOT_ALLOW_ERROR_MSG = "inquiry.proxy.quotation.not.allow.error.msg";
    public static final String INQUIRY_SUPPLIER_REVIEW_BASIS_DESC_EMPTY_ERROR_MSG = "inquiry.supplier.review.basis.desc.empty.error.msg";
    public static final String INQUIRY_QUOTATION_FEEDBACK_DEADLINE_NOT_COMPLETE_ERROR_MSG = "inquiry.quotation.feedback.deadline.not.complete.error.msg";
    public static final String INQUIRY_QUOTATION_NOT_COMPLETE_ERROR_MSG = "inquiry.quotation.not.complete.error.msg";
    public static final String INQUIRY_QUOTATION_INTENTION_REJECT_ERROR_MSG = "inquiry.quotation.intention.reject.error.msg";
    public static final String INQUIRY_QUOTATION_INTENTION_FEEDBACK_TIMEOUT_ERROR_MSG = "inquiry.quotation.intention.feedback.timeout.error.msg";
    public static final String INQUIRY_NOT_MATCH_QUOTATION_TEMPLATE_ERROR_MSG = "inquiry.not.match.quotation.template.error.msg";
    public static final String INQUIRY_QUOTATION_SUBMITTED_ERROR_MSG = "inquiry.quotation.submitted.error.msg";
    public static final String INQUIRY_QUOTATION_NOT_SUBMITTED_ERROR_MSG = "inquiry.quotation.not.submitted.error.msg";
    public static final String INQUIRY_NOT_QUOTATION_ALL_ERROR_MSG = "inquiry.not.quotation.all.error.msg";
    public static final String INQUIRY_QUOTATION_TIMEOUT_ERROR_MSG = "inquiry.quotation.timeout.error.msg";
    public static final String INQUIRY_LAST_CALL_OCCURRED_ERROR_MSG = "inquiry.last.call.occurred.error.msg";
    public static final String INQUIRY_NO_STRATEGY_SUPPLIER_ERROR_MSG = "inquiry.no.strategy.supplier.error.msg";
    public static final String INQUIRY_LAST_CALL_TIMES_LIMIT_ERROR_MSG = "inquiry.last.call.times.limit.error.msg";
    public static final String INQUIRY_LAST_CALL_REJECT_SUPPLIER_ERROR_MSG = "inquiry.last.call.reject.supplier.error.msg";
    public static final String INQUIRY_NO_SUPPLIER_QUOTE_CLOSE_DESC_MSG = "无供应商报价";
    public static final String INQUIRY_NOT_ENOUGH_QUOTE_SUPPLIER_CLOSE_DESC_MSG = "低于询比价最低供应商报价数量要求";
    public static final String INQUIRY_CHANGE_STRATEGY_CLOSE_DESC_MSG = "变更寻源策略";
    public static final String INQUIRY_BUSINESS_DESIGNATION_DISCARD_CLOSE_DESC_MSG = "商务定点单废弃关闭";
    public static final String INQUIRY_BUSINESS_DESIGNATION_CLOSE_DESC_MSG = "商务定点单关联关闭";
    public static final String INQUIRY_CATEGORY_NO_CPE_MASTER_ERROR_MSG = "inquiry.category.no.cpe.master.error.msg";
    public static final String INQUIRY_BUSINESS_DESIGNATION_CREATED_ERROR_MSG = "inquiry.business.designation.created.error.msg";
    public static final String INQUIRY_TICKET_DESC_MSG = "关于询价单号{0}的寻源策略申请";
    public static final String INQUIRY_LAST_CALL_DESC_MSG = "Last Call";
    public static final String INQUIRY_PPAP_CLOSE_DESC_MSG = "批产放行关联关闭";

    public static final String BUSINESS_DESIGNATION_SUPPLIER_NO_AGREEMENTS_ERROR_MSG = "business.designation.supplier.no.agreements.error.msg";
    public static final String BUSINESS_DESIGNATION_EXISTS_ERROR_MSG = "business.designation.exists.error.msg";
    public static final String BUSINESS_DESIGNATION_TICKET_DESC_MSG = "关于询价单号{0}的商务定点审批申请";
    public static final String BUSINESS_DESIGNATION_DISCARD_TICKET_DESC_MSG = "关于定点单号{0}的废弃申请";
    public static final String BUSINESS_DESIGNATION_DISCARD_PPAP_ERROR_MSG = "business.designation.discard.ppap.error.msg";
    public static final String BUSINESS_DESIGNATION_DISCARD_PRICE_LEDGER_ERROR_MSG = "business.designation.discard.price.ledger.error.msg";

    public static final String BIDDING_MAX_ROUND_ERROR_MSG = "bidding.max.round.error.msg";
    public static final String BIDDING_DURATION_MINUTES_ERROR_MSG = "bidding.duration.minutes.error.msg";
    public static final String BIDDING_FEEDBACK_DEADLINE_ERROR_MSG = "bidding.feedback.deadline.error.msg";
    public static final String BIDDING_START_TIME_ERROR_MSG = "bidding.start.time.error.msg";
    public static final String BIDDING_NOT_ENOUGH_SUPPLIER_ERROR_MSG = "bidding.not.enough.supplier.error.msg";
    public static final String BIDDING_NOT_ENOUGH_PARTICIPATE_SUPPLIER_DESC_MSG = "资料更新";
    public static final String BIDDING_FEEDBACK_TIMEOUT_ERROR_MSG = "bidding.feedback.timeout.error.msg";
    public static final String BIDDING_PRICE_TIMEOUT_ERROR_MSG = "bidding.price.timeout.error.msg";
    public static final String BIDDING_CAN_NOT_USE_PREVIOUS_PRICE_ERROR_MSG = "bidding.can.not.use.previous.price.error.msg";
    public static final String BIDDING_NOT_PARTICIPATE_FIRST_ROUND_ERROR_MSG = "bidding.not.participate.first.round.error.msg";
    public static final String BIDDING_PRICE_GT_PREVIOUS_ERROR_MSG = "bidding.price.gt.previous.error.msg";
    public static final String BIDDING_NOT_START_ERROR_MSG = "bidding.not.start.error.msg";
}
