package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderBaseFeePO;

import java.util.List;

public interface SupplierQuotationOrderBaseFeeMapperService extends IService<SupplierQuotationOrderBaseFeePO> {

    SupplierQuotationOrderBaseFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);

    List<SupplierQuotationOrderBaseFeePO> queryBy(List<Long> quotationOrderMaterialIds);
}
