package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;

import java.util.List;

public interface AttachmentRecordMapperService extends IService<AttachmentRecordPO> {

    void deleteBy(String businessNo, String businessSubNo, String businessType);

    List<AttachmentRecordPO> queryBy(String businessNo);

    List<AttachmentRecordPO> queryBy(String businessNo, String businessType);

    List<AttachmentRecordPO> queryBy(List<String> businessNos, String businessType);

    List<AttachmentRecordPO> queryBy(List<String> businessNos, List<String> businessTypes);

    List<AttachmentRecordPO> queryAll(String businessNo);

    AttachmentRecordPO queryByFileName(String fileName);
}
