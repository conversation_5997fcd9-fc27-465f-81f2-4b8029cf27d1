package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderOutsourcingCostFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderOutsourcingCostFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderOutsourcingCostFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderOutsourcingCostFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderOutsourcingCostFeeMapper, SupplierQuotationOrderOutsourcingCostFeePO>
        implements SupplierQuotationOrderOutsourcingCostFeeMapperService {

    @Override
    public List<SupplierQuotationOrderOutsourcingCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderOutsourcingCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderOutsourcingCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderOutsourcingCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderOutsourcingCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
