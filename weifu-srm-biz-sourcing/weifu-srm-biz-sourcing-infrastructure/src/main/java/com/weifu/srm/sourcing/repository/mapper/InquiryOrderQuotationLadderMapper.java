package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderQuotationLadderPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InquiryOrderQuotationLadderMapper extends BaseMapper<InquiryOrderQuotationLadderPO> {

    @Select("select * from inquiry_order_quotation_ladder where inquiry_no = #{inquiryNo}")
    List<InquiryOrderQuotationLadderPO> selectAll(@Param("inquiryNo") String inquiryNo);
}
