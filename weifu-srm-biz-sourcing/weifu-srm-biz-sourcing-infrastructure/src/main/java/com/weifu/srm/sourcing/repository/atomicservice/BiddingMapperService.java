package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BiddingPO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQuery4SupplierReqDTO;
import com.weifu.srm.sourcing.request.bidding.BiddingListQueryDTO;
import com.weifu.srm.sourcing.response.bidding.BiddingSubmitterRespDTO;
import com.weifu.srm.sourcing.response.bidding.SupplierBiddingListRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

public interface BiddingMapperService extends IService<BiddingPO> {

    BiddingPO queryBy(String biddingNo);

    IPage<BiddingPO> queryPage(Page<BiddingPO> page, BiddingListQueryDTO paramDTO,
                               List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    IPage<SupplierBiddingListRespDTO> queryPage4Supplier(Page<SupplierBiddingListRespDTO> page, BiddingListQuery4SupplierReqDTO paramDTO);

    List<BiddingPO> queryBy(List<String> statusAry);

    List<BiddingSubmitterRespDTO> querySubmitter(Long userId, List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);
}
