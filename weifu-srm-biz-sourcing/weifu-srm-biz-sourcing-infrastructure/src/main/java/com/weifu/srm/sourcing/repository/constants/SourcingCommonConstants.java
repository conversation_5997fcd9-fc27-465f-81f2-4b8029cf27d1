package com.weifu.srm.sourcing.repository.constants;

/**
 * <p>
 * 通用常量
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public class SourcingCommonConstants {

    private SourcingCommonConstants() {
    }

    public static final String DEFAULT_EMPTY_STR = "";
    public static final int NO_PAGE_SIZE = -1;
    public static final String SIMPLE_DATE_PATTERN = "yyMMdd";

    public static final String EXPORT_SUPPLIER_HEAD = "供应商:CB";
    public static final String EXPORT_STANDARD_HEAD = "规格:CB";
    public static final String EXPORT_PRICE_UNIT_HEAD = "价格单位:CB";
    public static final String EXPORT_CURRENCY_HEAD = "币种:CB";
    public static final String EXPORT_TAX_RATE_HEAD = "税率:CB";
    public static final String EXPORT_MATERIAL_DESC_HEAD = "物料描述:CB";
    public static final String EXPORT_ONE_TIME_FEE_HEAD = "一次性费用（元）:CB";
    public static final String EXPORT_MIN_PACKAGE_QTY_HEAD = "最小包装量:CB";
    public static final String EXPORT_PURCHASE_AMOUNT_HEAD = "采购金额（元）:CB";
    public static final String EXPORT_MASS_PRODUCTION_DELIVERY_DAYS_HEAD = "量产交付周期（天）:CB";
    public static final String EXPORT_IS_INCLUDE_WEIFU_PURCHASE_MATERIAL_HEAD = "是否包含从威孚采购材料:CB";
    public static final String EXPORT_FIRST_SAMPLE_DELIVERY_DAYS_HEAD = "首批样品交付周期（天）:CB";
    public static final String EXPORT_MIN_ORDER_QTY_HEAD = "最小起订量:CB";
}
