package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderSupplierMapperService;
import com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderSupplierMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderSupplierPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BusinessDesignationOrderSupplierMapperServiceImpl
        extends ServiceImpl<BusinessDesignationOrderSupplierMapper, BusinessDesignationOrderSupplierPO>
        implements BusinessDesignationOrderSupplierMapperService {

    @Override
    public List<BusinessDesignationOrderSupplierPO> queryBy(String designationOrderNo, String requirementPartsNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderSupplierPO::getDesignationOrderNo, designationOrderNo)
                .eq(BusinessDesignationOrderSupplierPO::getRequirementPartsNo, requirementPartsNo)
                .list();
    }

    @Override
    public List<BusinessDesignationOrderSupplierPO> queryBy(String designationOrderNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderSupplierPO::getDesignationOrderNo, designationOrderNo)
                .list();
    }

    @Override
    public void deleteBy(String designationOrderNo) {
        this.lambdaUpdate()
                .eq(BusinessDesignationOrderSupplierPO::getDesignationOrderNo, designationOrderNo)
                .remove();
    }

    @Override
    public List<BusinessDesignationOrderSupplierPO> queryDesignationSuppliers(List<String> materialCodes, List<String> statusAry) {
        return this.baseMapper.selectDesignationSuppliers(materialCodes, statusAry);
    }
}
