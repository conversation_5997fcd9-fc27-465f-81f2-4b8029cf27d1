package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderBaseFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderBaseFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderBaseFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderBaseFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderBaseFeeMapper, SupplierQuotationOrderBaseFeePO>
        implements SupplierQuotationOrderBaseFeeMapperService {

    @Override
    public SupplierQuotationOrderBaseFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderBaseFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .one();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderBaseFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }

    @Override
    public List<SupplierQuotationOrderBaseFeePO> queryBy(List<Long> quotationOrderMaterialIds) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderBaseFeePO::getQuotationOrderMaterialId, quotationOrderMaterialIds)
                .list();
    }
}
