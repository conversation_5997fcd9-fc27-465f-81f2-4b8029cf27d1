package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderOutsourcingCostFeePO;

import java.util.List;

public interface SupplierQuotationOrderOutsourcingCostFeeMapperService extends IService<SupplierQuotationOrderOutsourcingCostFeePO> {

    List<SupplierQuotationOrderOutsourcingCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
