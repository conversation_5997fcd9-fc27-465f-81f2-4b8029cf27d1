package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMouldAmortizationFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderMouldAmortizationFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMouldAmortizationFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderMouldAmortizationFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderMouldAmortizationFeeMapper, SupplierQuotationOrderMouldAmortizationFeePO>
        implements SupplierQuotationOrderMouldAmortizationFeeMapperService {

    @Override
    public List<SupplierQuotationOrderMouldAmortizationFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderMouldAmortizationFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderMouldAmortizationFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderMouldAmortizationFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderMouldAmortizationFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
