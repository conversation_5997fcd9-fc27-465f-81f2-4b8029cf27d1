package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderSupplierPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessDesignationOrderSupplierMapper extends BaseMapper<BusinessDesignationOrderSupplierPO> {

    List<BusinessDesignationOrderSupplierPO> selectDesignationSuppliers(@Param("materialCodes") List<String> materialCodes, @Param("statusAry") List<String> statusAry);
}
