package com.weifu.srm.sourcing.repository.atomicservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMaterialMapperService;
import com.weifu.srm.sourcing.repository.mapper.InquiryOrderMaterialMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderMaterialPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Service
public class InquiryOrderMaterialMapperServiceImpl
        extends ServiceImpl<InquiryOrderMaterialMapper, InquiryOrderMaterialPO> implements InquiryOrderMaterialMapperService {

    @Override
    public List<InquiryOrderMaterialPO> queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .eq(InquiryOrderMaterialPO::getInquiryNo, inquiryNo)
                .list();
    }

    @Override
    public List<InquiryOrderMaterialPO> queryBy(String inquiryNo, String materialCode) {
        return this.lambdaQuery()
                .eq(InquiryOrderMaterialPO::getInquiryNo, inquiryNo)
                .eq(InquiryOrderMaterialPO::getMaterialCode, materialCode)
                .list();
    }

    @Override
    public List<InquiryOrderMaterialPO> queryBy(String inquiryNo, List<String> materialCodes) {
        return this.lambdaQuery()
                .eq(InquiryOrderMaterialPO::getInquiryNo, inquiryNo)
                .in(CollUtil.isNotEmpty(materialCodes), InquiryOrderMaterialPO::getMaterialCode, materialCodes)
                .list();
    }

    @Override
    public List<InquiryOrderMaterialPO> queryAll(String inquiryNo) {
        return baseMapper.selectAll(inquiryNo);
    }

    @Override
    public int restoreById(Serializable id) {
        return baseMapper.restoreById(id);
    }
}
