package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRelationFeePO;

import java.util.List;

public interface SupplierQuotationOrderRelationFeeMapperService extends IService<SupplierQuotationOrderRelationFeePO> {

    SupplierQuotationOrderRelationFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    List<SupplierQuotationOrderRelationFeePO> queryBy(List<Long> quotationOrderMaterialIds);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
