package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierPO;

import java.io.Serializable;
import java.util.List;

public interface BiddingSupplierMapperService extends IService<BiddingSupplierPO> {

    List<BiddingSupplierPO> queryBy(String biddingNo);

    List<BiddingSupplierPO> queryBy(List<String> biddingNos);

    List<BiddingSupplierPO> queryAll(String biddingNo);

    int restoreById(Serializable id);

    int queryCountBySupplier(String sapSupplierCode, String status, Integer biddingIntention, Integer feedbackIntention);
}
