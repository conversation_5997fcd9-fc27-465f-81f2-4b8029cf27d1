package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMaterialPO;

import java.util.List;

public interface SupplierQuotationOrderMaterialMapperService extends IService<SupplierQuotationOrderMaterialPO> {

    SupplierQuotationOrderMaterialPO queryBy(Long quotationOrderId, String requirementPartsNo, String quotationTemplate);

    List<SupplierQuotationOrderMaterialPO> queryBy(Long quotationOrderId);

    List<SupplierQuotationOrderMaterialPO> queryBy(String inquiryNo, Integer quotationRound);

    List<SupplierQuotationOrderMaterialPO> queryBy(String inquiryNo);

    SupplierQuotationOrderMaterialPO queryPreviousMassProdPriceBy(String excludeInquiryNo, String inquiryStatus,
                                                                  String sapSupplierCode, String materialCode,
                                                                  List<String> quotationTemplates);
}
