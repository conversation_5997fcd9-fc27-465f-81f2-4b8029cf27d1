package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderQuotationLadderMapperService;
import com.weifu.srm.sourcing.repository.mapper.InquiryOrderQuotationLadderMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderQuotationLadderPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class InquiryOrderQuotationLadderMapperServiceImpl
        extends ServiceImpl<InquiryOrderQuotationLadderMapper, InquiryOrderQuotationLadderPO> implements InquiryOrderQuotationLadderMapperService {

    @Override
    public List<InquiryOrderQuotationLadderPO> queryBy(String inquiryNo, String requirementPartsNo) {
        return this.lambdaQuery()
                .eq(InquiryOrderQuotationLadderPO::getInquiryNo, inquiryNo)
                .eq(InquiryOrderQuotationLadderPO::getRequirementPartsNo, requirementPartsNo)
                .list();
    }

    @Override
    public List<InquiryOrderQuotationLadderPO> queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .eq(InquiryOrderQuotationLadderPO::getInquiryNo, inquiryNo)
                .list();
    }

    @Override
    public List<InquiryOrderQuotationLadderPO> queryAll(String inquiryNo) {
        return this.baseMapper.selectAll(inquiryNo);
    }

    @Override
    public void deleteBy(String inquiryNo, String requirementPartsNo) {
        this.lambdaUpdate()
                .eq(InquiryOrderQuotationLadderPO::getInquiryNo, inquiryNo)
                .eq(InquiryOrderQuotationLadderPO::getRequirementPartsNo, requirementPartsNo)
                .remove();
    }
}
