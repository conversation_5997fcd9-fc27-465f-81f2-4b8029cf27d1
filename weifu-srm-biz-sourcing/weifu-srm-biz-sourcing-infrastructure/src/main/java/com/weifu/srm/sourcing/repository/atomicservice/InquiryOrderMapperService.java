package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.request.InquiryOrderQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

public interface InquiryOrderMapperService extends IService<InquiryOrderPO> {

    IPage<InquiryOrderPO> queryPage(Page<InquiryOrderPO> page, InquiryOrderQueryReqDTO paramDTO,
                                    List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    InquiryOrderPO queryBy(String inquiryNo);

    List<InquiryOrderPO> queryBy(List<String> inquiryNos);
}
