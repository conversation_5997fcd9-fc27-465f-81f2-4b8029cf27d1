package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderNotificationMapperService;
import com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderNotificationMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderNotificationQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BusinessDesignationOrderNotificationMapperServiceImpl
        extends ServiceImpl<BusinessDesignationOrderNotificationMapper, BusinessDesignationOrderNotificationPO>
        implements BusinessDesignationOrderNotificationMapperService {

    @Override
    public IPage<BusinessDesignationOrderNotificationPO> queryPage(Page<BusinessDesignationOrderNotificationPO> page,
                                                                   BusinessDesignationOrderNotificationQueryReqDTO paramDTO,
                                                                   List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys) {
        return baseMapper.queryPage(page, paramDTO, dataPermissionKeys);
    }

    @Override
    public BusinessDesignationOrderNotificationPO queryBy(String designationOrderNotificationNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderNotificationPO::getDesignationOrderNotificationNo, designationOrderNotificationNo)
                .one();
    }

    @Override
    public List<BusinessDesignationOrderNotificationPO> queryByDesignationOrderNo(String designationOrderNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderNotificationPO::getDesignationOrderNo, designationOrderNo)
                .list();
    }

    @Override
    public void deleteBy(String designationOrderNo) {
        this.lambdaUpdate()
                .eq(BusinessDesignationOrderNotificationPO::getDesignationOrderNo, designationOrderNo)
                .remove();
    }
}
