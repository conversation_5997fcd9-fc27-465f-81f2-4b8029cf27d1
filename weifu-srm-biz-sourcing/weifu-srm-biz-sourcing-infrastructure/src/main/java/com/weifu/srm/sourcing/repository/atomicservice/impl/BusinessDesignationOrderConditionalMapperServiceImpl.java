package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderConditionalMapperService;
import com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderConditionalMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderConditionalPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BusinessDesignationOrderConditionalMapperServiceImpl
        extends ServiceImpl<BusinessDesignationOrderConditionalMapper, BusinessDesignationOrderConditionalPO>
        implements BusinessDesignationOrderConditionalMapperService {

    @Override
    public void deleteBy(String designationOrderNo) {
        this.lambdaUpdate()
                .eq(BusinessDesignationOrderConditionalPO::getDesignationOrderNo, designationOrderNo)
                .remove();
    }

    @Override
    public List<BusinessDesignationOrderConditionalPO> queryBy(String designationOrderNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderConditionalPO::getDesignationOrderNo, designationOrderNo)
                .list();
    }
}
