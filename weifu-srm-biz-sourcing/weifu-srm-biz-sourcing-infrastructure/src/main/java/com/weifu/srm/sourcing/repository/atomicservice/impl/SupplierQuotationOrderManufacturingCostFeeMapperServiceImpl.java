package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderManufacturingCostFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderManufacturingCostFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderManufacturingCostFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderManufacturingCostFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderManufacturingCostFeeMapper, SupplierQuotationOrderManufacturingCostFeePO>
        implements SupplierQuotationOrderManufacturingCostFeeMapperService {

    @Override
    public List<SupplierQuotationOrderManufacturingCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderManufacturingCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderManufacturingCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderManufacturingCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderManufacturingCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
