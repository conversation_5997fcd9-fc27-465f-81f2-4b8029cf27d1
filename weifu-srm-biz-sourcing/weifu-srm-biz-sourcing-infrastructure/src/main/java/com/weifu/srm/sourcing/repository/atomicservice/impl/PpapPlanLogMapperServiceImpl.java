package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.PpapPlanLogMapperService;
import com.weifu.srm.sourcing.repository.mapper.PpapPlanLogMapper;
import com.weifu.srm.sourcing.repository.po.PpapPlanLogPO;
import com.weifu.srm.sourcing.request.ppap.PpapPlanLogPageReqDTO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * PPAP计划修订记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapPlanLogMapperServiceImpl extends ServiceImpl<PpapPlanLogMapper, PpapPlanLogPO> implements PpapPlanLogMapperService {


    @Override
    public IPage<PpapPlanLogPO> listByPlanNo(Page<PpapPlanLogPO> page, PpapPlanLogPageReqDTO reqDTO) {
        LambdaQueryWrapper<PpapPlanLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PpapPlanLogPO::getPlanNo,reqDTO.getPlanNo());
        return this.page(page,queryWrapper);
    }
}
