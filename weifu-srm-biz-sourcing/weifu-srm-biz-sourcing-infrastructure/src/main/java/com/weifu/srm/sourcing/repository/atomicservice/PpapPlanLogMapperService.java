package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.PpapPlanLogPO;
import com.weifu.srm.sourcing.request.ppap.PpapPlanLogPageReqDTO;

/**
 * <p>
 * ppap计划修订记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface PpapPlanLogMapperService extends IService<PpapPlanLogPO> {

    IPage<PpapPlanLogPO> listByPlanNo(Page<PpapPlanLogPO> page, PpapPlanLogPageReqDTO reqDTO);
}
