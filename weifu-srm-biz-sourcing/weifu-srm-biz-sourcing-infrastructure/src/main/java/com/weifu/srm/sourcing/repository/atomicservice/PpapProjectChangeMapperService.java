package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.PpapProjectChangePO;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangePageReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO;


/**
 * <p>
 * 工程变更清单 Mapper服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface PpapProjectChangeMapperService extends IService<PpapProjectChangePO> {
    IPage<PpapProjectChangeRespDTO> listPageByQuery(Page<PpapProjectChangeRespDTO> page, PpapProjectChangePageReqDTO reqDTO);

    PpapProjectChangePO getByPlanNo(String planNo);

    PpapProjectChangePO getByChangeNo(String changeNo);

    PpapProjectChangePO getLastByReleaseNo(String releaseNo);
}
