package com.weifu.srm.sourcing.repository.enums;

import lombok.Getter;

@Getter
public enum SupplierQuotationOrderStatusEnum {

    INQUIRING("INQUIRING", "询价中"),

    INQUIRY_COMPLETED("INQUIRY_COMPLETED", "询价截止");

    private final String code;
    private final String desc;

    SupplierQuotationOrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
