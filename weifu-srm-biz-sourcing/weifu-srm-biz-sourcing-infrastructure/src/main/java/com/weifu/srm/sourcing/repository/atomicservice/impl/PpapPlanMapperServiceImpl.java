package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.PpapPlanMapperService;
import com.weifu.srm.sourcing.repository.mapper.PpapPlanMapper;
import com.weifu.srm.sourcing.repository.po.PpapPlanPO;
import com.weifu.srm.sourcing.request.ppap.PpapPlanPageReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * PPAP计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapPlanMapperServiceImpl extends ServiceImpl<PpapPlanMapper, PpapPlanPO> implements PpapPlanMapperService {
    @Override
    public IPage<PpapPlanPO> listPageByQuery(Page<PpapPlanPO> page, PpapPlanPageReqDTO reqDTO) {
        LambdaQueryWrapper<PpapPlanPO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        return this.page(page, queryWrapper);
    }

    @Override
    public PpapPlanPO getByPlanNo(String planNo) {
        return this.getOne(Wrappers.<PpapPlanPO>lambdaQuery().eq(PpapPlanPO::getPlanNo,planNo),false);
    }

    @Override
    public List<PpapPlanPO> listByPlanNo(List<String> planNoList) {
        return this.list(Wrappers.<PpapPlanPO>lambdaQuery().in(PpapPlanPO::getPlanNo,planNoList));
    }

    private LambdaQueryWrapper<PpapPlanPO> buildLambdaQueryWrapper(PpapPlanPageReqDTO reqDTO) {
        LambdaQueryWrapper<PpapPlanPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getReleaseNo()), PpapPlanPO::getReleaseNo, reqDTO.getReleaseNo());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getMaterialNo()), PpapPlanPO::getMaterialNo, reqDTO.getMaterialNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getSupplierCode()), PpapPlanPO::getSupplierCode, reqDTO.getSupplierCode());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStatus()), PpapPlanPO::getStatus, reqDTO.getStatus());
        if (CollectionUtils.isNotEmpty(reqDTO.getCategoryCodePermissionList()) || CollectionUtils.isNotEmpty(reqDTO.getDivisionIdPermissionList())) {
            queryWrapper.and(wrapper -> wrapper.in(PpapPlanPO::getCategoryCode, reqDTO.getCategoryCodePermissionList()).or()
                    .in(PpapPlanPO::getDivisionId, reqDTO.getDivisionIdPermissionList()).or().eq(PpapPlanPO::getCreateBy, reqDTO.getOperateBy()));
        }
        queryWrapper.orderByDesc(PpapPlanPO::getId);
        return queryWrapper;
    }
}
