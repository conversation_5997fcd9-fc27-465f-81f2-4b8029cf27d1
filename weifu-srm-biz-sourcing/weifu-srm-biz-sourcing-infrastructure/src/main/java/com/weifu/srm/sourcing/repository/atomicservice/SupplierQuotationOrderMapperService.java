package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.QuotationOrderHistoryQueryReqDTO;
import com.weifu.srm.sourcing.request.QuotationOrderQueryReqDTO;
import com.weifu.srm.sourcing.request.SampleOrderRefQueryReqDTO;
import com.weifu.srm.sourcing.response.QuotationOrderHistoryRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderRespDTO;
import com.weifu.srm.sourcing.response.SampleOrderRefRespDTO;

import java.util.List;

public interface SupplierQuotationOrderMapperService extends IService<SupplierQuotationOrderPO> {

    IPage<QuotationOrderRespDTO> queryPage(Page<QuotationOrderRespDTO> page, QuotationOrderQueryReqDTO paramDTO);

    IPage<QuotationOrderHistoryRespDTO> queryHistoryPage(Page<QuotationOrderHistoryRespDTO> page, QuotationOrderHistoryQueryReqDTO paramDTO);

    List<SampleOrderRefRespDTO> queryForSampleOrderRef(SampleOrderRefQueryReqDTO paramDTO);

    List<SupplierQuotationOrderPO> queryBy(String inquiryNo, Integer quotationRound);

    List<SupplierQuotationOrderPO> queryLatestRoundBy(List<String> inquiryNos);

    SupplierQuotationOrderPO queryBy(String inquiryNo, String sapSupplierCode, Integer quotationRound);

    List<SupplierQuotationOrderPO> queryBy(List<Long> ids);

    List<SupplierQuotationOrderPO> queryBy(String inquiryNo);

    Integer queryCountBy(String sapSupplierCode, Integer isProxyQuotation,
                         Integer isShow, List<String> quotationStatusAry);
}
