package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderOtherFeePO;

public interface SupplierQuotationOrderOtherFeeMapperService extends IService<SupplierQuotationOrderOtherFeePO> {

    SupplierQuotationOrderOtherFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
