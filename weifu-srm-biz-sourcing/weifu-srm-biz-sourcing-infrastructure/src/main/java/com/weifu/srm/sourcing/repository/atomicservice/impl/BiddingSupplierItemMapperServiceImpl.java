package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingSupplierItemMapperService;
import com.weifu.srm.sourcing.repository.mapper.BiddingSupplierItemMapper;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierItemPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BiddingSupplierItemMapperServiceImpl
        extends ServiceImpl<BiddingSupplierItemMapper, BiddingSupplierItemPO> implements BiddingSupplierItemMapperService {

    @Override
    public List<BiddingSupplierItemPO> queryBy(String biddingNo) {
        return this.lambdaQuery()
                .eq(BiddingSupplierItemPO::getBiddingNo, biddingNo)
                .list();
    }

    @Override
    public List<BiddingSupplierItemPO> queryBy(String biddingNo, Integer round) {
        return this.lambdaQuery()
                .eq(BiddingSupplierItemPO::getBiddingNo, biddingNo)
                .eq(BiddingSupplierItemPO::getRound, round)
                .list();
    }

    @Override
    public List<BiddingSupplierItemPO> queryBy(String biddingNo, String sapSupplierCode) {
        return this.lambdaQuery()
                .eq(BiddingSupplierItemPO::getBiddingNo, biddingNo)
                .eq(BiddingSupplierItemPO::getSapSupplierCode, sapSupplierCode)
                .list();
    }

    @Override
    public void deleteBy(String biddingNo) {
        this.lambdaUpdate()
                .eq(BiddingSupplierItemPO::getBiddingNo, biddingNo)
                .remove();
    }
}
