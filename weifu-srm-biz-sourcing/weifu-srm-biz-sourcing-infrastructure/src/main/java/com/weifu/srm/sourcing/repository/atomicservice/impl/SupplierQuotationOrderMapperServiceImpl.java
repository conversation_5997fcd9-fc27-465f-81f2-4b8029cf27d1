package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO;
import com.weifu.srm.sourcing.request.QuotationOrderHistoryQueryReqDTO;
import com.weifu.srm.sourcing.request.QuotationOrderQueryReqDTO;
import com.weifu.srm.sourcing.request.SampleOrderRefQueryReqDTO;
import com.weifu.srm.sourcing.response.QuotationOrderHistoryRespDTO;
import com.weifu.srm.sourcing.response.QuotationOrderRespDTO;
import com.weifu.srm.sourcing.response.SampleOrderRefRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderMapper, SupplierQuotationOrderPO> implements SupplierQuotationOrderMapperService {
    @Override
    public IPage<QuotationOrderRespDTO> queryPage(Page<QuotationOrderRespDTO> page, QuotationOrderQueryReqDTO paramDTO) {
        return this.baseMapper.queryPage(page, paramDTO);
    }

    @Override
    public IPage<QuotationOrderHistoryRespDTO> queryHistoryPage(Page<QuotationOrderHistoryRespDTO> page, QuotationOrderHistoryQueryReqDTO paramDTO) {
        return this.baseMapper.queryHistoryPage(page, paramDTO);
    }

    @Override
    public List<SampleOrderRefRespDTO> queryForSampleOrderRef(SampleOrderRefQueryReqDTO paramDTO) {
        return this.baseMapper.queryForSampleOrderRef(paramDTO);
    }

    @Override
    public List<SupplierQuotationOrderPO> queryBy(String inquiryNo, Integer quotationRound) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderPO::getInquiryNo, inquiryNo)
                .eq(SupplierQuotationOrderPO::getQuotationRound, quotationRound)
                .list();
    }

    @Override
    public List<SupplierQuotationOrderPO> queryLatestRoundBy(List<String> inquiryNos) {
        return this.baseMapper.queryLatestRoundQuotationOrderBy(inquiryNos);
    }

    @Override
    public SupplierQuotationOrderPO queryBy(String inquiryNo, String sapSupplierCode, Integer quotationRound) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderPO::getInquiryNo, inquiryNo)
                .eq(SupplierQuotationOrderPO::getSapSupplierCode, sapSupplierCode)
                .eq(SupplierQuotationOrderPO::getQuotationRound, quotationRound)
                .one();
    }

    @Override
    public List<SupplierQuotationOrderPO> queryBy(List<Long> ids) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderPO::getId, ids)
                .list();
    }

    @Override
    public List<SupplierQuotationOrderPO> queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderPO::getInquiryNo, inquiryNo)
                .list();
    }

    @Override
    public Integer queryCountBy(String sapSupplierCode, Integer isProxyQuotation,
                                Integer isShow, List<String> quotationStatusAry) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderPO::getSapSupplierCode, sapSupplierCode)
                .eq(SupplierQuotationOrderPO::getIsProxyQuotation, isProxyQuotation)
                .eq(SupplierQuotationOrderPO::getIsShow, isShow)
                .in(SupplierQuotationOrderPO::getQuotationStatus, quotationStatusAry)
                .count();
    }
}
