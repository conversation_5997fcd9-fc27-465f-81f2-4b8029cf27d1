package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderNotificationQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessDesignationOrderNotificationMapper extends BaseMapper<BusinessDesignationOrderNotificationPO> {

    IPage<BusinessDesignationOrderNotificationPO> queryPage(Page<BusinessDesignationOrderNotificationPO> page,
                                                            @Param("model") BusinessDesignationOrderNotificationQueryReqDTO paramDTO,
                                                            @Param("dataPermissionKeys") List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);
}
