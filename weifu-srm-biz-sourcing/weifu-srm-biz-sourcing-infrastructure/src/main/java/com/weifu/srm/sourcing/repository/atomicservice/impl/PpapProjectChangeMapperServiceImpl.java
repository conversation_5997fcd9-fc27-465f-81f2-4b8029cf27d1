package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.PpapProjectChangeMapperService;
import com.weifu.srm.sourcing.repository.mapper.PpapProjectChangeMapper;
import com.weifu.srm.sourcing.repository.po.PpapProjectChangePO;
import com.weifu.srm.sourcing.request.ppap.PpapProjectChangePageReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 工程变更清单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapProjectChangeMapperServiceImpl extends ServiceImpl<PpapProjectChangeMapper, PpapProjectChangePO> implements PpapProjectChangeMapperService {

    @Override
    public IPage<PpapProjectChangeRespDTO> listPageByQuery(Page<PpapProjectChangeRespDTO> page, PpapProjectChangePageReqDTO reqDTO) {
        return getBaseMapper().listPageByQuery(page, reqDTO);
    }

    @Override
    public PpapProjectChangePO getByPlanNo(String planNo) {
        if (StringUtils.isBlank(planNo)) {
            return null;
        }
        return this.getOne(Wrappers.<PpapProjectChangePO>lambdaQuery().eq(PpapProjectChangePO::getPlanNo, planNo), false);
    }

    @Override
    public PpapProjectChangePO getByChangeNo(String changeNo) {
        if (StringUtils.isBlank(changeNo)) {
            return null;
        }
        return this.getOne(Wrappers.<PpapProjectChangePO>lambdaQuery().eq(PpapProjectChangePO::getChangeNo, changeNo), false);
    }

    @Override
    public PpapProjectChangePO getLastByReleaseNo(String releaseNo) {
        if (StringUtils.isBlank(releaseNo)) {
            return null;
        }
        List<PpapProjectChangePO> poList = this.list(Wrappers.<PpapProjectChangePO>lambdaQuery().eq(PpapProjectChangePO::getReleaseNo, releaseNo).orderByDesc(PpapProjectChangePO::getId));
        return CollectionUtils.isEmpty(poList) ? null : poList.get(0);
    }
}
