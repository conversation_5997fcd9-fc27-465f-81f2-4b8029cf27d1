package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.InquiryOrderMaterialPO;

import java.io.Serializable;
import java.util.List;

public interface InquiryOrderMaterialMapperService extends IService<InquiryOrderMaterialPO> {

    List<InquiryOrderMaterialPO> queryBy(String inquiryNo);
    
    List<InquiryOrderMaterialPO> queryBy(String inquiryNo, String materialCode);

    List<InquiryOrderMaterialPO> queryBy(String inquiryNo, List<String> materialCodes);

    List<InquiryOrderMaterialPO> queryAll(String inquiryNo);

    int restoreById(Serializable id);
}
