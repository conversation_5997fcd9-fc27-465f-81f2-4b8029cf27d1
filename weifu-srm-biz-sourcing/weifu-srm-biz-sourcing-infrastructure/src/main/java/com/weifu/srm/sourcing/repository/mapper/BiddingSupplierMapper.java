package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;

@Mapper
public interface BiddingSupplierMapper extends BaseMapper<BiddingSupplierPO> {

    @Select("select * from bidding_supplier where bidding_no = #{biddingNo}")
    List<BiddingSupplierPO> selectAll(@Param("biddingNo") String biddingNo);

    @Update("update bidding_supplier set is_delete = 0 where id = #{id}")
    int restoreById(@Param("id") Serializable id);

    int selectCountBySupplier(@Param("sapSupplierCode") String sapSupplierCode,
                              @Param("status") String statuses,
                              @Param("biddingIntention") Integer biddingIntention,
                              @Param("feedbackIntention") Integer feedbackIntention);
}
