package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderNotificationItemMapperService;
import com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderNotificationItemMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationItemPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BusinessDesignationOrderNotificationItemMapperServiceImpl
        extends ServiceImpl<BusinessDesignationOrderNotificationItemMapper, BusinessDesignationOrderNotificationItemPO>
        implements BusinessDesignationOrderNotificationItemMapperService {

    @Override
    public List<BusinessDesignationOrderNotificationItemPO> queryBy(String designationOrderNotificationNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderNotificationItemPO::getDesignationOrderNotificationNo, designationOrderNotificationNo)
                .list();
    }

    @Override
    public List<BusinessDesignationOrderNotificationItemPO> queryByDesignationOrderNo(String designationOrderNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderNotificationItemPO::getDesignationOrderNo, designationOrderNo)
                .list();
    }

    @Override
    public void deleteBy(String designationOrderNo, String sapSupplierCode) {
        this.lambdaUpdate()
                .eq(BusinessDesignationOrderNotificationItemPO::getDesignationOrderNo, designationOrderNo)
                .eq(BusinessDesignationOrderNotificationItemPO::getSapSupplierCode, sapSupplierCode)
                .remove();
    }
}
