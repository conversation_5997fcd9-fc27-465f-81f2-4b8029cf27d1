package com.weifu.srm.sourcing.repository.atomicservice.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.mybatis.uilts.BaseEntityUtil;
import com.weifu.srm.sourcing.repository.atomicservice.PpapReleaseMapperService;
import com.weifu.srm.sourcing.repository.bo.PpapEngineeringChangeMinTimeBO;
import com.weifu.srm.sourcing.repository.constants.SourcingCommonConstants;
import com.weifu.srm.sourcing.repository.mapper.PpapReleaseMapper;
import com.weifu.srm.sourcing.repository.po.PpapReleasePO;
import com.weifu.srm.sourcing.request.OperateUserReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseExecuteSituationReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleasePageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseReportPageReqDTO;
import com.weifu.srm.sourcing.request.ppap.PpapReleaseWorkbenchReqDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseEngineerRowRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO;
import com.weifu.srm.sourcing.response.ppap.PpapReleaseWorkbenchRespDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 批产放行 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapReleaseMapperServiceImpl extends ServiceImpl<PpapReleaseMapper, PpapReleasePO> implements PpapReleaseMapperService {

    @Override
    public IPage<PpapReleasePO> listPageByQuery(Page<PpapReleasePO> page, PpapReleasePageReqDTO reqDTO) {
        LambdaQueryWrapper<PpapReleasePO> queryWrapper = buildLambdaQueryWrapper(reqDTO);
        return this.page(page, queryWrapper);
    }

    @Override
    public IPage<PpapReleaseReportRespDTO> listReportPageByQuery(Page<PpapReleaseReportRespDTO> page, PpapReleaseReportPageReqDTO reqDTO) {
        if (reqDTO.getPageSize() != SourcingCommonConstants.NO_PAGE_SIZE) {
            return this.getBaseMapper().listReportPageByQuery(page, reqDTO);
        } else {
            List<PpapReleaseReportRespDTO> poList = this.getBaseMapper().listReportByQuery(reqDTO);
            IPage<PpapReleaseReportRespDTO> resultPage = new Page<>(reqDTO.getPageNum(), reqDTO.getPageSize(), poList.size());
            resultPage.setRecords(poList);
            return resultPage;
        }
    }

    @Override
    public PpapReleasePO getByReleaseNo(String releaseNo) {
        return this.getOne(Wrappers.<PpapReleasePO>lambdaQuery().eq(PpapReleasePO::getReleaseNo, releaseNo), false);
    }

    @Override
    public PpapReleaseWorkbenchRespDTO workbench(PpapReleaseWorkbenchReqDTO reqDTO) {
        return this.getBaseMapper().workbench(reqDTO);
    }

    @Override
    public List<PpapReleaseEngineerRowRespDTO> executeSituation(PpapReleaseExecuteSituationReqDTO reqDTO) {
        return this.getBaseMapper().executeSituation(reqDTO);
    }

    @Override
    public void saveOrUpdateRelease(PpapReleasePO po) {
        if (po.getId() == null) {
            this.save(po);
        } else {
            this.lambdaUpdate()
                    .set(po.getPswCompleteTime() == null, PpapReleasePO::getPswCompleteTime, po.getPswCompleteTime())
                    .set(po.getMasterConfirmTime() == null, PpapReleasePO::getMasterConfirmTime, po.getMasterConfirmTime())
                    .set(po.getApprovedTime() == null, PpapReleasePO::getApprovedTime, po.getApprovedTime())
                    .set(PpapReleasePO::getReleaseStatus, po.getReleaseStatus())
                    .set(PpapReleasePO::getStage, po.getStage())
                    .set(PpapReleasePO::getSourceType, po.getSourceType())
                    .set(PpapReleasePO::getExecuteStatus, po.getExecuteStatus())
                    .set(PpapReleasePO::getIsProjectChange, po.getIsProjectChange())
                    .set(PpapReleasePO::getDrawingNo, po.getDrawingNo())
                    .set(PpapReleasePO::getDrawingVersionNo, po.getDrawingVersionNo())
                    .set(PpapReleasePO::getUpdateBy, po.getUpdateBy())
                    .set(PpapReleasePO::getUpdateName, po.getUpdateName())
                    .set(PpapReleasePO::getUpdateTime, po.getUpdateTime())
                    .eq(PpapReleasePO::getId, po.getId()).update();
        }

    }

    @Override
    public PpapReleasePO updatePlanNoByReleaseNo(String releaseNo, String planNo, OperateUserReqDTO reqDTO) {
        PpapReleasePO po = getByReleaseNo(releaseNo);
        if (po == null) {
            return null;
        }
        po.setPlanNo(planNo);
        BaseEntityUtil.setCommonForU(po, reqDTO.getOperateBy(), reqDTO.getOperateName(), DateUtil.date());
        this.updateById(po);
        return po;
    }

    @Override
    public PpapEngineeringChangeMinTimeBO selectOptMinTime(String requirementNo, String releaseNo) {
        return this.getBaseMapper().selectOptMinTime(requirementNo, releaseNo);
    }

    @Override
    public Integer countByInternal(Long operationBy, List<String> cpeCategoryCodeList, List<String> sqeCategoryCodeList) {
        return this.getBaseMapper().countByInternal(operationBy, cpeCategoryCodeList, sqeCategoryCodeList);
    }

    @Override
    public List<PpapReleasePO> listByPendingProcessing(Long operationBy, List<String> cpeCategoryCodeList, List<String> sqeCategoryCodeList) {
        return this.getBaseMapper().listByPendingProcessing(operationBy, cpeCategoryCodeList, sqeCategoryCodeList);
    }

    private LambdaQueryWrapper<PpapReleasePO> buildLambdaQueryWrapper(PpapReleasePageReqDTO reqDTO) {
        LambdaQueryWrapper<PpapReleasePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getReleaseNo()), PpapReleasePO::getReleaseNo, reqDTO.getReleaseNo());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getDesignationOrderNo()), PpapReleasePO::getDesignationOrderNo, reqDTO.getDesignationOrderNo());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getCategoryCode()), PpapReleasePO::getCategoryCode, reqDTO.getCategoryCode());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getMaterialNo()), PpapReleasePO::getMaterialNo, reqDTO.getMaterialNo());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getRequirement()), PpapReleasePO::getRequirement, reqDTO.getRequirement());
        queryWrapper.like(StringUtils.isNotBlank(reqDTO.getRequirementNo()), PpapReleasePO::getRequirementNo, reqDTO.getRequirementNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getSupplierCode()), PpapReleasePO::getSupplierCode, reqDTO.getSupplierCode());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getStage()), PpapReleasePO::getStage, reqDTO.getStage());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getReleaseStatus()), PpapReleasePO::getReleaseStatus, reqDTO.getReleaseStatus());
        if (CollectionUtils.isNotEmpty(reqDTO.getCategoryCodePermissionList()) || CollectionUtils.isNotEmpty(reqDTO.getDivisionIdPermissionList())) {
            queryWrapper.and(wrapper -> wrapper.in(PpapReleasePO::getCategoryCode, reqDTO.getCategoryCodePermissionList())
                    .or(orWrapper -> orWrapper.in(PpapReleasePO::getDivisionId, reqDTO.getDivisionIdPermissionList())));
        }

        queryWrapper.orderByDesc(PpapReleasePO::getId);
        return queryWrapper;
    }
}
