package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.InquiryOrderMapperService;
import com.weifu.srm.sourcing.repository.mapper.InquiryOrderMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.request.InquiryOrderQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class InquiryOrderMapperServiceImpl extends ServiceImpl<InquiryOrderMapper, InquiryOrderPO> implements InquiryOrderMapperService {

    @Override
    public IPage<InquiryOrderPO> queryPage(Page<InquiryOrderPO> page, InquiryOrderQueryReqDTO paramDTO,
                                           List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys) {
        return baseMapper.queryPage(page, paramDTO, dataPermissionKeys);
    }

    @Override
    public InquiryOrderPO queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .eq(InquiryOrderPO::getInquiryNo, inquiryNo)
                .one();
    }

    @Override
    public List<InquiryOrderPO> queryBy(List<String> inquiryNos) {
        return this.lambdaQuery()
                .in(InquiryOrderPO::getInquiryNo, inquiryNos)
                .list();
    }
}
