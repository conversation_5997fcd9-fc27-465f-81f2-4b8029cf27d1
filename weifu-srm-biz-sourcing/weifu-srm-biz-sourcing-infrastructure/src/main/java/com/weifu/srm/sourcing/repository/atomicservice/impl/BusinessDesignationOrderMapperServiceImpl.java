package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BusinessDesignationOrderMapperService;
import com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderMapper;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderQueryReqDTO;
import com.weifu.srm.sourcing.response.BusinessDesignationOrderPriceRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessDesignationOrderMapperServiceImpl
        extends ServiceImpl<BusinessDesignationOrderMapper, BusinessDesignationOrderPO>
        implements BusinessDesignationOrderMapperService {

    @Override
    public BusinessDesignationOrderPO queryBy(String designationOrderNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderPO::getDesignationOrderNo, designationOrderNo)
                .one();
    }

    @Override
    public List<BusinessDesignationOrderPO> queryBy(List<String> designationOrderNos) {
        return this.lambdaQuery()
                .in(BusinessDesignationOrderPO::getDesignationOrderNo, designationOrderNos)
                .list();
    }

    @Override
    public List<BusinessDesignationOrderPO> queryByInquiryNo(String inquiryNo) {
        return this.lambdaQuery()
                .eq(BusinessDesignationOrderPO::getInquiryNo, inquiryNo)
                .list();
    }
    
    @Override
    public IPage<BusinessDesignationOrderPO> queryPage(Page<BusinessDesignationOrderPO> page,
                                                       BusinessDesignationOrderQueryReqDTO paramDTO,
                                                       List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys) {
        return baseMapper.queryPage(page, paramDTO, dataPermissionKeys);
    }

    @Override
    public List<BusinessDesignationOrderPriceRespDTO> queryPrice(String sapSupplierNo, List<String> materialCodes) {
        return baseMapper.queryPrice(sapSupplierNo, materialCodes);
    }
}
