package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMouldAmortizationFeePO;

import java.util.List;

public interface SupplierQuotationOrderMouldAmortizationFeeMapperService extends IService<SupplierQuotationOrderMouldAmortizationFeePO> {

    List<SupplierQuotationOrderMouldAmortizationFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
