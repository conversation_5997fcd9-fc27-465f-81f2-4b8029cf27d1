package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderLadderFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderLadderFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderLadderFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderLadderFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderLadderFeeMapper, SupplierQuotationOrderLadderFeePO>
        implements SupplierQuotationOrderLadderFeeMapperService {

    @Override
    public List<SupplierQuotationOrderLadderFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderLadderFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderLadderFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderLadderFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderLadderFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }

    @Override
    public List<SupplierQuotationOrderLadderFeePO> queryBy(List<Long> quotationOrderMaterialIds) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderLadderFeePO::getQuotationOrderMaterialId, quotationOrderMaterialIds)
                .list();
    }
}
