package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import com.weifu.srm.sourcing.repository.atomicservice.AttachmentRecordMapperService;
import com.weifu.srm.sourcing.repository.mapper.AttachmentRecordMapper;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class AttachmentRecordMapperServiceImpl extends ServiceImpl<AttachmentRecordMapper, AttachmentRecordPO> implements AttachmentRecordMapperService {

    @Override
    @Transactional
    public void deleteBy(String businessNo, String businessSubNo, String businessType) {
        this.lambdaUpdate()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .eq(StringUtils.isNotBlank(businessSubNo), AttachmentRecordPO::getBusinessSubNo, businessSubNo)
                .eq(StringUtils.isNotBlank(businessType), AttachmentRecordPO::getBusinessType, businessType)
                .remove();
    }

    @Override
    public List<AttachmentRecordPO> queryBy(String businessNo) {
        return this.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .list();
    }

    @Override
    public List<AttachmentRecordPO> queryBy(String businessNo, String businessType) {
        return this.lambdaQuery()
                .eq(AttachmentRecordPO::getBusinessNo, businessNo)
                .eq(AttachmentRecordPO::getBusinessType, businessType)
                .list();
    }

    @Override
    public List<AttachmentRecordPO> queryBy(List<String> businessNos, String businessType) {
        return this.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessNo, businessNos)
                .eq(AttachmentRecordPO::getBusinessType, businessType)
                .list();
    }

    @Override
    public List<AttachmentRecordPO> queryBy(List<String> businessNos, List<String> businessTypes) {
        return this.lambdaQuery()
                .in(AttachmentRecordPO::getBusinessNo, businessNos)
                .in(AttachmentRecordPO::getBusinessType, businessTypes)
                .list();
    }

    @Override
    public List<AttachmentRecordPO> queryAll(String businessNo) {
        return this.baseMapper.selectAll(businessNo);
    }

    @Override
    public AttachmentRecordPO queryByFileName(String fileName) {
        return this.lambdaQuery()
                .eq(AttachmentRecordPO::getFileName, fileName)
                .one();
    }
}
