package com.weifu.srm.sourcing.repository.enums;

import lombok.Getter;

@Getter
public enum SupplierQuotationStatusEnum {

    REJECTED("REJECTED", "拒绝报价"),
    ABANDON("ABANDON", "放弃报价"),
    PENDING("PENDING", "待处理"),
    QUOTING("QUOTING", "报价中"),
    QUOTED("QUOTED", "报价完成");

    private final String code;
    private final String desc;

    SupplierQuotationStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
