package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.AttachmentRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AttachmentRecordMapper extends BaseMapper<AttachmentRecordPO> {

    @Select("select * from attachment_record where business_no = #{businessNo}")
    List<AttachmentRecordPO> selectAll(@Param("businessNo") String businessNo);
}
