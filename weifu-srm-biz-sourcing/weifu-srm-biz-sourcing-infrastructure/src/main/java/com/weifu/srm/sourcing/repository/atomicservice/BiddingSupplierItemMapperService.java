package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierItemPO;

import java.util.List;

public interface BiddingSupplierItemMapperService extends IService<BiddingSupplierItemPO> {

    List<BiddingSupplierItemPO> queryBy(String biddingNo);

    List<BiddingSupplierItemPO> queryBy(String biddingNo, Integer round);

    List<BiddingSupplierItemPO> queryBy(String biddingNo, String sapSupplierCode);

    void deleteBy(String biddingNo);
}
