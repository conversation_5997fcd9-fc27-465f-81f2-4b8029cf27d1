package com.weifu.srm.sourcing.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderPO;
import com.weifu.srm.sourcing.request.InquiryOrderQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InquiryOrderMapper extends BaseMapper<InquiryOrderPO> {

    IPage<InquiryOrderPO> queryPage(Page<InquiryOrderPO> page, @Param("model") InquiryOrderQueryReqDTO paramDTO,
                                    @Param("dataPermissionKeys") List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);
}
