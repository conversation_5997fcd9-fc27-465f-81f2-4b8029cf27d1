package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMaterialPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SupplierQuotationOrderMaterialMapper extends BaseMapper<SupplierQuotationOrderMaterialPO> {

    SupplierQuotationOrderMaterialPO queryPreviousMassProdPriceBy(@Param("excludeInquiryNo") String excludeInquiryNo,
                                                                  @Param("inquiryStatus") String inquiryStatus,
                                                                  @Param("sapSupplierCode") String sapSupplierCode,
                                                                  @Param("materialCode") String materialCode,
                                                                  @Param("quotationTemplates") List<String> quotationTemplates);
}
