package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.PpapPlanTaskMapperService;
import com.weifu.srm.sourcing.repository.mapper.PpapPlanTaskMapper;
import com.weifu.srm.sourcing.repository.po.PpapPlanTaskPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * PPAP计划-任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class PpapPlanTaskMapperServiceImpl extends ServiceImpl<PpapPlanTaskMapper, PpapPlanTaskPO> implements PpapPlanTaskMapperService {

    @Override
    public List<PpapPlanTaskPO> listByPlanNo(String planNo) {
        return this.lambdaQuery().eq(PpapPlanTaskPO::getPlanNo, planNo).list();
    }

    @Override
    public List<PpapPlanTaskPO> listByPlanNoAndLiabilityType(String planNo, String liabilityType) {
        return this.lambdaQuery().eq(PpapPlanTaskPO::getPlanNo, planNo)
                .eq(StringUtils.isNotBlank(liabilityType), PpapPlanTaskPO::getLiabilityType, liabilityType).list();
    }

    @Override
    public int countByPlanNoAndComplete(String planNo, String liabilityType, Integer isComplete) {
        return this.lambdaQuery().eq(PpapPlanTaskPO::getPlanNo, planNo)
                .eq(StringUtils.isNotBlank(liabilityType), PpapPlanTaskPO::getLiabilityType, liabilityType)
                .eq(PpapPlanTaskPO::getIsComplete, isComplete).count();
    }
}
