package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPackageFeePO;

import java.util.List;

public interface SupplierQuotationOrderPackageFeeMapperService extends IService<SupplierQuotationOrderPackageFeePO> {

    List<SupplierQuotationOrderPackageFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
