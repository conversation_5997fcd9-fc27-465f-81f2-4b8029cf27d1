package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.BiddingSupplierMapperService;
import com.weifu.srm.sourcing.repository.mapper.BiddingSupplierMapper;
import com.weifu.srm.sourcing.repository.po.BiddingSupplierPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Service
public class BiddingSupplierMapperServiceImpl extends ServiceImpl<BiddingSupplierMapper, BiddingSupplierPO> implements BiddingSupplierMapperService {

    @Override
    public List<BiddingSupplierPO> queryBy(String biddingNo) {
        return this.lambdaQuery()
                .eq(BiddingSupplierPO::getBiddingNo, biddingNo)
                .list();
    }

    @Override
    public List<BiddingSupplierPO> queryBy(List<String> biddingNos) {
        return this.lambdaQuery()
                .in(BiddingSupplierPO::getBiddingNo, biddingNos)
                .list();
    }

    @Override
    public List<BiddingSupplierPO> queryAll(String biddingNo) {
        return baseMapper.selectAll(biddingNo);
    }

    @Override
    public int restoreById(Serializable id) {
        return baseMapper.restoreById(id);
    }

    @Override
    public int queryCountBySupplier(String sapSupplierCode, String status, Integer biddingIntention, Integer feedbackIntention) {
        return baseMapper.selectCountBySupplier(sapSupplierCode, status, biddingIntention, feedbackIntention);
    }
}
