package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderMaterialMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderMaterialMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMaterialPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderMaterialMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderMaterialMapper, SupplierQuotationOrderMaterialPO> implements SupplierQuotationOrderMaterialMapperService {

    @Override
    public SupplierQuotationOrderMaterialPO queryBy(Long quotationOrderId, String requirementPartsNo, String quotationTemplate) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderMaterialPO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderMaterialPO::getRequirementPartsNo, requirementPartsNo)
                .eq(SupplierQuotationOrderMaterialPO::getQuotationTemplate, quotationTemplate)
                .one();
    }

    @Override
    public List<SupplierQuotationOrderMaterialPO> queryBy(Long quotationOrderId) {
        return this.lambdaQuery()
                .eq(SupplierQuotationOrderMaterialPO::getQuotationOrderId, quotationOrderId)
                .list();
    }

    @Override
    public List<SupplierQuotationOrderMaterialPO> queryBy(String inquiryNo, Integer quotationRound) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderMaterialPO::getInquiryNo, inquiryNo)
                .in(SupplierQuotationOrderMaterialPO::getQuotationRound, quotationRound)
                .list();
    }

    @Override
    public List<SupplierQuotationOrderMaterialPO> queryBy(String inquiryNo) {
        return this.lambdaQuery()
                .in(SupplierQuotationOrderMaterialPO::getInquiryNo, inquiryNo)
                .list();
    }

    @Override
    public SupplierQuotationOrderMaterialPO queryPreviousMassProdPriceBy(String excludeInquiryNo, String inquiryStatus,
                                                                         String sapSupplierCode, String materialCode,
                                                                         List<String> quotationTemplates) {
        return this.baseMapper.queryPreviousMassProdPriceBy(excludeInquiryNo, inquiryStatus, sapSupplierCode, materialCode, quotationTemplates);
    }
}
