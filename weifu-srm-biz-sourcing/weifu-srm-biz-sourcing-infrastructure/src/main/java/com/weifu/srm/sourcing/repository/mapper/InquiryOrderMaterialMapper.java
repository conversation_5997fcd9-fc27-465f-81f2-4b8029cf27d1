package com.weifu.srm.sourcing.repository.mapper;

import com.weifu.srm.mybatis.base.BaseMapper;
import com.weifu.srm.sourcing.repository.po.InquiryOrderMaterialPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.util.List;

@Mapper
public interface InquiryOrderMaterialMapper extends BaseMapper<InquiryOrderMaterialPO> {

    @Select("select * from inquiry_order_material where inquiry_no = #{inquiryNo}")
    List<InquiryOrderMaterialPO> selectAll(@Param("inquiryNo") String inquiryNo);

    @Update("update inquiry_order_material set is_delete = 0 where id = #{id}")
    int restoreById(@Param("id") Serializable id);
}
