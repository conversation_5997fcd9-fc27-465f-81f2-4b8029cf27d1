package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderQueryReqDTO;
import com.weifu.srm.sourcing.response.BusinessDesignationOrderPriceRespDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

public interface BusinessDesignationOrderMapperService extends IService<BusinessDesignationOrderPO> {

    BusinessDesignationOrderPO queryBy(String designationOrderNo);

    List<BusinessDesignationOrderPO> queryBy(List<String> designationOrderNos);

    List<BusinessDesignationOrderPO> queryByInquiryNo(String inquiryNo);

    IPage<BusinessDesignationOrderPO> queryPage(Page<BusinessDesignationOrderPO> page,
                                                BusinessDesignationOrderQueryReqDTO paramDTO,
                                                List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    /**
     * 根据供应商编码 和物料编码 查询已发送的定点价格数据
     */
    List<BusinessDesignationOrderPriceRespDTO> queryPrice(String sapSupplierNo, List<String> materialCodes);
}
