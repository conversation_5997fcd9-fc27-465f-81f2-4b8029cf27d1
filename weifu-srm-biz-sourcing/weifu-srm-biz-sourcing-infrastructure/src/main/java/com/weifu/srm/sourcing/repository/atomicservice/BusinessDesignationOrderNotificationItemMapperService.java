package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationItemPO;

import java.util.List;

public interface BusinessDesignationOrderNotificationItemMapperService extends IService<BusinessDesignationOrderNotificationItemPO> {

    List<BusinessDesignationOrderNotificationItemPO> queryBy(String designationOrderNotificationNo);

    List<BusinessDesignationOrderNotificationItemPO> queryByDesignationOrderNo(String designationOrderNo);

    void deleteBy(String designationOrderNo, String sapSupplierCode);
}
