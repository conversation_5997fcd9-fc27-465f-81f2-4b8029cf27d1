package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderOtherFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderOtherFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderOtherFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SupplierQuotationOrderOtherFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderOtherFeeMapper, SupplierQuotationOrderOtherFeePO>
        implements SupplierQuotationOrderOtherFeeMapperService {

    @Override
    public SupplierQuotationOrderOtherFeePO queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderOtherFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderOtherFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .one();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderOtherFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderOtherFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
