package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO;
import com.weifu.srm.sourcing.request.BusinessDesignationOrderNotificationQueryReqDTO;
import com.weifu.srm.user.response.DataPermissionRespDTO;

import java.util.List;

public interface BusinessDesignationOrderNotificationMapperService extends IService<BusinessDesignationOrderNotificationPO> {

    IPage<BusinessDesignationOrderNotificationPO> queryPage(Page<BusinessDesignationOrderNotificationPO> page,
                                                            BusinessDesignationOrderNotificationQueryReqDTO paramDTO,
                                                            List<DataPermissionRespDTO.DataPermissionKeyRespDTO> dataPermissionKeys);

    BusinessDesignationOrderNotificationPO queryBy(String designationOrderNotificationNo);

    List<BusinessDesignationOrderNotificationPO> queryByDesignationOrderNo(String designationOrderNo);

    void deleteBy(String designationOrderNo);
}
