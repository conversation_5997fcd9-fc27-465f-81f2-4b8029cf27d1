package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRawMaterialCostFeePO;

import java.util.List;

public interface SupplierQuotationOrderRawMaterialCostFeeMapperService extends IService<SupplierQuotationOrderRawMaterialCostFeePO> {

    List<SupplierQuotationOrderRawMaterialCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
