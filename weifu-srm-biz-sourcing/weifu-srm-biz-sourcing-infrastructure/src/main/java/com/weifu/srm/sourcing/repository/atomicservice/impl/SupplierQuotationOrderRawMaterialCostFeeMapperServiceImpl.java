package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderRawMaterialCostFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderRawMaterialCostFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderRawMaterialCostFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderRawMaterialCostFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderRawMaterialCostFeeMapper, SupplierQuotationOrderRawMaterialCostFeePO>
        implements SupplierQuotationOrderRawMaterialCostFeeMapperService {

    @Override
    public List<SupplierQuotationOrderRawMaterialCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderRawMaterialCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderRawMaterialCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderRawMaterialCostFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderRawMaterialCostFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
