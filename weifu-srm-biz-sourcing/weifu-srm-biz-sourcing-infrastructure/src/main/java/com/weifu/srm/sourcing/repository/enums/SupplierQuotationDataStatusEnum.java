package com.weifu.srm.sourcing.repository.enums;

import lombok.Getter;

@Getter
public enum SupplierQuotationDataStatusEnum {

    INITIALIZE("INITIALIZE", "初始化"),

    DRAFT("DRAFT", "草稿状态"),

    SUBMITTED("SUBMITTED", "已提交");

    private final String code;
    private final String desc;

    SupplierQuotationDataStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
