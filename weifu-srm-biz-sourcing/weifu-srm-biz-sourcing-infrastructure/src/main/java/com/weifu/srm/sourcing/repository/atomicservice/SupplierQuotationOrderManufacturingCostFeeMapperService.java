package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderManufacturingCostFeePO;

import java.util.List;

public interface SupplierQuotationOrderManufacturingCostFeeMapperService extends IService<SupplierQuotationOrderManufacturingCostFeePO> {

    List<SupplierQuotationOrderManufacturingCostFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId);

    void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId);
}
