package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierLastCallMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierLastCallMapper;
import com.weifu.srm.sourcing.repository.po.SupplierLastCallPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierLastCallMapperServiceImpl
        extends ServiceImpl<SupplierLastCallMapper, SupplierLastCallPO> implements SupplierLastCallMapperService {

    @Override
    public List<SupplierLastCallPO> queryUsedTimesBy(List<String> sapSupplierCodes, Integer year) {
        return this.lambdaQuery()
                .in(SupplierLastCallPO::getSapSupplierCode, sapSupplierCodes)
                .eq(SupplierLastCallPO::getYear, year)
                .list();
    }
}
