package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.PpapPlanTaskPO;

import java.util.List;

/**
 * <p>
 * PPAP计划-任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface PpapPlanTaskMapperService extends IService<PpapPlanTaskPO> {

    List<PpapPlanTaskPO> listByPlanNo(String planNo);

    List<PpapPlanTaskPO> listByPlanNoAndLiabilityType(String planNo, String liabilityType);

    int countByPlanNoAndComplete(String planNo, String liabilityType, Integer isComplete);

}
