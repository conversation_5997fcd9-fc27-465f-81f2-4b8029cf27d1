package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.PpapPlanPO;
import com.weifu.srm.sourcing.request.ppap.PpapPlanPageReqDTO;

import java.util.List;

/**
 * <p>
 * PPAP计划 Mapper服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface PpapPlanMapperService extends IService<PpapPlanPO> {
    IPage<PpapPlanPO> listPageByQuery(Page<PpapPlanPO> page, PpapPlanPageReqDTO reqDTO);
    PpapPlanPO getByPlanNo(String planNo);
    List<PpapPlanPO> listByPlanNo(List<String> planNoList);



}
