package com.weifu.srm.sourcing.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderSupplierPO;

import java.util.List;

public interface BusinessDesignationOrderSupplierMapperService extends IService<BusinessDesignationOrderSupplierPO> {

    List<BusinessDesignationOrderSupplierPO> queryBy(String designationOrderNo, String requirementPartsNo);

    List<BusinessDesignationOrderSupplierPO> queryBy(String designationOrderNo);

    void deleteBy(String designationOrderNo);

    List<BusinessDesignationOrderSupplierPO> queryDesignationSuppliers(List<String> materialCodes, List<String> statusAry);
}
