package com.weifu.srm.sourcing.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weifu.srm.sourcing.repository.atomicservice.SupplierQuotationOrderPackageFeeMapperService;
import com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderPackageFeeMapper;
import com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPackageFeePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierQuotationOrderPackageFeeMapperServiceImpl
        extends ServiceImpl<SupplierQuotationOrderPackageFeeMapper, SupplierQuotationOrderPackageFeePO>
        implements SupplierQuotationOrderPackageFeeMapperService {

    @Override
    public List<SupplierQuotationOrderPackageFeePO> queryBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        return this.lambdaQuery()
                .eq(quotationOrderId != null, SupplierQuotationOrderPackageFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderPackageFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .list();
    }

    @Override
    public void deleteBy(Long quotationOrderId, Long quotationOrderMaterialId) {
        this.lambdaUpdate()
                .eq(SupplierQuotationOrderPackageFeePO::getQuotationOrderId, quotationOrderId)
                .eq(SupplierQuotationOrderPackageFeePO::getQuotationOrderMaterialId, quotationOrderMaterialId)
                .remove();
    }
}
