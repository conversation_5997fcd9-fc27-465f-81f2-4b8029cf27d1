<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderMapper">

    <select id="queryPage" resultType="com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderPO">
        SELECT *
        FROM business_designation_order bdo
        WHERE bdo.is_delete = 0
        <if test="model.designationOrderNo != null and model.designationOrderNo != ''">
            AND bdo.designation_order_no LIKE CONCAT('%', #{model.designationOrderNo}, '%')
        </if>
        <if test="model.inquiryNo != null and model.inquiryNo != ''">
            AND bdo.inquiry_no LIKE CONCAT('%', #{model.inquiryNo}, '%')
        </if>
        <if test="model.categoryCode != null and model.categoryCode != ''">
            AND bdo.category_code = #{model.categoryCode}
        </if>
        <if test="model.inquiryType != null and model.inquiryType != ''">
            AND bdo.inquiry_type = #{model.inquiryType}
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND bdo.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataPermissionKeys != null and dataPermissionKeys.size() > 0">
            AND (
                <trim prefixOverrides="OR">
                    <foreach collection="dataPermissionKeys" item="dataPermissionKey">
                        <choose>
                            <when test="dataPermissionKey.key == 'MANAGED_CATEGORY'">
                                OR bdo.category_code IN
                                <foreach collection="dataPermissionKey.categoryCodes" open="(" item="item" separator="," close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <when test="dataPermissionKey.key == 'CREATOR'">
                                OR bdo.create_by = #{model.userId}
                            </when>
                            <otherwise>
                                OR 1 = 1
                            </otherwise>
                        </choose>
                    </foreach>
                </trim>
            )
        </if>
        ORDER BY CASE bdo.status WHEN 'DRAFT' THEN 0 ELSE 1 END ASC, bdo.id DESC
    </select>

    <select id="queryPrice" resultType="com.weifu.srm.sourcing.response.BusinessDesignationOrderPriceRespDTO">
        select
        DISTINCT
        tb1.designation_order_no,tb1.inquiry_no,
        tb2.sap_supplier_code,
        tb3.material_code,tb3.price,tb3.price_unit,tb3.requirement_parts_no
        from business_designation_order tb1
        left join business_designation_order_notification tb2 on tb1.designation_order_no=tb2.designation_order_no
        left join business_designation_order_notification_item tb3 on
        tb2.designation_order_notification_no=tb3.designation_order_notification_no
        where
        tb1.is_delete=0 and tb2.is_delete=0 and tb3.is_delete=0 and tb1.status='SENT' and
        tb2.sap_supplier_code=#{sapSupplierNo} and
        tb3.material_code in
        <foreach collection="materialCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>