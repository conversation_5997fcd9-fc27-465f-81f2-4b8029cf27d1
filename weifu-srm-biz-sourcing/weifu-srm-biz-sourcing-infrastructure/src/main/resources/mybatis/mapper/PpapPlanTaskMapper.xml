<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapPlanTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapPlanTaskPO">
        <id column="id" property="id" />
        <result column="plan_no" property="planNo" />
        <result column="grade_config_id" property="gradeConfigId" />
        <result column="file_type" property="fileType" />
        <result column="liability_type" property="liabilityType" />
        <result column="is_lock" property="isLock" />
        <result column="is_required" property="isRequired" />
        <result column="is_need" property="isNeed" />
        <result column="remark" property="remark" />
        <result column="is_complete" property="isComplete" />
        <result column="complete_time" property="completeTime" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`ppap_plan_task`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`plan_no`
    ,`grade_config_id`
    ,`file_type`
    ,`liability_type`
    ,`is_lock`
    ,`is_required`
    ,`is_need`
    ,`remark`
    ,`is_complete`
    ,`complete_time`
    ,`status`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="planNo != null and planNo != ''">
            AND `plan_no` = #{planNo}
        </if>
        <if test="gradeConfigId != null">
            AND `grade_config_id` = #{gradeConfigId}
        </if>
        <if test="fileType != null and fileType != ''">
            AND `file_type` = #{fileType}
        </if>
        <if test="liabilityType != null and liabilityType != ''">
            AND `liability_type` = #{liabilityType}
        </if>
        <if test="isLock != null">
            AND `is_lock` = #{isLock}
        </if>
        <if test="isRequired != null">
            AND `is_required` = #{isRequired}
        </if>
        <if test="isNeed != null">
            AND `is_need` = #{isNeed}
        </if>
        <if test="remark != null and remark != ''">
            AND `remark` = #{remark}
        </if>
        <if test="isComplete != null">
            AND `is_complete` = #{isComplete}
        </if>
        <if test="completeTime != null">
            AND `complete_time` = #{completeTime}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
