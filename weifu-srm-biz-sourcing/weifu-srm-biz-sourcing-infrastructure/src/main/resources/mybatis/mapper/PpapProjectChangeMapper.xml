<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapProjectChangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapProjectChangePO">
        <id column="id" property="id"/>
        <result column="change_no" property="changeNo"/>
        <result column="release_no" property="releaseNo"/>
        <result column="plan_no" property="planNo"/>
        <result column="change_type" property="changeType"/>
        <result column="is_involve_drawings" property="isInvolveDrawings"/>
        <result column="drawing_change_method" property="drawingChangeMethod"/>
        <result column="drawing_no" property="drawingNo"/>
        <result column="drawing_version_no" property="drawingVersionNo"/>
        <result column="old_drawing_no" property="oldDrawingNo"/>
        <result column="old_drawing_version_no" property="oldDrawingVersionNo"/>
        <result column="remark" property="remark"/>
        <result column="restart_phase" property="restartPhase"/>
        <result column="category_code" property="categoryCode"/>
        <result column="division_id" property="divisionId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`ppap_project_change`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        t.`id`
        ,t.`change_no`
        ,t.`release_no`
        ,t.`plan_no`
        ,t.`change_type`
        ,t.`is_involve_drawings`
        ,t.`drawing_change_method`
        ,t.`drawing_no`
        ,t.`drawing_version_no`
        ,t.`old_drawing_no`
        ,t.`old_drawing_version_no`
        ,t.`remark`
        ,t.`restart_phase`
        ,t.`division_id`
        ,t.`category_code`
        ,t.`create_by`
        ,t.`create_time`
        ,t.`create_name`
        ,t.`update_by`
        ,t.`update_time`
        ,t.`update_name`
        ,t.`is_delete`
    </sql>
    <sql id="selectTable">FROM
        ppap_project_change t LEFT JOIN ppap_release t2 on t.release_no = t2.release_no and t2.is_delete = 0
    </sql>

    <sql id="whereSql">
        <where>
            AND t.is_delete = 0
            AND t2.is_delete = 0
            <if test="dto.releaseNo != null and dto.releaseNo != ''">
                AND t.`release_no` like concat("%",#{dto.releaseNo},"%")
            </if>
            <if test="dto.releaseNoEq != null and dto.releaseNoEq != ''">
                AND t.`release_no` = #{dto.releaseNoEq}
            </if>
            <if test="dto.materialNo != null and dto.materialNo != ''">
                AND t2.`material_no` like concat("%",#{dto.materialNo},"%")
            </if>
            <if test="dto.materialDescription != null and dto.materialDescription != ''">
                AND t2.`material_description` like concat("%",#{dto.materialDescription},"%")
            </if>
            <if test="dto.supplierCode != null and dto.supplierCode != ''">
                AND t2.`supplier_code` like concat("%",#{dto.supplierCode},"%")
            </if>
            <if test="dto.supplierCodeEqual != null and dto.supplierCodeEqual != ''">
                AND t2.`supplier_code` = #{dto.supplierCodeEqual}
            </if>
            <if test="dto.isInvolveDrawings != null">
                AND t.`is_involve_drawings` = #{dto.isInvolveDrawings}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                AND t2.`supplier_name` like concat("%",#{dto.supplierName},"%")
            </if>
            <if test="dto.drawingNo != null and dto.drawingNo != ''">
                AND t.`drawing_no` like concat("%",#{dto.drawingNo},"%")
            </if>
            <if test="dto.changeType != null and dto.changeType != ''">
                AND t.`change_type` = #{dto.changeType}
            </if>
            <if test="dto.drawingVersionNo != null and dto.drawingVersionNo != ''">
                AND t.`drawing_version_no` = #{dto.drawingVersionNo}
            </if>
            <if test="dto.oldDrawingNo != null and dto.oldDrawingNo != ''">
                AND t.`old_drawing_no` like concat("%",#{dto.oldDrawingNo},"%")
            </if>
            <if test="dto.oldDrawingVersionNo != null and dto.oldDrawingVersionNo != ''">
                AND t.`old_drawing_version_no` = #{dto.oldDrawingVersionNo}
            </if>
            <if test="dto.changeStartTime != null">
                AND t.`create_time` &gt;=#{dto.changeStartTime}
            </if>
            <if test="dto.changeEndTime != null">
                AND t.`create_time` &lt;=#{dto.changeEndTime}
            </if>
            <if test="dto.createBy != null">
                AND t.`create_by` = #{dto.createBy}
            </if>
            <if test="dto.createByName != null and dto.createByName != null">
                AND t.`create_name` like concat("%",#{dto.createByName},"%")
            </if>
            <if test="dto.categoryCodePermissionList != null or dto.divisionIdPermissionList != null ">
                AND (
                t2.category_code in
                <foreach collection="dto.categoryCodePermissionList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                OR t2.division_id in
                <foreach collection="dto.divisionIdPermissionList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </sql>


    <select id="listPageByQuery" resultType="com.weifu.srm.sourcing.response.ppap.PpapProjectChangeRespDTO">
        SELECT
        t2.`material_no` ,
        t2.`material_description`,
        t2.`supplier_code`,
        t2.`supplier_name`,
        t2.`supplier_name_en`,
        t2.`customer_code`,
        t2.`customer_name`,
        <include refid="baseColumn"/>
        <include refid="selectTable"/>
        <include refid="whereSql"/>
        ORDER BY `id` DESC
    </select>


</mapper>
