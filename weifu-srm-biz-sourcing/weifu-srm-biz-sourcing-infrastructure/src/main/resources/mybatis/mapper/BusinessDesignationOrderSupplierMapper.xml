<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderSupplierMapper">

    <select id="selectDesignationSuppliers" resultType="com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderSupplierPO">
        SELECT DISTINCT bfpos.sap_supplier_code, bfpos.material_code
        FROM business_designation_order_supplier bfpos LEFT JOIN business_designation_order bfpo ON bfpos.designation_order_no = bfpo.designation_order_no
        WHERE bfpo.is_delete = 0 AND bfpos.is_delete = 0
        <if test="materialCodes != null and materialCodes.size() > 0">
            AND bfpos.material_code IN
            <foreach collection="materialCodes" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
        </if>
        <if test="statusAry != null and statusAry.size() > 0">
            AND bfpo.status IN
            <foreach collection="statusAry" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>