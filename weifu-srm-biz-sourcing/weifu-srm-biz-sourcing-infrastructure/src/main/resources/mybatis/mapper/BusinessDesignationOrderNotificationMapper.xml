<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.BusinessDesignationOrderNotificationMapper">

    <select id="queryPage" resultType="com.weifu.srm.sourcing.repository.po.BusinessDesignationOrderNotificationPO">
        SELECT *
        FROM business_designation_order_notification bdon
        WHERE bdon.is_delete = 0
        <if test="model.designationOrderNotificationNo != null and model.designationOrderNotificationNo !=''">
            AND bdon.designation_order_notification_no LIKE CONCAT('%', #{model.designationOrderNotificationNo}, '%')
        </if>
        <choose>
            <when test="model.isAdminView == 1">
                <if test="model.sapSupplierCode != null and model.sapSupplierCode != ''">
                    AND (
                        bdon.sap_supplier_code LIKE CONCAT('%', #{model.sapSupplierCode}, '%') OR
                        bdon.supplier_name LIKE CONCAT('%', #{model.sapSupplierCode}, '%') OR
                        bdon.supplier_name_en LIKE CONCAT('%', #{model.sapSupplierCode}, '%')
                    )
                </if>
            </when>
            <otherwise>
                AND bdon.sap_supplier_code = #{model.sapSupplierCode}
            </otherwise>
        </choose>
        <if test="model.inquiryNo != null and model.inquiryNo != ''">
            AND bdon.inquiry_no LIKE CONCAT('%', #{model.inquiryNo}, '%')
        </if>
        <if test="model.inquiryName != null and model.inquiryName != ''">
            AND bdon.inquiry_name LIKE CONCAT('%', #{model.inquiryName}, '%')
        </if>
        <if test="model.isStrategySupplier != null">
            AND bdon.is_strategy_supplier = #{model.isStrategySupplier}
        </if>
        <if test="model.status != null and model.status != ''">
            AND bdon.status = #{model.status}
        </if>
        <if test="dataPermissionKeys != null and dataPermissionKeys.size() > 0">
            AND (
                <trim prefixOverrides="OR">
                    <foreach collection="dataPermissionKeys" item="dataPermissionKey">
                        <choose>
                            <when test="dataPermissionKey.key == 'MANAGED_CATEGORY'">
                                OR bdon.category_code IN
                                <foreach collection="dataPermissionKey.categoryCodes" open="(" item="item" separator="," close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <when test="dataPermissionKey.key == 'CREATOR'">
                                OR bdon.sent_by = #{model.userId}
                            </when>
                            <otherwise>
                                OR 1 = 1
                            </otherwise>
                        </choose>
                    </foreach>
                </trim>
            )
        </if>
        ORDER BY bdon.create_time DESC
    </select>

    <select id="queryPrice" resultType="com.weifu.srm.sourcing.response.BusinessDesignationOrderPriceRespDTO">
        select
        tb1.designation_order_no,tb1.inquiry_no,
        tb2.sap_supplier_code,
        tb3.material_code,tb3.price,tb3.price_unit
        from business_designation_order tb1
        left join business_designation_order_notice tb2 on tb1.designation_order_no=tb2.designation_order_no
        left join business_designation_order_notice_item tb3 on
        tb2.designation_order_notice_no=tb3.designation_order_notice_no
        where
        tb1.is_delete=0 and tb1.status='SENT' and
        tb2.sap_supplier_code=#{sapSupplierNo} and
        tb3.material_code in
        <foreach collection="materialCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>