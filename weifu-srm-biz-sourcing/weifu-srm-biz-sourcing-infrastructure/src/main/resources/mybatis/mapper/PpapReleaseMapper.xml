<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapReleaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapReleasePO">
        <id column="id" property="id"/>
        <result column="release_no" property="releaseNo"/>
        <result column="project_no" property="projectNo"/>
        <result column="requirement_no" property="requirementNo"/>
        <result column="designation_order_no" property="designationOrderNo"/>
        <result column="designation_order_supplier_id" property="designationOrderSupplierId"/>
        <result column="plan_no" property="planNo"/>
        <result column="category_code" property="categoryCode"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_name_en" property="categoryNameEn"/>
        <result column="material_no" property="materialNo"/>
        <result column="material_description" property="materialDescription"/>
        <result column="requirement" property="requirement"/>
        <result column="customer_code" property="customerCode"/>
        <result column="customer_name" property="customerName"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="supplier_name_en" property="supplierNameEn"/>
        <result column="category_engineer_id" property="categoryEngineerId"/>
        <result column="category_engineer_name" property="categoryEngineerName"/>
        <result column="quality_engineer_id" property="qualityEngineerId"/>
        <result column="quality_engineer_name" property="qualityEngineerName"/>
        <result column="recommend_engineer_id" property="recommendEngineerId"/>
        <result column="recommend_engineer_name" property="recommendEngineerName"/>
        <result column="release_status" property="releaseStatus"/>
        <result column="close_status" property="closeStatus"/>
        <result column="close_source" property="closeSource"/>
        <result column="close_comment" property="closeComment"/>
        <result column="close_time" property="closeTime"/>
        <result column="stage" property="stage"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="is_upload_ots" property="isUploadOts"/>
        <result column="is_upload_process_report" property="isUploadProcessReport"/>
        <result column="is_notice_master" property="isNoticeMaster"/>
        <result column="is_master_confirm" property="isMasterConfirm"/>
        <result column="master_confirm_time" property="masterConfirmTime"/>
        <result column="psw_complete_time" property="pswCompleteTime"/>
        <result column="first_psw_complete_time" property="firstPswCompleteTime"/>
        <result column="is_psw_complete" property="isPswComplete"/>
        <result column="is_project_change" property="isProjectChange"/>
        <result column="division_id" property="divisionId"/>
        <result column="drawing_no" property="drawingNo"/>
        <result column="drawing_version_no" property="drawingVersionNo"/>
        <result column="purchase_group_code" property="purchaseGroupCode"/>
        <result column="approved_time" property="approvedTime"/>
        <result column="cpe_confirm_status" property="cpeConfirmStatus"/>
        <result column="data_source" property="dataSource"/>
        <result column="change_release_no" property="changeReleaseNo"/>
        <result column="close_by" property="closeBy"/>
        <result column="close_name" property="closeName"/>
        <result column="source_type" property="sourceType" />
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_name" property="updateName"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <sql id="tableName">`ppap_release`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
        `id`
        ,`release_no`
        ,`project_no`
        ,`requirement_no`
        ,`designation_order_no`
        ,`designation_order_supplier_id`
        ,`plan_no`
        ,`drawing_no`
        ,`drawing_version_no`
        ,`purchase_group_code`
        ,`category_code`
        ,`category_name`
        ,`category_name_en`
        ,`material_no`
        ,`material_description`
        ,`requirement`
        ,`customer_code`
        ,`customer_name`
        ,`supplier_code`
        ,`supplier_name`
        ,`supplier_name_en`
        ,`category_engineer_id`
        ,`category_engineer_name`
        ,`quality_engineer_id`
        ,`quality_engineer_name`
        ,`recommend_engineer_id`
        ,`recommend_engineer_name`
        ,`release_status`
        ,`close_status`
        ,`close_comment`
        ,`close_time`
        ,`close_by`
        ,`close_name`
        ,`stage`
        ,`execute_status`
        ,`is_upload_ots`
        ,`is_upload_process_report`
        ,`is_notice_master`
        ,`is_master_confirm`
        ,`master_confirm_time`
        ,`psw_complete_time`
        ,`first_psw_complete_time`
        ,`is_psw_complete`
        ,`is_project_change`
        ,`division_id`
        ,`approved_time`
        ,`cpe_confirm_status`
        ,`data_source`
        ,`source_type`
        ,`create_by`
        ,`create_time`
        ,`create_name`
        ,`update_by`
        ,`update_time`
        ,`update_name`
        ,`is_delete`
    </sql>

    <sql id="whereSql">
        <where>
            t1.is_delete = 0
            <if test="dto.designationOrderNo != null and dto.designationOrderNo != ''">
                AND t1.`designation_order_no` like concat('%',#{dto.designationOrderNo},'%')
            </if>
            <if test="dto.releaseNo != null and dto.releaseNo != ''">
                AND t1.`release_no` like concat('%',#{dto.releaseNo},'%')
            </if>
            <if test="dto.categoryCode != null and dto.categoryCode != ''">
                AND t1.`category_code` like concat('%',#{dto.categoryCode},'%')
            </if>
            <if test="dto.materialNo != null and dto.materialNo != ''">
                AND t1.`material_no` = #{dto.materialNo}
            </if>
            <if test="dto.requirementNo != null and dto.requirementNo != ''">
                AND t1.`requirement_no` like concat('%',#{dto.requirementNo},'%')
            </if>
            <if test="dto.requirement != null and dto.requirement != ''">
                AND t1.`requirement` like concat('%',#{dto.requirement},'%')
            </if>
            <if test="dto.supplierCode != null and dto.supplierCode != ''">
                AND t1.`supplier_code` like concat('%',#{dto.supplierCode},'%')
            </if>
            <if test="dto.releaseStatus != null and dto.releaseStatus != ''">
                AND t1.`release_status` = #{dto.releaseStatus}
            </if>
            <if test="dto.categoryCodePermissionList != null or dto.divisionIdPermissionList != null ">
                AND (
                t1.category_code in
                <foreach collection="dto.categoryCodePermissionList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                OR t1.division_id in
                <foreach collection="dto.divisionIdPermissionList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>

    </sql>

    <sql id="reportSelectSql">
        SELECT
        t1.`id`,
        t1.`release_no`,
        t1.`category_code`,
        t1.`category_name`,
        t1.`category_name_en`,
        t1.`material_no`,
        t1.`material_description`,
        t1.`requirement_no`,
        t1.`requirement`,
        t1.`customer_code`,
        t1.`customer_name`,
        t1.`supplier_code`,
        t1.`supplier_name`,
        t1.`supplier_name_en`,
        t1.purchase_group_code as purchaseGroupCode,-- 采购组织
        t4.grade,
        t1.source_type,
        t2.reason_type,
        t1.`category_engineer_id`,
        t1.`category_engineer_name`,
        t1.`quality_engineer_id`,
        t1.`quality_engineer_name`,
        t1.`recommend_engineer_id`,
        t1.`recommend_engineer_name`,
        t2.predict_complete_date,
        t1.`project_no`,
        t1.`designation_order_no`,
        t1.`designation_order_supplier_id`,
        t1.`plan_no`,
        t1.`release_status`,
        t1.`stage`,
        t1.`execute_status`,
        t1.`is_notice_master`,
        t1.`is_master_confirm`,
        t1.`master_confirm_time`,
        t1.`psw_complete_time`,
        t1.`is_psw_complete`,
        t1.`is_project_change`,
        t1.`division_id`,
        t1.`create_by`,
        t1.`create_time`,
        t1.`create_name`,
        t1.`update_by`,
        t1.`update_time`,
        t1.`update_name`,
        t1.drawing_no,
        t1.drawing_version_no,
        t1.`is_delete`
        FROM
        ppap_release t1
        LEFT JOIN ppap_plan t2 on t2.plan_no = t1.plan_no and t2.release_no = t1.release_no and t2.is_delete = 0
        LEFT JOIN ppap_plan_grade t4 ON t2.grade_id = t4.id and t4.is_delete = 0
    </sql>

    <select id="listReportPageByQuery"
            resultType="com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO">
        <include refid="reportSelectSql"/>
        <include refid="whereSql"/>
        order by t1.id desc
    </select>

    <select id="listReportByQuery"
            resultType="com.weifu.srm.sourcing.response.ppap.PpapReleaseReportRespDTO">
        <include refid="reportSelectSql"/>
        <include refid="whereSql"/>
        order by t1.id desc
    </select>

    <select id="selectOptMinTime" resultType="com.weifu.srm.sourcing.repository.bo.PpapEngineeringChangeMinTimeBO">
        SELECT
        MIN(t1.master_confirm_time) as otsOutsourceSubmitCompleteTime,
        MIN(t1.approved_time) as supplierPpapSubmitCompleteTime,
        MIN(t1.psw_complete_time) as supplierPpapCompletionCompleteTime,
        MIN( IF ( t1.release_status = "WAIT_SUBMIT_OTS" OR t1.release_status = "WAIT_ALLOCATION_SQE", NULL, t2.publish_time ) ) AS supplierPpapCompleteCompleteTime
        FROM
        ppap_release t1
        LEFT JOIN ppap_plan t2 ON t1.release_no = t2.release_no and t2.is_delete= 0
        AND t2.plan_no = t1.plan_no
        AND t2.`status` NOT IN ( "PLAN_WITHDRAW", "PLAN_CLOSE" )
        WHERE t1.is_delete= 0
        AND t1.requirement_no = #{requirementNo}
        AND t1.release_status not in ("RELEASE_CLOSE","PPAP_REJECT")
    </select>


    <select id="workbench" resultType="com.weifu.srm.sourcing.response.ppap.PpapReleaseWorkbenchRespDTO">
        SELECT
        count(IF(t.release_status = 'WAIT_SUBMIT_OTS',t.id,null)) as todoNum,
        count(IF(t.release_status = 'WAIT_CONFIRM_OTS' ||
        t.release_status = 'WAIT_ALLOCATION_SQE' ||
        t.release_status = 'PLAN_FORMULATE' ||
        t.release_status = 'PLAN_EXECUTING' ||
        t.release_status = 'IN_APPROVAL' ||
        t.release_status = 'PSW_SIGNING' ,t.id,null)) as inProcessNum,
        count(IF(t.release_status = 'RELEASED',t.id,null)) as completedNum
        FROM
        ppap_release t
        where t.is_delete = 0
        <if test="operateBy != null">
            AND (
                t.category_engineer_id = #{operateBy} or t.quality_engineer_id = #{operateBy}
            )
        </if>
    </select>
    <select id="executeSituation" resultType="com.weifu.srm.sourcing.response.ppap.PpapReleaseEngineerRowRespDTO">
        SELECT
        <if test="roleId == 'CPE'">
            category_engineer_id as engineerId ,
            category_engineer_name as engineerName,
        </if>
        <if test="roleId == 'SQE'">
            quality_engineer_id as engineerId ,
            quality_engineer_name as engineerName,
        </if>
        count(IF(t.release_status = 'WAIT_SUBMIT_OTS',t.id,null)) as todoNum,
        count(IF(t.release_status = 'WAIT_CONFIRM_OTS' ||
        t.release_status = 'WAIT_ALLOCATION_SQE' ||
        t.release_status = 'PLAN_FORMULATE' ||
        t.release_status = 'PLAN_EXECUTING' ||
        t.release_status = 'IN_APPROVAL' ||
        t.release_status = 'PSW_SIGNING' ,t.id,null)) as inProcessNum,
        count(IF(t.release_status = 'RELEASED',t.id,null)) as completedNum
        FROM
        ppap_release t
        where t.is_delete = 0
        <if test="roleId == 'CPE'">
            group by category_engineer_id,category_engineer_name
        </if>
        <if test="roleId == 'SQE'">
            group by quality_engineer_id,quality_engineer_name
        </if>
    </select>
    <sql id="ownerWhere">
        is_delete = 0
        and
        (
        (
        category_engineer_id = #{operationBy}
        and release_status in ("WAIT_SUBMIT_OTS","PLAN_EXECUTING")
        )
        <if test="cpeCategoryCodeList != null and cpeCategoryCodeList.size != 0">
            or
            (
            `category_code` in
            <foreach collection="cpeCategoryCodeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            and release_status in ("WAIT_CONFIRM_OTS")
            )
        </if>
        <if test="sqeCategoryCodeList != null and sqeCategoryCodeList.size != 0">
            or
            (
            `category_code` in
            <foreach collection="sqeCategoryCodeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            and release_status in ("WAIT_ALLOCATION_SQE")
            )
        </if>
        or
        (
        `quality_engineer_id` = #{operationBy}
        and release_status in ("PLAN_FORMULATE","PLAN_EXECUTING","PSW_SIGNING")
        )
        )
    </sql>
    <select id="countByInternal" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        ppap_release
        WHERE
        <include refid="ownerWhere"/>
    </select>

    <select id="listByPendingProcessing" resultMap="BaseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM
        ppap_release
        WHERE
        <include refid="ownerWhere"/>
    </select>
</mapper>
