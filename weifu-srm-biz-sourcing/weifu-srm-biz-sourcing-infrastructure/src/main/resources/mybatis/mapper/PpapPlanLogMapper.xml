<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapPlanLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapPlanLogPO">
        <id column="id" property="id" />
        <result column="plan_no" property="planNo" />
        <result column="content" property="content" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`ppap_plan_log`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`plan_no`
    ,`content`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="planNo != null and planNo != ''">
            AND `plan_no` = #{planNo}
        </if>
        <if test="content != null and content != ''">
            AND `content` = #{content}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
