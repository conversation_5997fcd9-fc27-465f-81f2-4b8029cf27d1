<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.BiddingSupplierMapper">

    <select id="selectCountBySupplier" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM bidding b INNER JOIN bidding_supplier bs ON b.bidding_no = bs.bidding_no
        WHERE bs.sap_supplier_code = #{sapSupplierCode}
        <if test="status != null and status != ''">
            AND b.status = #{status}
        </if>
        <if test="biddingIntention != null">
            AND (bs.bidding_intention = #{biddingIntention}
            <if test="feedbackIntention != null and feedbackIntention == 1">
                OR bs.bidding_intention IS NULL
            </if>
            )
        </if>
    </select>
</mapper>