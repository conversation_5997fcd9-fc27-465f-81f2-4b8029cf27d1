<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapPlanPO">
        <id column="id" property="id" />
        <result column="plan_no" property="planNo" />
        <result column="release_no" property="releaseNo" />
        <result column="category_code" property="categoryCode" />
        <result column="category_name" property="categoryName" />
        <result column="category_name_en" property="categoryNameEn" />
        <result column="material_no" property="materialNo" />
        <result column="material_description" property="materialDescription" />
        <result column="supplier_code" property="supplierCode" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_name_en" property="supplierNameEn" />
        <result column="category_engineer_id" property="categoryEngineerId" />
        <result column="category_engineer_name" property="categoryEngineerName" />
        <result column="quality_engineer_id" property="qualityEngineerId" />
        <result column="quality_engineer_name" property="qualityEngineerName" />
        <result column="status" property="status" />
        <result column="grade_id" property="gradeId" />
        <result column="grade_version" property="gradeVersion" />
        <result column="reason_type" property="reasonType" />
        <result column="plan_release_time" property="planReleaseTime" />
        <result column="predict_complete_date" property="predictCompleteDate" />
        <result column="is_need_report" property="isNeedReport" />
        <result column="division_id" property="divisionId" />
        <result column="publish_time" property="publishTime" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`ppap_plan`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`plan_no`
    ,`release_no`
    ,`category_code`
    ,`category_name`
    ,`category_name_en`
    ,`material_no`
    ,`material_description`
    ,`supplier_code`
    ,`supplier_name`
    ,`supplier_name_en`
    ,`category_engineer_id`
    ,`category_engineer_name`
    ,`quality_engineer_id`
    ,`quality_engineer_name`
    ,`status`
    ,`grade_id`
    ,`grade_version`
    ,`reason_type`
    ,`plan_release_time`
    ,`predict_complete_date`
    ,`is_need_report`
    ,`division_id`
    ,`publish_time`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="planNo != null and planNo != ''">
            AND `plan_no` = #{planNo}
        </if>
        <if test="releaseNo != null and releaseNo != ''">
            AND `release_no` = #{releaseNo}
        </if>
        <if test="categoryCode != null and categoryCode != ''">
            AND `category_code` = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND `category_name` = #{categoryName}
        </if>
        <if test="categoryNameEn != null and categoryNameEn != ''">
            AND `category_name_en` = #{categoryNameEn}
        </if>
        <if test="materialNo != null and materialNo != ''">
            AND `material_no` = #{materialNo}
        </if>
        <if test="materialDescription != null and materialDescription != ''">
            AND `material_description` = #{materialDescription}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            AND `supplier_code` = #{supplierCode}
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND `supplier_name` = #{supplierName}
        </if>
        <if test="supplierNameEn != null and supplierNameEn != ''">
            AND `supplier_name_en` = #{supplierNameEn}
        </if>
        <if test="categoryEngineerId != null">
            AND `category_engineer_id` = #{categoryEngineerId}
        </if>
        <if test="categoryEngineerName != null and categoryEngineerName != ''">
            AND `category_engineer_name` = #{categoryEngineerName}
        </if>
        <if test="qualityEngineerId != null">
            AND `quality_engineer_id` = #{qualityEngineerId}
        </if>
        <if test="qualityEngineerName != null and qualityEngineerName != ''">
            AND `quality_engineer_name` = #{qualityEngineerName}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="gradeId != null">
            AND `grade_id` = #{gradeId}
        </if>
        <if test="reasonType != null and reasonType != ''">
            AND `reason_type` = #{reasonType}
        </if>
        <if test="planReleaseTime != null">
            AND `plan_release_time` = #{planReleaseTime}
        </if>
        <if test="predictCompleteDate != null">
            AND `predict_complete_date` = #{predictCompleteDate}
        </if>
        <if test="isNeedReport != null">
            AND `is_need_report` = #{isNeedReport}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
