<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.PpapPlanGradeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.weifu.srm.sourcing.repository.po.PpapPlanGradeConfigPO">
        <id column="id" property="id" />
        <result column="file_no" property="fileNo" />
        <result column="file_type" property="fileType" />
        <result column="liability_type" property="liabilityType" />
        <result column="is_lock" property="isLock" />
        <result column="required" property="required" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_name" property="createName" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_name" property="updateName" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <sql id="tableName">`ppap_plan_grade_config`</sql>
    <!-- 通用查询结果列 -->
    <sql id="baseColumn">
    `id`
    ,`file_no`
    ,`file_type`
    ,`liability_type`
    ,`is_lock`
    ,`required`
    ,`create_by`
    ,`create_time`
    ,`create_name`
    ,`update_by`
    ,`update_time`
    ,`update_name`
    ,`is_delete`
    </sql>


    <sql id="whereSql">
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="fileNo != null and fileNo != ''">
            AND `file_no` = #{fileNo}
        </if>
        <if test="fileType != null and fileType != ''">
            AND `file_type` = #{fileType}
        </if>
        <if test="liabilityType != null and liabilityType != ''">
            AND `liability_type` = #{liabilityType}
        </if>
        <if test="isLock != null">
            AND `is_lock` = #{isLock}
        </if>
        <if test="required != null and required != ''">
            AND `required` = #{required}
        </if>
    </sql>

    <sql id="pageSql">
        <if test="pageSize != null and pageSize != 0">
            LIMIT #{startIndex,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </sql>






</mapper>
