<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderMaterialMapper">

    <select id="queryPreviousMassProdPriceBy"
        resultType="com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderMaterialPO">
        SELECT
            sqom.*
        FROM supplier_quotation_order_material sqom INNER JOIN inquiry_order io ON sqom.inquiry_no = io.inquiry_no
        WHERE sqom.is_delete = 0 AND io.is_delete = 0 AND sqom.quotation_status = 1
        AND sqom.quotation_round = io.inquiry_round
        AND sqom.inquiry_no &lt;&gt; #{excludeInquiryNo}
        AND sqom.sap_supplier_code = #{sapSupplierCode}
        AND sqom.material_code = #{materialCode}
        AND io.status = #{inquiryStatus}
        <if test="quotationTemplates != null and quotationTemplates.size() > 0">
            AND sqom.quotation_template IN
                <foreach collection="quotationTemplates" open="(" separator="," item="item" close=")">
                    #{item}
                </foreach>
        </if>
        ORDER BY io.id DESC LIMIT 1
    </select>
</mapper>