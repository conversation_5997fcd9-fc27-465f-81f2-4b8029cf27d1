<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.InquiryOrderMapper">

    <select id="queryPage" resultType="com.weifu.srm.sourcing.repository.po.InquiryOrderPO">
        SELECT DISTINCT io.*
        FROM inquiry_order io
             <if test="model.materialCode != null and model.materialCode != ''">
             LEFT JOIN inquiry_order_material iom ON io.inquiry_no = iom.inquiry_no
             </if>
        WHERE io.is_delete = 0
        <if test="model.inquiryNo != null and model.inquiryNo != ''">
            AND io.inquiry_no LIKE CONCAT('%', #{model.inquiryNo}, '%')
        </if>
        <if test="model.categoryCode != null and model.categoryCode != ''">
            AND io.category_code = #{model.categoryCode}
        </if>
        <if test="model.materialCode != null and model.materialCode != ''">
            AND iom.is_delete = 0
            AND iom.material_code LIKE CONCAT('%', #{model.materialCode}, '%')
        </if>
        <if test="model.inquiryType != null and model.inquiryType != ''">
            AND io.inquiry_type = #{model.inquiryType}
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND io.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataPermissionKeys != null and dataPermissionKeys.size() > 0">
           AND (
                <trim prefixOverrides="OR">
                    <foreach collection="dataPermissionKeys" item="dataPermissionKey">
                       <choose>
                            <when test="dataPermissionKey.key == 'MANAGED_CATEGORY'">
                                OR io.category_code IN
                                <foreach collection="dataPermissionKey.categoryCodes" open="(" item="item" separator="," close=")">
                                    #{item}
                                </foreach>
                            </when>
                            <when test="dataPermissionKey.key == 'CREATOR'">
                                OR io.create_by = #{model.userId}
                            </when>
                            <otherwise>
                                OR 1 = 1
                            </otherwise>
                       </choose>
                    </foreach>
                </trim>
           )
        </if>
        ORDER BY CASE io.status WHEN 'DRAFT' THEN 0 ELSE 1 END ASC, io.id DESC
    </select>
</mapper>