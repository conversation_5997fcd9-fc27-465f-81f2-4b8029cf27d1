<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.BiddingMapper">

    <select id="queryPage" resultType="com.weifu.srm.sourcing.repository.po.BiddingPO">
        SELECT DISTINCT
        b.*
        FROM bidding b <if test="model.supplier != null and model.supplier != ''">LEFT JOIN bidding_supplier bs ON b.bidding_no = bs.bidding_no</if>
        WHERE b.is_delete = 0 <if test="model.supplier != null and model.supplier != ''">AND bs.is_delete = 0</if>
        <if test="model.biddingNo != null and model.biddingNo != ''">
            AND b.bidding_no LIKE CONCAT('%', #{model.biddingNo}, '%')
        </if>
        <if test="model.biddingName != null and model.biddingName != ''">
            AND b.bidding_name LIKE CONCAT('%', #{model.biddingName}, '%')
        </if>
        <if test="model.submitBy != null and model.submitBy != ''">
            AND b.submit_by = #{model.submitBy}
        </if>
        <if test="model.supplier != null and model.supplier != ''">
            AND (
                bs.sap_supplier_code LIKE CONCAT('%', #{model.supplier}, '%') OR
                bs.supplier_name LIKE CONCAT('%', #{model.supplier}, '%') OR
                bs.supplier_name_en LIKE CONCAT('%', #{model.supplier}, '%')
            )
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND b.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataPermissionKeys != null and dataPermissionKeys.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <foreach collection="dataPermissionKeys" item="dataPermissionKey">
                    <choose>
                        <when test="dataPermissionKey.key == 'CREATOR'">
                            OR b.create_by = #{model.userId}
                        </when>
                        <otherwise>
                            OR 1 = 1
                        </otherwise>
                    </choose>
                </foreach>
            </trim>
            )
        </if>
        ORDER BY CASE b.status WHEN 'DRAFT' THEN 0 ELSE 1 END ASC, b.bidding_start_time DESC
    </select>

    <select id="queryPage4Supplier" resultType="com.weifu.srm.sourcing.response.bidding.SupplierBiddingListRespDTO">
        SELECT DISTINCT
        b.*,
        bs.sap_supplier_code,
        bs.supplier_name,
        bs.supplier_name_en,
        bs.bidding_intention,
        bs.is_opened_windows,
        bsi.ranking
        FROM  bidding b INNER JOIN bidding_supplier bs ON b.bidding_no = bs.bidding_no
              LEFT JOIN bidding_supplier_item bsi ON b.bidding_no = bsi.bidding_no AND b.round = bsi.round AND bs.sap_supplier_code = bsi.sap_supplier_code
        WHERE b.is_delete = 0 AND bs.is_delete = 0
        AND bs.sap_supplier_code = #{model.sapSupplierCode}
        <if test="model.biddingNo != null and model.biddingNo != ''">
            AND b.bidding_no LIKE CONCAT('%', #{model.biddingNo}, '%')
        </if>
        <if test="model.biddingName != null and model.biddingName != ''">
            AND b.bidding_name LIKE CONCAT('%', #{model.biddingName}, '%')
        </if>
        <if test="model.biddingIntention != null">
            <choose>
                <when test="model.biddingIntention == 0 or model.biddingIntention == 1">
                    AND bs.bidding_intention = #{model.biddingIntention}
                </when>
                <otherwise>
                    AND bs.bidding_intention IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND b.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY b.submit_time DESC
    </select>

    <select id="querySubmitter" resultType="com.weifu.srm.sourcing.response.bidding.BiddingSubmitterRespDTO">
        SELECT DISTINCT
            submit_by,
            submit_name
        FROM bidding b
        WHERE b.is_delete = 0 AND b.submit_by IS NOT NULL
        <if test="dataPermissionKeys != null and dataPermissionKeys.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <foreach collection="dataPermissionKeys" item="dataPermissionKey">
                    <choose>
                        <when test="dataPermissionKey.key == 'CREATOR'">
                            OR b.submit_by = #{userId}
                        </when>
                        <otherwise>
                            OR 1 = 1
                        </otherwise>
                    </choose>
                </foreach>
            </trim>
            )
        </if>
    </select>
</mapper>