<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.weifu.srm.sourcing.repository.mapper.SupplierQuotationOrderMapper">

    <select id="queryPage" resultType="com.weifu.srm.sourcing.response.QuotationOrderRespDTO">
        SELECT
        io.inquiry_no,
        io.inquiry_name,
        io.inquiry_type,
        sqo.sap_supplier_code,
        sqo.supplier_name,
        sqo.supplier_name_en,
        sqo.invite_quotation_time,
        sqo.quotation_feedback_deadline,
        sqo.quotation_cutoff_deadline,
        sqo.status AS inquiry_status,
        sqo.quotation_round AS inquiry_round,
        sqo.quotation_intention,
        sqo.quotation_status,
        sqo.is_opened_windows,
        sqo.data_status
        FROM inquiry_order io
        INNER JOIN supplier_quotation_order sqo ON io.inquiry_no = sqo.inquiry_no
        <if test="model.materialCode != null and model.materialCode != ''">
            INNER JOIN supplier_quotation_order_material sqom ON sqom.quotation_order_id = sqo.id
        </if>
        WHERE io.is_delete = 0 AND sqo.is_delete = 0 AND sqo.is_show = 1
        <if test="model.isProxyQuotation != null">
            AND io.is_proxy_quotation = #{model.isProxyQuotation}
        </if>
        <if test="model.inquiryNo != null and model.inquiryNo != ''">
            AND io.inquiry_no LIKE CONCAT('%', #{model.inquiryNo}, '%')
        </if>
        <if test="model.sapSupplierCode != null and model.sapSupplierCode != ''">
            AND sqo.sap_supplier_code = #{model.sapSupplierCode}
        </if>
        <if test="model.materialCode != null and model.materialCode != ''">
            AND sqom.material_code LIKE CONCAT('%', #{model.materialCode}, '%')
        </if>
        <if test="model.inquiryType != null and model.inquiryType != ''">
            AND io.inquiry_type = #{model.inquiryType}
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND sqo.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="model.quotationIntentionOverview != null and model.quotationIntentionOverview == 1">
                AND sqo.quotation_intention IS NOT NULL
            </when>
            <when test="model.quotationIntentionOverview != null and model.quotationIntentionOverview == 0">
                AND sqo.quotation_intention IS NULL
            </when>
        </choose>
        <if test="model.quotationIntention != null">
            AND sqo.quotation_intention = #{model.quotationIntention}
        </if>
        <if test="model.quotationStatus != null and model.quotationStatus != ''">
            AND sqo.quotation_status = #{model.quotationStatus}
        </if>
        <if test="model.inquiryCreateBy != null">
            AND io.create_by = #{model.inquiryCreateBy}
        </if>
        ORDER BY sqo.invite_quotation_time DESC
    </select>

    <select id="queryHistoryPage" resultType="com.weifu.srm.sourcing.response.QuotationOrderHistoryRespDTO">
        SELECT DISTINCT
        io.inquiry_no,
        io.inquiry_name,
        io.inquiry_type,
        sqo.sap_supplier_code,
        sqo.supplier_name,
        sqo.supplier_name_en,
        sqo.status AS inquiry_status,
        sqo.quotation_round AS inquiry_round,
        sqo.quotation_status,
        sqom.requirement_parts_no,
        sqom.material_code,
        sqom.quotation_template,
        sqom.logistics_destination,
        sqom.id AS quotationOrderMaterialId
        FROM inquiry_order io
        INNER JOIN supplier_quotation_order sqo ON io.inquiry_no = sqo.inquiry_no
        INNER JOIN supplier_quotation_order_material sqom ON sqom.quotation_order_id = sqo.id
        WHERE io.is_delete = 0 AND sqo.is_delete = 0 AND sqom.quotation_status = 1
        AND io.inquiry_round = sqo.quotation_round
        <if test="model.excludeInquiryNo != null and model.excludeInquiryNo != ''">
            AND io.inquiry_no &lt;&gt; #{model.excludeInquiryNo}
        </if>
        <if test="model.status != null and model.status.size() > 0">
            AND io.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.sapSupplierCode != null and model.sapSupplierCode != ''">
            AND sqo.sap_supplier_code = #{model.sapSupplierCode}
        </if>
        <if test="model.materialCode != null and model.materialCode != ''">
            AND sqom.material_code = #{model.materialCode}
        </if>
        <if test="model.quotationTemplate != null and model.quotationTemplate != ''">
            AND sqom.quotation_template = #{model.quotationTemplate}
        </if>
        <if test="model.purchaseGroupCode != null and model.purchaseGroupCode != ''">
            AND sqom.purchase_group_code = #{model.purchaseGroupCode}
        </if>
    </select>

    <select id="queryForSampleOrderRef" resultType="com.weifu.srm.sourcing.response.SampleOrderRefRespDTO">
        SELECT DISTINCT
        io.id,
        io.inquiry_no,
        sqom.requirement_parts_no,
        sqom.material_code,
        sqom.quotation_template,
        sqom.id AS quotationOrderMaterialId
        FROM inquiry_order io
        INNER JOIN supplier_quotation_order sqo ON io.inquiry_no = sqo.inquiry_no
        INNER JOIN supplier_quotation_order_material sqom ON sqom.quotation_order_id = sqo.id
        WHERE io.is_delete = 0 AND sqo.is_delete = 0 AND sqom.quotation_status = 1
        AND io.inquiry_round = sqo.quotation_round
        <if test="model.status != null and model.status.size() > 0">
            AND io.status IN
            <foreach collection="model.status" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.sapSupplierCode != null and model.sapSupplierCode != ''">
            AND sqo.sap_supplier_code = #{model.sapSupplierCode}
        </if>
        <if test="model.materialCode != null and model.materialCode != ''">
            AND sqom.material_code = #{model.materialCode}
        </if>
        <if test="model.purchaseGroupCode != null and model.purchaseGroupCode != ''">
            AND sqom.purchase_group_code = #{model.purchaseGroupCode}
        </if>
        ORDER BY io.id DESC
    </select>

    <select id="queryLatestRoundQuotationOrderBy"
            resultType="com.weifu.srm.sourcing.repository.po.SupplierQuotationOrderPO">
        SELECT
            sqo.*
        FROM supplier_quotation_order sqo
             LEFT JOIN inquiry_order io ON sqo.inquiry_no = io.inquiry_no
        WHERE sqo.quotation_round = io.inquiry_round AND sqo.is_delete = 0
        <if test="inquiryNos != null and inquiryNos.size() > 0">
            AND sqo.inquiry_no IN
            <foreach collection="inquiryNos" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>