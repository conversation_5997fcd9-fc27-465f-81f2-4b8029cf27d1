<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.weifu</groupId>
        <artifactId>weifu-srm-biz-sourcing</artifactId>
        <version>1.0.2</version>
    </parent>

    <artifactId>weifu-srm-biz-sourcing-infrastructure</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-biz-sourcing-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mybatis</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-cache</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common-util</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-common</artifactId>
            <scope>compile</scope>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-framework-starter-mq-sender</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.weifu</groupId>
            <artifactId>weifu-srm-common-user-api</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
</project>
